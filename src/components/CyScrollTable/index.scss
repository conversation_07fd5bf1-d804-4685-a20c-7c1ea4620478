.comp-cy-table {
    background-color: #fff;
    position: relative;
    flex: 1;
    &-col {
        display: flex;
        flex-direction: row;
        background-color: #fff;
        z-index: 9999;
        &-sroll {
            flex: 1;
        }
    }
    &-container {
        display: flex;
        flex-direction: row;
        width: 100%;
    }
    &-left {
        // width: 180px;
    }
    &-right {
        width: 570px;
        flex: 1;
    }
    &-titlecontent {
        display: flex;
        flex-direction: row;
    }
    &-scrolly {
        &-scrollx {
            flex: 1;
            display: flex;
            flex-direction: column;
            &-title {
                display: flex;
                flex-direction: row;
                flex: 1;
            }
        }
    }
}

.right-content-title {
    display: flex;
    flex-direction: row;
    flex: 1;
}

.right-content {
    display: flex;
    flex-direction: column;
    flex: 1;
    /* #ifdef h5 */
    width: fit-content;
    /* #endif  */
}

.fixed-left-content {
    display: flex;
    flex-direction: column;
}

.title {
    text-align: center;
    background-color: #4d7bff;
    color: #fff;
    height: 100px;
    font-size: 28px;
    border-right: 1px solid #fff;
    width: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
}

.value {
    text-align: center;
    color: #333;
    height: 100px;
    width: 100%;
    font-size: 28px;
    border-right: 1px solid #fff;
    display: flex;
    justify-content: center;
    align-items: center;
}

.total-value {
    background-color: #ADB2C1;
    color: #fff;
}

.action-value {
    color: #4d7bff;
}