import {useState, useEffect} from 'react';
import {ScrollView, Text, View} from '@tarojs/components';
import Popup from '~components/Popup';
import { IRepaymentType, ILendingTrailSchduleItem } from 'src/types/common-types';
import styles from './index.module.scss';
import {showModal, showToast} from '~utils/common';
import {formatToFixedTwo} from '~utils/number';
import RepaymentPlanItem from './components/RepaymentProcessItem';

type RepaymenModeItem = {
  repaymentType: string;
  repaymentTypeDesc: string;
  repaymentTypeName: string;
  terms: number[]
}
export interface RepaymentProps {
  visible?: boolean;
  amountRepaymentType?: IRepaymentType;
  repaymentModes: RepaymenModeItem[];
  schedules: ILendingTrailSchduleItem[];
  currentPeriod: number;
  onClose?: () => void;
  onChange?: (key: IRepaymentType, isClose?: boolean) => void;
}

const repaymentTypeExplain = {
	MCEP: '每月还相同金额的本金，和剩余借款的利息',
	MCIP: '先还利息，到期还本金',
};


export default (props: RepaymentProps) => {
  const {visible = false, amountRepaymentType, repaymentModes, schedules,currentPeriod, onChange, onClose} = props;
  const [repaymentType, setRepaymentType] = useState('');

  useEffect(() => {
    setRepaymentType(amountRepaymentType || '');
  }, [amountRepaymentType]);

  const handleChooseWay = (type: IRepaymentType) => {
    if (type === 'MCIP' && repaymentType !== 'MCIP' && !getMCIPTerems().includes(currentPeriod)) {
      showModal({
        title: '温馨提示',
        content: `“先息后本”不支持当前期限，请修改借款期限`,
        confirmText: `去修改`,
        confirmColor: '#2F54EB',
        cancelText: '取消',
        cancelColor: '#999',
        success: function (res) {
          if (res.confirm) {
            setRepaymentType(type);
            onChange?.(type, false);
          }
        }
      })
    } else {
      setRepaymentType(type);
      onChange?.(type, true);
    }
  };

  const getMaxPeriod = () => {
    const findItem = repaymentModes.find(item => item.repaymentType === 'MCIP');
    const terms = [...(findItem?.terms || [])];
    return Math.max(...terms);
  }

  const getMCIPTerems = () => { 
    const findItem = repaymentModes.find(item => item.repaymentType === 'MCIP');
    return findItem?.terms || [];
  }

  return (
    <Popup
      visible={visible}
      title='怎么还'
      note='仅供参考，实际还款计划以借款成功后实际生成为准'
      showClose
      onClose={onClose}
      bodyStyle={{height: '75vh'}}>
      <View className={styles.repayment_container}>
        <View className={styles.repayment_way}>
          {
            (repaymentModes || []).map(item => (
              <View
                className={`${styles.repayment_item} ${repaymentType === item.repaymentType ? styles.repayment_item_active : ''}`}
                onClick={() => handleChooseWay(item.repaymentType as IRepaymentType)}>
                <View className={styles.repayment_item_title}>{item.repaymentTypeName}</View>
                <View className={styles.repayment_item_note}>{repaymentTypeExplain[item.repaymentType as IRepaymentType]}</View>
              </View>
            ))
          }
        </View>
        {/* <View className={styles.repayment_tip} style={{border: 'none'}}>
          借满12期，使用优惠券最多可省利息<Text className={styles.repayment_r}>123456.00元</Text>
        </View> */}
        <ScrollView scrollY className={styles.repayment_list}>
          {(schedules || []).map((item, index) => (
            <RepaymentPlanItem key={index} left={
              <View className={styles.repayment_cell_left}>
                <View className={styles.repayment_cell_term}>{`第${item.currentTerm}期`}</View>
                <View className={styles.repayment_cell_note}>{item.dueDate}</View>
                <View className={styles.repayment_cell_note}>{`(共${item.days}天)`}</View>
              </View>
            } right={
              <View className={styles.repayment_cell_right}>
                <View className={styles.repayment_cell_money}>{item.totalAmount}元</View>
                <View className={styles.repayment_cell_note}>{`含本金${formatToFixedTwo(item.principal)}+利息${formatToFixedTwo(item.interest)}`}</View>
              </View>
            } />
            // <View className={styles.repayment_list_cell} key={index}>
            //   <View className={styles.repayment_cell_left}>
            //     <View className={styles.repayment_cell_term}>{`第${item.currentTerm}期`}</View>
            //     <View className={styles.repayment_cell_note}>{item.dueDate}</View>
            //     <View className={styles.repayment_cell_note}>{`(共${item.days}天)`}</View>
            //   </View>
            //   <View className={styles.repayment_cell_middle}>
            //     <View className={styles.repayment_cell_line}></View>
            //     <View className={styles.repayment_cell_line}></View>
            //     <View className={styles.repayment_cell_dot}></View>
            //   </View>
            //   <View className={styles.repayment_cell_right}>
            //     <View className={styles.repayment_cell_money}>{item.totalAmount}元</View>
            //     <View className={styles.repayment_cell_note}>{`含本金${formatToFixedTwo(item.principal)}+利息${formatToFixedTwo(item.interest)}`}</View>
            //   </View>
            // </View>
          ))}
        </ScrollView>
      </View>
    </Popup>
  );
};
