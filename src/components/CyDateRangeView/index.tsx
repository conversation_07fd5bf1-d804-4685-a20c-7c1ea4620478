import {FC, Fragment, useEffect, useState} from 'react';
import {BaseEvent<PERSON><PERSON>, Picker, PickerDateProps, View} from '@tarojs/components';
import {useLoad} from '@tarojs/taro';
import dayjs from 'dayjs';
import {isEmpty} from '../../utils/common';
import './index.scss';

export type DateRangeType = {
  startDt: string;
  endDt: string;
};

type Props = {
  onDateRangeChange?: (dateRange: DateRangeType) => void;
  isYesterday?: boolean;
  defaultIndex?: number;
  defaultRange?: 'now' | 'yesterday' | 'year' | 'none';
  quickOptions?: string[];
};

/**
 * 组件名称：时间区间选择组件
 * 组件描述：时间区间选择
 */
const CyDateRangeView: FC<Props> = props => {
  const {
    onDateRangeChange,
    isYesterday = false,
    defaultIndex = 0,
    defaultRange = 'now',
    quickOptions = ['day', 'week', 'month'],
  } = props;

  const nowDate = dayjs().format('YYYY-MM-DD');
  const yesterdayDate = dayjs(nowDate, 'YYYY-MM-DD').subtract(1, 'day').format('YYYY-MM-DD');

  const [dateIndex, setDateIndex] = useState(defaultIndex);
  const [startDt, setStartDt] = useState('');
  const [endDt, setEndDt] = useState('');

  const [custStartDt, setCustStartDt] = useState('');
  const [custEndDt, setCustEndDt] = useState('');

  useEffect(() => {
    if (defaultIndex === 0) {
      if (isYesterday) {
        setCustStartDt(yesterdayDate);
        setCustEndDt(yesterdayDate);
      } else {
        if (defaultRange === 'yesterday') {
          setCustStartDt(yesterdayDate);
          setCustEndDt(yesterdayDate);
        } else if (defaultRange === 'year') {
          const startYearDay = dayjs().startOf('year').format('YYYY-MM-DD');
          setCustStartDt(startYearDay);
          setCustEndDt(yesterdayDate);
        } else {
          setCustStartDt(nowDate);
          setCustEndDt(nowDate);
        }
      }
    } else if (defaultIndex === 1) {
      const startWeekDay = dayjs().startOf('week').format('YYYY-MM-DD');
      setCustStartDt(startWeekDay);
      setCustEndDt(nowDate);
    } else if (defaultIndex === 2) {
      const startMonthDay = dayjs().startOf('month').format('YYYY-MM-DD');
      setCustStartDt(startMonthDay);
      setCustEndDt(nowDate);
    } else {
      if (defaultRange === 'yesterday') {
        setCustStartDt(yesterdayDate);
        setCustEndDt(yesterdayDate);
      } else if (defaultRange === 'year') {
        const startYearDay = dayjs().startOf('year').format('YYYY-MM-DD');
        setCustStartDt(startYearDay);
        setCustEndDt(yesterdayDate);
      } else if (defaultRange === 'none') {
        setCustStartDt('');
        setCustEndDt('');
      } else {
        setCustStartDt(nowDate);
        setCustEndDt(nowDate);
      }
    }
  }, []);

  useEffect(() => {
    onDateRangeChange && onDateRangeChange({startDt, endDt});
  }, [startDt, endDt]);

  useEffect(() => {
    setStartDt(custStartDt);
    setEndDt(custEndDt);
  }, [custEndDt, custStartDt]);

  const dateItemClick = (index: number) => {
    setDateIndex(index);
    if (index === 0) {
      //本日
      setCustStartDt(isYesterday ? yesterdayDate : nowDate);
      setCustEndDt(isYesterday ? yesterdayDate : nowDate);
    } else if (index === 1) {
      //本周
      const startWeekDay = dayjs().startOf('week').format('YYYY-MM-DD');
      setCustStartDt(startWeekDay);
      setCustEndDt(isYesterday ? yesterdayDate : nowDate);
    } else if (index === 2) {
      const startMonthDay = dayjs().startOf('month').format('YYYY-MM-DD');
      setCustStartDt(startMonthDay);
      setCustEndDt(isYesterday ? yesterdayDate : nowDate);
    } else {
      setStartDt(custStartDt);
      setEndDt(custEndDt);
    }
  };
  const onDateChange = (e: BaseEventOrig<PickerDateProps.ChangeEventDetail>, flag: string) => {
    // if (dateIndex !== 3) {
    //   return;
    // }
    const date = e.detail.value;
    if (flag === 'start') {
      setCustStartDt(date);
    } else {
      setCustEndDt(date);
    }
    setDateIndex(3);
  };
  return (
    <View className='comp-cy-date-range-view'>
      {quickOptions.includes('day') && (
        <View className={`date-item ${dateIndex === 0 ? 'select' : 'unselect'}`} onClick={() => dateItemClick(0)}>
          {isYesterday ? '昨日' : '今日'}
        </View>
      )}
      {quickOptions.includes('week') && (
        <View className={`date-item ${dateIndex === 1 ? 'select' : 'unselect'}`} onClick={() => dateItemClick(1)}>
          本周
        </View>
      )}
      {quickOptions.includes('month') && (
        <View className={`date-item ${dateIndex === 2 ? 'select' : 'unselect'}`} onClick={() => dateItemClick(2)}>
          本月
        </View>
      )}
      <View className='date-item large' onClick={() => dateItemClick(3)}>
        {/* {dateIndex === 3 && ( */}
        <>
          <Picker
            value={custStartDt}
            mode='date'
            start='2010-01-01'
            end={!isEmpty(custEndDt) ? custEndDt : nowDate}
            onChange={e => onDateChange(e, 'start')}>
            <View className={`date-label ${dateIndex === 3 ? 'select' : 'unselect'}`}>
              {!isEmpty(custStartDt) ? custStartDt : '开始时间'}
            </View>
          </Picker>
          <View className='date-label-space'>至</View>
          <Picker
            value={custEndDt}
            mode='date'
            start={!isEmpty(custStartDt) ? custStartDt : '2010-01-01'}
            end={isYesterday ? yesterdayDate : nowDate}
            onChange={e => onDateChange(e, 'end')}>
            <View className={`date-label ${dateIndex === 3 ? 'select' : 'unselect'}`}>
              {!isEmpty(custEndDt) ? custEndDt : '结束时间'}
            </View>
          </Picker>
        </>
        {/* )} */}
        {/* {dateIndex !== 3 && (
          <View className='date-item'>
            <View className='date-label unselect'>{startDt}</View>
            <View className='date-label-space' style={{color: '#999'}}>
              至
            </View>
            <View className='date-label unselect'>{endDt}</View>
          </View>
        )} */}
      </View>
    </View>
  );
};

export default CyDateRangeView;
