import React, {FC, ReactNode, isValidElement, useEffect, useRef, useState} from 'react';
import {
  FlatList,
  Image,
  LayoutChangeEvent,
  NativeScrollEvent,
  NativeSyntheticEvent,
  ScrollView,
  StyleProp,
  StyleSheet,
  Text,
  TextStyle,
  TouchableOpacity,
  TouchableWithoutFeedback,
  View,
  ViewStyle,
} from 'react-native';
import Taro from '@tarojs/taro';
import EyesClose from '~images/icon/icon-eye-close.png';
import EyesOpen from '~images/icon/icon-eye-open.png';
import IconFalse from '~images/icon/icon-false.png';
import IconTrue from '~images/icon/icon-true.png';
import CheckedImg from '~images/icon/square_checked.png';
import UncheckImg from '~images/icon/square_uncheck.png';
import {desensitization} from '../../utils/common';
import CyEmpty from '../CyEmpty';
import CyLoadMore from '../CyLoadMore';
import CyCellItem from './components/CyCellItem';

type Props = {
  dataSource: Record<string, number | string | boolean>[]; // 数据源
  columns: IColumns[]; // 表头
  totalSource?: Record<string, number | string | boolean>;
  needEncrypt?: boolean;
  onAction?: (info: Record<string, number | string | boolean>, flag: string, index: number) => void;
  onLoadMore?: () => void;
  finished?: boolean;
  loading?: boolean;
  multiHeader?: boolean;
  onColumnsClick?: (dateIndex: string) => void;
  cellHeight?: number;
  needCacheAction?: boolean;
};
export type IColumns = {
  title?: string | ReactNode;
  dataIndex: string;
  flex?: number;
  width?: number;
  fixed?: boolean; // 是否需要固定到左边---暂不可以
  style?: ViewStyle; // 单元格样式
  encrypt?: string; // 脱敏字段
  isAction?: boolean;
  // 显示操作文字的key
  actionLabelKey?: string;
  // 如果对应key没有值 用actionValue
  actionValue?: string;
  // 不要操作时显示的字段
  normalValue?: string;

  // 对应操作的code值
  actionCode?: string;
  showIcon?: boolean;
  canCall?: boolean;
  canDetail?: boolean;
  formatter?: <T extends string>(value: T) => string;
  alwaysEncrypt?: boolean;
  headerClick?: Function;
};

const defaultProps = {
  dataSource: [],
  columns: [],
  needEncrypt: false,
  finished: false,
  loading: false,
  cellHeight: 50,
};

/**
 * 组件名称：支持左右滑动的Table
 * 组件描述：支持左右滑动的Table
 */
const CyScrollTable: FC<Props> = props => {
  let {
    dataSource,
    columns,
    totalSource,
    onAction,
    needEncrypt,
    onColumnsClick,
    cellHeight,
    needCacheAction = true,
  } = {
    ...defaultProps,
    ...props,
  };
  if (!dataSource) {
    dataSource = [];
  }
  const headScrollRef = useRef<any>();
  const footerScrollRef = useRef<any>();

  const canLoadMore = useRef(true);

  const [rightBoxWidth, setRightBoxWidth] = useState(0);
  const [showRows, setShowRows] = useState<number[]>([]);
  const [encrypt, setEncrypt] = useState(needEncrypt);

  const myOnAction = useRef(onAction);

  // const clearShowRows = () => {
  //   setShowRows([]);
  // };

  // dataSource为空时，清空显示的row
  useEffect(() => {
    if (dataSource.length === 0) {
      if (needEncrypt) {
        setShowRows([]);
      }
    }
  }, [dataSource]);

  // 获取右侧所占屏幕物理宽度
  const getRightBoxWidth = ({nativeEvent}: LayoutChangeEvent) => {
    if (!rightBoxWidth) {
      setRightBoxWidth(nativeEvent?.layout?.width);
    }
  };

  if (!props.loading && (!dataSource || dataSource.length === 0)) {
    return <CyEmpty />;
  }
  let datas = [];
  let isHaveFooter = false;
  // 如果总条数没有8条 并且有totalSource 就合并在datas里面去
  if (totalSource && Object.keys(totalSource).length > 0) {
    if (dataSource.length < 8) {
      datas = [...dataSource, totalSource];
      isHaveFooter = false;
    } else {
      datas = [...dataSource];
      isHaveFooter = true;
    }
  } else {
    datas = [...dataSource];
    isHaveFooter = false;
  }

  const renderQualityTableHeader = () => {
    return (
      <View style={[styles.multiThead]}>
        <View style={[styles.tableRow, {borderBottomWidth: 0}]}>
          {columns
            .filter(item => {
              return item.fixed;
            })
            .map((item, index) => (
              <View style={[styles.tableFirstCol, {width: item.width ? item.width / 2 : 90}]} key={index}>
                <View style={[styles.tableMultiCell, item?.style]}>
                  {isValidElement(item?.title) ? (
                    item.title
                  ) : (
                    <Text style={{...styles.tableCellText, ...item.style}}></Text>
                  )}
                </View>
              </View>
            ))}

          <ScrollView
            horizontal
            ref={headScrollRef}
            scrollEnabled={false}
            showsHorizontalScrollIndicator={false}
            style={[styles.tableRestCols]}
            removeClippedSubviews
            onLayout={getRightBoxWidth}>
            <View>{renderQualityMultiRightHeader()}</View>
          </ScrollView>
        </View>
      </View>
    );
  };

  const renderQualityItem = (title: string) => {
    return (
      <View
        style={[
          styles.tableCell,
          {
            width: 100,
            height: 60,
          },
        ]}>
        <Text style={styles.tableCellText}>{title}</Text>
      </View>
    );
  };

  const renderQualityMultiRightHeader = () => {
    return (
      <View style={styles.multiRightHeader}>
        {renderQualityItem('')}
        {renderQualityItem('期末余额')}
        {renderQualityItem('新增放款')}
        {renderQualityItem('0天（正常）')}
        {renderQualityMultiItem('1-3天')}
        {renderQualityMultiItem('4-7天')}
        {renderQualityMultiItem('8-15天')}
        {renderQualityMultiItem('16-30天')}
        {renderQualityMultiItem('31-60天')}
        {renderQualityMultiItem('61-90天')}
        {renderQualityMultiItem('91-180天')}
        {renderQualityMultiItem('181-360天')}
        {renderQualityMultiItem('360天以上')}
      </View>
    );
  };

  const renderQualityMultiItem = (topName: string) => {
    return (
      <View>
        <View style={styles.multiCellTop}>
          <Text style={styles.tableCellText}>{topName}</Text>
        </View>
        <View style={styles.multiCellBottom}>
          <View style={styles.multiCellBottomItem}>
            <Text style={styles.tableCellText}>电催</Text>
          </View>
          <View style={styles.multiCellBottomItem}>
            <Text style={styles.tableCellText}>客户经理</Text>
          </View>
          <View style={styles.multiCellBottomItem}>
            <Text style={styles.tableCellText}>无处置</Text>
          </View>
          <View style={styles.multiCellBottomItem}>
            <Text style={styles.tableCellText}>委外</Text>
          </View>
          <View style={styles.multiCellBottomItem}>
            <Text style={styles.tableCellText}>法诉</Text>
          </View>
        </View>
      </View>
    );
  };

  const renderHeader = () => {
    return (
      <View style={[styles.thead]}>
        <View style={[styles.tableRow, {borderBottomWidth: 0}]}>
          {columns
            .filter(item => {
              return item.fixed;
            })
            .map((item, index) => (
              <View style={[styles.tableFirstCol, {width: item.width ? item.width / 2 : 90}]} key={index}>
                <TouchableOpacity onPress={() => cellItemClick('thead', item, 0)} activeOpacity={1}>
                  <View style={[styles.tableCell, item?.style]}>
                    {isValidElement(item?.title) ? (
                      item.title
                    ) : (
                      <Text style={{...styles.tableCellText, ...columns[0].style}}>{item?.title}</Text>
                    )}
                  </View>
                </TouchableOpacity>
              </View>
            ))}
          <ScrollView
            horizontal
            ref={headScrollRef}
            scrollEnabled={false}
            showsHorizontalScrollIndicator={false}
            style={[styles.tableRestCols]}
            removeClippedSubviews
            onLayout={getRightBoxWidth}>
            {renderTableCellItem('thead')}
          </ScrollView>
        </View>
      </View>
    );
  };
  const encryptValue = (str: number | string, key?: string) => {
    if (
      key === 'name' ||
      key === 'certId' ||
      key === 'mobile' ||
      key === 'certIdShort' ||
      key === 'certIdShort4-6' ||
      key === 'certIdLastSix'
    ) {
      return desensitization(`${str}`, key);
    }
    return str;
  };

  // 判断某行是否需要脱敏
  const isShow = (rowIndex: number) => {
    return showRows.includes(rowIndex);
  };

  const clickFixedItem = (flag: string, rowIndex: number) => {
    if (flag === 'decrypt') {
      if (!needEncrypt) return;
      setEncrypt(false);
      let atIndex = showRows.indexOf(rowIndex);
      if (atIndex !== -1) {
        const rows = showRows.filter((item, index) => {
          return index !== atIndex;
        });
        setShowRows(rows);
      } else {
        const rows = [...showRows, rowIndex];
        setShowRows(rows);
      }
    } else {
      onAction && onAction(dataSource[rowIndex], flag, rowIndex);
    }
  };

  // 渲染右侧单元格数据
  const renderTableCellItem = (item: any, rowIndex?: number) => {
    return columns
      .filter(column => !column.fixed)
      .map((cell, index) => {
        let cellContent = null;
        if (item === 'thead') {
          return (
            <CyCellItem
              key={index}
              content={cell.title}
              cell={cell}
              dataSource={item}
              cellHeight={50}
              contentStyle={styles.tableCellText}
              onColumnsClick={onColumnsClick}></CyCellItem>
          );
        } else if (item === 'footer') {
          return (
            <CyCellItem
              key={index}
              content={totalSource && totalSource[cell.dataIndex]}
              customerStyle={cell.style}
              cell={cell}
              dataSource={item}
              cellHeight={50}
              contentStyle={styles.tableCellText}></CyCellItem>
          );
        }
        let style: StyleProp<any>;
        let isImage: boolean = false;
        if (cell.isAction) {
          if (cell.dataIndex !== '') {
            // 是否需要操作的判断
            const auditFlag = cell.dataIndex;
            // 判断后显示的字段
            const actionLabelKey = cell.actionLabelKey;
            // 无判断后显示字段的值，则显示改字段
            const normalValue = cell.normalValue;
            const actionValue = cell.actionValue;
            if (item[auditFlag]) {
              // 不需要操作
              if (actionLabelKey && item[actionLabelKey] && item[actionLabelKey] !== '') {
                cellContent = item[actionLabelKey];
              } else {
                cellContent = normalValue;
              }
            } else {
              // 需要操作
              if (actionLabelKey && item[actionLabelKey] && item[actionLabelKey] !== '') {
                cellContent = item[actionLabelKey];
                style = styles.actionText;
              } else {
                cellContent = actionValue;
                style = styles.actionText;
              }
            }
          } else {
            cellContent = cell.actionValue;
            style = styles.actionText;
          }
        } else {
          const flag = item[cell.dataIndex];
          if (encrypt || !isShow(rowIndex!)) {
            if (cell.showIcon) {
              cellContent = flag ? IconTrue : IconFalse;
              style = styles.image;
              isImage = true;
            } else if (cell.formatter) {
              cellContent = cell.formatter(item[cell.dataIndex]);
            } else {
              const itemValue = encryptValue(item[cell.dataIndex], cell.encrypt);
              cellContent = itemValue;
            }
          } else {
            if (cell.showIcon) {
              cellContent = flag ? IconTrue : IconFalse;
              style = styles.image;
              isImage = true;
            } else if (cell.formatter) {
              cellContent = cell.formatter(item[cell.dataIndex]);
            } else {
              if (cell.alwaysEncrypt) {
                const itemValue = encryptValue(item[cell.dataIndex], cell.encrypt);
                cellContent = itemValue;
              } else {
                cellContent = item[cell.dataIndex];
              }
            }
          }
        }

        if (style === undefined) {
          style = styles.tableRightCellText;
        }
        return (
          <CyCellItem
            key={index}
            content={cellContent}
            customerStyle={cell.style}
            cell={cell}
            dataSource={item}
            isImage={isImage}
            cellHeight={cellHeight}
            rowIndex={rowIndex}
            contentStyle={style}
            onAction={needCacheAction ? myOnAction.current : onAction}></CyCellItem>
        );
        // let cellContent = null;
        // if (item === 'thead') {
        //   cellContent = cell.title;
        // } else if (item === 'footer') {
        //   cellContent = totalSource && totalSource[cell.dataIndex];
        // } else {
        //   if (cell.isAction) {
        //     if (cell.dataIndex !== '') {
        //       // 是否需要操作的判断
        //       const auditFlag = cell.dataIndex;
        //       // 判断后显示的字段
        //       const actionLabelKey = cell.actionLabelKey;
        //       // 无判断后显示字段的值，则显示改字段
        //       const normalValue = cell.normalValue;
        //       const actionValue = cell.actionValue;
        //       if (item[auditFlag]) {
        //         // 不需要操作
        //         if (actionLabelKey && item[actionLabelKey] && item[actionLabelKey] !== '') {
        //           cellContent = item[actionLabelKey];
        //         } else {
        //           cellContent = normalValue;
        //         }
        //       } else {
        //         // 需要操作
        //         if (actionLabelKey && item[actionLabelKey] && item[actionLabelKey] !== '') {
        //           cellContent = <Text style={{...styles.actionText, ...cell.style}}>{item[actionLabelKey]}</Text>;
        //         } else {
        //           cellContent = <Text style={{...styles.actionText, ...cell.style}}>{actionValue}</Text>;
        //         }
        //       }
        //     } else {
        //       cellContent = <Text style={{...styles.actionText, ...cell.style}}>{cell.actionValue}</Text>;
        //     }
        //   } else {
        //     const flag = item[cell.dataIndex];
        //     if (encrypt || !isShow(rowIndex!)) {
        //       if (cell.showIcon) {
        //         cellContent = <Image source={flag ? IconTrue : IconFalse} style={{height: 16, width: 16}} />;
        //       } else if (cell.formatter) {
        //         cellContent = cell.formatter(item[cell.dataIndex]);
        //       } else {
        //         const itemValue = encryptValue(item[cell.dataIndex], cell.encrypt);
        //         cellContent = itemValue;
        //       }
        //     } else {
        //       if (cell.showIcon) {
        //         cellContent = <Image source={flag ? IconTrue : IconFalse} style={{height: 16, width: 16}} />;
        //       } else if (cell.formatter) {
        //         cellContent = cell.formatter(item[cell.dataIndex]);
        //       } else {
        //         if (cell.alwaysEncrypt) {
        //           const itemValue = encryptValue(item[cell.dataIndex], cell.encrypt);
        //           cellContent = itemValue;
        //         } else {
        //           cellContent = item[cell.dataIndex];
        //         }
        //       }
        //     }
        //   }
        // }
        // let renderResult = isValidElement(cellContent) ? (
        //   cellContent
        // ) : (
        //   <Text
        //     style={
        //       (item as string) === 'thead' || (item as string) === 'footer'
        //         ? {...styles.tableCellText, ...cell.style}
        //         : {...styles.tableRightCellText, ...cell.style}
        //     }>
        //     {cellContent}
        //   </Text>
        // );

        // return (
        //   <TouchableOpacity key={index} onPress={() => cellItemClick(item, cell, rowIndex!)} activeOpacity={1}>
        //     <View
        //       style={[
        //         styles.tableCell,
        //         {
        //           width: cell.width ? cell.width / 2 : 90,
        //           height: (item as string) === 'thead' || (item as string) === 'footer' ? 50 : cellHeight,
        //         },
        //       ]}>
        //       {renderResult}
        //     </View>
        //   </TouchableOpacity>
        // );
      });
  };

  const cellItemClick = (item: any, cell: IColumns, rowIndex: number) => {
    if (item === 'footer') {
      return;
    }
    if (item === 'thead') {
      onColumnsClick && onColumnsClick(cell.dataIndex);
      return;
    }

    if (cell.isAction) {
      // 如果是操作事件

      let canAction = false;
      if (cell.dataIndex !== '') {
        const auditFlag = cell.dataIndex;
        if (!item[auditFlag]) {
          canAction = true;
        }
      } else {
        canAction = true;
      }
      canAction && onAction && onAction(item, cell.actionCode!, rowIndex);
      return;
    }

    if (cell.canCall) {
      // if (encrypt || !isShow(rowIndex)) {
      //   return;
      // }
      // 拨打电话
      const phoneNumber = item[cell.dataIndex];
      Taro.makePhoneCall({
        phoneNumber,
      });
      return;
    }

    if (cell.canDetail) {
      console.log('详情页');
      onAction && onAction(item, 'detail', rowIndex);
      return;
    }
  };
  // 渲染第一列的行数据
  const renderFirstColRowItem = (item: any, index: number, column: IColumns) => {
    let firstColContent = null;
    if (needEncrypt && column.encrypt) {
      const isHide = encrypt || !isShow(index);

      firstColContent = (
        <View style={styles.showOrHideStyle}>
          <Image source={isHide ? EyesClose : EyesOpen} style={{marginRight: 6, height: 12, width: 12}} />
          <Text style={{...styles.tableRightCellText, ...column?.style}}>
            {isHide ? encryptValue(item[column?.dataIndex], column?.encrypt) : item[column?.dataIndex]}
          </Text>
        </View>
      );
    } else if (column.dataIndex === 'selected') {
      firstColContent = (
        <Image source={item[column?.dataIndex] ? CheckedImg : UncheckImg} style={{height: 16, width: 16}} />
      );
    } else {
      firstColContent = item[column?.dataIndex];
    }
    let renderResult = isValidElement(firstColContent) ? (
      firstColContent
    ) : (
      <Text style={{...styles.tableRightCellText, ...column?.style}}>{firstColContent}</Text>
    );

    const isExpandChildren = item['isExpandChildren'];

    return (
      <TouchableWithoutFeedback
        key={index}
        onPress={() => clickFixedItem(needEncrypt && column.encrypt ? 'decrypt' : column.actionCode || 'first', index)}>
        <View style={[styles.tableRow]}>
          <View
            style={[
              styles.tableCell,
              {flex: 1, height: cellHeight},
              isExpandChildren ? styles.childCell : index % 2 === 0 ? styles.whiteCell : styles.greyCell,
            ]}>
            {renderResult}
          </View>
        </View>
      </TouchableWithoutFeedback>
    );
  };

  // 渲染右侧滚动的行数据
  const renderRestRowItem = (item: any, index: number) => {
    return (
      <TouchableWithoutFeedback key={index}>
        <View style={[styles.tableRow, index % 2 === 0 ? styles.whiteCell : styles.greyCell]}>
          {renderTableCellItem(item, index)}
        </View>
      </TouchableWithoutFeedback>
    );
  };

  // 表格左右滚动
  const handleTbodyScroll = ({nativeEvent}: NativeSyntheticEvent<NativeScrollEvent>) => {
    headScrollRef?.current?.scrollTo({
      x: nativeEvent.contentOffset.x,
      y: 0,
      animated: false,
    });
    footerScrollRef?.current?.scrollTo({
      x: nativeEvent.contentOffset.x,
      y: 0,
      animated: false,
    });
  };

  // 渲染表格数据
  const renderTableBody = () => {
    // return <CyTableBody datas={datas} columns={columns}></CyTableBody>;
    return (
      <View style={[styles.tbody]}>
        {columns
          .filter(column => column.fixed)
          .map((columnData, i) => (
            <View style={[styles.tableFirstCol, {width: columnData.width ? columnData.width / 2 : 90}]} key={i}>
              {datas.map((item, index) => renderFirstColRowItem(item, index, columnData))}
            </View>
          ))}
        <View style={[styles.tableRestCols]}>
          <ScrollView
            horizontal
            scrollEventThrottle={16}
            showsHorizontalScrollIndicator={false}
            onScroll={handleTbodyScroll}
            scrollEnabled
            removeClippedSubviews
            bounces={false}>
            <View>{datas.map((item, index) => renderRestRowItem(item, index))}</View>
          </ScrollView>
          {/* <FlatList data={datas} renderItem={renderFlatListItem}></FlatList> */}
        </View>
      </View>
    );
  };

  // const renderFlatListItem = item => {
  //   return columns.slice(1).map((columnsItem, index) => {
  //     return (
  //       <View key={index} style={{backgroundColor: '#43d54a'}}>
  //         <Text>{item[columnsItem.dataIndex]}</Text>
  //       </View>
  //     );
  //   });
  // };

  const renderFooter = () => {
    return (
      <View style={[styles.footer]}>
        <View style={[styles.tableRow, {borderBottomWidth: 0}]}>
          <View style={[styles.tableFirstCol, {width: columns[0].width ? columns[0].width / 2 : 90}]}>
            <View style={[styles.tableCell, columns[0]?.style]}>
              {isValidElement(props.columns[0]?.title) ? (
                props.columns[0].title
              ) : (
                <Text style={styles.tableCellText}>{totalSource && totalSource[props.columns[0]?.dataIndex]}</Text>
              )}
            </View>
          </View>
          <ScrollView
            horizontal
            ref={footerScrollRef}
            scrollEnabled={false}
            showsHorizontalScrollIndicator={false}
            removeClippedSubviews
            style={[styles.tableRestCols]}
            onLayout={getRightBoxWidth}>
            {renderTableCellItem('footer')}
          </ScrollView>
        </View>
      </View>
    );
  };

  // 监听是否到底部
  const myOnScroll = (e: NativeSyntheticEvent<NativeScrollEvent>) => {
    if (!props.onLoadMore || props.loading || props.finished) return;
    let y = e.nativeEvent.contentOffset.y;
    let height = e.nativeEvent.layoutMeasurement.height;
    let contentHeight = e.nativeEvent.contentSize.height;
    if (y + height >= contentHeight - 40) {
      //已经滚动到底部
      console.log('已经滚动到底部', canLoadMore);
      if (canLoadMore.current) {
        console.log('设置canLoadMore-false');
        canLoadMore.current = false;
        props.onLoadMore?.();
        setTimeout(() => {
          canLoadMore.current = true;
        }, 3000);
      }
    }
  };

  return (
    <View style={{height: '100%', display: 'flex', flexDirection: 'column'}}>
      {dataSource.length > 0 && !props.multiHeader && renderHeader()}
      {dataSource.length > 0 && props.multiHeader && renderQualityTableHeader()}
      <ScrollView
        style={styles.scrollViewStyle}
        onScroll={e => myOnScroll(e)}
        removeClippedSubviews
        scrollEventThrottle={5}>
        {renderTableBody()}
        {props.onLoadMore && (
          <CyLoadMore finished={props.finished!} loading={props.loading!} dataLength={dataSource.length} />
        )}
      </ScrollView>
      {isHaveFooter && renderFooter()}
    </View>
  );
};

export default CyScrollTable;

const styles = StyleSheet.create({
  thead: {
    backgroundColor: '#4D7BFF',
    borderBottomWidth: StyleSheet.hairlineWidth,
    height: 50,
  },

  footer: {
    backgroundColor: '#ADB2C1',
    borderBottomWidth: StyleSheet.hairlineWidth,
    height: 50,
  },
  tbody: {
    flexDirection: 'row',
  },
  tableFirstCol: {
    // flex: 0.5,
  },
  tableRestCols: {
    flex: 1,
  },
  tableRow: {
    flexDirection: 'row',
  },
  tableCell: {
    // padding: 10,
    justifyContent: 'center',
    alignItems: 'center',
    height: 50,
    color: '#fff',
    borderRightColor: '#fff',
    borderRightWidth: 1,
  },

  greyCell: {
    backgroundColor: '#F8FAFF',
    overflow: 'hidden', //配合removeClippedSubviews 提高性能？？？？？
  },
  whiteCell: {
    backgroundColor: '#FfF',
    overflow: 'hidden',
  },
  childCell: {
    backgroundColor: '#f1f1f1',
    overflow: 'hidden',
  },
  tableCellText: {
    color: '#fff',
    display: 'flex',
    justifyContent: 'center',
    alignContent: 'center',
    textAlign: 'center',
  },
  tableRightCellText: {
    color: '#333',
    display: 'flex',
    justifyContent: 'center',
    alignContent: 'center',
    textAlign: 'center',
  },
  scrollViewStyle: {
    flex: 1,
  },
  actionText: {
    color: '#4D7BFF',
  },
  showOrHideStyle: {
    display: 'flex',
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  loadingCon: {
    display: 'flex',
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    height: 50,
  },
  loadingText: {
    color: '#666',
    fontSize: 14,
    marginLeft: 10,
  },
  noMoreDataText: {
    color: '#666',
    fontSize: 14,
    textAlign: 'center',
    height: 50,
    lineHeight: 50,
  },

  multiThead: {
    backgroundColor: '#4D7BFF',
    borderBottomWidth: StyleSheet.hairlineWidth,
    height: 60,
  },
  tableMultiCell: {
    justifyContent: 'center',
    alignItems: 'center',
    height: 60,
    color: '#fff',
    borderRightColor: '#fff',
    borderRightWidth: 1,
  },
  multiCell: {
    color: '#fff',
    textAlign: 'center',
  },

  multiRightHeader: {
    display: 'flex',
    flexDirection: 'row',
  },
  multiCellTop: {
    color: '#fff',
    textAlign: 'center',
    height: 30,
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
    borderBottomColor: '#fff',
    borderBottomWidth: 1,
    borderRightColor: '#fff',

    // ? 不知道为什么要设置为0.5  设置为1宽度有点宽
    borderRightWidth: 0.5,
  },
  multiCellBottom: {
    color: '#fff',
    display: 'flex',
    flexDirection: 'row',
    height: 30,
  },
  multiCellBottomItem: {
    color: '#fff',
    width: 80,
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
    borderRightColor: '#fff',
    borderRightWidth: 1,
  },

  image: {
    width: 16,
    height: 16,
  },
});
