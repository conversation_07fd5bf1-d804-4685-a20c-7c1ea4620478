import React, {CSSProperties, FC, ReactNode, useEffect, useState} from 'react';
import {Icon, Image, Text, View} from '@tarojs/components';
import Taro from '@tarojs/taro';
import arrowPng from '~images/icon/icon-down.png';
import './index.scss';

type Props = {
  defaultActiveKey: string[];
  children: React.ReactElement[] | React.ReactElement;
  handleClick?: (key: string) => void; //当设置是外层自己处理打开和关闭
};

type PanelProps = {
  header: string | ReactNode;
  headerBgColor?: string;
  headerHeight?: string;
  children: React.ReactElement;
  active?: boolean;
  key: string;
  className?: string;
  style?: string | CSSProperties;
  handleClick?: () => void;
};

type HeaderProps = {
  children: React.ReactElement[] | React.ReactElement;
  backgroundColor?: string;
  height?: number;
};

type TCyCollapse = FC<Props> & {Panel: FC<PanelProps>};

/**
 * 组件名称：折叠面板
 * 组件描述：支持折叠展开各个部分
 */
const CyCollapse: TCyCollapse = ({defaultActiveKey, children, handleClick}) => {
  const [activeKey, setActiveKey] = useState(defaultActiveKey);
  useEffect(() => {
    //外层自己处理时才需要根据外层来更新
    if (handleClick) {
      setActiveKey(defaultActiveKey);
    }
  }, [defaultActiveKey]);
  return (
    <View className='comp-cy-collapse'>
      {React.Children.map(children, (child: React.ReactElement<PanelProps>) => {
        const active = child.key ? activeKey?.includes(child.key) : false;
        if (React.isValidElement(child)) {
          return React.cloneElement(child, {
            active,
            handleClick: () => {
              if (!child.key) return;

              if (handleClick) {
                handleClick(child.key);
                return;
              }
              if (activeKey.includes(child.key)) {
                setActiveKey(activeKey.filter(key => key !== child.key));
              } else {
                setActiveKey([...activeKey, child.key]);
              }
            },
          });
        }
        return child;
      })}
    </View>
  );
};

export const Header: FC<HeaderProps> = ({children, backgroundColor = '#F6F6F6', height = 44}) => {
  return (
    <View className='panel-header' style={{backgroundColor, height}}>
      {children}
    </View>
  );
};

const Panel: FC<PanelProps> = ({
  children,
  header,
  active,
  className,
  style,
  key,
  handleClick,
  headerBgColor = '#F6F6F6',
  headerHeight = '88px',
}) => {
  let height = 44;
  if (headerHeight.endsWith('px') || headerHeight.endsWith('PX')) {
    height = parseInt(Taro.pxTransform(parseInt(headerHeight.substring(0, headerHeight.length - 2))));
  } else {
    height = parseInt(Taro.pxTransform(parseInt(headerHeight)));
  }

  return (
    <View className={`panel ${className}`} style={style}>
      <View onClick={handleClick}>
        {typeof header === 'string' ? (
          <Header backgroundColor={headerBgColor} height={height}>
            <Text className='panel-text'>{header}</Text>
            <Image className={`panel-icon ${active ? 'up' : ''}`} src={arrowPng}></Image>
          </Header>
        ) : (
          header
        )}
      </View>
      <View className={`panel-content ${active ? 'panel-active' : ''}`}>{children}</View>
    </View>
  );
};

CyCollapse.Panel = Panel;

export default CyCollapse;
