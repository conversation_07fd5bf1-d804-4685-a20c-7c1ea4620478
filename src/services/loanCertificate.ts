import request from '../http/request';

//是否允许开具结清证明
export const canAllCreate = () => {
  return request<boolean>({
    url: '/api/settleProof/can-all-create',
    method: 'POST',
  });
};

//查询全部结清证明列表
export const getAllList = () => {
  return request<API.AllListReq>({
    url: '/api/settleProof/query-all-list',
    method: 'GET',
  });
};

//查询全部结清证明详情
export const getAllDetail = ({securityCode, contractCode}: {securityCode: string; contractCode: string}) => {
  return request<API.SettleProofList>({
    url: '/api/settleProof/query-all-detail',
    method: 'GET',
    params: {
      securityCode,
      contractCode,
    },
  });
};

//查询借据级结清证明列表
export const getLendingList = () => {
  return request<API.LendingListReq>({
    url: '/api/settleProof/query-loan-list',
    method: 'GET',
  });
};

//查询借据级结清证明详情
export const getLendingDetail = ({loanNo}: {loanNo: string}) => {
  return request<API.LoanDetail>({
    url: '/api/settleProof/query-loan-detail',
    method: 'GET',
    params: {
      loanNo,
    },
    args: {
      hideErrorToast: true,
    },
  });
};

//生成全部结清证明
export const createAll = () => {
  return request<API.SettleProofList>({
    url: 'api/settleProof/create-all',
    method: 'POST',
    args: {
      hideErrorToast: true,
    },
  });
};
