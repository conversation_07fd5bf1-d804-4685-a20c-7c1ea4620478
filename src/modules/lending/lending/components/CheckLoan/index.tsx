import {Button, Text, View} from '@tarojs/components';
import {CyButton} from 'hncy58-taro-components';
import Popup from '~components/Popup';
import styles from './index.module.scss';
import { ILoanInfo, IRepaymentType } from 'src/types/common-types';
import { formatToFixedTwo, twoNumberTimes } from '~utils/number';
import useGetRepayment from '../../useGetRepayment';

interface CheckLoanProps {
  visible: boolean;
  loanInfo:  ILoanInfo;
  onClose: () => void;
  onConfirm: () => void;
}

export default (props: CheckLoanProps) => {
  const {visible, loanInfo, onClose} = props;
  const repaymentTypeMap = useGetRepayment()
  return (
    <Popup title='核对借款信息' visible={visible} onClose={onClose} showClose>
      <View className={styles.check_container}>
        <View className={styles.check_item}>
          <View className={styles.check_item_title}>借款金额</View>
          <View className={styles.check_item_value}>
            <Text className={styles.check_item_money}>¥{formatToFixedTwo(loanInfo.loanAmount)}</Text>
          </View>
        </View>
        <View className={styles.check_item}>
          <View className={styles.check_item_title}>借款利率</View>
          <View className={styles.check_item_value}>
            {twoNumberTimes(loanInfo.rate, 100)}%
            <Text className={styles.check_item_tip}>{`(借一万元日利息${formatToFixedTwo(twoNumberTimes(loanInfo.rate, 10000))}元)`}</Text>
          </View>
        </View>
        <View className={styles.check_item}>
          <View className={styles.check_item_title}>年利率</View>
          <View className={styles.check_item_value}>
            {twoNumberTimes(loanInfo.rate, 36000)}%
            <View className={styles.check_item_note}>
              （按借款实际天数计算,单笔借款期内日利率固定不变， 年利率（单利）=日利率x360）
            </View>
          </View>
        </View>
        <View className={styles.check_item}>
          <View className={styles.check_item_title}>还款方式</View>
          <View className={styles.check_item_value}>{repaymentTypeMap[loanInfo.repaymentType as IRepaymentType]}</View>
        </View>
        <View className={styles.check_item}>
          <View className={styles.check_item_title}>借款期限</View>
          <View className={styles.check_item_value}>{loanInfo.loanTerm}个月</View>
        </View>

        <CyButton round block type='primary' className={styles.check_btn} onClick={props?.onConfirm}>
          下一步
        </CyButton>
      </View>
    </Popup>
  );
};
