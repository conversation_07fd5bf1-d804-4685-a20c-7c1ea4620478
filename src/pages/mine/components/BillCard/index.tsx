import {CyButton} from 'hncy58-taro-components';
import {Text, View} from '@tarojs/components';
import classNames from 'classnames';
import Card from '~components/Card';
import MoneyDisplay from '~components/MoneyDisplay';
import styles from './index.module.scss';
import { useEffect, useMemo, useState } from 'react';
import { getRepaymentSummary } from '~services/repayment';
import { router } from '~utils/common';
import useUserStore from '~store/user';
import currency from 'currency.js';
import useRepaymentStore from '~store/repayment';

export default () => {
  const {repaymentSummary} = useRepaymentStore();

  const goRepayment = () => {
    router.push({
      url: '/modules/repayment/entry/index',
    });
  };

  const isOverdue = useMemo(() => {
    return repaymentSummary?.overdueLoanNum && repaymentSummary?.overdueLoanNum > 0
  }, [repaymentSummary]);

  const isTodayPay = useMemo(() => {
    return repaymentSummary?.todayRepayableNum && repaymentSummary?.todayRepayableNum > 0
  }, [repaymentSummary]);

  const titleDecorate = isOverdue ? 
    <Text className={styles['tag-error']}>已逾期{repaymentSummary?.maxOverdueDays}天</Text> 
      : isTodayPay ? 
        <Text className={styles['tag-primary']}>已出账</Text> :
        <Text className={styles.tag}>还款日{repaymentSummary?.nextRepaymentDate}</Text>;


  if(!repaymentSummary) return null;

  if(!repaymentSummary?.nextRepaymentNum && !repaymentSummary?.overdueLoanNum && !repaymentSummary?.todayRepayableNum) return null;


  const money = isOverdue ? 
    currency(repaymentSummary?.overdueAmount).add(repaymentSummary?.todayRepayableAmount).value 
    : (
        isTodayPay ? 
          repaymentSummary?.todayRepayableAmount : 
          repaymentSummary?.nextRepaymentAmount
      )

  return (
    <Card  type={isOverdue ? 'error' : 'normal'} title='待还账单' titleDecorate={titleDecorate}>
      <View className={styles.billCardItem}>
        <View className={styles.billCardRow}>
          <MoneyDisplay size='secondary' money={money} precision={2} color='#333' />
          <CyButton type='primary' round size='small' onClick={goRepayment} className={styles.billCardBtn}>
            {isOverdue ? '立即还款' : isTodayPay ? '去还款' : '查看详情'}
          </CyButton>
        </View>
        <View className={classNames(styles.billCardRow, styles.billCardDescRow)}>
          <View>{isOverdue || isTodayPay ? '当前待还账单' : '账单日待还账单'}</View>
          {
            isOverdue || isTodayPay ? (
              <View>共{(repaymentSummary?.todayRepayableNum || 0) + (repaymentSummary?.overdueLoanNum || 0)}笔</View>
            ) : (
              <View>共{repaymentSummary?.nextRepaymentNum || 0}笔</View>
            )
          }
          </View>
      </View>
    </Card>
  );
};
