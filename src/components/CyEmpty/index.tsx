import {FC} from 'react';
import {Image, Text, View} from '@tarojs/components';
import NoData from '~images/empty/no-data.png';
import './index.scss';

type Props = {
  status?: 'no-data' | 'no-network';
};

/**
 * 组件名称：空状态
 * 组件描述：状态界面
 */
const CyEmpty: FC<Props> = ({status = 'no-data'}) => {
  let imageSrc = NoData;
  let statusText = '暂无数据';

  switch (status) {
    case 'no-data':
      imageSrc = NoData;
      statusText = '暂无数据';
      break;
    case 'no-network':
      imageSrc = NoData;
      statusText = '暂无网络';
      break;
    default:
      imageSrc = NoData;
      statusText = '暂无数据';
      break;
  }

  return (
    <View className='comp-cy-empty'>
      <Image src={imageSrc} mode='widthFix' />
      <Text className='comp-cy-empty-text'>{statusText}</Text>
    </View>
  );
};

export default CyEmpty;
