import Card from '~components/Card';
import List from '~components/List/List';
import ListItem from '~components/List/ListItem';
import {useLendingProgress} from '~hooks/useLendingProgress';
import IconBankcard from '~icons/icon-bankcard.png';
import IconPassword from '~icons/icon-password.png';
import IconUserinfo from '~icons/icon-userinfo.png';
import {router} from '~utils/common';
import styles from './indes.module.scss';
import { useMemo } from 'react';
import Taro, { useRouter } from '@tarojs/taro';


export const UploadInfoListCard = () => {
  const pageRouter = useRouter();
  const [stateMap] = useLendingProgress();

  const goSetPassword = () => {
    if(!stateMap.needTradePassword)  return;
    router.push({
      url: '/modules/user/set-password-verifycode/index?immediate=true',
    });
  };
  
  const goBindcard = () => {
    router.push({
      url: '/modules/bankcard/bankcard-binding/index',
    });
  };
  
  const goBindUserinfo = () => {
    if(noNeedUpdateInfo)  return;
    // 跳转完善个人信息
    Taro.setStorageSync('backUrl', pageRouter.path)
    router.push({
      url: '/modules/user/baseinfo/index',
    });
  };

  const noNeedUpdateInfo = useMemo(() => {
    return stateMap.needContactInfo === false && stateMap.needRefillAddress  === false && stateMap.needSupplementInfo === false;
  }, [stateMap]);

  return (
    <Card title='补充资料' note='首次借款需要完成基本资料补充，您可在此处完成补充或者到借款页面补充'>
      <List className={styles.infoList}>
        <ListItem
          onClick={goSetPassword}
          key={1}
          title='交易密码'
          note='设置您的交易密码'
          extraText='去补充'
          thumb={IconPassword}
          isSuccess={stateMap.needTradePassword === false}
        />
        <ListItem
          key={2}
          isSuccess={stateMap.needBankCard === false}
          title='银行卡'
          note='绑定本人名下银行卡'
          extraText='去绑卡'
          thumb={IconBankcard}
          onClick={goBindcard}
        />
        <ListItem
          key={3}
          isSuccess={noNeedUpdateInfo}
          title='完善个人信息'
          note='借款前还需补充个人具体信息'
          extraText='去完善'
          thumb={IconUserinfo}
          onClick={goBindUserinfo}
        />
      </List>
    </Card>
  );
};
