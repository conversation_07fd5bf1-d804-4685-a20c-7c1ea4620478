import React from 'react';
import { View, Text } from '@tarojs/components';
import classNames from 'classnames';
import './index.scss';

interface CySpinnerProps {
  /** 是否显示加载状态 */
  loading?: boolean;
  /** 加载文本 */
  text?: string;
  /** 大小 */
  size?: 'small' | 'medium' | 'large';
  /** 颜色 */
  color?: string;
  /** 自定义类名 */
  className?: string;
  /** 是否垂直居中显示 */
  center?: boolean;
}

const CySpinner: React.FC<CySpinnerProps> = ({
  loading = true,
  text,
  size = 'medium',
  color = '#1890ff',
  className,
  center = false
}) => {
  if (!loading) {
    return null;
  }

  return (
    <View 
      className={classNames(
        'cy-spinner',
        {
          'cy-spinner--center': center,
          [`cy-spinner--${size}`]: size
        },
        className
      )}
    >
      <View 
        className="cy-spinner__icon"
        style={{ borderTopColor: color }}
      />
      {text && (
        <Text className="cy-spinner__text">{text}</Text>
      )}
    </View>
  );
};

export default CySpinner;