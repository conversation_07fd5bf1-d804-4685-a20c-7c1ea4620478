import request from '../http/request';

export enum FaceCheckType {
  login = 'login', // 登录
  lending = 'lending', // 借款
  firstCredit = 'firstCredit', // 首次授信
  certIdAuth = 'certIdAuth', // 重新授信
  //ase repayment //还款
  resetTradePassword = 'resetTradePassword', // 重置交易密码
  firstSetTradePassword = 'firstSetTradePassword', // 首次设置交易密码
  updateCustomerInfo = 'updateCustomerInfo', // 更新用户信息
  infoUpdate = 'infoUpdate', // 信息更新（新）
  logOff = 'logOff', // 销户
  creditOff = 'creditOff', // 关闭额度
  noCertPicAuth = 'noCertPicAuth', // 无身份证进件
  updateMobileNo = 'updateMobileNo', // 修改手机号码
  unionLoanApply = 'unionLoanApply', // 申请联合贷款
  installment = 'installment', // 申请分期还款
  applyGuideAuth = 'applyGuideAuth', // 导游证认证提额
  creditIntelligence = 'creditIntelligence', // 智能信用认证提额
  gracePrincipal = 'gracePrincipal', // 宽限本金偿还计划
  delayPrincipalAndInterest = 'delayPrincipalAndInterest', // 延本延息
  onlineMitigate = 'onlineMitigate', // 线上减免息申请
  fixedRateInstallment = 'fixedRateInstallment', // 固定利率协议分期
}
/**
 * 获取阿里云私有化活体识别认证token
 * @param param
 * @returns
 */
export const getAliyunFaceConfig = (params: API.GetAliyunFaceConfigParams, loading = false) => {
  return request<API.AliyunFaceTicket>({
    url: '/fas/api/cloudface/finance/certifyid',
    method: 'POST',
    data: params,
    loading,
  });
};

/**
 * 获取阿里云私有化活体识别结果
 * @param param
 * @returns
 */
export const getPrivateFaceResult = (params: API.GetPrivateFaceResultParams, loading = false) => {
  return request<API.GetPrivateFaceResultRes>({
    url: '/fas/api/cloudface/finance/privateResult',
    method: 'POST',
    data: params,
    loading,
  });
};
