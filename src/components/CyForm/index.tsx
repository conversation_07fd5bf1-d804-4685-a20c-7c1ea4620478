import React, {FC, createContext, useContext} from 'react';
import {FieldErrors, FieldName, FieldValues, RegisterOptions, UseFormRegister} from 'react-hook-form';
import {BaseEventOrig, Form, Image, InputProps, StandardProps, Target, Text, View, ViewProps} from '@tarojs/components';
import classnames from 'classnames';
import CySplitLine from '../CySplitLine';
import './index.scss';

type Props = {
  children: React.ReactNode[] | React.ReactNode;
  register: UseFormRegister<any>;
  errors?: FieldErrors<Record<string, any>>;
};
interface CyFormItemProps extends ViewProps {
  children: React.ReactNode;
  name?: string;
  required?: boolean;
  errors?: FieldErrors<Record<string, any>>;
  label?: string;
  register?: UseFormRegister<FieldValues>;
  breakRow?: boolean; //label和value是否在同一行
  registerOption?: RegisterOptions<FieldValues, string>;
  noSplitLine?: boolean;
  icon?: string;
  onIconClick?: () => void;
}
type TCyForm = FC<Props> & {Item: FC<CyFormItemProps>};

interface HooksTarget extends Target {
  name?: string;
}

interface BaseHooksEventOrig<T> extends BaseEventOrig<T> {
  target: HooksTarget;
}

export type EventType = BaseHooksEventOrig<InputProps.inputEventDetail>;

export interface FormField extends StandardProps {
  name?: string;
  onChange?: (event: EventType) => void;
}
/**
 * 组件名称：表单组件
 * 组件描述：支持自定义表单控件的表单组件
 */
const FormContext = createContext<{
  register: UseFormRegister<FieldValues>;
  errors?: FieldErrors<Record<string, any>>;
} | null>(null);

const CyForm: TCyForm = ({children, register, errors, ...restProps}) => {
  return (
    <Form>
      <FormContext.Provider
        value={{
          register,
          errors,
        }}>
        {children}
      </FormContext.Provider>
    </Form>
  );
};

const FormItem: FC<CyFormItemProps> = ({
  children,
  name,
  required,
  label,
  breakRow,
  style,
  className,
  registerOption,
  noSplitLine,
  icon,
  onIconClick,
  ...restProps
}) => {
  const context = useContext(FormContext);
  const errors = context?.errors;
  const register = context?.register;
  const fieldError = name && errors?.[name];
  const hookFormProps = name && register?.(name, registerOption);
  const inline = !breakRow;
  const childrenArr = Array.isArray(children) ? children : [children];
  return (
    <View>
      <View
        className={classnames(
          'form-item',
          {
            inline,
          },
          className ? className : '',
        )}
        style={style}>
        {label ? (
          <>
            <View
              className={classnames('label', {
                'inline-label': inline,
              })}>
              {required ? <Text className='required-mark'>*</Text> : null}
              <Text className='form-item-text'>{label}:</Text>
            </View>
            <View
              className={classnames('field-wrap', {
                'inline-field-wrap': inline,
              })}>
              {React.Children.map(childrenArr, child => {
                if (React.isValidElement(child)) {
                  return React.cloneElement(child, {
                    ...hookFormProps,
                  });
                }
                return child;
              })}
              {icon && inline ? <Image src={icon} className='form-item-right-icon' onClick={onIconClick} /> : null}
            </View>
          </>
        ) : (
          <>
            {React.Children.map(childrenArr, child => {
              if (React.isValidElement(child)) {
                return React.cloneElement(child, {
                  ...hookFormProps,
                });
              }
              return child;
            })}
          </>
        )}
      </View>
      {fieldError && typeof fieldError.message === 'string' ? (
        <View className='error-msg'>{fieldError.message}</View>
      ) : null}
      {!noSplitLine ? <CySplitLine lineColor='#f0f0f0'></CySplitLine> : null}
    </View>
  );
};

CyForm.Item = FormItem;

export default CyForm;
