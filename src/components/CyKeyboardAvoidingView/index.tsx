import React, {FC} from 'react';
import {View, ViewProps} from '@tarojs/components';
import './index.scss';

const onFocusDefault = (position: {x: number; y: number}) => {};
export const FocusContext = React.createContext<(e: {x: number; y: number}) => void>(onFocusDefault);
type Props = {};

/**
 * 组件名称：阻止键盘遮挡组件
 * 组件描述：阻止键盘遮挡组件
 */
const CyKeyboardAvoidingView: FC<Props & ViewProps> = props => {
  return (
    <View className='comp-cy-keyboard-avoiding-view' {...props}>
      {props.children}
    </View>
  );
};

export default CyKeyboardAvoidingView;
