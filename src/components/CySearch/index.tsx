import {FC, useEffect, useRef, useState} from 'react';
import {Icon, Input, View} from '@tarojs/components';
import Taro from '@tarojs/taro';
import './index.scss';

interface Props {
  defaultValue?: string;
  placeholder?: string;
  fontSize?: number;
  onSearch?: (value: string | undefined) => void;
}

/**
 * 组件名称：搜索组件
 * 组件描述：搜索
 */
const CySearch: FC<Props> = ({onSearch, placeholder = '请输入客户姓名或身份证后六位', defaultValue, fontSize = 14}) => {
  const [searchText, setSearchText] = useState<string | undefined>(defaultValue);
  useEffect(() => {
    if (defaultValue && defaultValue !== '') {
      setSearchText(defaultValue);
      onSearch && onSearch(defaultValue);
    }
  }, [defaultValue]);

  const onInput = (e: {detail: {value: any}}) => {
    const value = e.detail.value;
    if (value.length === 0) {
      setSearchText(undefined);
    } else {
      setSearchText(value);
    }
  };

  const toSearch = () => {
    Taro.hideKeyboard();
    onSearch && onSearch(searchText);
  };
  const onClear = () => {
    setSearchText(undefined);
  };
  return (
    <View className='comp-cy-search'>
      <View className='comp-cy-search-content'>
        <Input
          placeholder={placeholder}
          placeholderStyle='color: #999'
          value={searchText}
          className='comp-cy-search-input'
          style={{fontSize}}
          onInput={onInput}
        />
        {searchText && searchText.length > 0 && (
          <View onClick={onClear}>
            <Icon type='clear' size={16}></Icon>
          </View>
        )}
      </View>
      <View className='comp-cy-search-button' onClick={toSearch}>
        搜索
      </View>
    </View>
  );
};

export default CySearch;
