# OSX
#
.DS_Store
.swc

# Xcode
#
build/
*.pbxuser
!default.pbxuser
*.mode1v3
!default.mode1v3
*.mode2v3
!default.mode2v3
*.perspectivev3
!default.perspectivev3
xcuserdata
*.xccheckout
*.moved-aside
DerivedData
*.hmap
*.ipa
*.xcuserstate
ios/.xcode.env.local
ios/assets/node_modules/

# Android/IntelliJ
#
build/
.idea
.gradle/
local.properties
*.iml
*.hprof
.cxx/
/android/.gradle/


# harmony
/harmony/node_modules
/harmony/oh_modules
/harmony/local.properties
/harmony/.idea
/harmony**/build
/harmony/.hvigor
/harmony.cxx
/harmony/.clangd
/harmony/.clang-format
/harmony/.clang-tidy
/harmony**/.test
/harmony/entry/src/main/ets/
/harmony/entry/src/main/resources/

#weapp
/dist/

# node.js
#
/node_modules/
npm-debug.log
yarn-error.log

# fastlane
#
# It is recommended to not store the screenshots in the git repo. Instead, use fastlane to re-generate the
# screenshots whenever they are needed.
# For more information about the recommended setup visit:
# https://docs.fastlane.tools/best-practices/source-control/

**/fastlane/report.xml
**/fastlane/Preview.html
**/fastlane/screenshots
**/fastlane/test_output

# Bundle artifact
# *.jsbundle
*.jsbundle
*.android.bundle

# Expo
.expo/*

# Ruby / CocoaPods
/ios/Pods/
/vendor/bundle/

# Temporary files created by Metro to check the health of the file watcher
.metro-health-check*

# testing
/coverage

/android/app/src/main/java/com/rn/generated

**/packages/
.history/
## taro-react-native-release
!release/** 
/docs/.vitepress/


harmony/.gitignore
harmony/entry/.gitignore
