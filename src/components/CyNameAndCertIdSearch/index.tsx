import {FC, useEffect, useRef, useState} from 'react';
import {Icon, Input, View} from '@tarojs/components';
import Taro from '@tarojs/taro';
import classNames from 'classnames';
import './index.scss';

interface Props {
  defaultNameValue?: string;
  namePlaceholder?: string;
  defaultCertIdValue?: string;
  certIdPlaceholder?: string;
  fontSize?: number;
  onSearch?: (name: string | undefined, certId: string | undefined) => void;
}

/**
 * 组件名称：搜索组件
 * 组件描述：搜索
 */
const CyNameAndCertIdSearch: FC<Props> = ({
  onSearch,
  namePlaceholder = '请输入姓名',
  defaultNameValue,
  certIdPlaceholder = '请输入身份证后六位',
  defaultCertIdValue,
  fontSize = 14,
}) => {
  const [searchNameText, setSearchNameText] = useState<string | undefined>(defaultNameValue);
  const [searchCertIdText, setSearchCertIdText] = useState<string | undefined>(defaultCertIdValue);
  useEffect(() => {
    if (defaultNameValue && defaultNameValue !== '') {
      setSearchNameText(defaultNameValue);
      // onSearch && onSearch(defaultValue);
    }
  }, [defaultNameValue]);

  useEffect(() => {
    if (defaultCertIdValue && defaultCertIdValue !== '') {
      setSearchCertIdText(defaultCertIdValue);
      // onSearch && onSearch(defaultValue);
    }
  }, [defaultCertIdValue]);

  const onNameInput = (e: {detail: {value: any}}) => {
    const value = e.detail.value;
    if (value.length === 0) {
      setSearchNameText(undefined);
    } else {
      setSearchNameText(value);
    }
  };

  const onCertIdInput = (e: {detail: {value: any}}) => {
    const value = e.detail.value;
    if (value.length === 0) {
      setSearchCertIdText(undefined);
    } else {
      setSearchCertIdText(value);
    }
  };

  const toSearch = () => {
    Taro.hideKeyboard();
    onSearch && onSearch(searchNameText, searchCertIdText);
  };
  const onClearName = () => {
    setSearchNameText(undefined);
  };

  const onClearCertId = () => {
    setSearchCertIdText(undefined);
  };
  return (
    <View className='comp-cy-search'>
      <View className={classNames(['comp-cy-search-content', 'comp-cy-search-name'])}>
        <Input
          placeholder={namePlaceholder}
          placeholderStyle='color: #999'
          value={searchNameText}
          className='comp-cy-search-input'
          style={{fontSize}}
          onInput={onNameInput}
        />
        {searchNameText && searchNameText.length > 0 && (
          <View onClick={onClearName}>
            <Icon type='clear' size={16}></Icon>
          </View>
        )}
      </View>
      <View className={classNames(['comp-cy-search-content', 'comp-cy-search-certId'])}>
        <Input
          placeholder={certIdPlaceholder}
          placeholderStyle='color: #999'
          value={searchCertIdText}
          className='comp-cy-search-input'
          style={{fontSize}}
          onInput={onCertIdInput}
        />
        {searchCertIdText && searchCertIdText.length > 0 && (
          <View onClick={onClearCertId}>
            <Icon type='clear' size={16}></Icon>
          </View>
        )}
      </View>
      <View className='comp-cy-search-button' onClick={toSearch}>
        搜索
      </View>
    </View>
  );
};

export default CyNameAndCertIdSearch;
