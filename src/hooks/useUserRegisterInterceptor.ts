import Taro from '@tarojs/taro';
import {CyModal} from 'hncy58-taro-components';
import useUserStore from '~store/user';
import { jumpToActivityPage } from '~utils/activity';
import {goWebviewLinkOrRoute, router} from '~utils/common';

export const useUserRegisterInterceptor = () => {
  const {user, amountCreditInfo} = useUserStore();
  // 未登录用户拦截
  const loginInterceptor = () => {
    if (!user.mobile) {
      router.push({
        url: `/modules/user/login/index?type=intercept&redirectUrl=${encodeURIComponent('/' + Taro.getCurrentPages()[0].route || '')}`,
      });
      return false
    } 
    return true
  }

  // 未注册用户拦截
  const registerIntercept = () => {
    if (!loginInterceptor()) {
      return false
    } else if (!user.registerFlag) {
      CyModal.create({
        title: '温馨提示',
        content: '暂未获得额度，请先申请额度',
        isShowCancel: true,
        confirmText: '去申请',
        onConfirm: () => {
          router.push({
            url: '/modules/credit_authorization/identify/index',
          });
        },
      });
      return false
    }
    return true
  };

  const creditInterceptor = () => {
    if (!loginInterceptor()) {
      return false;
    } else if (!registerIntercept()) {
      return false
    } else if (['NO_AMOUNT', 'NO_AMOUNT_LACK_CARD_PIC', 'EXPIRED'].includes(amountCreditInfo.state)) {
      CyModal.create({
        title: '温馨提示',
        content: '暂未获得额度，请先申请额度',
        isShowCancel: true,
        confirmText: '去申请',
        onConfirm: () => {
          router.push({
            url: '/modules/credit_authorization/identify/index',
          });
        },
      });
      return false
    }
    return true
  }

  /**
   * 拦截未登录用户，打开三方链接或路由，支持活动链接
   * @param link 
   * @param isActivity 
   */

  const goLinkWithLoginInterceptor = (link: string, isActivity?: boolean) => {
    if(!loginInterceptor()) return
    if(isActivity) {
      jumpToActivityPage(link)
    } else {
      goWebviewLinkOrRoute(link)
    }
  };

  /**
   * 拦截未注册用户，打开三方链接或路由，支持活动链接
   * @param link 
   * @param isActivity 
   */
  const goLinkWithRegistInterceptor = (link: string, isActivity?: boolean) => {
    if(!registerIntercept()) return
    if(isActivity) {
      jumpToActivityPage(link)
    } else {
      goWebviewLinkOrRoute(link)
    }
  }

  /**
   * 拦截未授信用户，打开三方链接或路由，支持活动链接
   * @param link 
   * @param isActivity 
   */
  const goLinkWithCreditInterceptor = (link: string, isActivity?: boolean) => {
    if(!creditInterceptor()) return
    if(isActivity) {
      jumpToActivityPage(link)
    } else {
      goWebviewLinkOrRoute(link)
    }
  }

  return {
    registerIntercept,
    goLinkWithLoginInterceptor,
    goLinkWithRegistInterceptor,
    goLinkWithCreditInterceptor,
  };
};
