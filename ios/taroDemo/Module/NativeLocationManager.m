//
//  LocationManager.m
//  taroDemo
//
//  Created by captian on 2024/3/11.
//

#import "NativeLocationManager.h"
#import <React/RCTLog.h>
#import "LocationManager.h"

@implementation NativeLocationManager
static NativeLocationManager * sharedInstance = nil;

+ (instancetype)sharedInstance {
    if (sharedInstance == nil) {
      sharedInstance = [[self alloc] init];
    }
    return sharedInstance;
}

- (instancetype)init {
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
      sharedInstance = [super init];
        // 其他初始化代码
      [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(LocationDidChangeAuthorizationLocation:) name:@"LocationDidChangeAuthorizationLocation" object:nil];
    });
    return sharedInstance;
}

- (NSArray<NSString *> *)supportedEvents
{
  return @[@"LocationDidChangeAuthorization"];
}

- (void)LocationDidChangeAuthorizationLocation:(NSNotification *)notification
{
  NSString *status = notification.userInfo[@"status"];
  [self sendEventWithName:@"LocationDidChangeAuthorization" body:@{@"status": status}];
}

//React Native 知道您的模块是否需要在主线程上初始化
+ (BOOL)requiresMainQueueSetup
{
  return false;
}

RCT_EXPORT_MODULE();
//获取一次定位
RCT_REMAP_METHOD(getLocation,
                 resolve:(RCTPromiseResolveBlock)resolve
                 reject:(RCTPromiseRejectBlock)reject)
{
  
  BOOL res = [[LocationManager sharedInstance] requestLocationWithReGeocode:NO completionBlock:^(CLLocation *location, AMapLocationReGeocode *regeocode, NSError *error) {
    if (error){
        NSLog(@"locError:{%ld - %@};", (long)error.code, error.localizedDescription);
      reject([NSString stringWithFormat:@"%ld",error.code], error.localizedDescription, error);
      return;
    }
    NSLog(@"location:%@", location);
    resolve(@{
      @"latitude":@(location.coordinate.latitude),
      @"longitude":@(location.coordinate.longitude),
    });
    if (regeocode){
      
    }else{
      
    }
    
  }];
  
  if(!res){ //连续定位 开始持续定位时，单次定位会失败
    [[LocationManager sharedInstance] getUpdatingLocationInfoWithCompletionBlock:^(NSDictionary * _Nonnull location, NSError * _Nullable error) {
      if(error) {
        reject([NSString stringWithFormat:@"%ld",error.code], error.userInfo[@"message"], error);
      }else{
        resolve(location);
      }
    }];
  }
  
}

//开始持续定位
RCT_EXPORT_METHOD(startUpdatingLocation)
{
  [[LocationManager sharedInstance] startUpdatingLocation];
}


//关闭持续定位
RCT_EXPORT_METHOD(stopUpdatingLocation)
{
  [[LocationManager sharedInstance] stopUpdatingLocation];
}

//获取持续定位位置
RCT_REMAP_METHOD(getUpdatingLocation,
                 resolve1:(RCTPromiseResolveBlock)resolve
                 reject1:(RCTPromiseRejectBlock)reject)
{
  
  [[LocationManager sharedInstance] getUpdatingLocationInfoWithCompletionBlock:^(NSDictionary * _Nonnull location, NSError * _Nullable error) {
    if(error) {
      reject([NSString stringWithFormat:@"%ld",error.code], error.userInfo[@"message"], error);
    }else{
      resolve(location);
    }
  }];
  
}


//获取定位是否可用
RCT_REMAP_METHOD(getLocationAuthStatusIsValid,
                 resolve1:(RCTPromiseResolveBlock)resolve)
{
  
  resolve([LocationManager sharedInstance].locationAuthStatusIsValid ? @"1":@"0");
  
}


//当前应用是否可以使用/申请定位服务
RCT_REMAP_METHOD(getLocationServiceIsValid,
                 resolve:(RCTPromiseResolveBlock)resolve)
{
  
  resolve([LocationManager sharedInstance].locationServiceIsValid ? @"1":@"0");
}

//获取定位权限状态
RCT_REMAP_METHOD(getAuthorizationStatus,
                 resolve2:(RCTPromiseResolveBlock)resolve)
{
  
  resolve([NSString stringWithFormat:@"%d",[LocationManager sharedInstance].getAuthorizationStatus]);
  
}

@end
