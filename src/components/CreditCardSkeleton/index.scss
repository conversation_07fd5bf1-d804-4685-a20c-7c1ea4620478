.credit-card-skeleton {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  background-color: #fff;
  box-shadow: 0px 0px 8px 0px rgba(153, 153, 153, 0.4);
  border-radius: 16px;
  overflow: hidden;
  margin: 20px;

  &__topbar {
    height: 60px;
    background-color: #f0f0f0;
  }

  &__content {
    position: relative;
    text-align: center;
    width: 100%;
    padding: 40px 18px 16px 18px;
    box-sizing: border-box;
  }

  &__subtitle {
    height: 28px;
    width: 120px;
    margin: 0 auto 20px;
    border-radius: 4px;
  }

  &__amount {
    height: 70px;
    width: 280px;
    margin: 0 auto 20px;
    border-radius: 8px;
  }

  &__desc {
    height: 36px;
    width: 320px;
    margin: 0 auto 40px;
    border-radius: 4px;
  }

  &__buttons {
    display: flex;
    gap: 30px;
    padding: 0 22px;
    margin-bottom: 42px;
  }

  &__button {
    flex: 1;
    height: 88px;
    border-radius: 44px;
  }

  &__info-bar {
    display: flex;
    height: 88px;
    align-items: center;
    justify-content: space-around;
    background-color: #f8f8f8;
    border-radius: 8px;
    padding: 0 20px;
  }

  &__info-item {
    text-align: center;
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 8px;
    
    & + .credit-card-skeleton__info-item {
      border-left: 1px solid #e8e8e8;
    }
  }

  &__info-label {
    height: 20px;
    width: 80px;
    margin: 0 auto;
    border-radius: 4px;
  }

  &__info-value {
    height: 24px;
    width: 60px;
    margin: 0 auto;
    border-radius: 4px;
  }

  &__bottom {
    margin: 0 20px;
    border-top: 1px solid #e8e8e8;
    padding: 20px 0;
  }

  &__tip {
    height: 24px;
    width: 100%;
    border-radius: 4px;
  }
}

/* 骨架屏闪烁动画 */
.skeleton-shimmer {
  background: linear-gradient(
    90deg,
    #f0f0f0 25%,
    #e0e0e0 50%,
    #f0f0f0 75%
  );
  background-size: 200% 100%;
  animation: skeleton-loading 1.5s infinite;
}

@keyframes skeleton-loading {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}

/* 响应式适配 */
@media (max-width: 375px) {
  .credit-card-skeleton {
    margin: 16px;
    
    &__content {
      padding: 32px 16px 12px 16px;
    }
    
    &__amount {
      width: 240px;
      height: 60px;
    }
    
    &__desc {
      width: 280px;
    }
  }
}