import Taro, {ENV_TYPE, getEnv} from '@tarojs/taro';
import dayjs from 'dayjs';
import blob2base64 from './blob2base64';
import {cullObjectByValue, hideLoading, showLoading} from './common';

export interface SelectPhotoResult {
  /** 图片数据 */
  data: string;
  file?: string;
  /** 图片名称 */
  name: string;
  /** 图片原路径 */
  path: string;
}

export interface SizeType {
  /** 原图 */
  original: any;
  /** compressed */
  compressed: any;
}
export const selectPhoto = ({
  multiple,
  count,
  sourceType,
  sizeType,
  onSuccess,
  onFail,
}: {
  multiple?: boolean;
  count?: number; // 最多可选图片的张数
  sourceType?: string[];
  sizeType?: Array<keyof SizeType>;
  onSuccess?: (data: SelectPhotoResult[]) => void;
  onFail?: (error: Error) => void;
}) => {
  const params = cullObjectByValue({
    multiple,
    count,
    sourceType,
    sizeType,
  });
  console.log('count', params);
  return Taro.chooseImage(params)
    .then(async res => {
      showLoading();
      const newAddVals = await Promise.all(
        res.tempFiles.map(async ({path, type, size}, i) => {
          if (getEnv() === ENV_TYPE.RN) {
            console.log('大小=', size / 1024 / 1024, 'M');
            const name = `${dayjs().valueOf()}_1${path.substring(path.lastIndexOf('.'))}`;
            console.log('name=', name);
            const base64 = await blob2base64(path as unknown as Blob);
            return {data: `data:${type};base64,${base64}` as string, name, path};
          } else {
            const file = res.tempFiles[i].originalFileObj;
            const name = `${dayjs().valueOf()}_1${path.substring(path.lastIndexOf('.'))}`;
            if (!file) return {data: path, name, path};
            const base64 = await blob2base64(file);
            return {data: base64 as string, name, path};
          }
        }),
      );
      hideLoading();
      onSuccess && onSuccess(newAddVals as SelectPhotoResult[]);
    })
    .catch(e => {
      hideLoading();
      onFail?.(e);
    });
};

export const getPhotoData = (path: string) => {
  return new Promise<string>(async (resolve, reject) => {
    try {
      return resolve('');
      // return resolve(`data:image/png;base64,${data.file}` ?? '');
    } catch (e) {
      return resolve('');
    }
  });
};

export const getFilesBase64ListAndNames = async (key: string, pathList: string[]) => {
  let base64List: string[] = [];
  try {
    base64List = await Promise.all(
      pathList?.map(async (fileName: string) => {
        const filestr = await getPhotoData(fileName);
        return `data:image/png;base64,${filestr}`;
      }),
    );
  } catch (e) {
    //
  }
  const res: Record<string, any> = {};
  res[`${key}NameList`] = pathList;
  res[`${key}Base64List`] = base64List;
  return res;
};


/**
 * 通过文件路径获取文件拓展名
 * @param filePath 文件路径
 * @returns 文件扩展名（包含点号，如 .jpg）
 */
export const getFileExtension = (filePath: string): string => {
  if (!filePath) return '';
  const lastDotIndex = filePath.lastIndexOf('.');
  if (lastDotIndex === -1) return '';
  return filePath.substring(lastDotIndex + 1);
};