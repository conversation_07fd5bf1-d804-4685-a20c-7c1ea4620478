// sipkeyboard/Keyboard/index.js
// import {DisplayMode, KeyboardType, OrderType} from '../Util/KeyboardConstants.js';
// import {
//   CIPHER_TYPE_SM2,
//   DEFAULT_MAX_LENGTH,
//   DEFAULT_MIN_LENGTH,
//   KeyboardController,
//   OUTPUT_TYPE_ORIGINAL,
// } from './sip.min.js';

const {DisplayMode, KeyboardType, OrderType} = require('../Util/KeyboardConstants.js');
const {
    CIPHER_TYPE_SM2,
    DEFAULT_MAX_LENGTH,
    DEFAULT_MIN_LENGTH,
    KeyboardController,
    OUTPUT_TYPE_ORIGINAL,} = require('./sip.min.js');

Component({
  /**
   * focus-class 获取焦点输入状态外部class
   * text-class 默认状态输入框外部class
   * placeholder-class placeholder外部class
   */
  externalClasses: ['focus-class', 'text-class', 'placeholder-class'],
  /**
   * 组件的属性列表
   */
  properties: {
    sipId: {
      type: String,
      value: null,
    },
    serverRandom: {
      type: String,
      value: null,
    },
    cipherType: {
      type: Number,
      value: CIPHER_TYPE_SM2,
    },
    outputType: {
      type: Number,
      value: OUTPUT_TYPE_ORIGINAL,
    },
    inputRegex: {
      type: String,
      value: null,
    },
    keyword: {
      type: Array,
      value: [],
    },
    isEncryptState: {
      type: Boolean,
      value: true,
    },
    keyboardType: {
      type: Number,
      value: KeyboardType.CompleteKeyboard,
    },
    displayMode: {
      type: Number,
      value: DisplayMode.Light,
    },
    orderType: {
      type: Number,
      value: OrderType.NONE,
    },
    minLength: {
      type: Number,
      value: DEFAULT_MIN_LENGTH,
    },
    maxLength: {
      type: Number,
      value: DEFAULT_MAX_LENGTH,
    },
    operationKey: {
      type: Array,
      value: [],
    },
    doneKey: {
      type: Object,
      value: null,
    },
    backKey: {
      type: Object,
      value: null,
    },
    isGrid: {
      type: Boolean,
      value: false,
    },
    isShowKeyboard: {
      type: Boolean,
      value: false,
    },
    isKeyAnimation: {
      type: Boolean,
      value: false,
    },
    showLastCharacter: {
      type: Boolean,
      value: true,
    },
    precision: {
      type: Number,
      value: 2,
    },
    placeholder: {
      type: String,
      value: '',
    },
    isHideBottom: {
      type: Boolean,
      value: false,
    },
    allowScreenRecord: {
      type: Boolean,
      value: false,
    },
    isKeyFeedBack: {
      type: Boolean,
      value: false,
    },
  },

  observers: {
    ...KeyboardController.observers,
  },

  /**
   * 组件的初始数据
   */
  data: {
    canvasHeight: 0,
    canvasWidth: 0,
    backgroundColor: '#ffffff',
    bubbleShowText: '',
    text: '',
    bubbleStyle: null,
    bottomPadding: 0,
    bubbleClass: 'bubble',
    bubbleTextClass: 'bubble-text',
    canvasId: null,
    init: false,
    textClass: 'sip-input-normal',
    scrollLeft: 0,
    keyboardTop: null,
    isPC: false,
  },

  lifetimes: {
    created() {
      KeyboardController.life.create(this);
    },
    attached() {
      // 在组件实例进入页面节点树时执行
      this._initKeyboard(this.deviceInfo);
    },
    ready() {
      KeyboardController.life.ready(this);
    },
    detached() {
      // 在组件实例被从页面节点树移除时执行
    },
    moved() {},
    error() {},
  },

  pageLifetimes: {
    show() {},
    hide() {
      // 页面被隐藏
      this.setData({
        isShowKeyboard: false,
      });
    },
    resize(size) {
      // 页面尺寸变化
      this.setData({
        isShowKeyboard: false,
      });
    },
  },

  /**
   * 组件的方法列表
   */
  methods: {
    ...KeyboardController.methods,
  },
});
