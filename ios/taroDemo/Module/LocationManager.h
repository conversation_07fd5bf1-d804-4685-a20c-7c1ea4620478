//
//  MapManager.h
//  taroDemo
//
//  Created by captian on 2024/3/11.
//

#import <Foundation/Foundation.h>
#import <AMapFoundationKit/AMapFoundationKit.h>

#import <AMapLocationKit/AMapLocationKit.h>
NS_ASSUME_NONNULL_BEGIN
typedef void (^GetLocatingCompletionBlock)(NSDictionary * _Nullable location, NSError * _Nullable error);
@interface LocationManager : NSObject
+ (instancetype)sharedInstance;
- (BOOL)requestLocationWithReGeocode:(BOOL)withReGeocode completionBlock:(AMapLocatingCompletionBlock)completionBlock;
- (void)startUpdatingLocation;
- (void)stopUpdatingLocation;
- (void)getUpdatingLocationInfoWithCompletionBlock:(GetLocatingCompletionBlock)completionBlock;
- (CLAuthorizationStatus)getAuthorizationStatus;
- (BOOL)locationServiceIsValid;
- (BOOL)locationAuthStatusIsValid;
@end

NS_ASSUME_NONNULL_END
