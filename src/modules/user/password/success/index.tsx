import PageWrap from '~components/PageWrap';
import styles from './index.module.scss';
import { useMemo, useState } from 'react';
import successIcon from '~images/password/<EMAIL>';
import { Image, View } from '@tarojs/components';
import { useLoad } from '@tarojs/taro';

const TextMap: Record<string, string> = {
  set: '密码设置成功',
  reset: '密码重置成功',
  change: '密码修改成功'
}

export default () => {
  const [type, setType] = useState<string>();
  
  useLoad((query) => {
    if(query.type) {
      setType(query.type);
    }
  })
  return (
    <PageWrap className={styles.passwordSuccessPage}>
      <View className={styles.successContent}>
        <Image src={successIcon} className={styles.successIcon} />
        {
          type && TextMap[type] ? (
            <View className={styles.successDesc}>{TextMap[type]}</View>
          ) : null
        }
      </View>
    </PageWrap>
  );
};
