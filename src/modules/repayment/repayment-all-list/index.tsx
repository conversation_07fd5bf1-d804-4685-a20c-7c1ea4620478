import {Text, View} from '@tarojs/components';
import MoneyDisplay from '~components/MoneyDisplay';
import PageWrap from '~components/PageWrap';
import RepaymentAmount from '../components/RepaymentAmount';
import RepaymentBill from '../components/RepaymentBill';
import RepaymentFooterBar from '../components/RepaymentFooterBar';
import styles from './index.module.scss';
import { useEffect, useState } from 'react';
import { getRepaymentList } from '~services/repayment';
import { useLoad } from '@tarojs/taro';
import CyCheckbox from '~components/CyCheckbox';
import { router, showLoading, showToast } from '~utils/common';
import Taro from '@tarojs/taro';
import { CyButton, CyPopup } from 'hncy58-taro-components';
import currency from 'currency.js';
import MarqueeTip from '~components/MarqueeTip';

export default () => {
  const [aheadTipsVisible, setAheadTipsVisible] = useState(false);
  const [repaymentList, setRepaymentList] = useState<API.ListLoanDetail[]>([]);
  const [repaymentOtherInfo, setRepaymentOtherInfo] = useState<Omit<API.RepaymentListRes, 'list'>>()
  const [checkedList, setCheckedList] = useState<string[]>([])
  const [btSummary, setBtSummary] = useState<{
    total: number;
    num: number;
    principal: number;
    interest: number;
    penalty: number;
  }>()
  const [activeSelectCate, setActiveSelectCate] = useState<API.LoanStatus>()

  useLoad(async () => {
    try {
      Taro.showLoading()
      const {list, ...others} = await getRepaymentList({repayableSummaryCategory: 'ALL'})
      setRepaymentList(list)
      setRepaymentOtherInfo(others)
      Taro.hideLoading()
    } catch (error) {
      console.log('getRepaymentListErr====', error);
      Taro.hideLoading()
    }
  })

  const handleCheckboxChange = (val: any) => {
    setCheckedList(val.detail?.value)
    if(!activeSelectCate && val.detail?.value?.length === 1) {
      const target = repaymentList.find((ite) => ite.loanNo === val.detail?.value?.[0]);
      if(target) {
        setActiveSelectCate(target.loanStatus)
      }
    } else if (val.detail?.value?.length === 0) {
      setActiveSelectCate(undefined)
    }
  }

  useEffect(() => {
    const summary = {
      total: 0,
      num: 0,
      principal: 0,
      interest: 0,
      penalty: 0,
    }
    if(Array.isArray(checkedList)) {
      if(['OVERDUE', 'TODAY'].includes(activeSelectCate || '')) {
        checkedList.forEach((item: string) => {
          const findItem = repaymentList.find((ite) => ite.loanNo === item)
          summary.total = currency(summary.total).add(findItem?.repayableAmount || 0).value
          summary.principal = currency(summary.principal).add(findItem?.repayablePrincipal || 0).value
          summary.interest = currency(summary.interest).add(findItem?.repayableInterest || 0).value
          summary.penalty = currency(summary.penalty).add(findItem?.repayablePenaltyInterest || 0).value
          summary.num += 1
        })
      } else {
        checkedList.forEach((item: string) => {
          const findItem = repaymentList.find((ite) => ite.loanNo === item)
          summary.total = currency(summary.total).add(findItem?.remainPrincipal || 0).value
          summary.principal = currency(summary.principal).add(findItem?.remainPrincipal || 0).value
          summary.interest = currency(summary.interest).add(findItem?.repayableInterest || 0).value
          summary.penalty = currency(summary.penalty).add(findItem?.repayablePenaltyInterest || 0).value
          summary.num += 1
        })
      }
    }
    setBtSummary(summary) 
  }, [checkedList, activeSelectCate])

  const startConfirm = () => {
    if(repaymentOtherInfo?.maxRecordNum && checkedList?.length > repaymentOtherInfo?.maxRecordNum) {
      showToast(`最多只能选择${repaymentOtherInfo?.maxRecordNum}笔账单进行还款`)
      return
    }
    const repayableSummaryCategory = activeSelectCate === 'UNEXPIRED' ? 'AHEAD' : activeSelectCate
    if(repayableSummaryCategory === 'AHEAD') {
      setAheadTipsVisible(true)
      return    
    }
    goConfirmPage()
  }
  
  const goConfirmPage = () => {
    setAheadTipsVisible(false)
    const repayableSummaryCategory = activeSelectCate === 'UNEXPIRED' ? 'AHEAD' : activeSelectCate
    router.push({
      url: `/modules/repayment/repayment-confirm/index?loanNos=${checkedList.join(',')}&category=${repayableSummaryCategory}&repayAmount=${btSummary?.total || 0}`,
    })
  }

  return (
    <PageWrap className={styles.periodsContainer}>
      <MarqueeTip
        darkIcon
        content='本公司的非交易时间段为23:00-3:00（第二天）'
        color='#616C92'
        duration={0}
        loopNoGap={false}
      />
      <View className={styles.topAmountShortcut}><RepaymentAmount amount={repaymentOtherInfo?.sumAmount} amountTitle='剩余待还总本金（元）' /></View>
      <View className={styles.bill}>

      <CyCheckbox options={repaymentList?.map((ite, index) => {
          const disabled = (activeSelectCate !== undefined && ite.loanStatus !== activeSelectCate) || !ite.loanStatus
          return {
            label: '',
            child:  (
              <RepaymentBill overdueDays={ite.overdueDays} unSelectAble={disabled} principal={ite.principal} remainTerms={ite.remainTerms} key={index} amount={ite.repayableAmount ||  ite.remainPrincipal || ite.principal } date={ite.startDate} status={ite.loanStatus} loanNo={ite.loanNo} />
            ),
            disabled,
            value: ite.loanNo
          }
        })} maxSelect={repaymentOtherInfo?.maxRecordNum} maxSelectErrorMessage={`最多只能选择${repaymentOtherInfo?.maxRecordNum}笔账单进行还款`} onChange={handleCheckboxChange}></CyCheckbox>
      </View>
      <RepaymentFooterBar
        tips={
          repaymentOtherInfo?.enableRepay === false ? `提示：当前为自动扣款时间段(${repaymentOtherInfo?.startTime}-${repaymentOtherInfo?.endTime})，暂不支持主动还款，建议您稍后再试` :  ''
        }
        content={
          <>
            <View className={styles.footerBarText}>
              合计：
              <MoneyDisplay money={btSummary?.total || 0} color='#2F54EB' />
              <Text className={styles.numContainer}>
                共<Text className={styles.num}>{btSummary?.num}</Text>笔
              </Text>
            </View>
            {
              ['OVERDUE', 'TODAY'].includes(activeSelectCate || '') ? (
                <>
                  <View className={styles.footerBarSubText}>含: 本金：¥{btSummary?.principal}, 利息: ¥{btSummary?.interest}</View>
                  {btSummary?.penalty && btSummary.penalty > 0 ? <View className={styles.footerBarSubTextRow2}>罚息: ¥{btSummary?.penalty}</View> : null}
                </>
              ) : null
            }
          </>
        }
        btnText='立即还款'
        disabled={!checkedList?.length || !repaymentOtherInfo?.enableRepay}
        onBtnClick={startConfirm}
      />

      <CyPopup
        bodyStyle={{background: 'linear-gradient(0deg, #FFFFFF 12%, #FFFFFF 85%, #fffcea 100%)'}}
        onClose={() => setAheadTipsVisible(false)}
        showClose
        title='确定要提前还款吗？'
        visible={aheadTipsVisible}>
        <View className={styles.repaymentPrompt}>
          {/* <View className={styles.promptTitle}></View> */}
          <View>
            您的借款暂未到期，建议<Text className={styles.promptText}>按期还款</Text>,减轻还款压力
          </View>
          <CyButton className={styles.promptBtn} onClick={() => setAheadTipsVisible(false)} type='primary' block>
            按期还款
          </CyButton>
          <View className={styles.advance} onClick={goConfirmPage}>提前还款</View>
        </View>
      </CyPopup>
    </PageWrap>
  );
};
