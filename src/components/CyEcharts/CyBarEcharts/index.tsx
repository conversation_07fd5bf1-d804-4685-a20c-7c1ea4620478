import {FC, useEffect, useState} from 'react';
import type {BarSeriesOption} from 'echarts/charts';
import {ComposeOption} from 'echarts/core';
import CyEcharts from '..';

interface Props {
  width: number;
  height: number;
  customerToolTip?: (data: any) => React.ReactNode; //自定义tooltip
}
/**
 * 组件名称：图表
 * 组件描述：图表
 */
const CyBarEcharts: FC<Props & ComposeOption<BarSeriesOption>> = props => {
  const [options, setOptions] = useState<ComposeOption<BarSeriesOption>>({});
  useEffect(() => {
    setupOptions();
  }, [props]);
  const setupOptions = () => {
    const emphasisStyle = {
      itemStyle: {
        shadowBlur: 10,
        shadowOffsetX: 0,
        shadowOffsetY: 0,
        shadowColor: 'rgba(0,0,0,0.5)',
      },
    };

    const _option = {
      backgroundColor: '#fff',
      legend: {
        data: ['', '增速贡献', '', ''],
        textStyle: {
          fontSize: 12,
        },
        left: 10,
      },
      tooltip: {triggerOn: 'none'},
      xAxis: {
        type: 'category',
        name: '',
        axisLine: {onZero: true},
        splitLine: {show: false},
        splitArea: {show: false},
        axisLabel: {rotate: 45, fontSize: 12},
        data: props.xAxis || [
          '利息收入',
          '利息支出',
          '手续费及佣金收入',
          '手续费及佣金支出',
          '资产减值损失',
          '工资薪金支出',
          '其他业务管理费',
          '所得税费用',
          '增值税金及附加',
          '其他经营净收益',
          'ROE增速',
        ],
      },
      yAxis: {
        inverse: false,
        splitArea: {show: false},
        axisLabel: {
          formatter: '{value}%',
          fontSize: 12,
        },
      },
      grid: {
        left: '11%',
        right: 10,
        bottom: '25%',
      },
      series: [
        {
          name: '处理值',
          data: props.data1,
          type: 'bar',
          stack: 'one',
          emphasis: emphasisStyle,
          label: {show: false, fontSize: 12},
          itemStyle: {color: 'transparent'},
        },
        {
          name: '增速贡献',
          type: 'bar',
          stack: 'one',
          emphasis: emphasisStyle,
          label: {
            show: true,
            fontSize: 12,
            color: '#333333',
            formatter(v: {data: {tip: any}}) {
              let text = v.data.tip;
              return text;
            },
          },
          itemStyle: {color: '#FF9800'},
          data: props.data2,
        },
        {
          name: '增速贡献2',
          type: 'bar',
          stack: 'one',
          emphasis: emphasisStyle,
          label: {
            show: true,
            fontSize: 12,
            color: '#333333',
            formatter(v: {data: {tip: any}}) {
              let text = v.data.tip;
              return text;
            },
          },
          itemStyle: {color: '#f44336'},
          data: props.data3,
        },
        {
          name: '增速贡献3',
          stack: 'one',
          emphasis: emphasisStyle,
          label: {
            show: true,
            fontSize: 12,
            color: '#333333',
            formatter(v: {data: {tip: any}}) {
              let text = v.data.tip;
              return text;
            },
          },
          itemStyle: {color: '#FF9800'},
          type: 'bar',
          data: props.data4,
        },
      ],
    };

    // 使用刚指定的配置项和数据显示图表。
    setOptions(_option);
  };

  return (
    <CyEcharts option={options} width={props.width} height={props.height} customerToolTip={props.customerToolTip} />
  );
};

export default CyBarEcharts;
