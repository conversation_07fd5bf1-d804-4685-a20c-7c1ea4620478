import React, {ForwardRefRenderFunction, forwardRef, useImperativeHandle, useState} from 'react';
import {Icon, Input, View} from '@tarojs/components';
import classNames from 'classnames';
import styles from './index.module.scss';

interface Props {
  suffix?: React.ReactNode;
  defaultValue?: string;
  value?: string;
  prefix?: React.ReactNode;
  onInput?: (e: any) => void;
  inputClassName?: string;
  inputWrapClassName?: string;
  clearAble?: boolean;
  [key: string]: any;
}
const LoginInput: ForwardRefRenderFunction<unknown, Props> = (props, ref) => {
  const {suffix, defaultValue, value, prefix, onInput, inputClassName, inputWrapClassName, clearAble, ...inputProps} =
    props;
  console.log('input reRender');
  const [currentVal, setCurrentVal] = useState(value || defaultValue || '');

  useImperativeHandle(ref, () => ({
    get value() {
      return currentVal;
    },
    set value(newValue) {
      console.log('setValue', newValue);
      setCurrentVal(newValue);
    },
  }));

  const curOnInput = (e: any) => {
    const {value, cursor} = e.detail
    onInput?.(e);
    setCurrentVal(value);
  };

  const clear = () => {
    onInput?.({
      detail: {
        value: '',
      },
    });
    setCurrentVal('');
  };

  let customerValue: any;
  if (!defaultValue) {
    customerValue = {value};
  }

  return (
    <View className={classNames(styles.inputWrap, inputWrapClassName)}>
      {prefix ? <View className={styles.inputPrefix}>{prefix}</View> : null}
      <Input
        {...customerValue}
        defaultValue={value ?? currentVal}
        onInput={curOnInput}
        {...inputProps}
        className={classNames(styles.input, inputClassName)}
      />
      {suffix ? <View className={styles.inputSuffix}>{suffix}</View> : null}
      {clearAble && currentVal ? <Icon onClick={clear} className={styles.inputSuffix} type='clear' size={16} /> : null}
    </View>
  );
};
export default forwardRef(LoginInput);
