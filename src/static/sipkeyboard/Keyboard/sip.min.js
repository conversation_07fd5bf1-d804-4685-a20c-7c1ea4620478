/* eslint-disable */var goog=goog||{};goog.global={};goog.exportPath_=function(name,opt_object,opt_objectToExportTo){var parts=name.split(".");var cur=opt_objectToExportTo||goog.global;if(!(parts[0]in cur)&&cur.execScript)cur.execScript("var "+parts[0]);for(var part;parts.length&&(part=parts.shift());)if(!parts.length&&opt_object!==undefined)cur[part]=opt_object;else if(cur[part])cur=cur[part];else cur=cur[part]={}};
goog.exportSymbol=function(publicPath,object,opt_objectToExportTo){goog.exportPath_(publicPath,object,opt_objectToExportTo)};goog.exportProperty=function(object,publicName,symbol){object[publicName]=symbol};
'use strict';var k;function aa(a){switch(a){case 0:var b="0";break;case 1:b="1";break;case 2:b="2";break;case 3:b="3";break;case 4:b="4";break;case 5:b="5";break;case 6:b="6";break;case 7:b="7";break;case 8:b="8";break;case 9:b="9";break;case 10:b="a";break;case 11:b="b";break;case 12:b="c";break;case 13:b="d";break;case 14:b="e";break;case 15:b="f"}return b}
function ba(a){switch(a){case "0":var b=0;break;case "1":b=1;break;case "2":b=2;break;case "3":b=3;break;case "4":b=4;break;case "5":b=5;break;case "6":b=6;break;case "7":b=7;break;case "8":b=8;break;case "9":b=9;break;case "a":case "A":b=10;break;case "b":case "B":b=11;break;case "c":case "C":b=12;break;case "d":case "D":b=13;break;case "e":case "E":b=14;break;case "f":case "F":b=15}return b}
function w(a){var b=[];0!=(a.length&1)&&(a="0"+a);for(var c=0;c<a.length;c+=2)b.push(ba(a.charAt(c))<<4^ba(a.charAt(c+1)));return b}function ca(a){switch(a.length){case 1:var b=a[0];break;case 2:b=(a[0]&28)>>>2;var c=(a[0]&3)<<6;a=a[1]&63;b=b<<8^c^a;break;case 3:var d=(a[0]&15)<<4,e=(a[1]&60)>>>2;c=(a[1]&3)<<6;a=a[2]&63;b=(d^e)<<8^c^a;break;case 4:b=(a[0]&7)<<2^(a[1]&48)>>>4;d=(a[1]&15)<<4;e=(a[2]&60)>>>2;c=(a[2]&3)<<6;a=a[3]&63;b=b<<16^(d^e)<<8^c^a;break;default:b=-1}return b}
function da(a){for(var b=[],c=0;c<a.length;c++){var d=a.charCodeAt(c);if(55296<=d&&56319>=d&&c<a.length-1){d=d-55296<<10;var e=a.charCodeAt(++c)-56320;d=65536+(d|e)}var f=d;d=[];if(!(0<f>>>31))if(0<=f&&127>=f)d.push(f);else if(128<=f&&2047>=f){var g=f&255;e=128^g<<2>>>2;d.push(192^f>>>8<<2^g>>>6);d.push(e)}else if(2048<=f&&65535>=f){var h=f>>>8;g=f&255;e=128^(h&15)<<2^g>>>6;f=128^g&63;d.push(224^h>>>4);d.push(e);d.push(f)}else if(65536<=f&&1114111>=f){var l=f>>>16;h=f>>>8&255;g=f&255;e=128^(l&3)<<
4^h>>>4;f=128^(h&15)<<2^g>>>6;g=128^g&63;d.push(240^l>>>2);d.push(e);d.push(f);d.push(g)}b=b.concat(d)}return b};function ea(a,b,c,d,e){var f=[16843776,0,65536,16843780,16842756,66564,4,65536,1024,16843776,16843780,1024,16778244,16842756,16777216,4,1028,16778240,16778240,66560,66560,16842752,16842752,16778244,65540,16777220,16777220,65540,0,1028,66564,16777216,65536,16843780,4,16842752,16843776,16777216,16777216,1024,16842756,65536,66560,16777220,1024,4,16778244,66564,16843780,65540,16842752,16778244,16777220,1028,66564,16843776,1028,16778240,16778240,0,65540,66560,0,16842756],g=[-2146402272,-2147450880,32768,
1081376,1048576,32,-2146435040,-2147450848,-2147483616,-2146402272,-2146402304,-2147483648,-2147450880,1048576,32,-2146435040,1081344,1048608,-2147450848,0,-2147483648,32768,1081376,-2146435072,1048608,-2147483616,0,1081344,32800,-2146402304,-2146435072,32800,0,1081376,-2146435040,1048576,-2147450848,-2146435072,-2146402304,32768,-2146435072,-2147450880,32,-2146402272,1081376,32,32768,-2147483648,32800,-2146402304,1048576,-2147483616,1048608,-2147450848,-2147483616,1048608,1081344,0,-2147450880,32800,
-2147483648,-2146435040,-2146402272,1081344],h=[520,134349312,0,134348808,134218240,0,131592,134218240,131080,134217736,134217736,131072,134349320,131080,134348800,520,134217728,8,134349312,512,131584,134348800,134348808,131592,134218248,131584,131072,134218248,8,134349320,512,134217728,134349312,134217728,131080,520,131072,134349312,134218240,0,512,131080,134349320,134218240,134217736,512,0,134348808,134218248,131072,134217728,134349320,8,131592,131584,134217736,134348800,134218248,520,134348800,
131592,8,134348808,131584],l=[8396801,8321,8321,128,8396928,8388737,8388609,8193,0,8396800,8396800,8396929,129,0,8388736,8388609,1,8192,8388608,8396801,128,8388608,8193,8320,8388737,1,8320,8388736,8192,8396928,8396929,129,8388736,8388609,8396800,8396929,129,0,0,8396800,8320,8388736,8388737,1,8396801,8321,8321,128,8396929,129,1,8192,8388609,8193,8396928,8388737,8193,8320,8388608,8396801,128,8388608,8192,8396928],n=[256,34078976,34078720,1107296512,524288,256,1073741824,34078720,1074266368,524288,33554688,
1074266368,1107296512,1107820544,524544,1073741824,33554432,1074266112,1074266112,0,1073742080,1107820800,1107820800,33554688,1107820544,1073742080,0,1107296256,34078976,33554432,1107296256,524544,524288,1107296512,256,33554432,1073741824,34078720,1107296512,1074266368,33554688,1073741824,1107820544,34078976,1074266368,256,33554432,1107820544,1107820800,524544,1107296256,1107820800,34078720,0,1074266112,1107296256,524544,33554688,1073742080,524288,0,1074266112,34078976,1073742080],m=[536870928,541065216,
16384,541081616,541065216,16,541081616,4194304,536887296,4210704,4194304,536870928,4194320,536887296,536870912,16400,0,4194320,536887312,16384,4210688,536887312,16,541065232,541065232,0,4210704,541081600,16400,4210688,541081600,536870912,536887296,16,541065232,4210688,541081616,4194304,16400,536870928,4194304,536887296,536870912,16400,536870928,541081616,4210688,541065216,4210704,541081600,0,541065232,16,16384,541065216,4210704,16384,4194320,536887312,0,541081600,536870912,4194320,536887312],v=[2097152,
69206018,67110914,0,2048,67110914,2099202,69208064,69208066,2097152,0,67108866,2,67108864,69206018,2050,67110912,2099202,2097154,67110912,67108866,69206016,69208064,2097154,69206016,2048,2050,69208066,2099200,2,67108864,2099200,67108864,2099200,2097152,67110914,67110914,69206018,69206018,2,2097154,67108864,67110912,2097152,69208064,2050,2099202,69208064,2050,67108866,69208066,69206016,2099200,0,2,69208066,0,2099202,69206016,2048,67108866,67110912,2048,2097154],u=[268439616,4096,262144,268701760,268435456,
268439616,64,268435456,262208,268697600,268701760,266240,268701696,266304,4096,64,268697600,268435520,268439552,4160,266240,262208,268697664,268701696,4160,0,0,268697664,268435520,268439552,266304,262144,266304,262144,268701696,4096,64,268697664,4096,266304,268439552,64,268435520,268697600,268697664,268435456,262144,268439616,0,268701760,262208,268435520,268697600,268439552,268439616,0,268701760,266240,266240,4160,4160,262208,268435456,268701696];a=fa(a);var r=0,y,z=b.length,G=0,C=32==a.length?3:
9;var H=3==C?c?[0,32,2]:[30,-2,-2]:c?[0,32,2,62,30,-2,64,96,2]:[94,62,-2,32,64,2,30,-2,-2];2==e?b+="        ":1==e?(e=8-z%8,b+=String.fromCharCode(e,e,e,e,e,e,e,e),8==e&&(z+=8)):e||(b+="\x00\x00\x00\x00\x00\x00\x00\x00");var I="",N="";var K=d.charCodeAt(r++)<<24|d.charCodeAt(r++)<<16|d.charCodeAt(r++)<<8|d.charCodeAt(r++);var q=d.charCodeAt(r++)<<24|d.charCodeAt(r++)<<16|d.charCodeAt(r++)<<8|d.charCodeAt(r++);for(r=0;r<z;){var t=b.charCodeAt(r++)<<24|b.charCodeAt(r++)<<16|b.charCodeAt(r++)<<8|b.charCodeAt(r++);
var p=b.charCodeAt(r++)<<24|b.charCodeAt(r++)<<16|b.charCodeAt(r++)<<8|b.charCodeAt(r++);if(c)t^=K,p^=q;else{var x=K;var Ba=q;K=t;q=p}e=(t>>>4^p)&252645135;p^=e;t^=e<<4;e=(t>>>16^p)&65535;p^=e;t^=e<<16;e=(p>>>2^t)&858993459;t^=e;p^=e<<2;e=(p>>>8^t)&16711935;t^=e;p^=e<<8;e=(t>>>1^p)&1431655765;p^=e;t^=e<<1;t=t<<1|t>>>31;p=p<<1|p>>>31;for(y=0;y<C;y+=3){var ub=H[y+1];var Ha=H[y+2];for(d=H[y];d!=ub;d+=Ha){var ha=p^a[d];var Ya=(p>>>4|p<<28)^a[d+1];e=t;t=p;p=e^(g[ha>>>24&63]|l[ha>>>16&63]|m[ha>>>8&63]|
u[ha&63]|f[Ya>>>24&63]|h[Ya>>>16&63]|n[Ya>>>8&63]|v[Ya&63])}e=t;t=p;p=e}t=t>>>1|t<<31;p=p>>>1|p<<31;e=(t>>>1^p)&1431655765;p^=e;t^=e<<1;e=(p>>>8^t)&16711935;t^=e;p^=e<<8;e=(p>>>2^t)&858993459;t^=e;p^=e<<2;e=(t>>>16^p)&65535;p^=e;t^=e<<16;e=(t>>>4^p)&252645135;p^=e;t^=e<<4;c?(K=t,q=p):(t^=x,p^=Ba);N+=String.fromCharCode(t>>>24,t>>>16&255,t>>>8&255,t&255,p>>>24,p>>>16&255,p>>>8&255,p&255);G+=8;512==G&&(I+=N,N="",G=0)}return I+N}
function fa(a){for(var b=[0,4,536870912,536870916,65536,65540,536936448,536936452,512,516,536871424,536871428,66048,66052,536936960,536936964],c=[0,1,1048576,1048577,67108864,67108865,68157440,68157441,256,257,1048832,1048833,67109120,67109121,68157696,68157697],d=[0,8,2048,2056,16777216,16777224,16779264,16779272,0,8,2048,2056,16777216,16777224,16779264,16779272],e=[0,2097152,134217728,136314880,8192,2105344,134225920,136323072,131072,2228224,134348800,136445952,139264,2236416,134356992,136454144],
f=[0,262144,16,262160,0,262144,16,262160,4096,266240,4112,266256,4096,266240,4112,266256],g=[0,1024,32,1056,0,1024,32,1056,33554432,33555456,33554464,33555488,33554432,33555456,33554464,33555488],h=[0,268435456,524288,268959744,2,268435458,524290,268959746,0,268435456,524288,268959744,2,268435458,524290,268959746],l=[0,65536,2048,67584,536870912,536936448,536872960,536938496,131072,196608,133120,198656,537001984,537067520,537004032,537069568],n=[0,262144,0,262144,2,262146,2,262146,33554432,33816576,
33554432,33816576,33554434,33816578,33554434,33816578],m=[0,268435456,8,268435464,0,268435456,8,268435464,1024,268436480,1032,268436488,1024,268436480,1032,268436488],v=[0,32,0,32,1048576,1048608,1048576,1048608,8192,8224,8192,8224,1056768,1056800,1056768,1056800],u=[0,16777216,512,16777728,2097152,18874368,2097664,18874880,67108864,83886080,67109376,83886592,69206016,85983232,69206528,85983744],r=[0,4096,134217728,134221824,524288,528384,134742016,134746112,16,4112,134217744,134221840,524304,528400,
134742032,134746128],y=[0,4,256,260,0,4,256,260,1,5,257,261,1,5,257,261],z=8<a.length?3:1,G=Array(32*z),C=[0,0,1,1,1,1,1,1,0,1,1,1,1,1,1,0],H,I,N=0,K=0,q,t,p,x=0;x<z;x++){t=a.charCodeAt(N++)<<24|a.charCodeAt(N++)<<16|a.charCodeAt(N++)<<8|a.charCodeAt(N++);p=a.charCodeAt(N++)<<24|a.charCodeAt(N++)<<16|a.charCodeAt(N++)<<8|a.charCodeAt(N++);q=(t>>>4^p)&252645135;p^=q;t^=q<<4;q=(p>>>-16^t)&65535;t^=q;p^=q<<-16;q=(t>>>2^p)&858993459;p^=q;t^=q<<2;q=(p>>>-16^t)&65535;t^=q;p^=q<<-16;q=(t>>>1^p)&1431655765;
p^=q;t^=q<<1;q=(p>>>8^t)&16711935;t^=q;p^=q<<8;q=(t>>>1^p)&1431655765;p^=q;t^=q<<1;q=t<<8|p>>>20&240;t=p<<24|p<<8&16711680|p>>>8&65280|p>>>24&240;p=q;for(var Ba=0;Ba<C.length;Ba++)C[Ba]?(t=t<<2|t>>>26,p=p<<2|p>>>26):(t=t<<1|t>>>27,p=p<<1|p>>>27),t&=-15,p&=-15,H=b[t>>>28]|c[t>>>24&15]|d[t>>>20&15]|e[t>>>16&15]|f[t>>>12&15]|g[t>>>8&15]|h[t>>>4&15],I=l[p>>>28]|n[p>>>24&15]|m[p>>>20&15]|v[p>>>16&15]|u[p>>>12&15]|r[p>>>8&15]|y[p>>>4&15],q=(I>>>16^H)&65535,G[K++]=H^q,G[K++]=I^q<<16}return G}
function ia(a){return a.substr(0,a.length-a.charCodeAt(a.length-1))}function ja(a){for(var b="",c="0123456789abcdef".split(""),d=0;d<a.length;d++)b+=c[a.charCodeAt(d)>>4]+c[a.charCodeAt(d)&15];return b}function ka(a){for(var b="",c=0;c<a.length;c++)b+=String.fromCharCode(a[c]);return b}function la(a){for(var b=[],c=0;c<a.length;c++)b.push(a.charCodeAt(c))};function ma(){this.Ba=this.na=0;this.K=[]}ma.prototype.next=function(){this.na=this.na+1&255;this.Ba=this.Ba+this.K[this.na]&255;var a=this.K[this.na];this.K[this.na]=this.K[this.Ba];this.K[this.Ba]=a;return this.K[a+this.K[this.na]&255]};var na,oa,A;function pa(){var a=(new Date).getTime();oa[A++]^=a&255;oa[A++]^=a>>8&255;oa[A++]^=a>>16&255;oa[A++]^=a>>24&255;256<=A&&(A-=256)}if(null==oa){oa=[];A=0;for(var qa;256>A;)qa=Math.floor(window&&(window.crypto||window.msCrypto)?ra()[0]%65536:65536*Math.random()),oa[A++]=qa>>>8,oa[A++]=qa&255;A=0;pa()}
function ra(){var a=a||{type:"Array"};var b=new Uint16Array(16);(window.crypto||window.msCrypto).getRandomValues(b);switch(a.type){case "Array":return[].slice.call(b);case "Uint8Array":return b;default:throw Error(a.type+" is unsupported.");}}
function sa(a){var b;for(b=0;b<a.length;++b){var c=b;if(null==na){pa();na=new ma;var d,e,f=na,g=oa;for(e=0;256>e;++e)f.K[e]=e;for(e=d=0;256>e;++e){d=d+f.K[e]+g[e%g.length]&255;var h=f.K[e];f.K[e]=f.K[d];f.K[d]=h}f.na=0;for(A=f.Ba=0;A<oa.length;++A)oa[A]=0;A=0}h=na.next();a[c]=h}}function ta(){}ta.prototype.Da=sa;var ua=[214,144,233,254,204,225,61,183,22,182,20,194,40,251,44,5,43,103,154,118,42,190,4,195,170,68,19,38,73,134,6,153,156,66,80,244,145,239,152,122,51,84,11,67,237,207,172,98,228,179,28,169,201,8,232,149,128,223,148,250,117,143,63,166,71,7,167,252,243,115,23,186,131,89,60,25,230,133,79,168,104,107,129,178,113,100,218,139,248,235,15,75,112,86,157,53,30,36,14,94,99,88,209,162,37,34,124,59,1,33,120,135,212,0,70,87,159,211,39,82,76,54,2,231,160,196,200,158,234,191,138,210,64,199,56,181,163,247,242,206,
249,97,21,161,224,174,93,164,155,52,26,85,173,147,50,48,245,140,177,227,29,246,226,46,130,102,202,96,192,41,35,171,13,83,78,111,213,219,55,69,222,253,142,47,3,255,106,114,109,108,91,81,141,27,175,146,187,221,188,127,17,217,92,65,31,16,90,216,10,193,49,136,165,205,123,189,45,116,208,18,184,229,180,176,137,105,151,74,12,150,119,126,101,185,241,9,197,110,198,132,24,240,125,236,58,220,77,32,121,238,95,62,215,203,57,72];
function va(a){var b=0,c,d=[0,0,0,0],e=[0,0,0,0];for(c=3;0<=c;c--)d[c]=a&255,a>>>=8;e[0]=ua[d[0]];e[1]=ua[d[1]];e[2]=ua[d[2]];e[3]=ua[d[3]];for(a=0;4>a;a++)b=(b<<8)+e[a];return b}function B(a,b){b&=31;return a<<b|a>>>32-b}function wa(a,b){b=va(a[1]^a[2]^a[3]^b);return a[0]^b^B(b,2)^B(b,10)^B(b,18)^B(b,24)}
function xa(a){var b=[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0],c=[2746333894,1453994832,1736282519,2993693404],d=[462357,472066609,943670861,1415275113,1886879365,2358483617,2830087869,3301692121,3773296373,4228057617,404694573,876298825,1347903077,1819507329,2291111581,2762715833,3234320085,3705924337,4177462797,337322537,808926789,1280531041,1752135293,2223739545,2695343797,3166948049,3638552301,4110090761,269950501,741554753,1213159005,1684763257],e=[0,0,0,0,0,0,0,0,0,0,
0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0];e[0]=a[0]^c[0];e[1]=a[1]^c[1];e[2]=a[2]^c[2];e[3]=a[3]^c[3];for(a=0;32>a;a++)c=va(e[a+1]^e[a+2]^e[a+3]^d[a]),b[a]=e[a]^c^B(c,13)^B(c,23),e[a+4]=b[a];return b}
function ya(a,b,c){var d,e=[0,0,0,0];for(d=0;4>d;d++)e[d]=a[d]^c[d];a=[0,0,0,0];d=[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0];d[0]=e[0];d[1]=e[1];d[2]=e[2];d[3]=e[3];for(e=0;32>e;e++){var f=[0,0,0,0];f[0]=d[e];f[1]=d[e+1];f[2]=d[e+2];f[3]=d[e+3];d[e+4]=wa(f,b[e])}a[0]=d[35];a[1]=d[34];a[2]=d[33];a[3]=d[32];for(d=0;4>d;d++)c[d]=a[d];return{D:c,I:a}}
function za(a){var b=[0,0,0,0];if(0==a.length)return null;if(16>a.length)for(var c=16-a.length,d=0;d<c;d++)a.push(c);for(d=0;4>d;d++)b[d]=(a[4*d]<<24|a[4*d+1]<<16|a[4*d+2]<<8|a[4*d+3])>>>0;return b}function Aa(a){if(0==a.length)return null;for(var b=[],c=0;c<a.length;c++)b.push(a[c]>>>24&255),b.push(a[c]>>>16&255),b.push(a[c]>>>8&255),b.push(a[c]&255);return b};function Ca(a,b,c,d,e){var f,g=[0,0,0,0];for(f=0;4>f;f++)g[f]=a[f]^c[f];a=[0,0,0,0];f=[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0];d?(d=Da(e,!1),f[0]=g[0]^d[0],f[1]=g[1]^d[1],f[2]=g[2]^d[2],f[3]=g[3]^d[3]):(f[0]=g[0],f[1]=g[1],f[2]=g[2],f[3]=g[3]);for(g=0;32>g;g++)d=[0,0,0,0],d[0]=f[g],d[1]=f[g+1],d[2]=f[g+2],d[3]=f[g+3],f[g+4]=wa(d,b[g]);a[0]=f[35];a[1]=f[34];a[2]=f[33];a[3]=f[32];for(f=0;4>f;f++)c[f]=a[f];return{D:c,I:a}}
function Da(a,b,c){var d=[0,0,0,0];if(0==a.length)return null;if(16>a.length)for(var e=16-a.length,f=0;f<e;f++)b?a.push(e^c[a.length]):a.push(e);for(f=0;4>f;f++)d[f]=(a[4*f]<<24|a[4*f+1]<<16|a[4*f+2]<<8|a[4*f+3])>>>0;return d};var Ea;function D(a,b){null!=a&&("number"==typeof a?Fa(this,a,b,void 0):null==b&&"string"!=typeof a?Ga(this,a,256):Ga(this,a,b))}function E(){return new D(null)}function Ia(a,b,c,d,e,f){for(;0<=--f;){var g=b*this[a++]+c[d]+e;e=Math.floor(g/67108864);c[d++]=g&67108863}return e}function Ja(a,b,c,d,e,f){var g=b&32767;for(b>>=15;0<=--f;){var h=this[a]&32767,l=this[a++]>>15,n=b*h+l*g;h=g*h+((n&32767)<<15)+c[d]+(e&1073741823);e=(h>>>30)+(n>>>15)+b*l+(e>>>30);c[d++]=h&1073741823}return e}
function Ka(a,b,c,d,e,f){var g=b&16383;for(b>>=14;0<=--f;){var h=this[a]&16383,l=this[a++]>>14,n=b*h+l*g;h=g*h+((n&16383)<<14)+c[d]+e;e=(h>>28)+(n>>14)+b*l;c[d++]=h&268435455}return e}"undefined"==typeof wx?"Microsoft Internet Explorer"==navigator.appName?(D.prototype.L=Ja,Ea=30):"Netscape"!=navigator.appName?(D.prototype.L=Ia,Ea=26):(D.prototype.L=Ka,Ea=28):(D.prototype.L=Ka,Ea=28);k=D.prototype;k.c=Ea;k.F=(1<<Ea)-1;k.G=1<<Ea;k.Hb=Math.pow(2,52);k.hb=52-Ea;k.ib=2*Ea-52;var La=[],Ma,F;Ma=48;
for(F=0;9>=F;++F)La[Ma++]=F;Ma=97;for(F=10;36>F;++F)La[Ma++]=F;Ma=65;for(F=10;36>F;++F)La[Ma++]=F;function Na(a){return"0123456789abcdefghijklmnopqrstuvwxyz".charAt(a)}function Oa(a,b){a=La[a.charCodeAt(b)];return null==a?-1:a}function Pa(a){var b=E();Qa(b,a);return b}function Ra(a){var b=1,c;0!=(c=a>>>16)&&(a=c,b+=16);0!=(c=a>>8)&&(a=c,b+=8);0!=(c=a>>4)&&(a=c,b+=4);0!=(c=a>>2)&&(a=c,b+=2);0!=a>>1&&(b+=1);return b}function Sa(a){this.i=a}k=Sa.prototype;
k.Aa=function(a){return 0>a.b||0<=J(a,this.i)?L(a,this.i):a};k.Fa=function(a){return a};k.reduce=function(a){Ta(a,this.i,null,a)};k.ua=function(a,b,c){Ua(a,b,c);this.reduce(c)};k.$=function(a,b){Va(a,b);this.reduce(b)};
function Wa(a){this.i=a;if(1>a.t)var b=0;else if(b=a[0],0==(b&1))b=0;else{var c=b&3;c=c*(2-(b&15)*c)&15;c=c*(2-(b&255)*c)&255;c=c*(2-((b&65535)*c&65535))&65535;c=c*(2-b*c%a.G)%a.G;b=0<c?a.G-c:-c}this.wb=b;this.xb=this.wb&32767;this.Rb=this.wb>>15;this.Xb=(1<<a.c-15)-1;this.Sb=2*a.t}k=Wa.prototype;k.Aa=function(a){var b=E();Xa(a.abs(),this.i.t,b);Ta(b,this.i,null,b);0>a.b&&0<J(b,M)&&O(this.i,b,b);return b};k.Fa=function(a){var b=E();a.copyTo(b);this.reduce(b);return b};
k.reduce=function(a){for(;a.t<=this.Sb;)a[a.t++]=0;for(var b=0;b<this.i.t;++b){var c=a[b]&32767,d=c*this.xb+((c*this.Rb+(a[b]>>15)*this.xb&this.Xb)<<15)&a.F;c=b+this.i.t;for(a[c]+=this.i.L(0,d,a,b,0,this.i.t);a[c]>=a.G;)a[c]-=a.G,a[++c]++}P(a);Za(a,this.i.t,a);0<=J(a,this.i)&&O(a,this.i,a)};k.ua=function(a,b,c){Ua(a,b,c);this.reduce(c)};k.$=function(a,b){Va(a,b);this.reduce(b)};k=D.prototype;k.copyTo=function(a){for(var b=this.t-1;0<=b;--b)a[b]=this[b];a.t=this.t;a.b=this.b};
function Qa(a,b){a.t=1;a.b=0>b?-1:0;0<b?a[0]=b:-1>b?a[0]=b+a.G:a.t=0}
function Ga(a,b,c){if(16==c)c=4;else if(8==c)c=3;else if(256==c)c=8;else if(2==c)c=1;else if(32==c)c=5;else if(4==c)c=2;else{Qa(a,0);null==c&&(c=10);for(var d=Math.floor(Math.LN2*a.c/Math.log(c)),e=Math.pow(c,d),f=!1,g=0,h=0,l=0;l<b.length;++l){var n=Oa(b,l);0>n?"-"==b.charAt(l)&&0==$a(a)&&(f=!0):(h=c*h+n,++g>=d&&(ab(a,e),bb(a,h,0),h=g=0))}0<g&&(ab(a,Math.pow(c,g)),bb(a,h,0));f&&O(M,a,a);return}a.t=0;a.b=0;d=b.length;e=!1;for(f=0;0<=--d;)g=8==c?b[d]&255:Oa(b,d),0>g?"-"==b.charAt(d)&&(e=!0):(e=!1,
0==f?a[a.t++]=g:f+c>a.c?(a[a.t-1]|=(g&(1<<a.c-f)-1)<<f,a[a.t++]=g>>a.c-f):a[a.t-1]|=g<<f,f+=c,f>=a.c&&(f-=a.c));8==c&&0!=(b[0]&128)&&(a.b=-1,0<f&&(a[a.t-1]|=(1<<a.c-f)-1<<f));P(a);e&&O(M,a,a)}function P(a){for(var b=a.b&a.F;0<a.t&&a[a.t-1]==b;)--a.t}function Xa(a,b,c){var d;for(d=a.t-1;0<=d;--d)c[d+b]=a[d];for(d=b-1;0<=d;--d)c[d]=0;c.t=a.t+b;c.b=a.b}function Za(a,b,c){for(var d=b;d<a.t;++d)c[d-b]=a[d];c.t=Math.max(a.t-b,0);c.b=a.b}
function cb(a,b,c){var d=b%a.c,e=a.c-d,f=(1<<e)-1;b=Math.floor(b/a.c);var g=a.b<<d&a.F,h;for(h=a.t-1;0<=h;--h)c[h+b+1]=a[h]>>e|g,g=(a[h]&f)<<d;for(h=b-1;0<=h;--h)c[h]=0;c[b]=g;c.t=a.t+b+1;c.b=a.b;P(c)}function db(a,b,c){c.b=a.b;var d=Math.floor(b/a.c);if(d>=a.t)c.t=0;else{b%=a.c;var e=a.c-b,f=(1<<b)-1;c[0]=a[d]>>b;for(var g=d+1;g<a.t;++g)c[g-d-1]|=(a[g]&f)<<e,c[g-d]=a[g]>>b;0<b&&(c[a.t-d-1]|=(a.b&f)<<e);c.t=a.t-d;P(c)}}
function O(a,b,c){for(var d=0,e=0,f=Math.min(b.t,a.t);d<f;)e+=a[d]-b[d],c[d++]=e&a.F,e>>=a.c;if(b.t<a.t){for(e-=b.b;d<a.t;)e+=a[d],c[d++]=e&a.F,e>>=a.c;e+=a.b}else{for(e+=a.b;d<b.t;)e-=b[d],c[d++]=e&a.F,e>>=a.c;e-=b.b}c.b=0>e?-1:0;-1>e?c[d++]=a.G+e:0<e&&(c[d++]=e);c.t=d;P(c)}function Ua(a,b,c){var d=a.abs(),e=b.abs(),f=d.t;for(c.t=f+e.t;0<=--f;)c[f]=0;for(f=0;f<e.t;++f)c[f+d.t]=d.L(0,e[f],c,f,0,d.t);c.b=0;P(c);a.b!=b.b&&O(M,c,c)}
function Va(a,b){a=a.abs();for(var c=b.t=2*a.t;0<=--c;)b[c]=0;for(c=0;c<a.t-1;++c){var d=a.L(c,a[c],b,2*c,0,1);(b[c+a.t]+=a.L(c+1,2*a[c],b,2*c+1,d,a.t-c-1))>=a.G&&(b[c+a.t]-=a.G,b[c+a.t+1]=1)}0<b.t&&(b[b.t-1]+=a.L(c,a[c],b,2*c,0,1));b.b=0;P(b)}
function Ta(a,b,c,d){var e=b.abs();if(!(0>=e.t)){var f=a.abs();if(f.t<e.t)null!=c&&Qa(c,0),null!=d&&a.copyTo(d);else{null==d&&(d=E());var g=E(),h=a.b;b=b.b;var l=a.c-Ra(e[e.t-1]);0<l?(cb(e,l,g),cb(f,l,d)):(e.copyTo(g),f.copyTo(d));e=g.t;f=g[e-1];if(0!=f){var n=f*(1<<a.hb)+(1<e?g[e-2]>>a.ib:0),m=a.Hb/n;n=(1<<a.hb)/n;var v=1<<a.ib,u=d.t,r=u-e,y=null==c?E():c;Xa(g,r,y);0<=J(d,y)&&(d[d.t++]=1,O(d,y,d));Xa(Q,e,y);for(O(y,g,g);g.t<e;)g[g.t++]=0;for(;0<=--r;){var z=d[--u]==f?a.F:Math.floor(d[u]*m+(d[u-1]+
v)*n);if((d[u]+=g.L(0,z,d,r,0,e))<z)for(Xa(g,r,y),O(d,y,d);d[u]<--z;)O(d,y,d)}null!=c&&(Za(d,e,c),h!=b&&O(M,c,c));d.t=e;P(d);0<l&&db(d,l,d);0>h&&O(M,d,d)}}}}function R(a){return 0==(0<a.t?a[0]&1:a.b)}k.exp=function(a,b){if(4294967295<a||1>a)return Q;var c=E(),d=E(),e=b.Aa(this),f=Ra(a)-1;for(e.copyTo(c);0<=--f;)if(b.$(c,d),0<(a&1<<f))b.ua(d,e,c);else{var g=c;c=d;d=g}return b.Fa(c)};
k.toString=function(a){if(0>this.b)return"-"+this.pa().toString(a);if(16==a)a=4;else if(8==a)a=3;else if(2==a)a=1;else if(32==a)a=5;else if(4==a)a=2;else{var b=a;null==b&&(b=10);if(0==$a(this)||2>b||36<b)b="0";else{a=Math.pow(b,Math.floor(Math.LN2*this.c/Math.log(b)));var c=Pa(a),d=E(),e=E(),f="";for(Ta(this,c,d,e);0<$a(d);)f=(a+eb(e)).toString(b).substr(1)+f,Ta(d,c,d,e);b=eb(e).toString(b)+f}return b}c=(1<<a)-1;d=!1;e="";f=this.t;var g=this.c-f*this.c%a;if(0<f--)for(g<this.c&&0<(b=this[f]>>g)&&(d=
!0,e=Na(b));0<=f;)g<a?(b=(this[f]&(1<<g)-1)<<a-g,b|=this[--f]>>(g+=this.c-a)):(b=this[f]>>(g-=a)&c,0>=g&&(g+=this.c,--f)),0<b&&(d=!0),d&&(e+=Na(b));return d?e:"0"};k.pa=function(){var a=E();O(M,this,a);return a};k.abs=function(){return 0>this.b?this.pa():this};function J(a,b){var c=a.b-b.b;if(0!=c)return c;var d=a.t;c=d-b.t;if(0!=c)return 0>a.b?-c:c;for(;0<=--d;)if(0!=(c=a[d]-b[d]))return c;return 0}function fb(a){return 0>=a.t?0:a.c*(a.t-1)+Ra(a[a.t-1]^a.b&a.F)}
function L(a,b){var c=E();Ta(a.abs(),b,null,c);0>a.b&&0<J(c,M)&&O(b,c,c);return c}function gb(a,b,c){return a.exp(b,256>b||R(c)?new Sa(c):new Wa(c))}var M=Pa(0),Q=Pa(1);function hb(a,b){return a&b}function ib(a,b){return a|b}function jb(a,b){return a^b}function kb(){}function lb(a){return a}kb.prototype.Aa=lb;kb.prototype.Fa=lb;kb.prototype.ua=function(a,b,c){Ua(a,b,c)};kb.prototype.$=function(a,b){Va(a,b)};
function mb(a){this.qa=E();this.Bb=E();Xa(Q,2*a.t,this.qa);this.Tb=this.qa.qb(a);this.i=a}k=mb.prototype;k.Aa=function(a){if(0>a.b||a.t>2*this.i.t)return L(a,this.i);if(0>J(a,this.i))return a;var b=E();a.copyTo(b);this.reduce(b);return b};k.Fa=function(a){return a};
k.reduce=function(a){Za(a,this.i.t-1,this.qa);a.t>this.i.t+1&&(a.t=this.i.t+1,P(a));var b=this.Tb,c=this.qa,d=this.i.t+1,e=this.Bb;--d;var f=e.t=b.t+c.t-d;for(e.b=0;0<=--f;)e[f]=0;for(f=Math.max(d-b.t,0);f<c.t;++f)e[b.t+f-d]=b.L(d-f,c[f],e,0,0,b.t+f-d);P(e);Za(e,1,e);b=this.i;c=this.Bb;d=this.i.t+1;e=this.qa;f=Math.min(b.t+c.t,d);e.b=0;for(e.t=f;0<f;)e[--f]=0;var g;for(g=e.t-b.t;f<g;++f)e[f+b.t]=b.L(0,c[f],e,f,0,b.t);for(g=Math.min(c.t,d);f<g;++f)b.L(0,c[f],e,f,0,d-f);for(P(e);0>J(a,this.qa);)bb(a,
1,this.i.t+1);for(O(a,this.qa,a);0<=J(a,this.i);)O(a,this.i,a)};k.ua=function(a,b,c){Ua(a,b,c);this.reduce(c)};k.$=function(a,b){Va(a,b);this.reduce(b)};
var S=[2,3,5,7,11,13,17,19,23,29,31,37,41,43,47,53,59,61,67,71,73,79,83,89,97,101,103,107,109,113,127,131,137,139,149,151,157,163,167,173,179,181,191,193,197,199,211,223,227,229,233,239,241,251,257,263,269,271,277,281,283,293,307,311,313,317,331,337,347,349,353,359,367,373,379,383,389,397,401,409,419,421,431,433,439,443,449,457,461,463,467,479,487,491,499,503,509,521,523,541,547,557,563,569,571,577,587,593,599,601,607,613,617,619,631,641,643,647,653,659,661,673,677,683,691,701,709,719,727,733,739,
743,751,757,761,769,773,787,797,809,811,821,823,827,829,839,853,857,859,863,877,881,883,887,907,911,919,929,937,941,947,953,967,971,977,983,991,997],nb=67108864/S[S.length-1];function Fa(a,b,c,d){if("number"==typeof c)if(2>b)Qa(a,1);else for(Fa(a,b,d),ob(a,b-1)||pb(a,Q.shiftLeft(b-1),ib,a),R(a)&&bb(a,1,0);!qb(a,c);)bb(a,2,0),fb(a)>b&&O(a,Q.shiftLeft(b-1),a);else{d=[];var e=b&7;d.length=(b>>3)+1;c.Da(d);d[0]=0<e?d[0]&(1<<e)-1:0;Ga(a,d,256)}}
function pb(a,b,c,d){var e,f=Math.min(b.t,a.t);for(e=0;e<f;++e)d[e]=c(a[e],b[e]);if(b.t<a.t){var g=b.b&a.F;for(e=f;e<a.t;++e)d[e]=c(a[e],g);d.t=a.t}else{g=a.b&a.F;for(e=f;e<b.t;++e)d[e]=c(g,b[e]);d.t=b.t}d.b=c(a.b,b.b);P(d)}
function rb(a,b,c){for(var d=0,e=0,f=Math.min(b.t,a.t);d<f;)e+=a[d]+b[d],c[d++]=e&a.F,e>>=a.c;if(b.t<a.t){for(e+=b.b;d<a.t;)e+=a[d],c[d++]=e&a.F,e>>=a.c;e+=a.b}else{for(e+=a.b;d<b.t;)e+=b[d],c[d++]=e&a.F,e>>=a.c;e+=b.b}c.b=0>e?-1:0;0<e?c[d++]=e:-1>e&&(c[d++]=a.G+e);c.t=d;P(c)}function ab(a,b){a[a.t]=a.L(0,b-1,a,0,0,a.t);++a.t;P(a)}function bb(a,b,c){if(0!=b){for(;a.t<=c;)a[a.t++]=0;for(a[c]+=b;a[c]>=a.G;)a[c]-=a.G,++c>=a.t&&(a[a.t++]=0),++a[c]}}
function sb(a,b){var c=a.A(Q),d;a:{for(d=0;d<c.t;++d)if(0!=c[d]){var e=c[d];if(0==e)e=-1;else{var f=0;0==(e&65535)&&(e>>=16,f+=16);0==(e&255)&&(e>>=8,f+=8);0==(e&15)&&(e>>=4,f+=4);0==(e&3)&&(e>>=2,f+=2);0==(e&1)&&++f;e=f}d=d*c.c+e;break a}d=0>c.b?c.t*c.c:-1}if(0>=d)return!1;f=d;e=E();0>f?cb(c,-f,e):db(c,f,e);b=b+1>>1;b>S.length&&(b=S.length);f=E();for(var g=0;g<b;++g){Qa(f,S[window&&(window.crypto||window.msCrypto)?Math.floor(ra()[0]%S.length):Math.random()*S.length]);var h=tb(f,e,a);if(0!=J(h,Q)&&
0!=J(h,c)){for(var l=1;l++<d&&0!=J(h,c);)if(h=gb(h,2,a),0==J(h,Q))return!1;if(0!=J(h,c))return!1}}return!0}k=D.prototype;k.clone=function(){var a=E();this.copyTo(a);return a};function eb(a){if(0>a.b){if(1==a.t)return a[0]-a.G;if(0==a.t)return-1}else{if(1==a.t)return a[0];if(0==a.t)return 0}return(a[1]&(1<<32-a.c)-1)<<a.c|a[0]}function $a(a){return 0>a.b?-1:0>=a.t||1==a.t&&0>=a[0]?0:1}k.B=function(a){return 0==J(this,a)};k.min=function(a){return 0>J(this,a)?this:a};
k.max=function(a){return 0<J(this,a)?this:a};k.and=function(a){var b=E();pb(this,a,hb,b);return b};k.or=function(a){var b=E();pb(this,a,ib,b);return b};k.xor=function(a){var b=E();pb(this,a,jb,b);return b};k.shiftLeft=function(a){var b=E();0>a?db(this,-a,b):cb(this,a,b);return b};function ob(a,b){var c=Math.floor(b/a.c);return c>=a.t?0!=a.b:0!=(a[c]&1<<b%a.c)}k.add=function(a){var b=E();rb(this,a,b);return b};k.A=function(a){var b=E();O(this,a,b);return b};
k.multiply=function(a){var b=E();Ua(this,a,b);return b};k.qb=function(a){var b=E();Ta(this,a,b,null);return b};function vb(a,b){var c=E();Ta(a,b,null,c);return c}
function tb(a,b,c){var d=fb(b),e=Pa(1);if(0>=d)return e;var f=18>d?1:48>d?3:144>d?4:768>d?5:6;c=8>d?new Sa(c):R(c)?new mb(c):new Wa(c);var g=[],h=3,l=f-1,n=(1<<f)-1;g[1]=c.Aa(a);if(1<f)for(d=E(),c.$(g[1],d);h<=n;)g[h]=E(),c.ua(d,g[h-2],g[h]),h+=2;var m=b.t-1,v=!0,u=E();for(d=Ra(b[m])-1;0<=m;){if(d>=l)var r=b[m]>>d-l&n;else r=(b[m]&(1<<d+1)-1)<<l-d,0<m&&(r|=b[m-1]>>a.c+d-l);for(h=f;0==(r&1);)r>>=1,--h;0>(d-=h)&&(d+=a.c,--m);if(v)g[r].copyTo(e),v=!1;else{for(;1<h;)c.$(e,u),c.$(u,e),h-=2;0<h?c.$(e,u):
(h=e,e=u,u=h);c.ua(u,g[r],e)}for(;0<=m&&0==(b[m]&1<<d);)c.$(e,u),h=e,e=u,u=h,0>--d&&(d=a.c-1,--m)}return c.Fa(e)}
function wb(a,b){var c=R(b);if(R(a)&&c||0==$a(b))return M;for(var d=b.clone(),e=a.clone(),f=Pa(1),g=Pa(0),h=Pa(0),l=Pa(1);0!=$a(d);){for(;R(d);)db(d,1,d),c?(R(f)&&R(g)||(rb(f,a,f),O(g,b,g)),db(f,1,f)):R(g)||O(g,b,g),db(g,1,g);for(;R(e);)db(e,1,e),c?(R(h)&&R(l)||(rb(h,a,h),O(l,b,l)),db(h,1,h)):R(l)||O(l,b,l),db(l,1,l);0<=J(d,e)?(O(d,e,d),c&&O(f,h,f),O(g,l,g)):(O(e,d,e),c&&O(h,f,h),O(l,g,l))}if(0!=J(e,Q))return M;if(0<=J(l,b))return l.A(b);for(;0>$a(l);)rb(l,b,l);return l}
k.pow=function(a){return this.exp(a,new kb)};function qb(a,b){var c=a.abs();if(1==c.t&&c[0]<=S[S.length-1]){for(a=0;a<S.length;++a)if(c[0]==S[a])return!0;return!1}if(R(c))return!1;for(a=1;a<S.length;){for(var d=S[a],e=a+1;e<S.length&&d<nb;)d*=S[e++];if(0>=d)d=0;else{var f=c.G%d,g=0>c.b?d-1:0;if(0<c.t)if(0==f)g=c[0]%d;else for(var h=c.t-1;0<=h;--h)g=(f*g+c[h])%d;d=g}for(;a<e;)if(0==d%S[a++])return!1}return sb(c,b)}k.aa=function(){var a=E();Va(this,a);return a};function xb(a){var b,c="";for(b=0;b+3<=a.length;b+=3){var d=parseInt(a.substring(b,b+3),16);c+="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/".charAt(d>>6)+"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/".charAt(d&63)}b+1==a.length?(d=parseInt(a.substring(b,b+1),16),c+="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/".charAt(d<<2)):b+2==a.length&&(d=parseInt(a.substring(b,b+2),16),c+="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/".charAt(d>>
2)+"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/".charAt((d&3)<<4));for(;0<(c.length&3);)c+="=";return c}function yb(a){var b="",c,d=0;for(c=0;c<a.length&&"="!=a.charAt(c);++c){var e="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/".indexOf(a.charAt(c));if(!(0>e))if(0==d){b+=Na(e>>2);var f=e&3;d=1}else 1==d?(b+=Na(f<<2|e>>4),f=e&15,d=2):2==d?(b+=Na(f),b+=Na(e>>2),f=e&3,d=3):(b+=Na(f<<2|e>>4),b+=Na(e&15),d=0)}1==d&&(b+=Na(f<<2));return b}
function zb(a){var b,c="";for(b=0;b+3<=a.length;b+=3){var d=(a[b]&255)<<16^(a[b+1]&255)<<8^a[b+2]&255;c+="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/".charAt(d>>>18)+"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/".charAt((d&258048)>>>12)+"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/".charAt((d&4032)>>>6)+"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/".charAt(d&63)}b==a.length-1?(d=a[b]&255,c+="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/".charAt((d&
252)>>>2)+"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/".charAt((d&3)<<4)):b+1==a.length-1&&(d=(a[b]&255)<<8^a[b+1]&255,c+="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/".charAt((d&64512)>>>10)+"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/".charAt((d&1008)>>>4)+"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/".charAt((d&15)<<2));for(;0<(c.length&3);)c+="=";return c}
function Ab(a){var b=[];4==a.length?(b.push((a[0]&63)<<2^(a[1]&48)>>>4),b.push((a[1]&15)<<4^(a[2]&60)>>>2),b.push((a[2]&3)<<6^a[3]&255)):3==a.length?(b.push((a[0]&63)<<2^(a[1]&48)>>>4),b.push((a[1]&15)<<4^(a[2]&60)>>>2)):2==a.length&&b.push((a[0]&63)<<2^(a[1]&48)>>>4);return b}
function Bb(a){var b=[],c;for(c=0;c<a.length;c+=4){var d=[],e="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/".indexOf(a.charAt(c));if(!(0>e||(d.push(e),e="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/".indexOf(a.charAt(c+1)),0>e))){d.push(e);e=a.charAt(c+2);if("="==e){b=b.concat(Ab(d));break}else{e="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/".indexOf(e);if(0>e)continue;d.push(e)}e=a.charAt(c+3);if("="==e){b=b.concat(Ab(d));break}else{e="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/".indexOf(e);
if(0>e)continue;d.push(e)}b=b.concat(Ab(d))}}return b};var Cb;function Db(a,b){var c=(a&65535)+(b&65535);return(a>>16)+(b>>16)+(c>>16)<<16|c&65535};function Eb(a){a=Fb(Gb(a),8*a.length);return Hb(a)}function Gb(a){for(var b=Array(a.length>>2),c=0;c<b.length;c++)b[c]=0;for(c=0;c<8*a.length;c+=8)b[c>>5]|=(a[c/8]&255)<<24-c%32;return b}function Hb(a){for(var b=[],c=0;c<32*a.length;c+=8)b[c/8]=a[c>>5]>>>24-c%32&255;return b}function Ib(a,b){return a>>>b|a<<32-b}
var Jb=[1116352408,1899447441,-1245643825,-373957723,961987163,1508970993,-1841331548,-1424204075,-670586216,310598401,607225278,1426881987,1925078388,-2132889090,-1680079193,-1046744716,-459576895,-272742522,264347078,604807628,770255983,1249150122,1555081692,1996064986,-1740746414,-1473132947,-1341970488,-1084653625,-958395405,-710438585,113926993,338241895,666307205,773529912,1294757372,1396182291,1695183700,1986661051,-2117940946,-1838011259,-1564481375,-1474664885,-1035236496,-949202525,-778901479,
-694614492,-200395387,275423344,430227734,506948616,659060556,883997877,958139571,1322822218,1537002063,1747873779,1955562222,2024104815,-2067236844,-1933114872,-1866530822,-1538233109,-1090935817,-965641998];
function Fb(a,b){var c=[1779033703,-1150833019,1013904242,-1521486534,1359893119,-1694144372,528734635,1541459225],d=Array(64),e,f;a[b>>5]|=128<<24-b%32;a[(b+64>>9<<4)+15]=b;for(e=0;e<a.length;e+=16){b=c[0];var g=c[1];var h=c[2];var l=c[3];var n=c[4];var m=c[5];var v=c[6];var u=c[7];for(f=0;64>f;f++){if(16>f)var r=a[f+e];else{r=d[f-15];var y=d[f-2];r=T(T(T(Ib(y,17)^Ib(y,19)^y>>>10,d[f-7]),Ib(r,7)^Ib(r,18)^r>>>3),d[f-16])}d[f]=r;r=T(T(T(T(u,Ib(n,6)^Ib(n,11)^Ib(n,25)),n&m^~n&v),Jb[f]),d[f]);y=T(Ib(b,
2)^Ib(b,13)^Ib(b,22),b&g^b&h^g&h);u=v;v=m;m=n;n=T(l,r);l=h;h=g;g=b;b=T(r,y)}c[0]=T(b,c[0]);c[1]=T(g,c[1]);c[2]=T(h,c[2]);c[3]=T(l,c[3]);c[4]=T(n,c[4]);c[5]=T(m,c[5]);c[6]=T(v,c[6]);c[7]=T(u,c[7])}return c}function T(a,b){var c=(a&65535)+(b&65535);return(a>>16)+(b>>16)+(c>>16)<<16|c&65535};/*
 <a href="http://kjur.github.io/jsrsasign/license/">MIT License</a>
 asn1hex-1.1.5.js (c) 2012-2014 Kenji Urushima | kjur.github.com/jsrsasign/license
*/
var Kb=new function(){this.rb=function(a,b){if("8"!=a.substring(b+2,b+3))return 1;a=parseInt(a.substring(b+3,b+4));return 0==a?-1:0<a&&10>a?a+1:-2};this.Mb=function(a,b){var c=this.rb(a,b);return 1>c?"":a.substring(b+2,b+2+2*c)};this.Ya=function(a,b){a=this.Mb(a,b);return""==a?-1:eb(8>parseInt(a.substring(0,1))?new D(a,16):new D(a.substring(2),16))};this.Za=function(a,b){a=this.rb(a,b);return 0>a?a:b+2*(a+1)};this.Xa=function(a,b){var c=this.Za(a,b);b=this.Ya(a,b);return a.substring(c,c+2*b)};this.Nb=
function(a,b){var c=this.Za(a,b);a=this.Ya(a,b);return c+2*a};this.tb=function(a,b){var c=[],d=this.Za(a,b);c.push(d);b=this.Ya(a,b);for(var e=d,f=0;;){e=this.Nb(a,e);if(null==e||e-d>=2*b)break;if(200<=f)break;c.push(e);f++}return c};this.sb=function(a,b,c){if(0==c.length)return b;var d=c.shift();b=this.tb(a,b);return this.sb(a,b[d],c)}};
Kb.$b=function(a,b,c,d){b=this.sb(a,b,c);if(void 0===b)throw"can't find nthList object";if(void 0!==d&&a.substr(b,2)!=d)throw"checking tag doesn't match: "+a.substr(b,2)+"!="+d;return this.Xa(a,b)};
Kb.ac=function(a){function b(g,h){return g.length>=h?g:Array(h-g.length+1).join("0")+g}var c=[],d=parseInt(a.substr(0,2),16);c[0]=new String(Math.floor(d/40));c[1]=new String(d%40);var e=a.substr(2);a=[];for(d=0;d<e.length/2;d++)a.push(parseInt(e.substr(2*d,2),16));e=[];var f="";for(d=0;d<a.length;d++)a[d]&128?f+=b((a[d]&127).toString(2),7):(f+=b((a[d]&127).toString(2),7),e.push(new String(parseInt(f,2))),f="");c=c.join(".");0<e.length&&(c=c+"."+e.join("."));return c};String.prototype.repeat||(String.prototype.repeat=function(a){if(null==this)throw new TypeError("can't convert "+this+" to object");var b=""+this;a=+a;a!=a&&(a=0);if(0>a)throw new RangeError("repeat count must be non-negative");if(Infinity==a)throw new RangeError("repeat count must be less than infinity");a=Math.floor(a);if(0==b.length||0==a)return"";if(268435456<=b.length*a)throw new RangeError("repeat count must not overflow maximum string size");var c=b.length*a;for(a=Math.floor(Math.log(a)/Math.log(2));a;)b+=
b,a--;return b+=b.substring(0,c-b.length)});function Lb(){this.n=null;this.e=0;this.Ib=this.Lb=this.Kb=this.q=this.p=this.d=null}
Lb.prototype.encrypt=function(a){var b=fb(this.n)+7>>3;var c=fb(this.n)+7>>3;if(c<a.length+11)throw Error("Message too long for RSA");for(var d=[],e=a.length-1;0<=e&&0<c;){var f=a.charCodeAt(e--);128>f?d[--c]=f:127<f&&2048>f?(d[--c]=f&63|128,d[--c]=f>>6|192):(d[--c]=f&63|128,d[--c]=f>>6&63|128,d[--c]=f>>12|224)}d[--c]=0;a=new ta;for(e=[];2<c;){for(e[0]=0;0==e[0];)a.Da(e);d[--c]=e[0]}d[--c]=2;d[--c]=0;c=new D(d);if(null==c)return null;c=gb(c,this.e,this.n);if(null==c)return null;c=c.toString(16);b=
2*b-c.length;return 0<b?"0".repeat(b)+c:c};function Mb(a,b){var c=fb(a.n)+7>>3,d=c;if(c<b.length+11)throw Error("Message too long for RSA");for(var e=[],f=b.length-1;0<=f&&0<c;)e[--c]=b[f--];e[--c]=0;b=new ta;for(f=[];2<c;){for(f[0]=0;0==f[0];)b.Da(f);e[--c]=f[0]}e[--c]=2;e[--c]=0;c=new D(e);a=gb(c,a.e,a.n);if(null==a)return null;a=a.toString(16);d=2*d-a.length;return 0<d?"0".repeat(d)+a:a}
Lb.prototype.decrypt=function(a){var b=new D(a,16);if(null==this.p||null==this.q)a=tb(b,this.d,this.n);else{a=tb(L(b,this.p),this.Kb,this.p);for(b=tb(L(b,this.q),this.Lb,this.q);0>J(a,b);)a=a.add(this.p);a=L(a.A(b).multiply(this.Ib),this.p).multiply(this.q).add(b)}if(null==a)var c=null;else a:{b=fb(this.n)+7>>3;var d=a,e=d.t;a=[];a[0]=d.b;var f=d.c-e*d.c%8,g=0;if(0<e--)for(f<d.c&&(c=d[e]>>f)!=(d.b&d.F)>>f&&(a[g++]=c|d.b<<d.c-f);0<=e;)if(8>f?(c=(d[e]&(1<<f)-1)<<8-f,c|=d[--e]>>(f+=d.c-8)):(c=d[e]>>
(f-=8)&255,0>=f&&(f+=d.c,--e)),0!=(c&128)&&(c|=-256),0==g&&(d.b&128)!=(c&128)&&++g,0<g||c!=d.b)a[g++]=c;for(c=0;c<a.length&&0==a[c];)++c;if(a.length-c!=b-1||2!=a[c])c=null;else{for(++c;0!=a[c];)if(++c>=a.length){c=null;break a}for(b="";++c<a.length;)d=a[c]&255,128>d?b+=String.fromCharCode(d):191<d&&224>d?(b+=String.fromCharCode((d&31)<<6|a[c+1]&63),++c):(b+=String.fromCharCode((d&15)<<12|(a[c+1]&63)<<6|a[c+2]&63),c+=2);c=b}}return c};function Nb(a,b){this.x=b;this.q=a}k=Nb.prototype;k.B=function(a){return a==this?!0:this.q.B(a.q)&&this.x.B(a.x)};k.pa=function(){return new Nb(this.q,L(this.x.pa(),this.q))};k.add=function(a){return new Nb(this.q,L(this.x.add(a.x),this.q))};k.A=function(a){return new Nb(this.q,L(this.x.A(a.x),this.q))};k.multiply=function(a){return new Nb(this.q,L(this.x.multiply(a.x),this.q))};k.aa=function(){return new Nb(this.q,L(this.x.aa(),this.q))};
k.qb=function(a){return new Nb(this.q,L(this.x.multiply(wb(a.x,this.q)),this.q))};function Ob(a,b,c,d){this.curve=a;this.x=b;this.y=c;this.z=null==d?Q:d;this.ya=null}function Pb(a){null==a.ya&&(a.ya=wb(a.z,a.curve.q));var b=a.x.x.multiply(a.ya);a.curve.reduce(b);return U(a.curve,b)}function Qb(a){null==a.ya&&(a.ya=wb(a.z,a.curve.q));var b=a.y.x.multiply(a.ya);a.curve.reduce(b);return U(a.curve,b)}
Ob.prototype.B=function(a){return a==this?!0:Rb(this)?Rb(a):Rb(a)?Rb(this):L(a.y.x.multiply(this.z).A(this.y.x.multiply(a.z)),this.curve.q).B(M)?L(a.x.x.multiply(this.z).A(this.x.x.multiply(a.z)),this.curve.q).B(M):!1};function Rb(a){return null==a.x&&null==a.y?!0:a.z.B(M)&&!a.y.x.B(M)}Ob.prototype.pa=function(){return new Ob(this.curve,this.x,this.y.pa(),this.z)};
Ob.prototype.add=function(a){if(Rb(this))return a;if(Rb(a))return this;var b=L(a.y.x.multiply(this.z).A(this.y.x.multiply(a.z)),this.curve.q),c=L(a.x.x.multiply(this.z).A(this.x.x.multiply(a.z)),this.curve.q);if(M.B(c))return M.B(b)?Sb(this):this.curve.$a;var d=new D("3"),e=this.x.x,f=this.y.x,g=c.aa(),h=g.multiply(c);e=e.multiply(g);g=b.aa().multiply(this.z);c=L(g.A(e.shiftLeft(1)).multiply(a.z).A(h).multiply(c),this.curve.q);b=L(e.multiply(d).multiply(b).A(f.multiply(h)).A(g.multiply(b)).multiply(a.z).add(b.multiply(h)),
this.curve.q);a=L(h.multiply(this.z).multiply(a.z),this.curve.q);return new Ob(this.curve,U(this.curve,c),U(this.curve,b),a)};
function Sb(a){if(Rb(a))return a;if(0==$a(a.y.x))return a.curve.$a;var b=new D("3"),c=a.x.x,d=a.y.x,e=d.multiply(a.z),f=L(e.multiply(d),a.curve.q);d=a.curve.Ta.x;var g=c.aa().multiply(b);M.B(d)||(g=g.add(a.z.aa().multiply(d)));g=L(g,a.curve.q);d=L(g.aa().A(c.shiftLeft(3).multiply(f)).shiftLeft(1).multiply(e),a.curve.q);b=L(g.multiply(b).multiply(c).A(f.shiftLeft(1)).shiftLeft(2).multiply(f).A(g.aa().multiply(g)),a.curve.q);e=L(e.aa().multiply(e).shiftLeft(3),a.curve.q);return new Ob(a.curve,U(a.curve,
d),U(a.curve,b),e)}Ob.prototype.multiply=function(a){if(Rb(this))return this;if(0==$a(a))return this.curve.$a;var b=a.multiply(new D("3")),c=this.pa(),d=this,e;for(e=fb(b)-2;0<e;--e){d=Sb(d);var f=ob(b,e);f!=ob(a,e)&&(d=d.add(f?this:c))}return d};
function Tb(){var a=new D("FFFFFFFEFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF00000000FFFFFFFFFFFFFFFF",16),b=new D("FFFFFFFEFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF00000000FFFFFFFFFFFFFFFC",16),c=new D("28E9FA9E9D9F5E344D5A9E4BCF6509A7F39789F515AB8F92DDBCBD414D940E93",16);this.q=a;this.Ta=U(this,b);this.nb=U(this,c);this.$a=new Ob(this,null,null);this.Wb=new mb(this.q)}Tb.prototype.B=function(a){return a==this?!0:this.q.B(a.q)&&this.Ta.B(a.Ta)&&this.nb.B(a.nb)};function U(a,b){return new Nb(a.q,b)}
Tb.prototype.reduce=function(a){this.Wb.reduce(a)};function Ub(a,b){var c=(a&65535)+(b&65535);return(((a&4294901760)>>>16)+((b&4294901760)>>>16)+(c>>>16)&65535)<<16^c&65535}
function Vb(a,b,c){for(var d=[],e=0;16>e;e++){for(var f=0,g=0;4>g;g++)f=f<<8^c[(e<<2)+g];d[e]=f}c=[0];e=[0];for(f=0;16>f;f++)c[f]=d[f];for(f=16;68>f;f++)d=c[f-16]^c[f-9]^B(c[f-3],15),c[f]=d^B(d,15)^B(d,23)^B(c[f-13],7)^c[f-6];for(d=0;64>d;d++)e[d]=c[d]^c[d+4];var h;d=[0,0,0,0,0,0,0,0];f=a[0];g=a[1];var l=a[2];var n=a[3];var m=a[4];var v=a[5];var u=a[6];var r=a[7];for(h=0;64>h;h++){var y=B(Ub(Ub(B(f,12),m),B(0<=h&&15>=h?2043430169:16<=h&&63>=h?2055708042:0,h)),7),z=y^B(f,12),G;0<=h&&15>=h?G=f^g^l:
16<=h&&63>=h?G=f&g|f&l|g&l:G={Ea:-1};z=Ub(Ub(Ub(G,n),z),e[h]);0<=h&&15>=h?n=m^v^u:16<=h&&63>=h?n=m&v|~m&u:n={Ea:-1};y=Ub(Ub(Ub(n,r),y),c[h]);n=l;l=B(g,9);g=f;f=z;r=u;u=B(v,19);v=m;m=y^B(y,9)^B(y,17)}d[0]=f^a[0];d[1]=g^a[1];d[2]=l^a[2];d[3]=n^a[3];d[4]=m^a[4];d[5]=v^a[5];d[6]=u^a[6];d[7]=r^a[7];a=[0,0,0,0,0,0,0,0];for(c=0;8>c;c++)a[c]=d[c];return{D:a,Fb:b+64}}
function Wb(){var a=[0,0,0,0,0,0,0,0];a[0]=1937774191;a[1]=1226093241;a[2]=388252375;a[3]=-628488704;a[4]=-1452330820;a[5]=372324522;a[6]=-477237683;a[7]=-1325724082;for(var b=[],c=0;64>c;c++)b[c]=0;return{D:a,U:0,I:b,Z:0}}
function Xb(a,b,c,d,e,f){var g;var h=0;if(64>=d+f){for(g=d;g<d+f;g++)c[g]=e[g-d];return{D:a,U:b,I:c,Z:d+f}}for(g=d;64>g;g++)c[g]=e[g-d];d=64-d;a=Vb(a,b,c);if(0<f-d){h=f-d>>>6;for(b=0;b<h;b++){g=[];for(var l=0;64>l;l++)g.push(e[d+64*b+l]);a=Vb(a.D,a.Fb,g)}b=f-d&63;for(f=0;f<b;f++)c[f]=e[d+64*h+f];h=b}return{D:a.D,U:a.Fb,I:c,Z:h}}
function Yb(a,b,c,d,e){b=Xb(a,b,c,d,e,0);c=b.I;d=b.Z;e=b.U+b.Z;a=[];if(56>d){for(var f=0;64>f;f++)a[f]=0;for(f=0;f<d;f++)a[f]=c[f];a[f]=128;c=e<<3;for(d=7;0<=d;d--)a[56+d]=c&255,c>>>=8}else if(64>=d){for(f=0;128>f;f++)a[f]=0;for(f=0;f<d;f++)a[f]=c[f];a[f]=128;c=e<<3;for(d=7;0<=d;d--)a[120+d]=c&255,c>>>=8}b=Vb(b.D,b.U,a);if(64<a.length){d=[];for(c=0;64>c;c++)d[c]=a[64+c];b=Vb(b.D,b.U,d)}a=[0,0,0,0,0,0,0,0];for(c=0;8>c;c++)a[c]=b.D[c];d=[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
0];for(b=0;8>b;b++)for(e=a[b],c=3;0<=c;c--)d[4*b+c]=e&255,e>>>=8;return d}function Zb(a,b){var c=Wb();b=Xb(c.D,c.U,c.I,c.Z,a,b);return Yb(b.D,b.U,b.I,b.Z,a)};function $b(a){var b=[];if(33==a.length)for(var c=1;33>c;c++)b.push(a[c]);else if(32==a.length)for(c=0;32>c;c++)b.push(a[c]);return b};var ac=null;"undefined"===typeof Buffer?(ac=function(a){if("number"===typeof a){for(var b=[],c=0;c<a;c++)b.push(0);return b}for(c=0;c<a.length;c++)if(0>a[c]||256<=a[c]||"number"!==typeof a[c])throw Error("invalid byte at index "+c+"("+a[c]+")");return a.slice(0)},Array.prototype.Jb=function(a){var b,c;null==b&&(b=0);null==c&&(c=0);if(null==d)var d=this.length;for(;c<d;c++)a[b++]=this[c]}):ac=function(a){return new Buffer(a)};
var bc={16:10,24:12,32:14},cc=[1,2,4,8,16,32,64,128,27,54,108,216,171,77,154,47,94,188,99,198,151,53,106,212,179,125,250,239,197,145],dc=[99,124,119,123,242,107,111,197,48,1,103,43,254,215,171,118,202,130,201,125,250,89,71,240,173,212,162,175,156,164,114,192,183,253,147,38,54,63,247,204,52,165,229,241,113,216,49,21,4,199,35,195,24,150,5,154,7,18,128,226,235,39,178,117,9,131,44,26,27,110,90,160,82,59,214,179,41,227,47,132,83,209,0,237,32,252,177,91,106,203,190,57,74,76,88,207,208,239,170,251,67,77,
51,133,69,249,2,127,80,60,159,168,81,163,64,143,146,157,56,245,188,182,218,33,16,255,243,210,205,12,19,236,95,151,68,23,196,167,126,61,100,93,25,115,96,129,79,220,34,42,144,136,70,238,184,20,222,94,11,219,224,50,58,10,73,6,36,92,194,211,172,98,145,149,228,121,231,200,55,109,141,213,78,169,108,86,244,234,101,122,174,8,186,120,37,46,28,166,180,198,232,221,116,31,75,189,139,138,112,62,181,102,72,3,246,14,97,53,87,185,134,193,29,158,225,248,152,17,105,217,142,148,155,30,135,233,206,85,40,223,140,161,
137,13,191,230,66,104,65,153,45,15,176,84,187,22],ec=[82,9,106,213,48,54,165,56,191,64,163,158,129,243,215,251,124,227,57,130,155,47,255,135,52,142,67,68,196,222,233,203,84,123,148,50,166,194,35,61,238,76,149,11,66,250,195,78,8,46,161,102,40,217,36,178,118,91,162,73,109,139,209,37,114,248,246,100,134,104,152,22,212,164,92,204,93,101,182,146,108,112,72,80,253,237,185,218,94,21,70,87,167,141,157,132,144,216,171,0,140,188,211,10,247,228,88,5,184,179,69,6,208,44,30,143,202,63,15,2,193,175,189,3,1,19,
138,107,58,145,17,65,79,103,220,234,151,242,207,206,240,180,230,115,150,172,116,34,231,173,53,133,226,249,55,232,28,117,223,110,71,241,26,113,29,41,197,137,111,183,98,14,170,24,190,27,252,86,62,75,198,210,121,32,154,219,192,254,120,205,90,244,31,221,168,51,136,7,199,49,177,18,16,89,39,128,236,95,96,81,127,169,25,181,74,13,45,229,122,159,147,201,156,239,160,224,59,77,174,42,245,176,200,235,187,60,131,83,153,97,23,43,4,126,186,119,214,38,225,105,20,99,85,33,12,125],fc=[3328402341,4168907908,4000806809,
4135287693,4294111757,3597364157,3731845041,2445657428,1613770832,33620227,3462883241,1445669757,3892248089,3050821474,1303096294,3967186586,2412431941,528646813,2311702848,4202528135,4026202645,2992200171,2387036105,4226871307,1101901292,3017069671,1604494077,1169141738,597466303,1403299063,3832705686,2613100635,1974974402,3791519004,1033081774,1277568618,1815492186,2118074177,4126668546,2211236943,1748251740,1369810420,3521504564,4193382664,3799085459,2883115123,1647391059,706024767,134480908,2512897874,
1176707941,2646852446,806885416,932615841,168101135,798661301,235341577,605164086,461406363,3756188221,3454790438,1311188841,2142417613,3933566367,302582043,495158174,1479289972,874125870,907746093,3698224818,3025820398,1537253627,2756858614,1983593293,3084310113,2108928974,1378429307,3722699582,1580150641,327451799,2790478837,3117535592,0,3253595436,1075847264,3825007647,2041688520,3059440621,3563743934,2378943302,1740553945,1916352843,2487896798,2555137236,2958579944,2244988746,3151024235,3320835882,
1336584933,3992714006,2252555205,2588757463,1714631509,293963156,2319795663,3925473552,67240454,4269768577,2689618160,2017213508,631218106,1269344483,2723238387,1571005438,2151694528,93294474,1066570413,563977660,1882732616,4059428100,1673313503,2008463041,2950355573,1109467491,537923632,3858759450,4260623118,3218264685,2177748300,403442708,638784309,3287084079,3193921505,899127202,2286175436,773265209,2479146071,1437050866,4236148354,2050833735,3362022572,3126681063,840505643,3866325909,3227541664,
427917720,2655997905,2749160575,1143087718,1412049534,999329963,193497219,2353415882,3354324521,1807268051,672404540,2816401017,3160301282,369822493,2916866934,3688947771,1681011286,1949973070,336202270,2454276571,201721354,1210328172,3093060836,2680341085,3184776046,1135389935,3294782118,965841320,831886756,3554993207,4068047243,3588745010,2345191491,1849112409,3664604599,26054028,2983581028,2622377682,1235855840,3630984372,2891339514,4092916743,3488279077,3395642799,4101667470,1202630377,268961816,
1874508501,4034427016,1243948399,1546530418,941366308,1470539505,1941222599,2546386513,3421038627,2715671932,3899946140,1042226977,2521517021,1639824860,227249030,260737669,3765465232,2084453954,1907733956,3429263018,2420656344,100860677,4160157185,470683154,3261161891,1781871967,2924959737,1773779408,394692241,2579611992,974986535,664706745,3655459128,3958962195,731420851,571543859,3530123707,2849626480,126783113,865375399,765172662,1008606754,361203602,3387549984,2278477385,2857719295,1344809080,
2782912378,59542671,1503764984,160008576,437062935,1707065306,3622233649,2218934982,3496503480,2185314755,697932208,1512910199,504303377,2075177163,2824099068,1841019862,739644986],gc=[2781242211,2230877308,2582542199,2381740923,234877682,3184946027,2984144751,1418839493,1348481072,50462977,2848876391,2102799147,434634494,1656084439,3863849899,2599188086,1167051466,2636087938,1082771913,2281340285,368048890,3954334041,3381544775,201060592,3963727277,1739838676,4250903202,3930435503,3206782108,4149453988,
2531553906,1536934080,3262494647,484572669,2923271059,1783375398,1517041206,1098792767,49674231,1334037708,1550332980,4098991525,886171109,150598129,2481090929,1940642008,1398944049,1059722517,201851908,1385547719,1699095331,1587397571,674240536,2704774806,252314885,3039795866,151914247,908333586,2602270848,1038082786,651029483,1766729511,3447698098,2682942837,454166793,2652734339,1951935532,775166490,758520603,3000790638,4004797018,4217086112,4137964114,1299594043,1639438038,3464344499,2068982057,
1054729187,1901997871,2534638724,4121318227,1757008337,0,750906861,1614815264,535035132,3363418545,3988151131,3201591914,1183697867,3647454910,1265776953,3734260298,3566750796,3903871064,1250283471,1807470800,717615087,3847203498,384695291,3313910595,3617213773,1432761139,2484176261,3481945413,283769337,100925954,2180939647,4037038160,1148730428,3123027871,3813386408,4087501137,4267549603,3229630528,2315620239,2906624658,3156319645,1215313976,82966005,3747855548,3245848246,1974459098,1665278241,807407632,
451280895,251524083,1841287890,1283575245,337120268,891687699,801369324,3787349855,2721421207,3431482436,959321879,1469301956,4065699751,2197585534,1199193405,2898814052,3887750493,724703513,2514908019,2696962144,2551808385,3516813135,2141445340,1715741218,2119445034,2872807568,2198571144,3398190662,700968686,3547052216,1009259540,2041044702,3803995742,487983883,1991105499,1004265696,1449407026,1316239930,504629770,3683797321,168560134,1816667172,3837287516,1570751170,1857934291,4014189740,2797888098,
2822345105,2754712981,936633572,2347923833,852879335,1133234376,1500395319,3084545389,2348912013,1689376213,3533459022,3762923945,3034082412,4205598294,133428468,634383082,2949277029,2398386810,3913789102,403703816,3580869306,2297460856,1867130149,1918643758,607656988,4049053350,3346248884,1368901318,600565992,2090982877,2632479860,557719327,3717614411,3697393085,2249034635,2232388234,2430627952,1115438654,3295786421,2865522278,3633334344,84280067,33027830,303828494,2747425121,1600795957,4188952407,
3496589753,2434238086,1486471617,658119965,3106381470,953803233,334231800,3005978776,857870609,3151128937,1890179545,2298973838,2805175444,3056442267,574365214,2450884487,550103529,1233637070,4289353045,2018519080,2057691103,2399374476,4166623649,2148108681,387583245,3664101311,836232934,3330556482,3100665960,3280093505,2955516313,2002398509,287182607,3413881008,4238890068,3597515707,975967766],hc=[1671808611,2089089148,2006576759,2072901243,4061003762,1807603307,1873927791,3310653893,810573872,16974337,
1739181671,729634347,4263110654,3613570519,2883997099,1989864566,3393556426,2191335298,3376449993,2106063485,4195741690,1508618841,1204391495,4027317232,2917941677,3563566036,2734514082,2951366063,2629772188,2767672228,1922491506,3227229120,3082974647,4246528509,2477669779,644500518,911895606,1061256767,4144166391,3427763148,878471220,2784252325,3845444069,4043897329,1905517169,3631459288,827548209,356461077,67897348,3344078279,593839651,3277757891,405286936,2527147926,84871685,2595565466,118033927,
305538066,2157648768,3795705826,3945188843,661212711,2999812018,1973414517,152769033,2208177539,745822252,439235610,455947803,1857215598,1525593178,2700827552,1391895634,994932283,3596728278,3016654259,695947817,3812548067,795958831,2224493444,1408607827,3513301457,0,3979133421,543178784,4229948412,2982705585,1542305371,1790891114,3410398667,3201918910,961245753,1256100938,1289001036,1491644504,3477767631,3496721360,4012557807,2867154858,4212583931,1137018435,1305975373,861234739,2241073541,1171229253,
4178635257,33948674,2139225727,1357946960,1011120188,2679776671,2833468328,1374921297,2751356323,1086357568,2408187279,2460827538,2646352285,944271416,4110742005,3168756668,3066132406,3665145818,560153121,271589392,4279952895,4077846003,3530407890,3444343245,202643468,322250259,3962553324,1608629855,2543990167,1154254916,389623319,3294073796,2817676711,2122513534,1028094525,1689045092,1575467613,422261273,1939203699,1621147744,2174228865,1339137615,3699352540,577127458,712922154,2427141008,2290289544,
1187679302,3995715566,3100863416,339486740,3732514782,1591917662,186455563,3681988059,3762019296,844522546,978220090,169743370,1239126601,101321734,611076132,1558493276,3260915650,3547250131,2901361580,1655096418,2443721105,2510565781,3828863972,2039214713,3878868455,3359869896,928607799,1840765549,2374762893,3580146133,1322425422,2850048425,1823791212,1459268694,4094161908,3928346602,1706019429,2056189050,2934523822,135794696,3134549946,2022240376,628050469,779246638,472135708,2800834470,3032970164,
3327236038,3894660072,3715932637,1956440180,522272287,1272813131,3185336765,2340818315,2323976074,1888542832,1044544574,3049550261,1722469478,1222152264,50660867,4127324150,236067854,1638122081,895445557,1475980887,3117443513,2257655686,3243809217,489110045,2662934430,3778599393,4162055160,2561878936,288563729,1773916777,3648039385,2391345038,2493985684,2612407707,505560094,2274497927,3911240169,3460925390,1442818645,678973480,3749357023,2358182796,2717407649,2306869641,219617805,3218761151,3862026214,
1120306242,1756942440,1103331905,2578459033,762796589,252780047,2966125488,1425844308,3151392187,372911126],ic=[1667474886,2088535288,2004326894,2071694838,4075949567,1802223062,1869591006,3318043793,808472672,16843522,1734846926,724270422,4278065639,3621216949,2880169549,1987484396,3402253711,2189597983,3385409673,2105378810,4210693615,1499065266,1195886990,4042263547,2913856577,3570689971,2728590687,2947541573,2627518243,2762274643,1920112356,3233831835,3082273397,4261223649,2475929149,640051788,
909531756,1061110142,4160160501,3435941763,875846760,2779116625,3857003729,4059105529,1903268834,3638064043,825316194,353713962,67374088,3351728789,589522246,3284360861,404236336,2526454071,84217610,2593830191,117901582,303183396,2155911963,3806477791,3958056653,656894286,2998062463,1970642922,151591698,2206440989,741110872,437923380,454765878,1852748508,1515908788,2694904667,1381168804,993742198,3604373943,3014905469,690584402,3823320797,791638366,2223281939,1398011302,3520161977,0,3991743681,538992704,
4244381667,2981218425,1532751286,1785380564,3419096717,3200178535,960056178,1246420628,1280103576,1482221744,3486468741,3503319995,4025428677,2863326543,4227536621,1128514950,1296947098,859002214,2240123921,1162203018,4193849577,33687044,2139062782,1347481760,1010582648,2678045221,2829640523,1364325282,2745433693,1077985408,2408548869,2459086143,2644360225,943212656,4126475505,3166494563,3065430391,3671750063,555836226,269496352,4294908645,4092792573,3537006015,3452783745,202118168,320025894,3974901699,
1600119230,2543297077,1145359496,387397934,3301201811,2812801621,2122220284,1027426170,1684319432,1566435258,421079858,1936954854,1616945344,2172753945,1330631070,3705438115,572679748,707427924,2425400123,2290647819,1179044492,4008585671,3099120491,336870440,3739122087,1583276732,185277718,3688593069,3772791771,842159716,976899700,168435220,1229577106,101059084,606366792,1549591736,3267517855,3553849021,2897014595,1650632388,2442242105,2509612081,3840161747,2038008818,3890688725,3368567691,926374254,
1835907034,2374863873,3587531953,1313788572,2846482505,1819063512,1448540844,4109633523,3941213647,1701162954,2054852340,2930698567,134748176,3132806511,2021165296,623210314,774795868,471606328,2795958615,3031746419,3334885783,3907527627,3722280097,1953799400,522133822,1263263126,3183336545,2341176845,2324333839,1886425312,1044267644,3048588401,1718004428,1212733584,50529542,4143317495,235803164,1633788866,892690282,1465383342,3115962473,2256965911,3250673817,488449850,2661202215,3789633753,4177007595,
2560144171,286339874,1768537042,3654906025,2391705863,2492770099,2610673197,505291324,2273808917,3924369609,3469625735,1431699370,673740880,3755965093,2358021891,2711746649,2307489801,218961690,3217021541,3873845719,1111672452,1751693520,1094828930,2576986153,757954394,252645662,2964376443,1414855848,3149649517,370555436],jc=[1374988112,2118214995,437757123,975658646,1001089995,530400753,2902087851,1273168787,540080725,2910219766,2295101073,4110568485,1340463100,3307916247,641025152,3043140495,3736164937,
632953703,1172967064,1576976609,3274667266,2169303058,2370213795,1809054150,59727847,361929877,3211623147,2505202138,3569255213,1484005843,1239443753,2395588676,1975683434,4102977912,2572697195,666464733,3202437046,4035489047,3374361702,2110667444,1675577880,3843699074,2538681184,1649639237,2976151520,3144396420,4269907996,4178062228,1883793496,2403728665,2497604743,1383856311,2876494627,1917518562,3810496343,1716890410,3001755655,800440835,2261089178,3543599269,807962610,599762354,33778362,3977675356,
2328828971,2809771154,4077384432,1315562145,1708848333,101039829,3509871135,3299278474,875451293,2733856160,92987698,2767645557,193195065,1080094634,1584504582,3178106961,1042385657,2531067453,3711829422,1306967366,2438237621,1908694277,67556463,1615861247,429456164,3602770327,2302690252,1742315127,2968011453,126454664,3877198648,2043211483,2709260871,2084704233,4169408201,0,159417987,841739592,504459436,1817866830,4245618683,260388950,1034867998,908933415,168810852,1750902305,2606453969,607530554,
202008497,2472011535,3035535058,463180190,2160117071,1641816226,1517767529,470948374,3801332234,3231722213,1008918595,303765277,235474187,4069246893,766945465,337553864,1475418501,2943682380,4003061179,2743034109,4144047775,1551037884,1147550661,1543208500,2336434550,3408119516,3069049960,3102011747,3610369226,1113818384,328671808,2227573024,2236228733,3535486456,2935566865,3341394285,496906059,3702665459,226906860,2009195472,733156972,2842737049,294930682,1206477858,2835123396,2700099354,1451044056,
573804783,2269728455,3644379585,2362090238,2564033334,2801107407,2776292904,3669462566,1068351396,742039012,1350078989,1784663195,1417561698,4136440770,2430122216,775550814,2193862645,2673705150,1775276924,1876241833,3475313331,3366754619,270040487,3902563182,3678124923,3441850377,1851332852,3969562369,2203032232,3868552805,2868897406,566021896,4011190502,3135740889,1248802510,3936291284,699432150,832877231,708780849,3332740144,899835584,1951317047,4236429990,3767586992,866637845,4043610186,1106041591,
2144161806,395441711,1984812685,1139781709,3433712980,3835036895,2664543715,1282050075,3240894392,1181045119,2640243204,25965917,4203181171,4211818798,3009879386,2463879762,3910161971,1842759443,2597806476,933301370,1509430414,3943906441,3467192302,3076639029,3776767469,2051518780,2631065433,1441952575,404016761,1942435775,1408749034,1610459739,3745345300,2017778566,3400528769,3110650942,941896748,3265478751,371049330,3168937228,675039627,4279080257,967311729,135050206,3635733660,1683407248,2076935265,
3576870512,1215061108,3501741890],kc=[1347548327,1400783205,3273267108,2520393566,3409685355,4045380933,2880240216,2471224067,1428173050,4138563181,2441661558,636813900,4233094615,3620022987,2149987652,2411029155,1239331162,1730525723,2554718734,3781033664,46346101,310463728,2743944855,3328955385,3875770207,2501218972,3955191162,3667219033,768917123,3545789473,692707433,1150208456,1786102409,2029293177,1805211710,3710368113,3065962831,401639597,1724457132,3028143674,409198410,2196052529,1620529459,
1164071807,3769721975,2226875310,486441376,2499348523,1483753576,428819965,2274680428,3075636216,598438867,3799141122,1474502543,711349675,129166120,53458370,2592523643,2782082824,4063242375,2988687269,3120694122,1559041666,730517276,2460449204,4042459122,2706270690,3446004468,3573941694,533804130,2328143614,2637442643,2695033685,839224033,1973745387,957055980,2856345839,106852767,1371368976,4181598602,1033297158,2933734917,1179510461,3046200461,91341917,1862534868,4284502037,605657339,2547432937,
3431546947,2003294622,3182487618,2282195339,954669403,3682191598,1201765386,3917234703,3388507166,0,2198438022,1211247597,2887651696,1315723890,4227665663,1443857720,507358933,657861945,1678381017,560487590,3516619604,975451694,2970356327,261314535,3535072918,2652609425,1333838021,2724322336,1767536459,370938394,182621114,3854606378,1128014560,487725847,185469197,2918353863,3106780840,3356761769,2237133081,1286567175,3152976349,4255350624,2683765030,3160175349,3309594171,878443390,1988838185,3704300486,
1756818940,1673061617,3403100636,272786309,1075025698,545572369,2105887268,4174560061,296679730,1841768865,1260232239,4091327024,3960309330,3497509347,1814803222,2578018489,4195456072,575138148,3299409036,446754879,3629546796,4011996048,3347532110,3252238545,4270639778,915985419,3483825537,681933534,651868046,2755636671,3828103837,223377554,2607439820,1649704518,3270937875,3901806776,1580087799,4118987695,3198115200,2087309459,2842678573,3016697106,1003007129,2802849917,1860738147,2077965243,164439672,
4100872472,32283319,2827177882,1709610350,2125135846,136428751,3874428392,3652904859,3460984630,3572145929,3593056380,2939266226,824852259,818324884,3224740454,930369212,2801566410,2967507152,355706840,1257309336,4148292826,243256656,790073846,2373340630,1296297904,1422699085,3756299780,3818836405,457992840,3099667487,2135319889,77422314,1560382517,1945798516,788204353,1521706781,1385356242,870912086,325965383,2358957921,2050466060,2388260884,2313884476,4006521127,901210569,3990953189,1014646705,
1503449823,1062597235,2031621326,3212035895,3931371469,1533017514,350174575,2256028891,2177544179,1052338372,741876788,1606591296,1914052035,213705253,2334669897,1107234197,1899603969,3725069491,2631447780,2422494913,1635502980,1893020342,1950903388,1120974935],lc=[2807058932,1699970625,2764249623,1586903591,1808481195,1173430173,1487645946,59984867,4199882800,1844882806,1989249228,1277555970,3623636965,3419915562,1149249077,2744104290,1514790577,459744698,244860394,3235995134,1963115311,4027744588,
2544078150,4190530515,1608975247,2627016082,2062270317,1507497298,2200818878,567498868,1764313568,3359936201,2305455554,2037970062,1047239E3,1910319033,1337376481,2904027272,2892417312,984907214,1243112415,830661914,861968209,2135253587,2011214180,2927934315,2686254721,731183368,1750626376,4246310725,1820824798,4172763771,3542330227,48394827,2404901663,2871682645,671593195,3254988725,2073724613,145085239,2280796200,2779915199,1790575107,2187128086,472615631,3029510009,4075877127,3802222185,4107101658,
3201631749,1646252340,4270507174,1402811438,1436590835,3778151818,3950355702,3963161475,4020912224,2667994737,273792366,2331590177,104699613,95345982,3175501286,2377486676,1560637892,3564045318,369057872,4213447064,3919042237,1137477952,2658625497,1119727848,2340947849,1530455833,4007360968,172466556,266959938,516552836,0,2256734592,3980931627,1890328081,1917742170,4294704398,945164165,3575528878,958871085,3647212047,2787207260,1423022939,775562294,1739656202,3876557655,2530391278,2443058075,3310321856,
547512796,1265195639,437656594,3121275539,719700128,3762502690,387781147,218828297,3350065803,2830708150,2848461854,428169201,122466165,3720081049,1627235199,648017665,4122762354,1002783846,2117360635,695634755,3336358691,4234721005,4049844452,3704280881,2232435299,574624663,287343814,612205898,1039717051,840019705,2708326185,793451934,821288114,1391201670,3822090177,376187827,3113855344,1224348052,1679968233,2361698556,1058709744,752375421,2431590963,1321699145,3519142200,2734591178,188127444,2177869557,
3727205754,2384911031,3215212461,2648976442,2450346104,3432737375,1180849278,331544205,3102249176,4150144569,2952102595,2159976285,2474404304,766078933,313773861,2570832044,2108100632,1668212892,3145456443,2013908262,418672217,3070356634,2594734927,1852171925,3867060991,3473416636,3907448597,2614737639,919489135,164948639,2094410160,2997825956,590424639,2486224549,1723872674,3157750862,3399941250,3501252752,3625268135,2555048196,3673637356,1343127501,4130281361,3599595085,2957853679,1297403050,81781910,
3051593425,2283490410,532201772,1367295589,3926170974,895287692,1953757831,1093597963,492483431,3528626907,1446242576,1192455638,1636604631,209336225,344873464,1015671571,669961897,3375740769,3857572124,2973530695,3747192018,1933530610,3464042516,935293895,3454686199,2858115069,1863638845,3683022916,4085369519,3292445032,875313188,1080017571,3279033885,621591778,1233856572,2504130317,24197544,3017672716,3835484340,3247465558,2220981195,3060847922,1551124588,1463996600],mc=[4104605777,1097159550,396673818,
660510266,2875968315,2638606623,4200115116,3808662347,821712160,1986918061,3430322568,38544885,3856137295,718002117,893681702,1654886325,2975484382,3122358053,3926825029,4274053469,796197571,1290801793,1184342925,3556361835,2405426947,2459735317,1836772287,1381620373,3196267988,1948373848,3764988233,3385345166,3263785589,2390325492,1480485785,3111247143,3780097726,2293045232,548169417,3459953789,3746175075,439452389,1362321559,1400849762,1685577905,1806599355,2174754046,137073913,1214797936,1174215055,
3731654548,2079897426,1943217067,1258480242,529487843,1437280870,3945269170,3049390895,3313212038,923313619,679998E3,3215307299,57326082,377642221,3474729866,2041877159,133361907,1776460110,3673476453,96392454,878845905,2801699524,777231668,4082475170,2330014213,4142626212,2213296395,1626319424,1906247262,1846563261,562755902,3708173718,1040559837,3871163981,1418573201,3294430577,114585348,1343618912,2566595609,3186202582,1078185097,3651041127,3896688048,2307622919,425408743,3371096953,2081048481,
1108339068,2216610296,0,2156299017,736970802,292596766,1517440620,251657213,2235061775,2933202493,758720310,265905162,1554391400,1532285339,908999204,174567692,1474760595,4002861748,2610011675,3234156416,3693126241,2001430874,303699484,2478443234,2687165888,585122620,454499602,151849742,2345119218,3064510765,514443284,4044981591,1963412655,2581445614,2137062819,19308535,1928707164,1715193156,4219352155,1126790795,600235211,3992742070,3841024952,836553431,1669664834,2535604243,3323011204,1243905413,
3141400786,4180808110,698445255,2653899549,2989552604,2253581325,3252932727,3004591147,1891211689,2487810577,3915653703,4237083816,4030667424,2100090966,865136418,1229899655,953270745,3399679628,3557504664,4118925222,2061379749,3079546586,2915017791,983426092,2022837584,1607244650,2118541908,2366882550,3635996816,972512814,3283088770,1568718495,3499326569,3576539503,621982671,2895723464,410887952,2623762152,1002142683,645401037,1494807662,2595684844,1335535747,2507040230,4293295786,3167684641,367585007,
3885750714,1865862730,2668221674,2960971305,2763173681,1059270954,2777952454,2724642869,1320957812,2194319100,2429595872,2815956275,77089521,3973773121,3444575871,2448830231,1305906550,4021308739,2857194700,2516901860,3518358430,1787304780,740276417,1699839814,1592394909,2352307457,2272556026,188821243,1729977011,3687994002,274084841,3594982253,3613494426,2701949495,4162096729,322734571,2837966542,1640576439,484830689,1202797690,3537852828,4067639125,349075736,3342319475,4157467219,4255800159,1030690015,
1155237496,2951971274,1757691577,607398968,2738905026,499347990,3794078908,1011452712,227885567,2818666809,213114376,3034881240,1455525988,3414450555,850817237,1817998408,3092726480],nc=[0,235474187,470948374,303765277,941896748,908933415,607530554,708780849,1883793496,2118214995,1817866830,1649639237,1215061108,1181045119,1417561698,1517767529,3767586992,4003061179,4236429990,4069246893,3635733660,3602770327,3299278474,3400528769,2430122216,2664543715,2362090238,2193862645,2835123396,2801107407,
3035535058,3135740889,3678124923,3576870512,3341394285,3374361702,3810496343,3977675356,4279080257,4043610186,2876494627,2776292904,3076639029,3110650942,2472011535,2640243204,2403728665,2169303058,1001089995,899835584,666464733,699432150,59727847,226906860,530400753,294930682,1273168787,1172967064,1475418501,1509430414,1942435775,2110667444,1876241833,1641816226,2910219766,2743034109,2976151520,3211623147,2505202138,2606453969,2302690252,2269728455,3711829422,3543599269,3240894392,3475313331,3843699074,
3943906441,4178062228,4144047775,1306967366,1139781709,1374988112,1610459739,1975683434,2076935265,1775276924,1742315127,1034867998,866637845,566021896,800440835,92987698,193195065,429456164,395441711,1984812685,2017778566,1784663195,1683407248,1315562145,1080094634,1383856311,1551037884,101039829,135050206,437757123,337553864,1042385657,807962610,573804783,742039012,2531067453,2564033334,2328828971,2227573024,2935566865,2700099354,3001755655,3168937228,3868552805,3902563182,4203181171,4102977912,
3736164937,3501741890,3265478751,3433712980,1106041591,1340463100,1576976609,1408749034,2043211483,2009195472,1708848333,1809054150,832877231,1068351396,766945465,599762354,159417987,126454664,361929877,463180190,2709260871,2943682380,3178106961,3009879386,2572697195,2538681184,2236228733,2336434550,3509871135,3745345300,3441850377,3274667266,3910161971,3877198648,4110568485,4211818798,2597806476,2497604743,2261089178,2295101073,2733856160,2902087851,3202437046,2968011453,3936291284,3835036895,4136440770,
4169408201,3535486456,3702665459,3467192302,3231722213,2051518780,1951317047,1716890410,1750902305,1113818384,1282050075,1584504582,1350078989,168810852,67556463,371049330,404016761,841739592,1008918595,775550814,540080725,3969562369,3801332234,4035489047,4269907996,3569255213,3669462566,3366754619,3332740144,2631065433,2463879762,2160117071,2395588676,2767645557,2868897406,3102011747,3069049960,202008497,33778362,270040487,504459436,875451293,975658646,675039627,641025152,2084704233,1917518562,1615861247,
1851332852,1147550661,1248802510,1484005843,1451044056,933301370,967311729,733156972,632953703,260388950,25965917,328671808,496906059,1206477858,1239443753,1543208500,1441952575,2144161806,1908694277,1675577880,1842759443,3610369226,3644379585,3408119516,3307916247,4011190502,3776767469,4077384432,4245618683,2809771154,2842737049,3144396420,3043140495,2673705150,2438237621,2203032232,2370213795],oc=[0,185469197,370938394,487725847,741876788,657861945,975451694,824852259,1483753576,1400783205,1315723890,
1164071807,1950903388,2135319889,1649704518,1767536459,2967507152,3152976349,2801566410,2918353863,2631447780,2547432937,2328143614,2177544179,3901806776,3818836405,4270639778,4118987695,3299409036,3483825537,3535072918,3652904859,2077965243,1893020342,1841768865,1724457132,1474502543,1559041666,1107234197,1257309336,598438867,681933534,901210569,1052338372,261314535,77422314,428819965,310463728,3409685355,3224740454,3710368113,3593056380,3875770207,3960309330,4045380933,4195456072,2471224067,2554718734,
2237133081,2388260884,3212035895,3028143674,2842678573,2724322336,4138563181,4255350624,3769721975,3955191162,3667219033,3516619604,3431546947,3347532110,2933734917,2782082824,3099667487,3016697106,2196052529,2313884476,2499348523,2683765030,1179510461,1296297904,1347548327,1533017514,1786102409,1635502980,2087309459,2003294622,507358933,355706840,136428751,53458370,839224033,957055980,605657339,790073846,2373340630,2256028891,2607439820,2422494913,2706270690,2856345839,3075636216,3160175349,3573941694,
3725069491,3273267108,3356761769,4181598602,4063242375,4011996048,3828103837,1033297158,915985419,730517276,545572369,296679730,446754879,129166120,213705253,1709610350,1860738147,1945798516,2029293177,1239331162,1120974935,1606591296,1422699085,4148292826,4233094615,3781033664,3931371469,3682191598,3497509347,3446004468,3328955385,2939266226,2755636671,3106780840,2988687269,2198438022,2282195339,2501218972,2652609425,1201765386,1286567175,1371368976,1521706781,1805211710,1620529459,2105887268,1988838185,
533804130,350174575,164439672,46346101,870912086,954669403,636813900,788204353,2358957921,2274680428,2592523643,2441661558,2695033685,2880240216,3065962831,3182487618,3572145929,3756299780,3270937875,3388507166,4174560061,4091327024,4006521127,3854606378,1014646705,930369212,711349675,560487590,272786309,457992840,106852767,223377554,1678381017,1862534868,1914052035,2031621326,1211247597,1128014560,1580087799,1428173050,32283319,182621114,401639597,486441376,768917123,651868046,1003007129,818324884,
1503449823,1385356242,1333838021,1150208456,1973745387,2125135846,1673061617,1756818940,2970356327,3120694122,2802849917,2887651696,2637442643,2520393566,2334669897,2149987652,3917234703,3799141122,4284502037,4100872472,3309594171,3460984630,3545789473,3629546796,2050466060,1899603969,1814803222,1730525723,1443857720,1560382517,1075025698,1260232239,575138148,692707433,878443390,1062597235,243256656,91341917,409198410,325965383,3403100636,3252238545,3704300486,3620022987,3874428392,3990953189,4042459122,
4227665663,2460449204,2578018489,2226875310,2411029155,3198115200,3046200461,2827177882,2743944855],pc=[0,218828297,437656594,387781147,875313188,958871085,775562294,590424639,1750626376,1699970625,1917742170,2135253587,1551124588,1367295589,1180849278,1265195639,3501252752,3720081049,3399941250,3350065803,3835484340,3919042237,4270507174,4085369519,3102249176,3051593425,2734591178,2952102595,2361698556,2177869557,2530391278,2614737639,3145456443,3060847922,2708326185,2892417312,2404901663,2187128086,
2504130317,2555048196,3542330227,3727205754,3375740769,3292445032,3876557655,3926170974,4246310725,4027744588,1808481195,1723872674,1910319033,2094410160,1608975247,1391201670,1173430173,1224348052,59984867,244860394,428169201,344873464,935293895,984907214,766078933,547512796,1844882806,1627235199,2011214180,2062270317,1507497298,1423022939,1137477952,1321699145,95345982,145085239,532201772,313773861,830661914,1015671571,731183368,648017665,3175501286,2957853679,2807058932,2858115069,2305455554,2220981195,
2474404304,2658625497,3575528878,3625268135,3473416636,3254988725,3778151818,3963161475,4213447064,4130281361,3599595085,3683022916,3432737375,3247465558,3802222185,4020912224,4172763771,4122762354,3201631749,3017672716,2764249623,2848461854,2331590177,2280796200,2431590963,2648976442,104699613,188127444,472615631,287343814,840019705,1058709744,671593195,621591778,1852171925,1668212892,1953757831,2037970062,1514790577,1463996600,1080017571,1297403050,3673637356,3623636965,3235995134,3454686199,4007360968,
3822090177,4107101658,4190530515,2997825956,3215212461,2830708150,2779915199,2256734592,2340947849,2627016082,2443058075,172466556,122466165,273792366,492483431,1047239E3,861968209,612205898,695634755,1646252340,1863638845,2013908262,1963115311,1446242576,1530455833,1277555970,1093597963,1636604631,1820824798,2073724613,1989249228,1436590835,1487645946,1337376481,1119727848,164948639,81781910,331544205,516552836,1039717051,821288114,669961897,719700128,2973530695,3157750862,2871682645,2787207260,
2232435299,2283490410,2667994737,2450346104,3647212047,3564045318,3279033885,3464042516,3980931627,3762502690,4150144569,4199882800,3070356634,3121275539,2904027272,2686254721,2200818878,2384911031,2570832044,2486224549,3747192018,3528626907,3310321856,3359936201,3950355702,3867060991,4049844452,4234721005,1739656202,1790575107,2108100632,1890328081,1402811438,1586903591,1233856572,1149249077,266959938,48394827,369057872,418672217,1002783846,919489135,567498868,752375421,209336225,24197544,376187827,
459744698,945164165,895287692,574624663,793451934,1679968233,1764313568,2117360635,1933530610,1343127501,1560637892,1243112415,1192455638,3704280881,3519142200,3336358691,3419915562,3907448597,3857572124,4075877127,4294704398,3029510009,3113855344,2927934315,2744104290,2159976285,2377486676,2594734927,2544078150],qc=[0,151849742,303699484,454499602,607398968,758720310,908999204,1059270954,1214797936,1097159550,1517440620,1400849762,1817998408,1699839814,2118541908,2001430874,2429595872,2581445614,
2194319100,2345119218,3034881240,3186202582,2801699524,2951971274,3635996816,3518358430,3399679628,3283088770,4237083816,4118925222,4002861748,3885750714,1002142683,850817237,698445255,548169417,529487843,377642221,227885567,77089521,1943217067,2061379749,1640576439,1757691577,1474760595,1592394909,1174215055,1290801793,2875968315,2724642869,3111247143,2960971305,2405426947,2253581325,2638606623,2487810577,3808662347,3926825029,4044981591,4162096729,3342319475,3459953789,3576539503,3693126241,1986918061,
2137062819,1685577905,1836772287,1381620373,1532285339,1078185097,1229899655,1040559837,923313619,740276417,621982671,439452389,322734571,137073913,19308535,3871163981,4021308739,4104605777,4255800159,3263785589,3414450555,3499326569,3651041127,2933202493,2815956275,3167684641,3049390895,2330014213,2213296395,2566595609,2448830231,1305906550,1155237496,1607244650,1455525988,1776460110,1626319424,2079897426,1928707164,96392454,213114376,396673818,514443284,562755902,679998E3,865136418,983426092,3708173718,
3557504664,3474729866,3323011204,4180808110,4030667424,3945269170,3794078908,2507040230,2623762152,2272556026,2390325492,2975484382,3092726480,2738905026,2857194700,3973773121,3856137295,4274053469,4157467219,3371096953,3252932727,3673476453,3556361835,2763173681,2915017791,3064510765,3215307299,2156299017,2307622919,2459735317,2610011675,2081048481,1963412655,1846563261,1729977011,1480485785,1362321559,1243905413,1126790795,878845905,1030690015,645401037,796197571,274084841,425408743,38544885,188821243,
3613494426,3731654548,3313212038,3430322568,4082475170,4200115116,3780097726,3896688048,2668221674,2516901860,2366882550,2216610296,3141400786,2989552604,2837966542,2687165888,1202797690,1320957812,1437280870,1554391400,1669664834,1787304780,1906247262,2022837584,265905162,114585348,499347990,349075736,736970802,585122620,972512814,821712160,2595684844,2478443234,2293045232,2174754046,3196267988,3079546586,2895723464,2777952454,3537852828,3687994002,3234156416,3385345166,4142626212,4293295786,3841024952,
3992742070,174567692,57326082,410887952,292596766,777231668,660510266,1011452712,893681702,1108339068,1258480242,1343618912,1494807662,1715193156,1865862730,1948373848,2100090966,2701949495,2818666809,3004591147,3122358053,2235061775,2352307457,2535604243,2653899549,3915653703,3764988233,4219352155,4067639125,3444575871,3294430577,3746175075,3594982253,836553431,953270745,600235211,718002117,367585007,484830689,133361907,251657213,2041877159,1891211689,1806599355,1654886325,1568718495,1418573201,
1335535747,1184342925];function rc(a){for(var b=[],c=0;c<a.length;c+=4)b.push(a[c]<<24|a[c+1]<<16|a[c+2]<<8|a[c+3]);return b}
function sc(a){this.key=ac(a);a=bc[this.key.length];if(null===a)throw Error("invalid key size (must be length 16, 24 or 32)");this.ia=[];this.V=[];for(var b=0;b<=a;b++)this.ia.push([0,0,0,0]),this.V.push([0,0,0,0]);var c=4*(a+1),d=this.key.length/4,e=rc(this.key);for(b=0;b<d;b++){var f=b>>2;this.ia[f][b%4]=e[b];this.V[a-f][b%4]=e[b]}f=0;for(var g=d;g<c;){b=e[d-1];e[0]=e[0]^dc[b>>16&255]<<24^dc[b>>8&255]<<16^dc[b&255]<<8^dc[b>>24&255]^cc[f]<<24;f+=1;if(8!=d)b=1;else{for(b=1;b<d/2;b++)e[b]^=e[b-1];
b=e[d/2-1];e[d/2]=e[d/2]^dc[b&255]^dc[b>>8&255]<<8^dc[b>>16&255]<<16^dc[b>>24&255]<<24;b=d/2+1}for(;b<d;b++)e[b]^=e[b-1];b=0;for(var h,l;b<d&&g<c;)h=g>>2,l=g%4,this.ia[h][l]=e[b],this.V[a-h][l]=e[b++],g++}for(h=1;h<a;h++)for(l=0;4>l;l++)b=this.V[h][l],this.V[h][l]=nc[b>>24&255]^oc[b>>16&255]^pc[b>>8&255]^qc[b&255]}
sc.prototype.encrypt=function(a){if(16!=a.length)return Error("plaintext must be a block of size 16");var b=this.ia.length-1,c=[0,0,0,0];a=rc(a);for(var d=0;4>d;d++)a[d]^=this.ia[0][d];for(var e=1;e<b;e++){for(d=0;4>d;d++)c[d]=fc[a[d]>>24&255]^gc[a[(d+1)%4]>>16&255]^hc[a[(d+2)%4]>>8&255]^ic[a[(d+3)%4]&255]^this.ia[e][d];a=c.slice(0)}c=ac(16);for(d=0;4>d;d++)e=this.ia[b][d],c[4*d]=(dc[a[d]>>24&255]^e>>24)&255,c[4*d+1]=(dc[a[(d+1)%4]>>16&255]^e>>16)&255,c[4*d+2]=(dc[a[(d+2)%4]>>8&255]^e>>8)&255,c[4*
d+3]=(dc[a[(d+3)%4]&255]^e)&255;return c};
sc.prototype.decrypt=function(a){if(16!=a.length)return Error("ciphertext must be a block of size 16");var b=this.V.length-1,c=[0,0,0,0];a=rc(a);for(var d=0;4>d;d++)a[d]^=this.V[0][d];for(var e=1;e<b;e++){for(d=0;4>d;d++)c[d]=jc[a[d]>>24&255]^kc[a[(d+3)%4]>>16&255]^lc[a[(d+2)%4]>>8&255]^mc[a[(d+1)%4]&255]^this.V[e][d];a=c.slice(0)}c=ac(16);for(d=0;4>d;d++)e=this.V[b][d],c[4*d]=(ec[a[d]>>24&255]^e>>24)&255,c[4*d+1]=(ec[a[(d+3)%4]>>16&255]^e>>16)&255,c[4*d+2]=(ec[a[(d+2)%4]>>8&255]^e>>8)&255,c[4*d+
3]=(ec[a[(d+1)%4]&255]^e)&255;return c};function tc(a,b){this.description="Cipher Block Chaining";this.name="cbc";if(null===b)b=ac([0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0]);else if(16!=b.length)return Error("initialation vector iv must be of length 16");this.za=ac(b);this.mb=new sc(a)}tc.prototype.encrypt=function(a){if(16!=a.length)return Error("plaintext must be a block of size 16");a=ac(a);for(var b=0;16>b;b++)a[b]^=this.za[b];return this.za=this.mb.encrypt(a)};
tc.prototype.decrypt=function(a){if(16!=a.length)return Error("ciphertext must be a block of size 16");for(var b=this.mb.decrypt(a),c=0;16>c;c++)b[c]^=this.za[c];a.Jb(this.za);return b};function uc(a){var b=[];b.push(a>>8&255);b.push(a&255);return b}function vc(a,b){for(var c=[],d=0;32>d;d++)57363==b?c[d]=a.w[32+d]^a.a[32+d]:61==b&&(c[d]=a.w[48+d]^a.a[48+d]);return c}function wc(a){for(var b=0;b<a.length;b++)a[b]=0}class xc{constructor(a,b,c,d,e,f){this.Y=a;this.w=[].concat(b);this.a=[].concat(c);this.P=d;this.g=e;this.ga=f}}function yc(a,b){if(b instanceof Array){var c=[];128==(b[0]&128)&&c.push(0);c=c.concat(b);zc(a,2,c)}}function V(a,b){b instanceof Array&&zc(a,4,b)}
function Ac(a){var b=new Bc;zc(b,48,a.a);return b.a.concat()}function zc(a,b,c){if(c instanceof Array){var d=c.length,e=127>d?1:Math.ceil(d/255)+1,f=[];if(1==e)f[0]=d&255;else{f[0]=128+e-1;for(var g=1;g<e;g++)f[e-g]=d>>8*(g-1)&255}a.a.push(b&255);a.a=a.a.concat(f);c&&(a.a=a.a.concat(c))}}class Bc{constructor(){this.a=[]}};goog.exportSymbol("CFCA_OK",0);goog.exportSymbol("CFCA_ERROR_INVALID_PARAMETER",4097);goog.exportSymbol("CFCA_ERROR_INVALID_SIP_HANDLE_ID",4098);
goog.exportSymbol("CFCA_ERROR_INPUT_LENGTH_OUT_OF_RANGE",4099);goog.exportSymbol("CFCA_ERROR_INPUT_VALUE_IS_NULL",4100);goog.exportSymbol("CFCA_ERROR_SERVER_RANDOM_INVALID",4101);
goog.exportSymbol("CFCA_ERROR_SERVER_RANDOM_IS_NULL",4102);goog.exportSymbol("CFCA_ERROR_INPUT_VALUE_NOT_MATCH_REGEX",4103);goog.exportSymbol("CFCA_ERROR_RSA_ENCRYPT_FAILED",4104);
goog.exportSymbol("CFCA_ERROR_SM4_ENCRYPT_FAILED",4105);goog.exportSymbol("CFCA_ERROR_DES3_ENCRYPT_FAILED",4112);goog.exportSymbol("CFCA_ERROR_SM2_ENCRYPT_FAILED",4113);
goog.exportSymbol("CFCA_ERROR_MATCH_KEYWORDS",4114);goog.exportSymbol("CFCA_ERROR_AES256_ENCRYPT_FAILED",4115);goog.exportSymbol("HTML5_SIP_VERSION","*******");goog.exportSymbol("KEYBOARD_TYPE_NUMBER",0);goog.exportSymbol("KEYBOARD_TYPE_COMPLETE",1);
goog.exportSymbol("KEYBOARD_TYPE_IDENTITY",3);goog.exportSymbol("KEYBOARD_TYPE_NUMBER_DECIMAL",4);goog.exportSymbol("KEYBOARD_DISORDER_NONE",0);
goog.exportSymbol("KEYBOARD_DISORDER_ONLY_DIGITAL",1);goog.exportSymbol("KEYBOARD_DISORDER_ALL",2);goog.exportSymbol("DEFAULT_MIN_LENGTH",6);
goog.exportSymbol("DEFAULT_MAX_LENGTH",8);goog.exportSymbol("OUTPUT_TYPE_HASH",1);goog.exportSymbol("OUTPUT_TYPE_ORIGINAL",2);
goog.exportSymbol("CIPHER_TYPE_SM2",1);goog.exportSymbol("CIPHER_TYPE_RSA",0);goog.exportSymbol("HASH_TYPE_SHA1",1);
goog.exportSymbol("HASH_TYPE_SM3",0);goog.exportSymbol("ENCODE_TYPE_RAW",0);goog.exportSymbol("ENCODE_TYPE_ASN1",1);
goog.exportSymbol("CFCA_KEYBOARD_INSERT",1);goog.exportSymbol("CFCA_KEYBOARD_DELETE",2);goog.exportSymbol("CFCA_KEYBOARD_CLEAR",3);
goog.exportSymbol("CFCA_IDENTITY_MAX_LENGTH",18);goog.exportSymbol("CFCA_INDENTITY_SPECIAL_CHAR","X");if("undefined"===typeof W)var W={};function X(a){for(var b=0;b<a.length;b++)a[b]=0}
function Cc(a,b,c){var d=W[a].Pa,e=W[a].Oa,f=W[a].Sa,g=W[a].Ga;if(2==c)W[a].Pa=0,W[a].Oa=0,W[a].Sa=0,W[a].Ga=0,W[a].sa=[],W[a].ta=[],W[a].wa=[];else{var h="A".charCodeAt(),l=W[a].Ob?"a".charCodeAt()-27:"a".charCodeAt(),n="0".charCodeAt()-54,m="!".charCodeAt()-65;b=da(b);1<b.length?(g=0==c?g+1:g-1,W[a].Ga=0>g?0:g,d=ca(b)):b[0]>="0".charCodeAt()&&b[0]<="9".charCodeAt()?(d=0==c?d+1:d-1,W[a].Pa=0>d?0:d,d=b[0]-n):b[0]>="a".charCodeAt()&&b[0]<="z".charCodeAt()?(e=0==c?e+1:e-1,W[a].Oa=0>e?0:e,d=b[0]-l):
b[0]>="A".charCodeAt()&&b[0]<="Z".charCodeAt()?(f=0==c?f+1:f-1,W[a].Sa=0>f?0:f,d=b[0]-h):(g=0==c?g+1:g-1,W[a].Ga=0>g?0:g,d=b[0],d=d<"0".charCodeAt()?d-m:d<"A".charCodeAt()?d-m-10:d<"a".charCodeAt()?d-m-10-26:d-m-10-52);Dc(a,W[a].sa,d,c,0,function(v,u){return 1==(65>v&&v-u)});Dc(a,W[a].ta,d,c,1,function(v,u){return-1==(65>v&&v-u)});Dc(a,W[a].wa,d,c,2,function(v,u){return 0==v-u});Ec(a)}}
function Dc(a,b,c,d,e,f){Fc(a,b);if(0==d){if(0==e)var g=2<=b.length?b[b.length-2]+b[b.length-1]-1:null;else 1==e?g=2<=b.length?b[b.length-2]-b[b.length-1]+1:null:g=2<=b.length?b[b.length-2]:null;null!==g&&f(c,g)?b[b.length-1]++:(b.push(c),b.push(1))}else 1==d&&(1<b[b.length-1]?b[b.length-1]--:b.splice(-2,2));Fc(a,b)}function Fc(a,b){for(var c=0;c<b.length;c++){for(var d=0,e=255<b[c]?65535<b[c]?3:2:1,f=0;f<e;f++)d+=((b[c]>>8*(e-1-f)^W[a].Cb[c%4])&255)*Math.pow(256,e-1-f);b[c]=d}}
function Ec(a){Fc(a,W[a].sa);Fc(a,W[a].ta);Fc(a,W[a].wa);(new ta).Da(W[a].Cb);Fc(a,W[a].sa);Fc(a,W[a].ta);Fc(a,W[a].wa)}function Gc(a){return 1==W[a].W&&2==W[a].va}function Hc(a){return 0==W[a].W&&2==W[a].va}
function Ic(a,b){if("undefined"===typeof W[a])return 4098;if(null!=W[a].Na&&b.match(W[a].Na)!=b)return 4103;if(Jc(a)==W[a].maxLength)return 4099;if(Gc(a))Kc(a,W[a].ha,b,16),Lc(a,W[a].ha,16);else if(Hc(a))Kc(a,W[a].da,b,8),Lc(a,W[a].da,8);else{var c="";0<W[a].H.length&&(c=ia(ea(W[a].la,W[a].H,0,W[a].ma,0)));W[a].H=ea(W[a].la,c+b,1,W[a].ma,1)}Cc(a,b,0);return 0}function Kc(a,b,c,d){c=da(c);0<c.length&&W[a].Ra.push(c.length);for(var e=0;e<c.length;e++)W[a].v.push(c[e]^b[Mc(a)%d])}
function Lc(a,b,c){var d=Array(c);X(d);d=[].concat(b);var e=new ta;b.length=c;e.Da(b);for(e=0;e<Mc(a);e++)W[a].v[e]=W[a].v[e]^d[e%c]^b[e%c]}function Jc(a){return W[a].Ra.length}function Mc(a){return W[a].v.length}function Nc(a,b,c){var d=Mc(a);if(0>=d)return 4114;for(var e=0;e<W[a].fa.length;e++)for(var f=W[a].fa[e],g=0;g<=W[a].fa[e].length-d;g++){for(var h=0,l=da(f.slice(g,g+d)),n=0;n<d;n++)h+=l[n]^b[n%c]^W[a].v[n];if(0==h)return 4114}return 0}
function Oc(a){if(1==W[a].va){var b=ia(ea(W[a].la,W[a].H,0,W[a].ma,0));if(b.length<W[a].cb||b.length>W[a].maxLength)return W[a].errorCode=4099,"";for(var c=0;c<W[a].fa.length;c++)if(0<=W[a].fa[c].indexOf(b))return W[a].errorCode=4114,"";var d="";a=-1;for(var e;++a<b.length;)c=b.charCodeAt(a),e=a+1<b.length?b.charCodeAt(a+1):0,55296<=c&&56319>=c&&56320<=e&&57343>=e&&(c=65536+((c&1023)<<10)+(e&1023),a++),127>=c?d+=String.fromCharCode(c):2047>=c?d+=String.fromCharCode(192|c>>>6&31,128|c&63):65535>=c?
d+=String.fromCharCode(224|c>>>12&15,128|c>>>6&63,128|c&63):2097151>=c&&(d+=String.fromCharCode(240|c>>>18&7,128|c>>>12&63,128|c>>>6&63,128|c&63));b=d;d=Array(b.length>>2);for(a=0;a<d.length;a++)d[a]=0;for(a=0;a<8*b.length;a+=8)d[a>>5]|=(b.charCodeAt(a/8)&255)<<24-a%32;b=8*b.length;d[b>>5]|=128<<24-b%32;d[(b+64>>9<<4)+15]=b;b=Array(80);a=1732584193;c=-271733879;e=-1732584194;for(var f=271733878,g=-1009589776,h=0;h<d.length;h+=16){for(var l=a,n=c,m=e,v=f,u=g,r=0;80>r;r++){if(16>r)var y=d[h+r];else y=
b[r-3]^b[r-8]^b[r-14]^b[r-16],y=y<<1|y>>>31;b[r]=y;y=Db(Db(a<<5|a>>>27,20>r?c&e|~c&f:40>r?c^e^f:60>r?c&e|c&f|e&f:c^e^f),Db(Db(g,b[r]),20>r?1518500249:40>r?1859775393:60>r?-1894007588:-899497514));g=f;f=e;e=c<<30|c>>>2;c=a;a=y}a=Db(a,l);c=Db(c,n);e=Db(e,m);f=Db(f,v);g=Db(g,u)}d=[a,c,e,f,g];b="";for(a=0;a<32*d.length;a+=8)b+=String.fromCharCode(d[a>>5]>>>24-a%32&255);d=b;try{Cb}catch(z){Cb=0}b=Cb?"0123456789ABCDEF":"0123456789abcdef";a="";for(e=0;e<d.length;e++)c=d.charCodeAt(e),a+=b.charAt(c>>>4&15)+
b.charAt(c&15);b=xb(a)}else{d=[];c=0;for(b=Mc(a);c<b;c++)1==W[a].W?d.push(W[a].v[c]^W[a].ha[c%16]):d.push(W[a].v[c]^W[a].da[c%8]);a:for(b="",a=0;a<d.length;){c=d[a];e=[];if(0==c>>>7)e.push(c),a+=1;else if(6==c>>>5)e=[],e.push(d[a]),e.push(d[a+1]),a+=2;else if(14==c>>>4)e=[],e.push(d[a]),e.push(d[a+1]),e.push(d[a+2]),a+=3;else if(30==c>>>3)e=[],e.push(d[a]),e.push(d[a+1]),e.push(d[a+2]),e.push(d[a+3]),a+=4;else{b="";break a}c=ca(e);65535>=c?c=String.fromCharCode(c):(c-=65536,c=String.fromCharCode((c>>>
10)+55296,(c&1023)+56320));b+=c}X(d)}return b}
function Pc(a,b,c,d){if(2==W[a].va){if(0<W[a].fa.length&&0!=Nc(a,W[a].ha,16))return W[a].errorCode=4114,"";var e=W[a].v;var f=W[a].ha,g=0;if(0==e.length||16!=b.length||16!=c.length)c=null;else{b=xa(Da(b));var h=Da(c,!1);for(c=[];g<e.length;g+=16){var l=Da(e.slice(g,g+16),!0,f);l=Ca(l,b,h,!0,f);c=c.concat(l.I)}0==(e.length&15)&&(e=Ca([269488144,269488144,269488144,269488144],b,h,!1,f),c=c.concat(e.I));c=Aa(c)}e=zb(c)}else if(e=Oc(a)){e=da(e);f=0;if(0==e.length||16!=b.length||16!=c.length)c=null;else{g=
xa(za(b));b=za(c);for(c=[];f<e.length;f+=16)h=ya(za(e.slice(f,f+16)),g,b),c=c.concat(h.I);0==(e.length&15)&&(e=ya([269488144,269488144,269488144,269488144],g,b),c=c.concat(e.I));c=Aa(c)}e=zb(c)}else return"";if(null===e||""===e)return W[a].errorCode=4105,"";W[a].errorCode=0;return 0==d?e:1==d?c:""}
function Qc(a,b){if("undefined"===typeof W[a])return W[a].errorCode=4098,"";if(null==W[a].J)return W[a].errorCode=4102,"";if(""==W[a].H&&!Gc(a)&&!Hc(a))return W[a].errorCode=4100,"";if(Hc(a)||Gc(a)){if(0>=Mc(a))return W[a].errorCode=4100,"";if(Jc(a)<W[a].cb||Jc(a)>W[a].maxLength)return W[a].errorCode=4099,""}b||(b=0);a:{var c=b;if(0==W[a].W)if(0==c){var d=Array(24);var e=Array(8);for(c=0;16>c;c++)12>c?(d[c]=W[a].J[c],d[c+12]=W[a].R[c]):(e[c-12]=W[a].J[c],e[c-12+4]=W[a].R[c])}else{if(61==c){if(80>
W[a].J.length){W[a].errorCode=4101;d=null;break a}d=Array(32);e=Array(16);for(c=0;32>c;c++)d[c]=W[a].J[c]^W[a].R[c];for(c=32;48>c;c++)e[c-32]=W[a].J[c]^W[a].R[c]}}else if(0==c)for(d=Array(16),e=Array(16),c=0;16>c;c++)8>c?(d[c]=W[a].J[c],d[c+8]=W[a].R[c]):(e[c-8]=W[a].J[c],e[c]=W[a].R[c]);else if(57363==c){if(64>W[a].J.length){W[a].errorCode=4101;d=null;break a}d=Array(16);e=Array(16);for(c=0;16>c;c++)d[c]=W[a].J[c]^W[a].R[c],e[c]=W[a].J[c+16]^W[a].R[c+16]}d=[d,e]}if(!(d&&d instanceof Array))return"";
var f=d[0];d=d[1];if(0==W[a].W)if(61==b){var g=Oc(a);b=da(g);var h=[];if(0!=b.length%16)for(e=16-b.length%16,g=0;g<e;g++)h.push(e);else for(g=0;16>g;g++)h.push(0);h=b.concat(h);X(b);b=[];d=new tc(f,d);for(g=0;g<h.length;g+=16){var l=h.slice(g,g+16);e=d.encrypt(l);c=b.length;for(var n=0;n<e.length;n++)b[c+n]=e[n]}g=zb(b);X(l);X(h);null===g||""===g?(W[a].errorCode=4115,a=""):(W[a].errorCode=0,a=b)}else a:{if(2==W[a].va){if(0<W[a].fa.length&&0!=Nc(a,W[a].da,8)){W[a].errorCode=4114;a="";break a}l=ka(W[a].v);
var m=ka(d);d=W[a].da;b=[16843776,0,65536,16843780,16842756,66564,4,65536,1024,16843776,16843780,1024,16778244,16842756,16777216,4,1028,16778240,16778240,66560,66560,16842752,16842752,16778244,65540,16777220,16777220,65540,0,1028,66564,16777216,65536,16843780,4,16842752,16843776,16777216,16777216,1024,16842756,65536,66560,16777220,1024,4,16778244,66564,16843780,65540,16842752,16778244,16777220,1028,66564,16843776,1028,16778240,16778240,0,65540,66560,0,16842756];e=[-2146402272,-2147450880,32768,1081376,
1048576,32,-2146435040,-2147450848,-2147483616,-2146402272,-2146402304,-2147483648,-2147450880,1048576,32,-2146435040,1081344,1048608,-2147450848,0,-2147483648,32768,1081376,-2146435072,1048608,-2147483616,0,1081344,32800,-2146402304,-2146435072,32800,0,1081376,-2146435040,1048576,-2147450848,-2146435072,-2146402304,32768,-2146435072,-2147450880,32,-2146402272,1081376,32,32768,-2147483648,32800,-2146402304,1048576,-2147483616,1048608,-2147450848,-2147483616,1048608,1081344,0,-2147450880,32800,-2147483648,
-2146435040,-2146402272,1081344];c=[520,134349312,0,134348808,134218240,0,131592,134218240,131080,134217736,134217736,131072,134349320,131080,134348800,520,134217728,8,134349312,512,131584,134348800,134348808,131592,134218248,131584,131072,134218248,8,134349320,512,134217728,134349312,134217728,131080,520,131072,134349312,134218240,0,512,131080,134349320,134218240,134217736,512,0,134348808,134218248,131072,134217728,134349320,8,131592,131584,134217736,134348800,134218248,520,134348800,131592,8,134348808,
131584];n=[8396801,8321,8321,128,8396928,8388737,8388609,8193,0,8396800,8396800,8396929,129,0,8388736,8388609,1,8192,8388608,8396801,128,8388608,8193,8320,8388737,1,8320,8388736,8192,8396928,8396929,129,8388736,8388609,8396800,8396929,129,0,0,8396800,8320,8388736,8388737,1,8396801,8321,8321,128,8396929,129,1,8192,8388609,8193,8396928,8388737,8193,8320,8388608,8396801,128,8388608,8192,8396928];var v=[256,34078976,34078720,1107296512,524288,256,1073741824,34078720,1074266368,524288,33554688,1074266368,
1107296512,1107820544,524544,1073741824,33554432,1074266112,1074266112,0,1073742080,1107820800,1107820800,33554688,1107820544,1073742080,0,1107296256,34078976,33554432,1107296256,524544,524288,1107296512,256,33554432,1073741824,34078720,1107296512,1074266368,33554688,1073741824,1107820544,34078976,1074266368,256,33554432,1107820544,1107820800,524544,1107296256,1107820800,34078720,0,1074266112,1107296256,524544,33554688,1073742080,524288,0,1074266112,34078976,1073742080],u=[536870928,541065216,16384,
541081616,541065216,16,541081616,4194304,536887296,4210704,4194304,536870928,4194320,536887296,536870912,16400,0,4194320,536887312,16384,4210688,536887312,16,541065232,541065232,0,4210704,541081600,16400,4210688,541081600,536870912,536887296,16,541065232,4210688,541081616,4194304,16400,536870928,4194304,536887296,536870912,16400,536870928,541081616,4210688,541065216,4210704,541081600,0,541065232,16,16384,541065216,4210704,16384,4194320,536887312,0,541081600,536870912,4194320,536887312],r=[2097152,
69206018,67110914,0,2048,67110914,2099202,69208064,69208066,2097152,0,67108866,2,67108864,69206018,2050,67110912,2099202,2097154,67110912,67108866,69206016,69208064,2097154,69206016,2048,2050,69208066,2099200,2,67108864,2099200,67108864,2099200,2097152,67110914,67110914,69206018,69206018,2,2097154,67108864,67110912,2097152,69208064,2050,2099202,69208064,2050,67108866,69208066,69206016,2099200,0,2,69208066,0,2099202,69206016,2048,67108866,67110912,2048,2097154],y=[268439616,4096,262144,268701760,268435456,
268439616,64,268435456,262208,268697600,268701760,266240,268701696,266304,4096,64,268697600,268435520,268439552,4160,266240,262208,268697664,268701696,4160,0,0,268697664,268435520,268439552,266304,262144,266304,262144,268701696,4096,64,268697664,4096,266304,268439552,64,268435520,268697600,268697664,268435456,262144,268439616,0,268701760,262208,268435520,268697600,268439552,268439616,0,268701760,266240,266240,4160,4160,262208,268435456,268701696];f=fa(ka(f));var z=0,G,C=l.length,H=0,I=32==f.length?
3:9;var N=3==I?[0,32,2]:[0,32,2,62,30,-2,64,96,2];var K=C;var q=8-C%8;l+=String.fromCharCode(q^d[K++%8],q^d[K++%8],q^d[K++%8],q^d[K++%8],q^d[K++%8],q^d[K++%8],q^d[K++%8],q^d[K++%8]);8==q&&(C+=8);var t=K="";q=m.charCodeAt(z++)<<24|m.charCodeAt(z++)<<16|m.charCodeAt(z++)<<8|m.charCodeAt(z++);var p=m.charCodeAt(z++)<<24|m.charCodeAt(z++)<<16|m.charCodeAt(z++)<<8|m.charCodeAt(z++);for(z=0;z<C;){0>=z&&(h=ka(d),g=h.charCodeAt(0)<<24|h.charCodeAt(1)<<16|h.charCodeAt(2)<<8|h.charCodeAt(3),h=h.charCodeAt(4)<<
24|h.charCodeAt(5)<<16|h.charCodeAt(6)<<8|h.charCodeAt(7));m=l.charCodeAt(z++)<<24|l.charCodeAt(z++)<<16|l.charCodeAt(z++)<<8|l.charCodeAt(z++);var x=l.charCodeAt(z++)<<24|l.charCodeAt(z++)<<16|l.charCodeAt(z++)<<8|l.charCodeAt(z++);m^=q^g;x^=p^h;q=(m>>>4^x)&252645135;x^=q;m^=q<<4;q=(m>>>16^x)&65535;x^=q;m^=q<<16;q=(x>>>2^m)&858993459;m^=q;x^=q<<2;q=(x>>>8^m)&16711935;m^=q;x^=q<<8;q=(m>>>1^x)&1431655765;x^=q;m^=q<<1;m=m<<1|m>>>31;x=x<<1|x>>>31;for(G=0;G<I;G+=3){var Ba=N[G+1];var ub=N[G+2];for(p=N[G];p!=
Ba;p+=ub){var Ha=x^f[p];var ha=(x>>>4|x<<28)^f[p+1];q=m;m=x;x=q^(e[Ha>>>24&63]|n[Ha>>>16&63]|u[Ha>>>8&63]|y[Ha&63]|b[ha>>>24&63]|c[ha>>>16&63]|v[ha>>>8&63]|r[ha&63])}q=m;m=x;x=q}m=m>>>1|m<<31;x=x>>>1|x<<31;q=(m>>>1^x)&1431655765;x^=q;m^=q<<1;q=(x>>>8^m)&16711935;m^=q;x^=q<<8;q=(x>>>2^m)&858993459;m^=q;x^=q<<2;q=(m>>>16^x)&65535;x^=q;m^=q<<16;q=(m>>>4^x)&252645135;x^=q;q=m^=q<<4;p=x;t+=String.fromCharCode(m>>>24,m>>>16&255,m>>>8&255,m&255,x>>>24,x>>>16&255,x>>>8&255,x&255);H+=8;512==H&&(K+=t,t="",
H=0)}g=K+t;la(g);g=xb(ja(g))}else if(g=Oc(a))g=ea(ka(f),g,1,ka(d),1),la(g),g=xb(ja(g));else{a="";break a}null===g||""===g?(W[a].errorCode=4112,a=""):(W[a].errorCode=0,a=g)}else a=57363==b?Pc(a,f,d,1):Pc(a,f,d,0);return a}
function Rc(a,b){if("undefined"===typeof W[a])return W[a].errorCode=4098,"";b||(b=0);var c;0==b?c=16:61==b?c=80:57363==b&&(c=64);c=W[a].R.slice(0,c);if(0==W[a].W){var d=yb("MIGJAoGBAKxoHb+QyMLeN+7LMVtDBYpqXH3PQOIa5JLcxIwa01BuwH9x2yMHTTuPRBOdcxp5h0uC9ry0180dmuIZIbFWdZpNN0Z+VLXMYIGKX2X5Vn+/wqrLH9XzbOjPIhf6rvQVUPNVt7nZmHTL6lJcIjxfCk0nNug1FDAdluWpt8kSy2XjAgMBAAE="),e=Kb.tb(d,0);if(2!=e.length)b=null;else{var f=Kb.Xa(d,e[0]);d=Kb.Xa(d,e[1]);e=new Lb;if(null!=f&&null!=d&&0<f.length&&0<d.length)e.n=new D(f,
16),e.e=parseInt(d,16);else throw Error("Invalid RSA public key");b=0==b?xb(Mb(e,c)):61==b?w(Mb(e,c)):""}X(c);if(null==b)return W[a].errorCode=4104,""}else{e=yb("iXf33eR4QwurB2hYrMsT8/+p2cDrtje8TNIjNOJojXamJ5f9h1YFzQ46lNPvujE5lFyU5Y5zyeGpAIWqRoRqYg==");f=(f=0,0);a:{d=c.length;var g=e.slice(0,64),h=e.slice(64);var l=new D(g,16);var n=new D(h,16);e=new D("FFFFFFFEFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF00000000FFFFFFFFFFFFFFFF",16);var m=new D("FFFFFFFEFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF00000000FFFFFFFFFFFFFFFC",
16);var v=new D("28E9FA9E9D9F5E344D5A9E4BCF6509A7F39789F515AB8F92DDBCBD414D940E93",16);var u=new D("2",16),r=new D("3",16);n=tb(n,u,e);n.toString(16);r=tb(l,r,e);l=vb(m.multiply(l),e);l=vb(r.add(l),e);l=vb(l.add(v),e);l.toString(16);if(0==n.B(l))d={Ea:-2};else{for(;;)if(e=new D("FFFFFFFEFFFFFFFFFFFFFFFFFFFFFFFF7203DF6B21C6052B53BBF40939D54123",16),v=e.A(Q),v=L(new D(fb(e),new ta),v).add(Q),v.toString(16),e=l=new Tb,m=(new Ob(e,U(e,new D("32C4AE2C1F1981195F9904466A39C9948FE30BBFF2660BE1715A4589334C74C7",
16)),U(e,new D("BC3736A2F4F6779C59BDCEE36B692153D0A9877CC62A474002DF32E52139F0A0",16)))).multiply(v),e=w(Pb(m).x.toString(16)),m=w(Qb(m).x.toString(16)),!(32>e.length||32>m.length)){e=$b(e);m=$b(m);e=e.concat(m);l=new Ob(l,U(l,new D(g,16)),U(l,new D(h,16)));if(Rb(l)){d={Ea:-3};break a}l=l.multiply(v);v=w(Pb(l).x.toString(16));l=w(Qb(l).x.toString(16));if(!(32>v.length||32>l.length)){v=$b(v);l=$b(l);m=v.concat(l);n=!0;for(r=0;r<m.length;r++)if(0!=m[r]){n=!1;break}if(!n)break}}h=m;r=m.length;u=d<<3;
g=[];if(!(0<u>>>30)){var y=1;m=u+256-1>>>8;var z,G=[0,0,0,0];n=[];for(z=1;z<=m;z++){var C=Wb();var H=Xb(C.D,C.U,C.I,C.Z,h,r);var I=y;for(C=3;0<=C;C--)G[C]=I&255,I>>>=8;H=Xb(H.D,H.U,H.I,H.Z,G,4);C=Yb(H.D,H.U,H.I,H.Z,null);for(I=0;I<C.length;I++)n.push(C[I]);y++}if(0==(u&255))for(C=0;C<m<<5;C++)g.push(n[C]);else{h=(u&255)>>>3;for(C=0;C<m-1<<5;C++)g.push(n[C]);for(I=0;I<h;I++)g.push(n[C+I])}}if(g.length!=d)d={Ea:-1};else{h=[];for(m=0;m<d;m++)h[m]=c[m]^g[m];d=v.concat(c);d=d.concat(l);g=Zb(d,d.length);
d=e.concat(g);d=d.concat(h);v="";for(l=0;l<e.length;l++)m=e[l]&15,v=v.concat(aa((e[l]&240)>>>4)),v=v.concat(aa(m));d={eb:d,Eb:v,Db:g,xa:h,Ea:0}}}}null==d.eb||void 0==d.eb?b=null:57363==b?b=d:f?(b=[2,32],f=w(d.Eb),b=b.concat(f.slice(0,32),b.concat(f.slice(32,64))),f=[4,32].concat(d.Db),e=128>d.xa.length?w(d.xa.length.toString(16)):w((Math.ceil(d.xa.length/256)+128).toString(16)).concat(w(d.xa.length.toString(16))),d=[4].concat(e,d.xa),e=b.length+f.length+d.length,e=128>e?w(e.toString(16)):w((Math.ceil(e/
256)+128).toString(16)).concat(w(e.toString(16))),b=zb([48].concat(e,b,f,d))):b=zb(d.eb);X(c);if(null==b)return W[a].errorCode=4113,""}W[a].errorCode=0;return b}
function Sc(a,b){if("undefined"===typeof W[a]||"undefined"===typeof W[b])return!1;if(a==b)return!0;if(Gc(a)&&Gc(b)){if(Mc(a)!==Mc(b))return!1;if(0==Jc(a))return W[a].errorCode=4100,!1;if(0==Jc(b))return W[b].errorCode=4100,!1;for(var c=0,d=Mc(a);c<d;c++)if((W[a].v[c]^W[b].ha[c%16])!==(W[b].v[c]^W[a].ha[c%16]))return!1;return!0}if(Hc(a)&&Hc(b)){if(Mc(a)!==Mc(b))return!1;if(0==Jc(a))return W[a].errorCode=4100,!1;if(0==Jc(b))return W[b].errorCode=4100,!1;c=0;for(d=Mc(a);c<d;c++)if((W[a].v[c]^W[b].da[c%
8])!==(W[b].v[c]^W[a].da[c%8]))return!1;return!0}if(Gc(a)||Gc(b)||Hc(a)||Hc(b))return!1;if(""==W[a].H)return W[a].errorCode=4100,!1;if(""==W[b].H)return W[b].errorCode=4100,!1;a=ia(ea(W[a].la,W[a].H,0,W[a].ma,0));b=ia(ea(W[b].la,W[b].H,0,W[b].ma,0));return a==b?!0:!1}function Tc(a,b){Fc(a,b);for(var c=0,d=1;d<b.length;d+=2){var e=b[d];e>c&&(c=e)}Fc(a,b);return c};class Uc{constructor(a){this.C=a;var b=Array(100),c=Array(24),d=Array(8),e=Array(16),f=Array(8),g=Array(4);X(b);X(c);X(d);X(e);X(f);X(g);sa(b);sa(c);sa(d);sa(e);sa(f);sa(g);W[a]={R:b,la:ka(c),ma:ka(d),J:null,Na:null,maxLength:8,cb:6,va:2,W:0,errorCode:0,H:"",ha:e,da:f,v:[],Ra:[],fa:[],Oa:0,Sa:0,Ga:0,Pa:0,sa:[],ta:[],wa:[],Cb:g,Ob:!0}}getEncryptedClientRandom(){return Rc(this.C)}delete(){var a=this.C;if("undefined"===typeof W[a])a=4098;else{var b=-1;if(Gc(a)||Hc(a)){if(0<Jc(a)){var c=W[a].Ra.pop();
1<c?b=-1:(b=W[a].v.length,b=1==W[a].W?(W[a].v[b-1]^W[a].ha[(b-1)%16])-"0".charCodeAt():(W[a].v[b-1]^W[a].da[(b-1)%8])-"0".charCodeAt());for(var d=0;d<c;d++)W[a].v.pop()}}else 0<W[a].H.length&&(c=ia(ea(W[a].la,W[a].H,0,W[a].ma,0)),b=c.slice(c.length-1).charCodeAt()-"0".charCodeAt(),1<c.length?(c=c.substr(0,c.length-1),W[a].H=ea(W[a].la,c,1,W[a].ma,1)):W[a].H="");Cc(a,0<=b&&10>b?"0":b>="A".charCodeAt()-"0".charCodeAt()&&b<="Z".charCodeAt()-"0".charCodeAt()?"A":b>="a".charCodeAt()-"0".charCodeAt()&&
b<="z".charCodeAt()-"0".charCodeAt()?"a":"!",1);a=0}return a}clear(){var a=this.C;"undefined"!==typeof W[a]&&(W[a].H="",W[a].errorCode=0,W[a].v=[],W[a].Ra=[],Cc(a,null,2))}getEncodedCipher(a){var b=this.C,c;var d=W[b].R;var e=W[b].J;if(0==W[b].W){var f=61;var g=Bb("MIGJAoGBAKxoHb+QyMLeN+7LMVtDBYpqXH3PQOIa5JLcxIwa01BuwH9x2yMHTTuPRBOdcxp5h0uC9ry0180dmuIZIbFWdZpNN0Z+VLXMYIGKX2X5Vn+/wqrLH9XzbOjPIhf6rvQVUPNVt7nZmHTL6lJcIjxfCk0nNug1FDAdluWpt8kSy2XjAgMBAAE=")}else f=57363,g=Bb("iXf33eR4QwurB2hYrMsT8/+p2cDrtje8TNIjNOJojXamJ5f9h1YFzQ46lNPvujE5lFyU5Y5zyeGpAIWqRoRqYg==");
if(c=Qc(b,f))if(b=Rc(b,f)){a=new xc(g,d,e,c,b,a);if(57363==f)if(f=uc(57363),d=[4].concat(a.Y),d=Zb(d,d.length),e=Zb(a.a,a.a.length),c=new Bc,g=w(a.g.Eb),yc(c,g.slice(0,32)),yc(c,g.slice(32,64)),V(c,a.g.Db),V(c,a.g.xa),c=Ac(c),g=a.P,(b=vc(a,57363))&&0!=b.length){var h=[].concat(b);64<h.length&&(h=Zb(h,h.length));var l=Array(64);b=Array(64);for(var n=0;64>n;n++)l[n]=h[n]^54,b[n]=h[n]^92;h=Zb(l.concat(g),64+g.length);b=Zb(b.concat(h),96);h=da(a.ga);l=new Bc;yc(l,[3]);V(l,f);V(l,d);V(l,e);V(l,c);V(l,
g);V(l,b);h instanceof Array&&zc(l,12,h);f=Ac(l);f=zb(f)}else f="";else if(f=uc(61),d=Eb(a.Y),e=Eb(a.a),c=a.g,g=a.P,(h=vc(a,61))&&0!=h.length){b=Gb(h);16<b.length&&(b=Fb(b,8*h.length));l=Array(16);h=Array(16);for(n=0;16>n;n++)l[n]=b[n]^909522486,h[n]=b[n]^1549556828;b=Fb(l.concat(Gb(g)),512+8*g.length);b=Hb(Fb(h.concat(b),768));h=da(a.ga);l=new Bc;yc(l,[3]);V(l,f);V(l,d);V(l,e);V(l,c);V(l,g);V(l,b);h instanceof Array&&zc(l,12,h);f=Ac(l);f=zb(f)}else f="";wc(a.Y);wc(a.w);wc(a.a);wc(a.P);wc(a.g);a=
f}else a="";else a="";return a}};class Vc{constructor(a,b,c,d){this.x=Math.floor(a);this.y=Math.floor(b);this.width=Math.floor(c);this.height=Math.floor(d);this.left=this.x;this.top=this.y}};class Wc{constructor(a){this.location=null;this.f=a.f;this.target=a.h.target;this.h=new Xc(a.h);this.j=new Yc(a.j);this.a=!1}}class Xc{constructor(a){this.width=a.width;this.height=a.height;this.o=a.o}}class Yc{constructor(a){this.backgroundColor=[...a.backgroundColor];this.color=[...a.color];this.g=a.fontName;this.a=a.fontSize;this.Ua=a.cornerRadius;this.image=a.image;this.ob=a.borderSize;this.title=a.title;this.shadowColor=a.shadowColor;this.Gb=a.shadowOffset}};function Zc(a,b){let c=null,d=0,e=0;switch(a){case "complete_capslock_normal":c=0===b?"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAE4AAABOCAMAAAC5dNAvAAAABGdBTUEAALGPC/xhBQAAAAFzUkdCAK7OHOkAAAAzUExURUxpcThBXTlCXz1KZjhCXTlCXzhBXW92fP///zhBXTlBXThBXThBXThBXTtDYDhBXThBXT6tqi0AAAAQdFJOUwDwaxChToACAZiyqpHbI8BSewiGAAABGUlEQVRYw+2Y2w6DIAyGRUU8gbz/007mMpFzodnN+l2S/l8KaknsOoL4F8SMahuXGdOmNZ7P2PB8lw3L97YtI5Lvss0Cx/fVoPgsCYLvoWj2OYJGnxdv8gXCDb5gtNoXCVb6orEqXyJU4UtGwL5MAOjLloN8BcUAX1Fpsa+wELcMexPYR4z9AmC/ntgfD/annQot9YNs99f7ljHL3dWBaT3WXQKb1tJN8nNN1F1RphPl75Udqw2P98M3m4P5u1XapY/3M7m1bHBLuKzXHSp0Bvxmzek2q1hlH+Ge002gp0U60pEOFgDrzvEnuToZLsTJPcxksvfIhA3BDMEBl4brJLDmzI52aWAfHrLE4IfcWUJcB0l/bgiC+AUvfdoirg4ySvIAAAAASUVORK5CYII=":
"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAADAAAAAwCAYAAABXAvmHAAAAAXNSR0IArs4c6QAAAaVJREFUaAXtlUtOwzAQhhsWFIkrgBDiImw5RS/CmzXn6IId4nWVXgB1w6ISYsGC8FnKSFawSWInsQMz0sitnRl//2+nnc001AF1QB34sw6UZXlDnk5SIODXpMTZpERAbcNPS4QHfhoiHPAPzL0IfTXmeZ088NvM75B5i/DBy4ubtYgm+AYR57KeZAT+irTD3Pm5D4Y113VKIwKYOvzjb/AiKgsRofCWiDk9nkk7xjkJdgxyXuBlpM/4IvqCTyKib/hRRQB/SdrR6oUVyKaRxq7rdNFU12qd5oPCC8QgIsaCH0SEA/7JuCSbDTX2chKp4MWUKBEUL0g7RnFe4GUEwLzYZm87FrLuHXl6ZVe0+PzubdawQO+vFv3tR1b1llv1Cb4fOOZymfrB5hJwC+1nLsQWh2EybPHBme5a59rXFSpCyFwnENInWY0KSGZ9tbGegJ5ApAP/9gp9YNymMs/8Jxx3NZKaE2rkt39dFEXZtUfU8wDcW39msR+XUTAhxRAfkZtYcurfyP0QhugaNj4k78g12TVeKViSe9Eg2kAdUAfUAXUg1IFv4xx/eJdkv5UAAAAASUVORK5CYII=";
e=d=26;break;case "complete_capslock_ferry":c=0===b?"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAE4AAABOBAMAAAB8hD0uAAAABGdBTUEAALGPC/xhBQAAAAFzUkdCAK7OHOkAAAAnUExURUxpcTlBXmVueTlCXTlBXjlCXzhBXThBXThBXTtHYjhBXThBXThBXQR7SL8AAAAMdFJOUwCXAqJXRn325xbRkPLFRgEAAAEASURBVEjH7dY9CsJAEIZhlSCk8wgST2BhZ2FhmcLGQi8SBHtTWAkiYi1YegAbUVRkD6WJ+dvZmdnBRoT96penWDY/tZrbr9aWZY2urOuolojrq7OMUxLwzSkJmHACMOUE4IezghlnBXPOAhacBSw5FqxwLFjlGFDjGFDnSBBwJAg5AjQ4AjQ5FEQ4FMQ4BEQ5BMQ5AyQ4A6Q4AJIcAGlOAxlOA+uK27HoQra75pm/ZLtn3nmK3yjrmsLOn7PZo3gPT9luXx7MepwMnuJlmGxiXMET6G7Ec+m6/+xC6sKDbUA3IzoP3NcB9SLfHeI4XkRRWt17go91EGxX7sfL7eu9AMFB18cb4eaQAAAAAElFTkSuQmCC":
"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAADAAAAAwCAYAAABXAvmHAAAAAXNSR0IArs4c6QAAAVtJREFUaAXtlUsKwjAURU0nCm5BByLosroR/45dhwM3Ie5CFyCdOCiIqJN6Cw2ktUXzaZLqCzzyfcm5N/20WlTIAXKAHPhZB5IkWSMmjRQI8BWCl2mjRIBahG+WiAr4Zoj4AO+3iC/h/RQhCe+XCEV4LmLm9OsEiiUn0ajdiDAEz3XbFWEY3q6ImuDtiKgZvl4RluDrEYFdF3xni/XcyCfWETz3SU+EY3g9EZ7Aq4nwDF5OBFaHPMPDOiy+2Kw4AOgjxkbFcU/6J8bYWGQpE3DHgra4yKP2AwI6Ik8gdrL2BvWzZNz1UMqUsuXK2w3kZis6eMy6mLpWTKsOB3A3kU0uuwHZPZyuJwFO7cfhdAN0A5oO/O0jdINxsaZ5Ynqk8g9IN1C6geywg0ig2d5r5sun4288RMQI3XLBBn15AgMZOHiA2CEihGw5I2GL6BlAoS3IAXKAHCAH1Bx4AZRjIRU67MjUAAAAAElFTkSuQmCC";
e=d=26;break;case "complete_capslock_highlight":c=0===b?"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAADAAAAAwCAYAAABXAvmHAAAAAXNSR0IArs4c6QAAAERlWElmTU0AKgAAAAgAAYdpAAQAAAABAAAAGgAAAAAAA6ABAAMAAAABAAEAAKACAAQAAAABAAAAMKADAAQAAAABAAAAMAAAAADbN2wMAAABeUlEQVRoBe1WQU5CMRD96kISr4ALwjE4DCdRUdeegwWXINzCCxg2LEwMUTfwJjKxPynQTmd++0Mnafo7TN+8N9OUNk21WoHLrsCNsfxX4E8wVsZ5TOBfgLo7jAeTDIagLvneifCR742IU+SLFxFCvlgRMeSLEyEhzyIeDS+SIOhnRDEZ6ZxNhAZ5Ft25CE3ynYuwIN+ZCEvy5iK6IG8mYqZw2zC50Pkp6B4MCMpBnkUmi8hJPllECeTFIkoiHy1i2qQ/Dzip9kzczto7IrQTa+ERt5ZdtVZ/i29Mtx5/Ca4fkBi4RK7dxeH7DfOvx5/bRZyIW8t8HWgFHFncwf915Depm4pJRy3KfB2IAsgdXAXUDiRWoB6hxAImb7/YDmxRus/k8v0DrPEZ/R9A26UdoGQrAlCypRJOFMwY0dSF1IfaBhj3UZkVg0fAWmDwEYgR84F9c4whRrXeVkD6GmXBopuDNzuzmIf0FnJy5/3svYC85avZawXyV2AP16zmJ0yCiHgAAAAASUVORK5CYII=":
"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAADAAAAAwCAYAAABXAvmHAAAAAXNSR0IArs4c6QAAAERlWElmTU0AKgAAAAgAAYdpAAQAAAABAAAAGgAAAAAAA6ABAAMAAAABAAEAAKACAAQAAAABAAAAMKADAAQAAAABAAAAMAAAAADbN2wMAAABf0lEQVRoBe2WS27CMBCGCRuQuEJZIKT2WFykLa91z9EFl6h6i3KAik0XkSoEbMIfKSM5aSIYj19Rx9LIzxl//9g4DAZaNAOaAW8ZKIpiC3v2toHPwADfwKi8+NzLeWxQm/D9EtEB3w8RN+DTFnEnfJoimPBpibCEJxGvzl8QTkBQrIlEUMcR4QiedIcV4Rg+rAhP8GFEeIb3KyIQvB8RiLqiyAHrJedF7FwbCZ7yJBMRGV4mIhF4OxGJwfNEYPWCPBKsF80fa9YcAPQXxh6b44n091mWPZksbQJOWDAyFyXUPkPA2OQZmp2q/Yb60jIee6hkKtlq5c8J1GY7OrhmE0z9dkzbDg+R3YLr3HYC3BhR16uAqOnH5noCegLCDPzbK3RE4nJh8kz3g803oAxgdQLVZp8mgbD9IfTnu+NrPIflMGn5QYApn8CBBzaewXawA4xbvuHwDntwgKIhomXA6t8o0ZZ3htqSGo+CNYfVKySBde3bewGuE6LxNAN9y8AVr2IlGoMRMrEAAAAASUVORK5CYII=";
e=d=26;break;case "complete_delete_normal":case "digit_delete_normal":c=0===b?"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAE4AAABOCAMAAAC5dNAvAAAABGdBTUEAALGPC/xhBQAAAAFzUkdCAK7OHOkAAAA2UExURUxpcTlCXThBXTlCXjhBXUlQbjhBXThBXThBXUBDYzpDXzhBXTtEYEJOajhBXTlBXThBXThBXXApSS8AAAARdFJOUwBW6kSBBrb51xc2mSQMxmqiGW3IbQAAAdBJREFUWMPtmNuSgyAMQEVFEeuF///ZDWKrliRcloedHfLo1KPCSUjaNDVq1PgPsZmUGNXccTRhkmN9kbRuTMeZVhM03RqjloSlWfRsefj7vYDWTomL3cFNK/ooxbw4w4P3Q/ZDrrBTXYYM8L2zf3WAp4gctzQsOPYMs2WpusBXfV/rgdZnqg+3IvruTSmc1XeQpXB6TNSXxWXpS+ImReQJKmEnedxi9cWSoce2Woz+Gj9wlL4CU0eMSArccaS+cvB5lqYmBsfo6/Nw2g23cfp+8wjahQvo++RRtA/O6rty+t55JO2Ns/oqXt+LR9NOHKkvxmNoDkfqi/E42oGzP4uq5QfPcMticXt09XU8ZpEtLqH8Hq1BH6go0YeDcK1BHyhQdvFEHE0NHM/h4rbW7ankeKfGMeK9DeF47yQLp8XlG8P7lIBQ0t7tpXlXgeJLyjMXSN6tfNqCN0fRaN69uNM6+3lK8B5HD5VtHZL1OO+Bo2oBaOTvOvzY7yifxzal8zQgDsldh5qKqDqa0PJEVPmkhix4BmW0i0M5HKtzBu4XzTHWajudyw0C8Udb3JhyDFFjsSEKdG4zdCZHvFPnYgNo6fG49PBe+q+FGjVq/NH4AZZhNbZSio+8AAAAAElFTkSuQmCC":
"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAADAAAAAwCAYAAABXAvmHAAAAAXNSR0IArs4c6QAAAopJREFUaAXtmb9O3EAQxu+QQkEFDQJRpkSiQgLCM1DQ8QQUScq8Ai00V1LzEEg0tNR5gTQIEH+TNjp+Ax5rvLc269vVnU/sSKNd7858883s2PhMr5clVyBXIFcgVyCiAv0I395wOBT/ffQA3UZX0C9oKvkH0B/0HD3t9/u/UwEL+a/oFTop+U+gATofnQQgW+g9Og25IOj4SeAs5J8Nc6nMMfoNXYiujgEAbxXdQy9RKwNjFj4FQcg/GaQb5jvhCONZEmMOPTJxpWjrrdBw8JFvB9IqYtWY+JKEPYmTqkXDVUHeto1UfmLklRoxpZ1Uwp5IWE+18kpeRrjIPaHy1+5551h2hrwSVPYy6pp3jCWP/yG66QV3FrHbQH86y95L7ErxGsgiFlGVx/97EeWRsTEJ9oX8XWH/q5ZUsVHYvQ1eW3ZiyS+BcWsC1SaBjSUvLmK77CUWkgDOUeQ1MDg+YpWTCLFRPDviV4pdj26bChgXRKlNomnPxXGvS/ZMyj3ma6gcn0qS5zxgviQOWNeel3i1LVYSNBNxUCmXWTjTRcYk5BUcPDcJeQ1QaUVeMNVRRo0hi9dmY7fcSDQBW5J4MDFk+oJW7ouQcBZD7OdCnDpvQ1a2heQRmOw9B6yJtJB7EydJwkNeej79TSwtArD7NyAqiRrybz3ftPdRu+JbyogtO0mSAMdtm5GnTYjNCMH3QtcnIA6xSeC/iE7nVUIzTpDEDzBERiqvMXTExp5W/MucAY5qJ4hN73U6VRKKk3KUY1UJwsXYPYmkrxpBJAojuLT7SangXUkCHu1/1HclCciP/1nlgyRm48NWQxKz82nRSWI2P+6aJDrxef3z/oNDTyKPuQK5ArkCn7sCry2o875qQ9HfAAAAAElFTkSuQmCC";
e=d=26;break;case "complete_delete_highlight":case "digit_delete_highlight":c=0===b?"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAE4AAABOCAMAAAC5dNAvAAAABGdBTUEAALGPC/xhBQAAAAFzUkdCAK7OHOkAAABFUExURUxpcThBXThBXTlBXjpDYEBIZThBXThBXTlCXjhBXThBXThBXfz+/Zuhrj1GYcTIz3mAk0lSa93g5GVsgri8xfDz84qQoaZkEbUAAAALdFJOUwCde10mENjyQL/ont+vzQAAAWVJREFUWMPt2NtugzAMBuCwAgk1DaVs7/+qW4uq0sanmOygKb6M1E8gfidOnatVq9Z/qDfIKd83gdMOkF1DS2oBDOWpB2w9mDz8+doj2GrAtK4Ha2GvO5g1aFKtsWvQF9XA74tvUvvjy3ABSnLG+BKcOb4oR8Z3QRcjz3VUfE/jKV08j1NkOUo7j2PqoYtbjoxvnNKfXrXLzHBMfFOP1EAT31eP1kAV32eP0VYuSPHdepwGyvg+PFYD7e5793gNmPhinqCBfi+/eYIGGbvv6rEa5Gy/1zdFW2v7sk2WJnj6g/D2FSbBc9pPu37TKHjaY/+eEMFT7uqPvPGeUzXtNr2spzoRn3uB8zTn9WtnMZ5imkj7lPbkWQfretKT57r5gvTpl/ex2I7teUK6Pr4v0lCxY4rFR55jUU4+g351XNztuaLDsf/ui0Dpa0rpS5Tril7xSl9AjXH24Ycu76X/WqhVq9YfrU+jvpqL5jt4tQAAAABJRU5ErkJggg==":
"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAADAAAAAwCAYAAABXAvmHAAAAAXNSR0IArs4c6QAAAkBJREFUaAXtWbtOwzAUrRGw8RhAQmJkZ4UdATMbH8DfMAJCIAYm/gEkxIxgAYkf6IKQQLRSVyjnVLmWU2InjdPGAV/pYie+j3Ou7TgNrVaUWIFYgViBWAGPCigP31a/36f/HnQfugldgc5Aq5IeArWhN9ALpdRLVYEJfg36AJ2UfCHRMXTWmwSCbEA/oHXILZKWJwFngu/WgdzIeSyzMNIeIHg4XkMXJEBN7TfyrnNPTBUFEBB4QibuA3am+SdPEvB8Eszn2U5wfIe5cpdQYJU369PDEppzEggY/IAICCjrHjCWTdkNe44sj2bJHP1njOkni8Ou2BDBQzvQsnLCTHBehOYddk+wWUrsD0dJmMkGAXyf85+IsSzB0XeR0OATArR9gxYSyaFbePlWXhJnARueiSI2Ei+z1cAT9lWBl2QugK4x8c9tNQFYrkI59VVLFtBLJBms+aRwriXmxGMSuHJa+g2mSOik6CBsafCEpGOh/8obY5RfJJDLCzyxkoD1HNDsAu8Igbsx4uQhtYVD893MgesOrrehRQ870z3dx2w0exOTDkg09zEq81EhidSmRdysDVvEBq52EdypFua+M1Hfq4QwqYBEfS9zQyS69gnMHTmDxfD7j82Jy+nINmi7T6x/9wfNgJ1S92h3oV1eByY94pGDzIoNB06oJNqFCNAoUBL8SuLeAzQwBZupuR+2SCSgmTgFlvJfqjkT0GZ+3JUlBQJBfF53ngMC1taCBP2b+Q8OG6l4P1YgViBW4H9V4AdohcfMR65w5QAAAABJRU5ErkJggg==",
e=d=26}return{ub:c,width:d,height:e}};function $c(a,b=null){return`${b||a.j.a}px ${a.j.g}`}class ad extends Wc{constructor(a){super(a);this.w=this.g=null;this.S=a.S;this.X=a.X}};function bd(a,b,c){const d=b.x,e=b.y,f=b.width;b=b.height;0==c?a.fillRect(d,e,f,b):(a.beginPath(),a.moveTo(d+c,e),a.lineTo(d+f-c,e),a.lineTo(d+f,e+b-c),a.lineTo(d+c,e+b),a.arc(d+c,e+c,c,Math.PI,3*Math.PI/2,!1),a.arc(d+f-c,e+c,c,3*Math.PI/2,2*Math.PI,!1),a.arc(d+f-c,e+b-c,c,0,Math.PI/2,!1),a.arc(d+c,e+b-c,c,Math.PI/2,Math.PI,!1),a.lineTo(d,e+c),a.closePath())}function cd(a){return 9==a.length?a.substr(0,1)+a.substr(3)+a.substr(1,2):a};function dd(a,b){a.N=b}class ed{constructor(a){this.g=a;this.N=!0}};function fd(a,b=0){return`${b||a.j.a}px ${a.j.g}`}class gd extends Wc{constructor(a){super(a);this.ja=this.l=0;this.Ab=a.h.positionFixed?a.h.positionFixed:!1}};class hd{constructor(a){this.id=a.id;this.width=a.width;this.height=a.height;this.Ka=a.Ka;this.o=a.o;this.fb=a.fb;this.Ca=a.Ca;this.Qa=a.Qa;this.ka=a.ka;this.oa=a.oa;this.ra=a.ra;this.bb=a.bb;this.backgroundColor=a.backgroundColor;this.type=a.type}};function id(a){let b="";for(let c=0;c<a;c++)b+="\u2022";return b}function jd(a,b){if("string"==typeof b){if(0!=b.length&&a.match(b)!=a)return!1}else if("object"==typeof b){if(a.match(b)!=a)return!1}else return!1;return!0}function kd(a){const b=[a];for(let c=0;c<a;c++)b[c]=c;return b}function ld(a,b){a=[...a];void 0!=b&&a.splice(b,1);const c=[];for(let d=0,e=a.length;d<e;d++){const f=Math.floor(Math.random()*(e-d));c[d]=a[f];a.splice(f,1)}void 0!=b&&c.splice(b,0,b);return c};function md(){const a=wx.getSystemInfoSync();return{isPad:1.6>=a.screenHeight/a.screenWidth?!0:!1,bottomPadding:a.screenHeight-a.safeArea.bottom}};function nd(a){var b=-4;if("undefined"===typeof b||0===+b)return Math.floor(a);a=+a;b=+b;if(isNaN(a)||"number"!==typeof b||0!==b%1)return NaN;a=a.toString().split("e");a=Math.floor(+(a[0]+"e"+(a[1]?+a[1]-b:-b)));a=a.toString().split("e");return+(a[0]+"e"+(a[1]?+a[1]+b:b))};function od(a,b){b.width&&(b.width=Math.trunc(b.width*a.M));b.height&&(b.height=Math.trunc(b.height*a.M*a.resizeHeight));b.leftMargin&&(b.o=Math.round(b.leftMargin*a.M))}function pd(a,b){b.borderSize&&(b.borderSize=Math.round(b.borderSize*a.M));b.fontSize&&(b.fontSize=Math.round(b.fontSize*a.M))}
function qd(a,b){b&&Object.keys(b).forEach(c=>{if(c.startsWith("key")){var d=c.split("_");if(3===d.length)c=Object.assign({},b[c]),od(a,c),rd(a,`key_${d[1]}_${d[2]}`,c);else{var e=d;c=Object.assign({},b[c]);od(a,c);d=e[1];var f=e[2];for(e=e[3];f<=e;f++)rd(a,`key_${d}_${f}`,c)}}})}
function sd(a,b){b&&Object.keys(b).forEach(c=>{if(c.startsWith("key")){var d=a.u.get(c);const e=Object.assign({},b[c]);pd(a,e);d&&(d.j=Object.assign({},d.j,b[c]),d instanceof ad&&(c=d,c.j.image&&(d=Zc(c.j.image[1],c.S),c.g=Zc(c.j.image[0],c.S),c.w=d)))}})}function td(a){return"letter"==a||"digit"==a||"symbol"==a||"ferry_letter"===a?!0:!1}
function rd(a,b,c){if(b)if(td(c.type)){var d=c.type;c="0"===b.split("_")[2]?new gd({h:Object.assign({},a.Ma,c,{o:c.o?c.o:a.m.o}),j:a.ab,f:d}):new gd({h:Object.assign({},a.Ma,c,{o:c.o?c.o:a.m.Ca}),j:a.ab,f:d});a.u.set(b,c)}else{{d=c.type;a.La.set(d,b);const e=b.split("_")[2];c.height||(c.height=a.Ma.height);c="0"===e?new ad({h:Object.assign({},a.Va,c,{o:c.o?c.o:a.m.o}),j:a.Wa,f:d,target:c.target,S:a.S,X:a.X}):new ad({h:Object.assign({},a.Va,c,{o:c.o?c.o:a.m.Ca}),j:a.Wa,f:d,target:c.target,S:a.S,X:a.X})}a.u.set(b,
c)}}function ud(a,b,c){switch(b){case "digit":if(a.ka)return a.ka[c];break;case "letter":case "ferry_letter":if(a.oa)return a.oa[c];break;case "symbol":if(a.ra)return a.ra[c]}return""}function vd(a){a=a.u.values();for(const b of a)td(b.f)&&(b.l=b.ja)}
function wd(a,b){if(0===b){if(0!==a.T)if(1===a.T){a.T=0;a=a.u.values();for(const l of a)"digit"===l.f&&(l.l=l.ja)}else 2===a.T&&(a.T=0,vd(a))}else if(1===b){if(0===a.T?a.T=1:2===a.T&&(a.T=1,vd(a)),a.ba){var c=null;for(const l of a.u.values())if("digit"===l.f&&l.Ab){c=l;break}c=c?ld(a.ba,c.ja):ld(a.ba);for(var d of a.u.values())"digit"===d.f&&(a=c.shift(),d.l=a)}}else if(a.T=2,d=a.u.values(),"letter"===a.X&&a.ba&&a.Ja){c=ld(a.ba);a=ld(a.Ja);for(var e of d){if("digit"===e.f){var f=c.pop();e.l=f}if("letter"===
e.f||"ferry_letter"===e.f)f=a.pop(),e.l=f}}else{if(a.ba){e=null;for(var g of d)if("digit"===g.f&&g.Ab){e=g;break}e=e?ld(a.ba,e.ja):ld(a.ba);for(var h of a.u.values())"digit"===h.f&&(g=e.shift(),h.l=g)}if(a.Ja){e=ld(a.Ja);for(f of d)if("letter"===f.f||"ferry_letter"===f.f)h=e.pop(),f.l=h}if(a.kb){a=ld(a.kb);for(c of d)"symbol"===c.f&&(f=a.pop(),c.l=f)}}}
function xd(a,b,c){for(const f of a.u.values()){var d=a.m.Ca,e=a.m.Qa;if(b>=f.location.x-d/2&&b<=f.location.x+f.h.width+d/2&&c>=f.location.y-e/2&&c<=f.location.y+f.h.height+e/2)return f}return null}function yd(a,b){if(a.u.length==b.u.length)for(const c of b.u){const [d,e]=c;e instanceof gd&&(a.u.get(d).l=e.l)}}
function zd(a,b,c){switch(b){case "digit":return a.ka&&a.ka.length-1>=c;case "letter":case "ferry_letter":return a.oa&&a.oa.length-1>=c;case "symbol":return a.ra&&a.ra.length-1>=c}return!1}
class Ad{constructor(a){this.u=new Map;this.La=new Map;this.m=null;this.M=0;this.resizeHeight=1;this.X=a.X;this.S=a.S;var b=a.Qb,c=a.Pb;a=a.h;var d=wx.getSystemInfoSync().windowWidth;b.width||console.error("keyboardLayout design width is null");this.M=nd(d/b.width);d=b.height*this.M;var e=wx.getSystemInfoSync().windowHeight;this.resizeHeight=d=d>e/2?e/2/d:1;this.T=0;this.ab=Object.assign({},c.normalInputKey);this.Wa=Object.assign({},c.normalFunctionKey);this.Ma=Object.assign({},a.normalInputKey);
this.Va=Object.assign({},a.normalFunctionKey);od(this,this.Ma);od(this,this.Va);pd(this,this.ab);pd(this,this.Wa);this.Ka=b.columns;b||console.error("keyboardLayoutParams is null");c||console.error("keyStyles is null");a||console.error("keyParams is null");b.columns||console.error("columns is null");this.m=new hd(Object.assign({},b,{bb:b.isShowBubble,backgroundColor:b.backgroundColor,width:Math.round(b.width*this.M),height:Math.round(b.height*this.M*this.resizeHeight),fb:Math.round(b.topMargin*this.M*
this.resizeHeight),o:Math.round(b.leftMargin*this.M),Ca:Math.round(b.keySpacing*this.M),Qa:Math.round(b.rowSpacing*this.M*this.resizeHeight)}));b.digitTitles&&(this.ka=b.digitTitles,this.ba=kd(this.ka.length));b.letterTitles&&(this.oa=b.letterTitles,this.Ja=kd(this.oa.length));b.symbolTitles&&(this.ra=b.symbolTitles,this.kb=kd(this.ra.length));qd(this,a);sd(this,c);{let h=e=d=a=c=b=0,l=0;for(let n=0;n<this.Ka.length;n++){const m=this.Ka[n];for(let v=0;v<m;v++){const u=this.u.get(`key_${n}_${v}`);
0===v?(e=0===n?this.m.fb:e+(l+this.m.Qa),l=u.h.height,d=u.h.o):d+=h+u.h.o;h=u.h.width;if(td(u.f))if("letter"===u.f||"ferry_letter"===u.f){var f=u,g=b++;f.l=g;f.ja=g}else"digit"===u.f?(f=u,g=c++,f.l=g,f.ja=g):(f=u,g=a++,f.l=g,f.ja=g);u.location={x:d,y:e}}}}}};function Bd(a){if(a.g){if(a.Ha||0==a.P.length)for(const b of a.g.u.values())Cd(a,b);else for(const b of a.P)b instanceof ad?Cd(a,b):void 0!=b.l&&null!=b.l&&zd(a.g,b.f,b.l)&&Cd(a,b);a.Ha=!1;a.P=[]}}function Dd(a,b){b?a.a.clearRect(b.x,b.y,b.width,b.height):a.a.clearRect(0,0,a.g.m.width,a.g.m.height);Bd(a)}
function Cd(a,b){a.a.save();a.a.translate(Math.round(b.location.x),Math.round(b.location.y));a:{var c=null,d=null,e=b.h,f=b.j,g=new Vc(0,0,e.width,e.height);if(f.Gb){var h=f.Gb.split(",");if(2!=h.length||!h[1])break a;bd(a.a,new Vc(0,e.height/2,e.width,e.height/2+parseInt(h[1],10)),f.Ua);a.a.fillStyle=f.shadowColor;a.a.fill()}0<f.ob&&(a.a.lineWidth=f.ob,a.a.strokeStyle=f.borderColor,bd(a.a,g,f.Ua),a.a.stroke());bd(a.a,g,f.Ua);f.normalBackgroundColorGradient&&(c=a.a.createLinearGradient(0,0,e.width,
e.height),c.addColorStop(0,f.normalBackgroundColorGradient[0]),c.addColorStop(1,f.normalBackgroundColorGradient[1]));f.pressBackgroundColorGradient&&(d=a.a.createLinearGradient(0,0,e.width,e.height),d.addColorStop(0,f.pressBackgroundColorGradient[0]),d.addColorStop(1,f.pressBackgroundColorGradient[1]));a.a.fillStyle=a.N&&b.a?f.pressBackgroundColorGradient?d:cd(f.backgroundColor[1]):f.normalBackgroundColorGradient?c:cd(f.backgroundColor[0]);a.a.fill()}b instanceof gd&&(f=b.h,c=b.j,d=f.width,e=f.width/
2,g=f.height/2,h=ud(a.g,b.f,b.l))&&(f=Math.floor(c.a*Math.min(1,.9*d/a.a.measureText(h).width)),a.gb?(a.a.setFontSize(f),a.a.setTextAlign("center"),a.a.setTextBaseline("middle"),a.a.setFillStyle(a.N&&b.a?c.color[1]:c.color[0]),a.a.fillText(h,e,g,.9*d),a.a.draw(!0)):(a.a.font=fd(b,f),a.a.textAlign="center",a.a.textBaseline="middle",a.a.fillStyle=a.N&&b.a?c.color[1]:c.color[0],a.a.fillText(h,e,g,.9*d)));b instanceof ad&&Ed(a,b);a.a.restore()}
function Fd(a,b){a.Ha||null==b||-1!==a.P.indexOf(b)||b instanceof gd&&(void 0==b.l||null==b.l||!zd(a.g,b.f,b.l))||(a.P.push(b),Dd(a,{x:b.location.x,y:b.location.y,width:b.h.width,height:b.h.height}))}
function Ed(a,b){var c=b.h,d=b.j;if(d.title){var e=c.width;const h=c.width/2;c=c.height/2;const l=d.title,n=Math.floor(d.a*Math.min(1,.9*e/a.a.measureText(l).width));a.gb?(a.a.setFontSize(n),a.a.setTextAlign("center"),a.a.setTextBaseline("middle"),a.a.setFillStyle(a.N&&b.a?d.color[1]:d.color[0]),a.a.fillText(l,h,c,.9*e),a.a.draw(!0)):(a.a.font=$c(b,n),a.a.textAlign="center",a.a.textBaseline="middle",a.a.fillStyle=b.a?d.color[1]:d.color[0],a.a.fillText(l,h,c,.9*e))}else if(b.g&&b.w){e=b.a?b.w:b.g;
let h=e.width,l=e.height;if(0!=h&&0!=l){d=h/l;h>Math.floor(.9*b.h.width)&&(h=Math.floor(.9*b.h.width),l=Math.floor(h/d));var f=Math.floor((b.h.width-h)/2),g=Math.floor((b.h.height-l)/2);if(a.gb)a.a.drawImage(e.ub,f,g,h,l),a.a.draw(!0);else{const n=a.Yb.createImage();n.src=e.ub;n.onload=()=>{a.a.drawImage(n,b.location.x+f,b.location.y+g,h,l)}}}}}class Gd extends ed{constructor(a,b,c){super(a);this.a=b;this.Yb=c;this.gb=null===c;this.P=[];this.Ha=!0;Bd(this)}};const Hd=120/63,Id=160/84;function Jd(a,b){if(b){switch(b.f){case "digit":case "letter":case "symbol":a.ea.zb(ud(a.g,b.f,b.l));break;case "ferry_letter":a.ea.zb(ud(a.g,b.f,b.l));a.ea.yb(b);break;case "delete":a.ea.Vb();break;case "capslock":case "switch":case "back":a.ea.yb(b);break;case "done":a.ea.Ub()}"capslock"!=b.f&&"switch"!=b.f&&"back"!=b.f&&"delete"!=b.f&&(b.a=!1,Fd(a,b),a.ca.pb())}}
function Kd(a,b){if(b)if(b instanceof gd&&a.N&&a.g.m.bb&&td(b.f)){{var c=new Vc(b.location.x,b.location.y,b.h.width,b.h.height);var d=a.Y;const e=c.width*Hd,f=c.height*Id,g=d.height-c.y;c=c.x<c.width/2?{frame:new Vc(c.x,g,e,f),position:0}:c.x+3*c.width/2>d.width?{frame:new Vc(c.x+c.width-e,g,e,f),position:1}:{frame:new Vc(c.x+c.width/2-e/2,g,e,f),position:2}}a.ca.show(c,b instanceof gd?ud(a.g,b.f,b.l):"")}else"capslock"!=b.f&&"switch"!=b.f&&"back"!=b.f&&"delete"!=b.f&&(b.a=!0,Fd(a,b))}
class Ld extends Gd{constructor(a,b,c){super(a,b,c);this.ca=this.ea=this.ga=this.w=null;this.Y=new Vc(0,0,a.m.width,a.m.height)}};class Md{constructor(){this.keyAnimation=this.isEncrypt=!0;this.minLength=6;this.maxLength=8;this.outputType=2;this.cipherType=0;this.serverRandom=null;this.lastShow=!0;this.disorderType=0;this.inputRegex=null;this.keywords=[];this.autoScroll=!0;this.precision=2;this.displayMode=0;this.functionBackgroundList=new Map;this.backTextPressColor=this.backTextNormalColor=this.backText=this.doneTextPressColor=this.doneTextNormalColor=this.doneText=null;this.isUserSetTopBar=!1;this.serverRandom=null;this.isKeyFeedBack=
this.allowScreenRecord=!1}};const Y=new Map,Nd=JSON.parse('{"keyboardParams":{"id":14,"width":1530,"height":496,"rowNumber":4,"topMargin":16,"leftMargin":8,"bottomMargin":8,"keySpacing":16,"rowSpacing":16,"isShowBubble":false,"backgroundColor":"#DCE0E5","digitTitles":"1234567890","columns":[3,3,3,3]},"keyStyles":{"normalInputKey":{"backgroundColor":["#FFFFFF","#B8BDC6"],"color":["#000000","#000000"],"fontName":"SFUIDisplay-Regular","fontSize":56,"cornerRadius":5,"shadowColor":"#848689","shadowOffset":"0,1"},"normalFunctionKey":{"backgroundColor":["#B8BDC6","#A4AAB5"],"color":["#000000","#000000"],"fontName":"SFUIDisplay-Regular","fontSize":40,"cornerRadius":5,"shadowColor":"#848689","shadowOffset":"0,1"},"key_3_0":{"title":"abc"},"key_3_2":{"image":["digit_delete_normal","digit_delete_highlight"]}},"keyParams":{"normalInputKey":{"width":494,"height":106},"normalFunctionKey":{"width":494,"height":106},"key_0_0_2":{"type":"digit"},"key_1_0_2":{"type":"digit"},"key_2_0_2":{"type":"digit"},"key_3_0":{"type":"switch","target":11,"desc":["\u5207\u6362\u81f3\u5b57\u6bcd\u952e\u76d8","Switchtoletterkeyboard"]},"key_3_1":{"type":"digit"},"key_3_2":{"type":"delete","desc":["\u5220\u9664","Backspace"]}}}');
Y.set("CompleteDigitLayout_pad",Nd);const Od=JSON.parse('{"keyboardParams":{"id":14,"width":1530,"height":496,"rowNumber":4,"topMargin":16,"leftMargin":8,"bottomMargin":8,"keySpacing":16,"rowSpacing":16,"isShowBubble":false,"backgroundColor":"#000000","digitTitles":"1234567890","columns":[3,3,3,3]},"keyStyles":{"normalInputKey":{"backgroundColor":["#3A3A3A","#5A5A5A"],"color":["#FFFFFF","#FFFFFF"],"fontName":"SFUIDisplay-Regular","fontSize":56,"cornerRadius":5,"shadowColor":"#66000000","shadowOffset":"0,1"},"normalFunctionKey":{"backgroundColor":["#5A5A5A","#7A7A7A"],"color":["#FFFFFF","#FFFFFF"],"fontName":"SFUIDisplay-Regular","fontSize":40,"cornerRadius":5,"shadowColor":"#66000000","shadowOffset":"0,1"},"key_3_0":{"title":"abc"},"key_3_2":{"image":["digit_delete_normal","digit_delete_highlight"]}},"keyParams":{"normalInputKey":{"width":494,"height":106},"normalFunctionKey":{"width":494,"height":106},"key_0_0_2":{"type":"digit"},"key_1_0_2":{"type":"digit"},"key_2_0_2":{"type":"digit"},"key_3_0":{"type":"switch","target":11,"desc":["\u5207\u6362\u81f3\u5b57\u6bcd\u952e\u76d8","Switchtoletterkeyboard"]},"key_3_1":{"type":"digit"},"key_3_2":{"type":"delete","desc":["\u5220\u9664","Backspace"]}}}');
Y.set("CompleteDigitLayout_pad_dark",Od);const Pd=JSON.parse('{"keyboardParams":{"id":14,"width":375,"height":216,"rowNumber":4,"topMargin":5,"leftMargin":3,"bottomMargin":3,"keySpacing":6,"rowSpacing":6,"isShowBubble":false,"backgroundColor":"#DCE0E5","digitTitles":"1234567890","columns":[3,3,3,3]},"keyStyles":{"normalInputKey":{"backgroundColor":["#FFFFFF","#B8BDC6"],"color":["#000000","#000000"],"fontName":"SFUIDisplay-Regular","fontSize":24,"cornerRadius":5,"shadowColor":"#848689","shadowOffset":"0,1"},"normalFunctionKey":{"backgroundColor":["#B8BDC6","#A4AAB5"],"color":["#000000","#000000"],"fontName":"SFUIDisplay-Regular","fontSize":18,"cornerRadius":5,"shadowColor":"#848689","shadowOffset":"0,1"},"key_3_0":{"title":"abc"},"key_3_2":{"image":["digit_delete_normal","digit_delete_highlight"]}},"keyParams":{"normalInputKey":{"width":119,"height":47.5},"normalFunctionKey":{"width":119,"height":47.5},"key_0_0_2":{"type":"digit"},"key_1_0_2":{"type":"digit"},"key_2_0_2":{"type":"digit"},"key_3_0":{"type":"switch","target":11,"desc":["\u5207\u6362\u81f3\u5b57\u6bcd\u952e\u76d8","Switchtoletterkeyboard"]},"key_3_1":{"type":"digit"},"key_3_2":{"type":"delete","desc":["\u5220\u9664","Backspace"]}}}');
Y.set("CompleteDigitLayout_phone",Pd);const Qd=JSON.parse('{"keyboardParams":{"id":14,"width":375,"height":216,"rowNumber":4,"topMargin":5,"leftMargin":3,"bottomMargin":3,"keySpacing":6,"rowSpacing":6,"isShowBubble":false,"backgroundColor":"#000000","digitTitles":"1234567890","columns":[3,3,3,3]},"keyStyles":{"normalInputKey":{"backgroundColor":["#3A3A3A","#5A5A5A"],"color":["#FFFFFF","#FFFFFF"],"fontName":"SFUIDisplay-Regular","fontSize":24,"cornerRadius":5,"shadowColor":"#66000000","shadowOffset":"0,1"},"normalFunctionKey":{"backgroundColor":["#5A5A5A","#7A7A7A"],"color":["#FFFFFF","#FFFFFF"],"fontName":"SFUIDisplay-Regular","fontSize":18,"cornerRadius":5,"shadowColor":"#66000000","shadowOffset":"0,1"},"key_3_0":{"title":"abc"},"key_3_2":{"image":["digit_delete_normal","digit_delete_highlight"]}},"keyParams":{"normalInputKey":{"width":119,"height":47.5},"normalFunctionKey":{"width":119,"height":47.5},"key_0_0_2":{"type":"digit"},"key_1_0_2":{"type":"digit"},"key_2_0_2":{"type":"digit"},"key_3_0":{"type":"switch","target":11,"desc":["\u5207\u6362\u81f3\u5b57\u6bcd\u952e\u76d8","Switchtoletterkeyboard"]},"key_3_1":{"type":"digit"},"key_3_2":{"type":"delete","desc":["\u5220\u9664","Backspace"]}}}');
Y.set("CompleteDigitLayout_phone_dark",Qd);const Rd=JSON.parse('{"keyboardParams":{"id":13,"letterTitles":"QWERTYUIOPASDFGHJKLZXCVBNM"},"keyStyles":{"key_2_0":{"image":["complete_capslock_ferry","complete_capslock_highlight"]}},"keyParams":{"key_1_0_9":{"type":"ferry_letter","target":11},"key_2_1_9":{"type":"ferry_letter","target":11},"key_3_1_7":{"type":"ferry_letter","target":11},"key_2_0":{"target":12,"desc":["\u5207\u6362\u81f3\u5927\u5199\u9501\u5b9a","Switchtouppercase"]}}}');
Y.set("CompleteLetterLayout_capital_ferry_pad",Rd);const Sd=JSON.parse('{"keyboardParams":{"id":13,"letterTitles":"QWERTYUIOPASDFGHJKLZXCVBNM"},"keyStyles":{"key_2_0":{"image":["complete_capslock_ferry","complete_capslock_highlight"]}},"keyParams":{"key_1_0_9":{"type":"ferry_letter","target":11},"key_2_1_9":{"type":"ferry_letter","target":11},"key_3_1_7":{"type":"ferry_letter","target":11},"key_2_0":{"target":12,"desc":["\u5207\u6362\u81f3\u5927\u5199\u9501\u5b9a","Switchtouppercase"]}}}');
Y.set("CompleteLetterLayout_capital_ferry_pad_dark",Sd);const Td=JSON.parse('{"keyboardParams":{"id":13,"letterTitles":"QWERTYUIOPASDFGHJKLZXCVBNM"},"keyStyles":{"key_2_0":{"image":["complete_capslock_ferry","complete_capslock_highlight"]}},"keyParams":{"key_1_0_9":{"type":"ferry_letter","target":11},"key_2_1_9":{"type":"ferry_letter","target":11},"key_3_1_7":{"type":"ferry_letter","target":11},"key_2_0":{"target":12,"desc":["\u5207\u6362\u81f3\u5927\u5199\u9501\u5b9a","Switchtouppercase"]}}}');
Y.set("CompleteLetterLayout_capital_ferry_phone",Td);const Ud=JSON.parse('{"keyboardParams":{"id":13,"letterTitles":"QWERTYUIOPASDFGHJKLZXCVBNM"},"keyStyles":{"key_2_0":{"image":["complete_capslock_ferry","complete_capslock_highlight"]}},"keyParams":{"key_1_0_9":{"type":"ferry_letter","target":11},"key_2_1_9":{"type":"ferry_letter","target":11},"key_3_1_7":{"type":"ferry_letter","target":11},"key_2_0":{"target":12,"desc":["\u5207\u6362\u81f3\u5927\u5199\u9501\u5b9a","Switchtouppercase"]}}}');
Y.set("CompleteLetterLayout_capital_ferry_phone_dark",Ud);const Vd=JSON.parse('{"keyboardParams":{"id":12,"letterTitles":"QWERTYUIOPASDFGHJKLZXCVBNM"},"keyStyles":{"key_2_0":{"image":["complete_capslock_highlight","complete_capslock_normal"]}},"keyParams":{"key_2_0":{"target":11,"desc":["\u5207\u6362\u81f3\u5c0f\u5199","Switchtolowercase"]}}}');Y.set("CompleteLetterLayout_capital_pad",Vd);const Wd=JSON.parse('{"keyboardParams":{"id":12,"letterTitles":"QWERTYUIOPASDFGHJKLZXCVBNM"},"keyStyles":{"key_2_0":{"image":["complete_capslock_highlight","complete_capslock_normal"]}},"keyParams":{"key_2_0":{"target":11,"desc":["\u5207\u6362\u81f3\u5c0f\u5199","Switchtolowercase"]}}}');
Y.set("CompleteLetterLayout_capital_pad_dark",Wd);const Xd=JSON.parse('{"keyboardParams":{"id":12,"letterTitles":"QWERTYUIOPASDFGHJKLZXCVBNM"},"keyStyles":{"key_2_0":{"image":["complete_capslock_highlight","complete_capslock_normal"]}},"keyParams":{"key_2_0":{"target":11,"desc":["\u5207\u6362\u81f3\u5c0f\u5199","Switchtolowercase"]}}}');Y.set("CompleteLetterLayout_capital_phone",Xd);const Yd=JSON.parse('{"keyboardParams":{"id":12,"letterTitles":"QWERTYUIOPASDFGHJKLZXCVBNM"},"keyStyles":{"key_2_0":{"image":["complete_capslock_highlight","complete_capslock_normal"]}},"keyParams":{"key_2_0":{"target":11,"desc":["\u5207\u6362\u81f3\u5c0f\u5199","Switchtolowercase"]}}}');
Y.set("CompleteLetterLayout_capital_phone_dark",Yd);const Zd=JSON.parse('{"keyboardParams":{"id":11,"width":1530,"height":496,"rowNumber":4,"topMargin":16,"leftMargin":8,"bottomMargin":8,"keySpacing":16,"rowSpacing":16,"isShowBubble":false,"backgroundColor":"#DCE0E5","digitTitles":"1234567890","letterTitles":"qwertyuiopasdfghjklzxcvbnm","columns":[10,10,10,9]},"keyStyles":{"normalInputKey":{"backgroundColor":["#FFFFFF","#B8BDC6"],"color":["#000000","#000000"],"fontName":"SFUIDisplay-Regular","fontSize":56,"cornerRadius":5,"shadowColor":"#848689","shadowOffset":"0,1"},"normalFunctionKey":{"backgroundColor":["#B8BDC6","#A4AAB5"],"color":["#000000","#000000"],"fontName":"SFUIDisplay-Regular","fontSize":40,"cornerRadius":5,"shadowColor":"#848689","shadowOffset":"0,1"},"key_2_0":{"image":["complete_capslock_normal","complete_capslock_highlight"]},"key_3_0":{"title":"#+="},"key_3_8":{"image":["complete_delete_normal","complete_delete_highlight"]}},"keyParams":{"normalInputKey":{"width":137,"height":106},"normalFunctionKey":{"width":137,"height":106},"key_0_0_9":{"type":"digit"},"key_1_0_9":{"type":"letter"},"key_2_1_9":{"type":"letter"},"key_2_0":{"type":"capslock","target":12,"desc":["\u5207\u6362\u81f3\u5927\u5199","Switchtouppercase"]},"key_3_1_7":{"type":"letter"},"key_3_0":{"type":"switch","width":213.5,"target":2,"desc":["\u5207\u6362\u81f3\u7b26\u53f7\u952e\u76d8","Switchtosymbolkeyboard"]},"key_3_8":{"width":213.5,"type":"delete","desc":["\u5220\u9664","Backspace"]}}}');
Y.set("CompleteLetterLayout_small_pad",Zd);const $d=JSON.parse('{"keyboardParams":{"id":11,"width":1530,"height":496,"rowNumber":4,"topMargin":16,"leftMargin":8,"bottomMargin":8,"keySpacing":16,"rowSpacing":16,"isShowBubble":false,"backgroundColor":"#000000","digitTitles":"1234567890","letterTitles":"qwertyuiopasdfghjklzxcvbnm","columns":[10,10,10,9]},"keyStyles":{"normalInputKey":{"backgroundColor":["#3A3A3A","#5A5A5A"],"color":["#FFFFFF","#FFFFFF"],"fontName":"SFUIDisplay-Regular","fontSize":56,"cornerRadius":5,"shadowColor":"#66000000","shadowOffset":"0,1"},"normalFunctionKey":{"backgroundColor":["#5A5A5A","#7A7A7A"],"color":["#FFFFFF","#FFFFFF"],"fontName":"SFUIDisplay-Regular","fontSize":40,"cornerRadius":5,"shadowColor":"#66000000","shadowOffset":"0,1"},"key_2_0":{"image":["complete_capslock_normal","complete_capslock_highlight"]},"key_3_0":{"title":"#+="},"key_3_8":{"image":["complete_delete_normal","complete_delete_highlight"]}},"keyParams":{"normalInputKey":{"width":137,"height":106},"normalFunctionKey":{"width":137,"height":106},"key_0_0_9":{"type":"digit"},"key_1_0_9":{"type":"letter"},"key_2_1_9":{"type":"letter"},"key_2_0":{"type":"capslock","target":12,"desc":["\u5207\u6362\u81f3\u5927\u5199","Switchtouppercase"]},"key_3_1_7":{"type":"letter"},"key_3_0":{"type":"switch","width":213.5,"target":2,"desc":["\u5207\u6362\u81f3\u7b26\u53f7\u952e\u76d8","Switchtosymbolkeyboard"]},"key_3_8":{"width":213.5,"type":"delete","desc":["\u5220\u9664","Backspace"]}}}');
Y.set("CompleteLetterLayout_small_pad_dark",$d);const ae=JSON.parse('{"keyboardParams":{"id":11,"width":375,"height":216,"rowNumber":4,"topMargin":8,"leftMargin":3,"bottomMargin":4,"keySpacing":6,"rowSpacing":12,"isShowBubble":true,"backgroundColor":"#DCE0E5","digitTitles":"1234567890","letterTitles":"qwertyuiopasdfghjklzxcvbnm","columns":[10,10,10,9]},"keyStyles":{"normalInputKey":{"backgroundColor":["#FFFFFF","#B8BDC6"],"color":["#000000","#000000"],"fontName":"SFUIDisplay-Regular","fontSize":24,"cornerRadius":5,"shadowColor":"#848689","shadowOffset":"0,1","bubblePaddingTop":40,"bubblePaddingLeft":35,"bubbleFontSize":36,"bubbleFontName":"SFUIDisplay-Regular"},"normalFunctionKey":{"backgroundColor":["#B8BDC6","#A4AAB5"],"color":["#000000","#000000"],"fontName":"SFUIDisplay-Regular","fontSize":18,"cornerRadius":5,"shadowColor":"#848689","shadowOffset":"0,1"},"key_0_0":{"bubbleType":"left","bubblePaddingLeft":30},"key_0_9":{"bubbleType":"right","bubblePaddingLeft":35},"key_1_0":{"bubbleType":"left","bubblePaddingLeft":30},"key_1_9":{"bubbleType":"right","bubblePaddingLeft":35},"key_2_0":{"image":["complete_capslock_normal","complete_capslock_highlight"]},"key_2_9":{"bubbleType":"right","bubblePaddingLeft":35},"key_3_0":{"title":"#+="},"key_3_8":{"image":["complete_delete_normal","complete_delete_highlight"]}},"keyParams":{"normalInputKey":{"width":31.5,"height":42},"normalFunctionKey":{"width":31.5,"height":42},"key_0_0_9":{"type":"digit"},"key_1_0_9":{"type":"letter"},"key_2_1_9":{"type":"letter"},"key_2_0":{"type":"capslock","target":12,"desc":["\u5207\u6362\u81f3\u5927\u5199","Switchtouppercase"]},"key_3_1_7":{"type":"letter"},"key_3_0":{"type":"switch","width":50.25,"target":2,"desc":["\u5207\u6362\u81f3\u7b26\u53f7\u952e\u76d8","Switchtosymbolkeyboard"]},"key_3_8":{"width":50.25,"type":"delete","desc":["\u5220\u9664","Backspace"]}}}');
Y.set("CompleteLetterLayout_small_phone",ae);const be=JSON.parse('{"keyboardParams":{"id":11,"width":375,"height":216,"rowNumber":4,"topMargin":8,"leftMargin":3,"bottomMargin":4,"keySpacing":6,"rowSpacing":12,"isShowBubble":true,"backgroundColor":"#000000","digitTitles":"1234567890","letterTitles":"qwertyuiopasdfghjklzxcvbnm","columns":[10,10,10,9]},"keyStyles":{"normalInputKey":{"backgroundColor":["#3A3A3A","#5A5A5A"],"color":["#FFFFFF","#FFFFFF"],"fontName":"SFUIDisplay-Regular","fontSize":24,"cornerRadius":5,"shadowColor":"#66000000","shadowOffset":"0,1","bubblePaddingTop":40,"bubblePaddingLeft":35,"bubbleFontSize":36,"bubbleFontName":"SFUIDisplay-Regular"},"normalFunctionKey":{"backgroundColor":["#5A5A5A","#7A7A7A"],"color":["#FFFFFF","#FFFFFF"],"fontName":"SFUIDisplay-Regular","fontSize":18,"cornerRadius":5,"shadowColor":"#66000000","shadowOffset":"0,1"},"key_0_0":{"bubbleType":"left","bubblePaddingLeft":30},"key_0_9":{"bubbleType":"right","bubblePaddingLeft":35},"key_1_0":{"bubbleType":"left","bubblePaddingLeft":30},"key_1_9":{"bubbleType":"right","bubblePaddingLeft":35},"key_2_0":{"image":["complete_capslock_normal","complete_capslock_highlight"]},"key_2_9":{"bubbleType":"right","bubblePaddingLeft":35},"key_3_0":{"title":"#+="},"key_3_8":{"image":["complete_delete_normal","complete_delete_highlight"]}},"keyParams":{"normalInputKey":{"width":31.5,"height":42},"normalFunctionKey":{"width":31.5,"height":42},"key_0_0_9":{"type":"digit"},"key_1_0_9":{"type":"letter"},"key_2_1_9":{"type":"letter"},"key_2_0":{"type":"capslock","target":12,"desc":["\u5207\u6362\u81f3\u5927\u5199","Switchtouppercase"]},"key_3_1_7":{"type":"letter"},"key_3_0":{"type":"switch","width":50.25,"target":2,"desc":["\u5207\u6362\u81f3\u7b26\u53f7\u952e\u76d8","Switchtosymbolkeyboard"]},"key_3_8":{"width":50.25,"type":"delete","desc":["\u5220\u9664","Backspace"]}}}');
Y.set("CompleteLetterLayout_small_phone_dark",be);const ce=JSON.parse('{"keyboardParams":{"id":2,"width":1530,"height":496,"rowNumber":4,"topMargin":16,"leftMargin":8,"bottomMargin":8,"keySpacing":16,"rowSpacing":16,"isShowBubble":false,"backgroundColor":"#DCE0E5","symbolTitles":"!@#$%^&*()-_+{}[]<>:;\\"\',.?=/\\\\|~`","columns":[10,9,8,7]},"keyStyles":{"normalInputKey":{"backgroundColor":["#FFFFFF","#B8BDC6"],"color":["#000000","#000000"],"fontName":"SFUIDisplay-Regular","fontSize":56,"cornerRadius":5,"shadowColor":"#848689","shadowOffset":"0,1"},"normalFunctionKey":{"backgroundColor":["#B8BDC6","#A4AAB5"],"color":["#000000","#000000"],"fontName":"SFUIDisplay-Regular","fontSize":40,"cornerRadius":5,"shadowColor":"#848689","shadowOffset":"0,1"},"key_3_0":{"title":"abc"},"key_3_6":{"image":["complete_delete_normal","complete_delete_highlight"]}},"keyParams":{"normalInputKey":{"width":137,"height":106},"normalFunctionKey":{"width":137,"height":106},"key_0_0_9":{"type":"symbol"},"key_1_0":{"type":"symbol","leftMargin":84.5},"key_1_1_8":{"type":"symbol"},"key_2_0":{"type":"symbol","leftMargin":161},"key_2_1_7":{"type":"symbol"},"key_3_2_5":{"type":"symbol","width":167.6},"key_3_1":{"type":"symbol","leftMargin":92.5,"width":167.6},"key_3_0":{"type":"back","width":213.5,"target":11,"desc":["\u8fd4\u56de\u5b57\u6bcd\u952e\u76d8","Backtoletterkeyboard"]},"key_3_6":{"width":213.5,"type":"delete","leftMargin":92.5,"desc":["\u5220\u9664","Backspace"]}}}');
Y.set("CompleteSymbolLayout_pad",ce);const de=JSON.parse('{"keyboardParams":{"id":2,"width":1530,"height":496,"rowNumber":4,"topMargin":16,"leftMargin":8,"bottomMargin":8,"keySpacing":16,"rowSpacing":16,"isShowBubble":false,"backgroundColor":"#000000","symbolTitles":"!@#$%^&*()-_+{}[]<>:;\\"\',.?=/\\\\|~`","columns":[10,9,8,7]},"keyStyles":{"normalInputKey":{"backgroundColor":["#3A3A3A","#5A5A5A"],"color":["#FFFFFF","#FFFFFF"],"fontName":"SFUIDisplay-Regular","fontSize":56,"cornerRadius":5,"shadowColor":"#66000000","shadowOffset":"0,1"},"normalFunctionKey":{"backgroundColor":["#5A5A5A","#7A7A7A"],"color":["#FFFFFF","#FFFFFF"],"fontName":"SFUIDisplay-Regular","fontSize":40,"cornerRadius":5,"shadowColor":"#66000000","shadowOffset":"0,1"},"key_3_0":{"title":"abc"},"key_3_6":{"image":["complete_delete_normal","complete_delete_highlight"]}},"keyParams":{"normalInputKey":{"width":137,"height":106},"normalFunctionKey":{"width":137,"height":106},"key_0_0_9":{"type":"symbol"},"key_1_0":{"type":"symbol","leftMargin":84.5},"key_1_1_8":{"type":"symbol"},"key_2_0":{"type":"symbol","leftMargin":161},"key_2_1_7":{"type":"symbol"},"key_3_2_5":{"type":"symbol","width":167.6},"key_3_1":{"type":"symbol","leftMargin":92.5,"width":167.6},"key_3_0":{"type":"back","width":213.5,"target":11,"desc":["\u8fd4\u56de\u5b57\u6bcd\u952e\u76d8","Backtoletterkeyboard"]},"key_3_6":{"width":213.5,"type":"delete","leftMargin":92.5,"desc":["\u5220\u9664","Backspace"]}}}');
Y.set("CompleteSymbolLayout_pad_dark",de);const ee=JSON.parse('{"keyboardParams":{"id":2,"width":375,"height":216,"rowNumber":4,"topMargin":8,"leftMargin":3,"bottomMargin":4,"keySpacing":6,"rowSpacing":12,"isShowBubble":true,"backgroundColor":"#DCE0E5","symbolTitles":"!@#$%^&*()-_+{}[]<>:;\\"\',.?=/\\\\|~`","columns":[10,9,8,7]},"keyStyles":{"normalInputKey":{"backgroundColor":["#FFFFFF","#B8BDC6"],"color":["#000000","#000000"],"fontName":"SFUIDisplay-Regular","fontSize":24,"cornerRadius":5,"shadowColor":"#848689","shadowOffset":"0,1","bubblePaddingTop":40,"bubblePaddingLeft":35,"bubbleFontSize":36,"bubbleFontName":"SFUIDisplay-Regular"},"normalFunctionKey":{"backgroundColor":["#B8BDC6","#A4AAB5"],"color":["#000000","#000000"],"fontName":"SFUIDisplay-Regular","fontSize":18,"cornerRadius":5,"shadowColor":"#848689","shadowOffset":"0,1"},"key_0_0":{"bubbleType":"left","bubblePaddingLeft":30},"key_0_9":{"bubbleType":"right","bubblePaddingLeft":35},"key_3_0":{"title":"abc"},"key_3_6":{"image":["complete_delete_normal","complete_delete_highlight"]}},"keyParams":{"normalInputKey":{"width":31.5,"height":42},"normalFunctionKey":{"width":31.5,"height":42},"key_0_0_9":{"type":"symbol"},"key_1_0":{"type":"symbol","leftMargin":21.75},"key_1_1_8":{"type":"symbol"},"key_2_0":{"type":"symbol","leftMargin":40.5},"key_2_1_7":{"type":"symbol"},"key_3_2_5":{"type":"symbol","width":39},"key_3_1":{"type":"symbol","leftMargin":24.75,"width":39},"key_3_0":{"type":"back","width":50.25,"target":11,"desc":["\u8fd4\u56de\u5b57\u6bcd\u952e\u76d8","Backtoletterkeyboard"]},"key_3_6":{"width":50.25,"type":"delete","leftMargin":24.5,"desc":["\u5220\u9664","Backspace"]}}}');
Y.set("CompleteSymbolLayout_phone",ee);const fe=JSON.parse('{"keyboardParams":{"id":2,"width":375,"height":216,"rowNumber":4,"topMargin":8,"leftMargin":3,"bottomMargin":4,"keySpacing":6,"rowSpacing":12,"isShowBubble":true,"backgroundColor":"#000000","symbolTitles":"!@#$%^&*()-_+{}[]<>:;\\"\',.?=/\\\\|~`","columns":[10,9,8,7]},"keyStyles":{"normalInputKey":{"backgroundColor":["#3A3A3A","#5A5A5A"],"color":["#FFFFFF","#FFFFFF"],"fontName":"SFUIDisplay-Regular","fontSize":24,"cornerRadius":5,"shadowColor":"#66000000","shadowOffset":"0,1","bubblePaddingTop":40,"bubblePaddingLeft":35,"bubbleFontSize":36,"bubbleFontName":"SFUIDisplay-Regular"},"normalFunctionKey":{"backgroundColor":["#5A5A5A","#7A7A7A"],"color":["#FFFFFF","#FFFFFF"],"fontName":"SFUIDisplay-Regular","fontSize":18,"cornerRadius":5,"shadowColor":"#66000000","shadowOffset":"0,1"},"key_0_0":{"bubbleType":"left","bubblePaddingLeft":30},"key_0_9":{"bubbleType":"right","bubblePaddingLeft":35},"key_3_0":{"title":"abc"},"key_3_6":{"image":["complete_delete_normal","complete_delete_highlight"]}},"keyParams":{"normalInputKey":{"width":31.5,"height":42},"normalFunctionKey":{"width":31.5,"height":42},"key_0_0_9":{"type":"symbol"},"key_1_0":{"type":"symbol","leftMargin":21.75},"key_1_1_8":{"type":"symbol"},"key_2_0":{"type":"symbol","leftMargin":40.5},"key_2_1_7":{"type":"symbol"},"key_3_2_5":{"type":"symbol","width":39},"key_3_1":{"type":"symbol","leftMargin":24.75,"width":39},"key_3_0":{"type":"back","width":50.25,"target":11,"desc":["\u8fd4\u56de\u5b57\u6bcd\u952e\u76d8","Backtoletterkeyboard"]},"key_3_6":{"width":50.25,"type":"delete","leftMargin":24.5,"desc":["\u5220\u9664","Backspace"]}}}');
Y.set("CompleteSymbolLayout_phone_dark",fe);const ge=JSON.parse('{"keyboardParams":{"id":3,"width":1530,"height":496,"rowNumber":4,"topMargin":16,"leftMargin":8,"bottomMargin":8,"keySpacing":16,"rowSpacing":16,"isShowBubble":false,"backgroundColor":"#DCE0E5","digitTitles":"123456789X0","columns":[3,3,3,3]},"keyStyles":{"normalInputKey":{"backgroundColor":["#FFFFFF","#B8BDC6"],"color":["#000000","#000000"],"fontName":"SFUIDisplay-Regular","fontSize":56,"cornerRadius":5,"shadowColor":"#848689","shadowOffset":"0,1"},"normalFunctionKey":{"backgroundColor":["#B8BDC6","#A4AAB5"],"color":["#000000","#000000"],"fontName":"SFUIDisplay-Regular","fontSize":40,"cornerRadius":5,"shadowColor":"#848689","shadowOffset":"0,1"},"key_3_2":{"image":["digit_delete_normal","digit_delete_highlight"]}},"keyParams":{"normalInputKey":{"width":494,"height":106},"normalFunctionKey":{"width":494,"height":106},"key_0_0_2":{"type":"digit"},"key_1_0_2":{"type":"digit"},"key_2_0_2":{"type":"digit"},"key_3_0":{"type":"digit","positionFixed":true},"key_3_1":{"type":"digit"},"key_3_2":{"type":"delete","desc":["\u5220\u9664","Backspace"]}}}');
Y.set("DigitIdLayout_pad",ge);const he=JSON.parse('{"keyboardParams":{"id":3,"width":1530,"height":496,"rowNumber":4,"topMargin":16,"leftMargin":8,"bottomMargin":8,"keySpacing":16,"rowSpacing":16,"isShowBubble":false,"backgroundColor":"#000000","digitTitles":"123456789X0","columns":[3,3,3,3]},"keyStyles":{"normalInputKey":{"backgroundColor":["#3A3A3A","#5A5A5A"],"color":["#FFFFFF","#FFFFFF"],"fontName":"SFUIDisplay-Regular","fontSize":56,"cornerRadius":5,"shadowColor":"#66000000","shadowOffset":"0,1"},"normalFunctionKey":{"backgroundColor":["#5A5A5A","#7A7A7A"],"color":["#FFFFFF","#FFFFFF"],"fontName":"SFUIDisplay-Regular","fontSize":40,"cornerRadius":5,"shadowColor":"#66000000","shadowOffset":"0,1"},"key_3_2":{"image":["digit_delete_normal","digit_delete_highlight"]}},"keyParams":{"normalInputKey":{"width":494,"height":106},"normalFunctionKey":{"width":494,"height":106},"key_0_0_2":{"type":"digit"},"key_1_0_2":{"type":"digit"},"key_2_0_2":{"type":"digit"},"key_3_0":{"type":"digit","positionFixed":true},"key_3_1":{"type":"digit"},"key_3_2":{"type":"delete","desc":["\u5220\u9664","Backspace"]}}}');
Y.set("DigitIdLayout_pad_dark",he);const ie=JSON.parse('{"keyboardParams":{"id":3,"width":375,"height":216,"rowNumber":4,"topMargin":5,"leftMargin":3,"bottomMargin":3,"keySpacing":6,"rowSpacing":6,"isShowBubble":false,"backgroundColor":"#DCE0E5","digitTitles":"123456789X0","columns":[3,3,3,3]},"keyStyles":{"normalInputKey":{"backgroundColor":["#FFFFFF","#B8BDC6"],"color":["#000000","#000000"],"fontName":"SFUIDisplay-Regular","fontSize":24,"cornerRadius":5,"shadowColor":"#848689","shadowOffset":"0,1"},"normalFunctionKey":{"backgroundColor":["#B8BDC6","#A4AAB5"],"color":["#000000","#000000"],"fontName":"SFUIDisplay-Regular","fontSize":18,"cornerRadius":5,"shadowColor":"#848689","shadowOffset":"0,1"},"key_3_2":{"image":["digit_delete_normal","digit_delete_highlight"]}},"keyParams":{"normalInputKey":{"width":119,"height":47.5},"normalFunctionKey":{"width":119,"height":47.5},"key_0_0_2":{"type":"digit"},"key_1_0_2":{"type":"digit"},"key_2_0_2":{"type":"digit"},"key_3_0":{"type":"digit","positionFixed":true},"key_3_1":{"type":"digit"},"key_3_2":{"type":"delete","desc":["\u5220\u9664","Backspace"]}}}');
Y.set("DigitIdLayout_phone",ie);const je=JSON.parse('{"keyboardParams":{"id":3,"width":375,"height":216,"rowNumber":4,"topMargin":5,"leftMargin":3,"bottomMargin":3,"keySpacing":6,"rowSpacing":6,"isShowBubble":false,"backgroundColor":"#000000","digitTitles":"123456789X0","columns":[3,3,3,3]},"keyStyles":{"normalInputKey":{"backgroundColor":["#3A3A3A","#5A5A5A"],"color":["#FFFFFF","#FFFFFF"],"fontName":"SFUIDisplay-Regular","fontSize":24,"cornerRadius":5,"shadowColor":"#66000000","shadowOffset":"0,1"},"normalFunctionKey":{"backgroundColor":["#5A5A5A","#7A7A7A"],"color":["#FFFFFF","#FFFFFF"],"fontName":"SFUIDisplay-Regular","fontSize":18,"cornerRadius":5,"shadowColor":"#66000000","shadowOffset":"0,1"},"key_3_2":{"image":["digit_delete_normal","digit_delete_highlight"]}},"keyParams":{"normalInputKey":{"width":119,"height":47.5},"normalFunctionKey":{"width":119,"height":47.5},"key_0_0_2":{"type":"digit"},"key_1_0_2":{"type":"digit"},"key_2_0_2":{"type":"digit"},"key_3_0":{"type":"digit","positionFixed":true},"key_3_1":{"type":"digit"},"key_3_2":{"type":"delete","desc":["\u5220\u9664","Backspace"]}}}');
Y.set("DigitIdLayout_phone_dark",je);const ke=JSON.parse('{"keyboardParams":{"id":4,"width":1530,"height":496,"rowNumber":4,"topMargin":16,"leftMargin":8,"bottomMargin":8,"keySpacing":16,"rowSpacing":16,"isShowBubble":false,"backgroundColor":"#DCE0E5","digitTitles":"123456789.0","columns":[3,3,3,3]},"keyStyles":{"normalInputKey":{"backgroundColor":["#FFFFFF","#B8BDC6"],"color":["#000000","#000000"],"fontName":"SFUIDisplay-Regular","fontSize":56,"cornerRadius":5,"shadowColor":"#848689","shadowOffset":"0,1"},"normalFunctionKey":{"backgroundColor":["#B8BDC6","#A4AAB5"],"color":["#000000","#000000"],"fontName":"SFUIDisplay-Regular","fontSize":40,"cornerRadius":5,"shadowColor":"#848689","shadowOffset":"0,1"},"key_3_2":{"image":["digit_delete_normal","digit_delete_highlight"]}},"keyParams":{"normalInputKey":{"width":494,"height":106},"normalFunctionKey":{"width":494,"height":106},"key_0_0_2":{"type":"digit"},"key_1_0_2":{"type":"digit"},"key_2_0_2":{"type":"digit"},"key_3_0":{"type":"digit","positionFixed":true},"key_3_1":{"type":"digit"},"key_3_2":{"type":"delete","desc":["\u5220\u9664","Backspace"]}}}');
Y.set("DigitMoneyLayout_pad",ke);const le=JSON.parse('{"keyboardParams":{"id":4,"width":1530,"height":496,"rowNumber":4,"topMargin":16,"leftMargin":8,"bottomMargin":8,"keySpacing":16,"rowSpacing":16,"isShowBubble":false,"backgroundColor":"#000000","digitTitles":"123456789.0","columns":[3,3,3,3]},"keyStyles":{"normalInputKey":{"backgroundColor":["#3A3A3A","#5A5A5A"],"color":["#FFFFFF","#FFFFFF"],"fontName":"SFUIDisplay-Regular","fontSize":56,"cornerRadius":5,"shadowColor":"#66000000","shadowOffset":"0,1"},"normalFunctionKey":{"backgroundColor":["#5A5A5A","#7A7A7A"],"color":["#FFFFFF","#FFFFFF"],"fontName":"SFUIDisplay-Regular","fontSize":40,"cornerRadius":5,"shadowColor":"#66000000","shadowOffset":"0,1"},"key_3_2":{"image":["digit_delete_normal","digit_delete_highlight"]}},"keyParams":{"normalInputKey":{"width":494,"height":106},"normalFunctionKey":{"width":494,"height":106},"key_0_0_2":{"type":"digit"},"key_1_0_2":{"type":"digit"},"key_2_0_2":{"type":"digit"},"key_3_0":{"type":"digit","positionFixed":true},"key_3_1":{"type":"digit"},"key_3_2":{"type":"delete","desc":["\u5220\u9664","Backspace"]}}}');
Y.set("DigitMoneyLayout_pad_dark",le);const me=JSON.parse('{"keyboardParams":{"id":4,"width":375,"height":216,"rowNumber":4,"topMargin":5,"leftMargin":3,"bottomMargin":3,"keySpacing":6,"rowSpacing":6,"isShowBubble":false,"backgroundColor":"#DCE0E5","digitTitles":"123456789.0","columns":[3,3,3,3]},"keyStyles":{"normalInputKey":{"backgroundColor":["#FFFFFF","#B8BDC6"],"color":["#000000","#000000"],"fontName":"SFUIDisplay-Regular","fontSize":24,"cornerRadius":5,"shadowColor":"#848689","shadowOffset":"0,1"},"normalFunctionKey":{"backgroundColor":["#B8BDC6","#A4AAB5"],"color":["#000000","#000000"],"fontName":"SFUIDisplay-Regular","fontSize":18,"cornerRadius":5,"shadowColor":"#848689","shadowOffset":"0,1"},"key_3_2":{"image":["digit_delete_normal","digit_delete_highlight"]}},"keyParams":{"normalInputKey":{"width":119,"height":47.5},"normalFunctionKey":{"width":119,"height":47.5},"key_0_0_2":{"type":"digit"},"key_1_0_2":{"type":"digit"},"key_2_0_2":{"type":"digit"},"key_3_0":{"type":"digit","positionFixed":true},"key_3_1":{"type":"digit"},"key_3_2":{"type":"delete","desc":["\u5220\u9664","Backspace"]}}}');
Y.set("DigitMoneyLayout_phone",me);const ne=JSON.parse('{"keyboardParams":{"id":4,"width":375,"height":216,"rowNumber":4,"topMargin":5,"leftMargin":3,"bottomMargin":3,"keySpacing":6,"rowSpacing":6,"isShowBubble":false,"backgroundColor":"#000000","digitTitles":"123456789.0","columns":[3,3,3,3]},"keyStyles":{"normalInputKey":{"backgroundColor":["#3A3A3A","#5A5A5A"],"color":["#FFFFFF","#FFFFFF"],"fontName":"SFUIDisplay-Regular","fontSize":24,"cornerRadius":5,"shadowColor":"#66000000","shadowOffset":"0,1"},"normalFunctionKey":{"backgroundColor":["#5A5A5A","#7A7A7A"],"color":["#FFFFFF","#FFFFFF"],"fontName":"SFUIDisplay-Regular","fontSize":18,"cornerRadius":5,"shadowColor":"#66000000","shadowOffset":"0,1"},"key_3_2":{"image":["digit_delete_normal","digit_delete_highlight"]}},"keyParams":{"normalInputKey":{"width":119,"height":47.5},"normalFunctionKey":{"width":119,"height":47.5},"key_0_0_2":{"type":"digit"},"key_1_0_2":{"type":"digit"},"key_2_0_2":{"type":"digit"},"key_3_0":{"type":"digit","positionFixed":true},"key_3_1":{"type":"digit"},"key_3_2":{"type":"delete","desc":["\u5220\u9664","Backspace"]}}}');
Y.set("DigitMoneyLayout_phone_dark",ne);const oe=JSON.parse('{"letter":[{"fileName":"CompleteLetterLayout_small"},{"fileName":"CompleteLetterLayout_capital","parent":"CompleteLetterLayout_small"},{"fileName":"CompleteLetterLayout_capital_ferry","parent":"CompleteLetterLayout_small"}],"digit":[{"fileName":"CompleteDigitLayout"}],"symbol":[{"fileName":"CompleteSymbolLayout"}],"pureDigit":[{"fileName":"PureDigitLayout"}],"digitId":[{"fileName":"DigitIdLayout"}],"digitMoney":[{"fileName":"DigitMoneyLayout"}]}');
Y.set("index",oe);const pe=JSON.parse('{"keyboardParams":{"id":5,"width":1530,"height":496,"rowNumber":4,"topMargin":16,"leftMargin":8,"bottomMargin":8,"keySpacing":16,"rowSpacing":16,"isShowBubble":false,"backgroundColor":"#DCE0E5","digitTitles":"1234567890","columns":[3,3,3,2]},"keyStyles":{"normalInputKey":{"backgroundColor":["#FFFFFF","#B8BDC6"],"color":["#000000","#000000"],"fontName":"SFUIDisplay-Regular","fontSize":56,"cornerRadius":5,"shadowColor":"#848689","shadowOffset":"0,1"},"normalFunctionKey":{"backgroundColor":["#B8BDC6","#A4AAB5"],"color":["#000000","#000000"],"fontName":"SFUIDisplay-Regular","fontSize":40,"cornerRadius":5,"shadowColor":"#848689","shadowOffset":"0,1"},"key_3_1":{"image":["digit_delete_normal","digit_delete_highlight"]}},"keyParams":{"normalInputKey":{"width":494,"height":106},"normalFunctionKey":{"width":494,"height":106},"key_0_0_2":{"type":"digit"},"key_1_0_2":{"type":"digit"},"key_2_0_2":{"type":"digit"},"key_3_0":{"type":"digit","leftMargin":518},"key_3_1":{"type":"delete","desc":["\u5220\u9664","Backspace"]}}}');
Y.set("PureDigitLayout_pad",pe);const qe=JSON.parse('{"keyboardParams":{"id":5,"width":1530,"height":496,"rowNumber":4,"topMargin":16,"leftMargin":8,"bottomMargin":8,"keySpacing":16,"rowSpacing":16,"isShowBubble":false,"backgroundColor":"#000000","digitTitles":"1234567890","columns":[3,3,3,2]},"keyStyles":{"normalInputKey":{"backgroundColor":["#3A3A3A","#5A5A5A"],"color":["#FFFFFF","#FFFFFF"],"fontName":"SFUIDisplay-Regular","fontSize":56,"cornerRadius":5,"shadowColor":"#66000000","shadowOffset":"0,1"},"normalFunctionKey":{"backgroundColor":["#5A5A5A","#7A7A7A"],"color":["#FFFFFF","#FFFFFF"],"fontName":"SFUIDisplay-Regular","fontSize":40,"cornerRadius":5,"shadowColor":"#66000000","shadowOffset":"0,1"},"key_3_1":{"image":["digit_delete_normal","digit_delete_highlight"]}},"keyParams":{"normalInputKey":{"width":494,"height":106},"normalFunctionKey":{"width":494,"height":106},"key_0_0_2":{"type":"digit"},"key_1_0_2":{"type":"digit"},"key_2_0_2":{"type":"digit"},"key_3_0":{"type":"digit","leftMargin":518},"key_3_1":{"type":"delete","desc":["\u5220\u9664","Backspace"]}}}');
Y.set("PureDigitLayout_pad_dark",qe);const re=JSON.parse('{"keyboardParams":{"id":5,"width":375,"height":216,"rowNumber":4,"topMargin":5,"leftMargin":3,"bottomMargin":3,"keySpacing":6,"rowSpacing":6,"isShowBubble":false,"backgroundColor":"#DCE0E5","digitTitles":"1234567890","columns":[3,3,3,2]},"keyStyles":{"normalInputKey":{"backgroundColor":["#FFFFFF","#B8BDC6"],"color":["#000000","#000000"],"fontName":"SFUIDisplay-Regular","fontSize":24,"cornerRadius":5,"shadowColor":"#848689","shadowOffset":"0,1"},"normalFunctionKey":{"backgroundColor":["#B8BDC6","#A4AAB5"],"color":["#000000","#000000"],"fontName":"SFUIDisplay-Regular","fontSize":18,"cornerRadius":5,"shadowColor":"#848689","shadowOffset":"0,1"},"key_3_1":{"image":["digit_delete_normal","digit_delete_highlight"]}},"keyParams":{"normalInputKey":{"width":119,"height":47.5},"normalFunctionKey":{"width":119,"height":47.5},"key_0_0_2":{"type":"digit"},"key_1_0_2":{"type":"digit"},"key_2_0_2":{"type":"digit"},"key_3_0":{"type":"digit","leftMargin":128},"key_3_1":{"type":"delete","desc":["\u5220\u9664","Backspace"]}}}');
Y.set("PureDigitLayout_phone",re);const se=JSON.parse('{"keyboardParams":{"id":5,"width":375,"height":216,"rowNumber":4,"topMargin":5,"leftMargin":3,"bottomMargin":3,"keySpacing":6,"rowSpacing":6,"isShowBubble":false,"backgroundColor":"#000000","digitTitles":"1234567890","columns":[3,3,3,2]},"keyStyles":{"normalInputKey":{"backgroundColor":["#3A3A3A","#5A5A5A"],"color":["#FFFFFF","#FFFFFF"],"fontName":"SFUIDisplay-Regular","fontSize":24,"cornerRadius":5,"shadowColor":"#66000000","shadowOffset":"0,1"},"normalFunctionKey":{"backgroundColor":["#5A5A5A","#7A7A7A"],"color":["#FFFFFF","#FFFFFF"],"fontName":"SFUIDisplay-Regular","fontSize":18,"cornerRadius":5,"shadowColor":"#66000000","shadowOffset":"0,1"},"key_3_1":{"image":["digit_delete_normal","digit_delete_highlight"]}},"keyParams":{"normalInputKey":{"width":119,"height":47.5},"normalFunctionKey":{"width":119,"height":47.5},"key_0_0_2":{"type":"digit"},"key_1_0_2":{"type":"digit"},"key_2_0_2":{"type":"digit"},"key_3_0":{"type":"digit","leftMargin":128},"key_3_1":{"type":"delete","desc":["\u5220\u9664","Backspace"]}}}');
Y.set("PureDigitLayout_phone_dark",se);function te(a,b,c,d){c=void 0===c?!1:c;d=void 0===d?!1:d;a=a.a[b];b=[];for(let f=0;f<a.length;f++){var e=a[f];if(e.parent){const g=Y.get(`${e.parent}${d?"_pad":"_phone"}${c?"_dark":""}`),h=Y.get(`${e.fileName}${d?"_pad":"_phone"}${c?"_dark":""}`),l={};Object.keys(h.keyStyles).forEach(m=>{l[m]=Object.assign({},g.keyStyles[m],h.keyStyles[m])});const n={};Object.keys(h.keyParams).forEach(m=>{n[m]=Object.assign({},g.keyParams[m],h.keyParams[m])});b[f]={keyboardParams:Object.assign({},g.keyboardParams,
h.keyboardParams),keyStyles:Object.assign({},g.keyStyles,l),keyParams:Object.assign({},g.keyParams,n)}}else e=Y.get(`${e.fileName}${d?"_pad":"_phone"}${c?"_dark":""}`),b[f]=Object.assign({},e)}return b}class ue{constructor(){this.a=Y.get("index")}};const ve=(a,b,c,d)=>{const e=[];a=te(a,b,1===c,d);for(d=0;d<a.length;d++){var f=a[d];f=new Ad({X:b,Qb:f.keyboardParams,Pb:f.keyStyles,h:f.keyParams,S:c});e.push(f)}return e};function we(a){return a.cfcakeyboardProperty.isEncrypt}function xe(a){a.cfcakeyboardProperty.isKeyFeedBack&&wx.vibrateShort&&wx.vibrateShort({type:"medium"})}
function ye(a,b){const c=b.target;if(a.currentKeyboardModel.m.id!==c){var d=Object.assign({},a.currentKeyboardModel);a.currentKeyboardModel=a.keyboardModels.find(e=>e.m.id===c);"capslock"===b.f||"ferry_letter"===b.f?yd(a.currentKeyboardModel,d):wd(a.currentKeyboardModel,a._getRandomType());ze(a,!0)}}function Ae(a,b){a.translateY(b).translateY(0).step()}
function ze(a,b){b?(a.setData({backgroundColor:a.currentKeyboardModel.m.backgroundColor}),b=a.cfcaKeyboardView,b.g=a.currentKeyboardModel,b.P=[],b.Ha=!0,Dd(b)):(b=a.cfcaKeyboardView,b.P=[],b.Ha=!0,Dd(b));b=a._getKeyAnimation();a.cfcaKeyboardView.N=b;Be(a)}
function Be(a){if(!a.data.isIOS){wx.setVisualEffectOnCapture&&wx.setVisualEffectOnCapture({["visualEffect"]:"hidden"});console.log('sip min js load and run')}else if(a.data.isIOS&&(wx.getScreenRecordingState&&wx.getScreenRecordingState({["success"]:b=>{"on"==b.state&&(a.cfcaKeyboardView.N=!1,a.setData({isEditable:a.cfcakeyboardProperty.allowScreenRecord}))}}),wx.onScreenRecordingStateChanged))wx.onScreenRecordingStateChanged(b=>{"start"==b.state?(a.cfcaKeyboardView.N=!1,a.setData({isEditable:a.cfcakeyboardProperty.allowScreenRecord})):
(b=a._getKeyAnimation(),a.cfcaKeyboardView.N=b,a.setData({isEditable:!0}))})}function Ce(a,b){a.translateY(0).translateY(b).step()}function De(a,b,c,d){switch(b){case 1:b=ve(a.keyboardParse,"letter",c,d);const e=ve(a.keyboardParse,"digit",c,d);a=ve(a.keyboardParse,"symbol",c,d);return[...b,...e,...a];case 0:return ve(a.keyboardParse,"pureDigit",c,d);case 3:return ve(a.keyboardParse,"digitId",c,d);case 4:return ve(a.keyboardParse,"digitMoney",c,d)}}
goog.exportSymbol("KeyboardController",{life:{create(a){a.keyboardParse=new ue;a.keyboardModels=null;a.currentKeyboardModel=null;a.cfcaKeyboardView=null;a.lastCharacterTimeout=null;a.precision=2;a.deviceInfo=md();a.cfcakeyboardProperty=new Md;a.isInitializing=!1;a.intoAnimation=wx.createAnimation({duration:150,timingFunction:"ease-out"});a.outAnimation=wx.createAnimation({duration:150,ec:"ease-out"});a.vb={zb(b){a.data.text===
a.data.placeholder&&a.setData({text:""});a.setData({dc:"sip-input-focus focus-class"});const c=a.data.text.length;if(!(c>=a._getMaxLength())){var d=a._getInputRegex();if(null==d||jd(b,d))if(we(a))a.s&&0==Ic(a.s.C,b)&&(a._isShowLastCharacter()?(a.lastCharacterTimeout&&(clearTimeout(a.lastCharacterTimeout),a.lastCharacterTimeout=null),a.setData({text:id(c)+b}),a.lastCharacterTimeout=setTimeout(()=>{a.setData({text:id(c+1)})},1E3)):a.setData({text:a.data.text+"\u2022"}),xe(a),a.triggerEvent("inputChangeCallBack",
{sipId:a.data.sipId,length:a.data.text.length,changeType:1}),a.setData({scrollLeft:1E3}));else if(3!=a.data.keyboardType||!(17!=c&&"X"==b||18<=c)){if(4==a.data.keyboardType){if(0==c&&"."==b||1==c&&"0"==a.data.text[0]&&"."!=b||0==a.precision&&"."==b)return;d=a.data.text.indexOf(".");if(-1!=d&&"."==b||-1!=d&&c-d>a.precision)return}a.setData({text:a.data.text+b});a.setData({scrollLeft:1E3})}}},Vb(){if(a.data.text!==a.data.placeholder&&(a.lastCharacterTimeout&&(clearTimeout(a.lastCharacterTimeout),a.lastCharacterTimeout=
null),a.s&&0==a.s.delete())){var b=a.data.text.substring(0,a.data.text.length-1),c=!a.data.isGrid&&a.data.placeholder;xe(a);a.setData({text:b?b:c?a.data.placeholder:b},()=>{a.triggerEvent("inputChangeCallBack",{sipId:a.data.sipId,length:a.data.text.length,changeType:2})})}},yb(b){ye(a,b)},Ub(){a.doneClick()}};a.ca={show(b,c){b.frame.y+=a.data.bottomPadding;a.setData({bubbleShowText:c,bubbleStyle:b.frame})},pb(){a.setData({bubbleShowText:""})}}},Zb(a){a._initKeyboard(a.deviceInfo)},ready(a){a.s||(a.s=
new Uc(a.data.sipId));a.currentKeyboardModel&&a.setData({["canvasWidth"]:a.currentKeyboardModel.m.width,["canvasHeight"]:a.currentKeyboardModel.m.height,["backgroundColor"]:a.currentKeyboardModel.m.backgroundColor});a.setData({["bottomPadding"]:a.deviceInfo.bottomPadding});const b=wx.getSystemInfoSync().system.toLowerCase();let c=!1;c=-1<b.indexOf("windows")||-1<b.indexOf("macos")?!0:!1;let d=!1;d=-1<b.indexOf("ios")?!0:!1;a.data.placeholder&&!a.data.isGrid&&a.setData({["text"]:a.data.placeholder});
a.setData({isPC:c,isIOS:d,init:!0,isShowKeyboard:!1,isEditable:!0},()=>{a.isInitializing||this.cfcaKeyboardView||a._initCanvas()})}},methods:{moveCatch(){},bindtouchstart(a){if(this.data.isEditable){var b=this.cfcaKeyboardView;a=a.changedTouches;for(let c=0;c<a.length;c++){const d=a[c],e=xd(b.g,d.x-b.Y.x,d.y-b.Y.y);b.ga=d.identifier;Jd(b,b.w);b.w=e;Kd(b,e)}}},bindtouchmove(a){if(this.data.isEditable){var b=this.cfcaKeyboardView;a=a.changedTouches;for(let e=0;e<a.length;e++){var c=a[e];if(b.ga!=c.identifier)continue;
const f=xd(b.g,c.x-b.Y.x,c.y-b.Y.y);if(b.w!=f){c=b;var d=b.w;d&&(d.a=!1,Fd(c,d),c.ca.pb());b.w=f;Kd(b,f)}}}},bindtouchend(a){var b=this.cfcaKeyboardView;a=a.changedTouches;for(let c=0;c<a.length;c++)if(b.ga==a[c].identifier){Jd(b,b.w);b.ga=null;b.w=null;break}},bindtouchcancel(){var a=this.cfcaKeyboardView;Jd(a,a.w);a.ga=null;a.w=null},inputClick(){this.triggerEvent("inputClick",{sipId:this.data.sipId,keyboardHeight:this.getKeyboardHeight()})},showKeyboard(){this.triggerEvent("showKeyboard",{sipId:this.data.sipId,
keyboardHeight:this.getKeyboardHeight()});this.data.text===this.data.placeholder&&this.setData({text:""});Ae(this.intoAnimation,this.getKeyboardHeight());this.cfcaKeyboardView?this.setData({isShowKeyboard:!0,animationData:this.intoAnimation["export"](),textClass:"sip-input-focus focus-class"},()=>{this.currentKeyboardModel!=this.keyboardModels[0]?(this.currentKeyboardModel=this.keyboardModels[0],wd(this.currentKeyboardModel,this._getRandomType()),ze(this,!0)):(wd(this.currentKeyboardModel,this._getRandomType()),
ze(this))}):this.setData({init:!0,isShowKeyboard:!0,animationData:this.intoAnimation["export"](),textClass:"sip-input-focus focus-class"},()=>{this._initCanvas()})},hideKeyboard(){this.setData({isShowKeyboard:!1});if(this.data.isIOS){wx.offScreenRecordingStateChanged&&wx.offScreenRecordingStateChanged();var a=this._getKeyAnimation();this.cfcaKeyboardView.N=a;this.setData({isEditable:!0})}else!this.data.isIOS&&wx.setVisualEffectOnCapture&&wx.setVisualEffectOnCapture({["visualEffect"]:"none"})},doneClick(){Ce(this.outAnimation,
this.getKeyboardHeight());this.setData({animationData:this.outAnimation["export"]()},()=>{this.triggerEvent("doneClick",{sipId:this.data.sipId})});setTimeout(()=>{this.setData({isShowKeyboard:!1})},150)},_initCanvas(){if(this.data.isPC){if(!this.cfcaKeyboardView&&!this.isInitializing){this.isInitializing=!0;var a=wx.createCanvasContext(this.data.canvasId,this);wd(this.currentKeyboardModel,this._getRandomType());this.cfcaKeyboardView=new Ld(this.currentKeyboardModel,a,null);dd(this.cfcaKeyboardView,
this._getKeyAnimation());this.cfcaKeyboardView.ea=this.vb;this.cfcaKeyboardView.ca=this.ca;this.isInitializing=!1}}else wx.createSelectorQuery()["in"](this).select("#"+this.data.canvasId).fields({node:!0,size:!0}).exec(b=>{if(!this.cfcaKeyboardView&&!this.isInitializing){this.isInitializing=!0;b=b[0].node;var c=b.getContext("2d"),d=wx.getSystemInfoSync().pixelRatio;b.width=this.data.canvasWidth*d;b.height=this.data.canvasHeight*d;b.left=0;b.top=0;c.scale(d,d);wd(this.currentKeyboardModel,this._getRandomType());
this.cfcaKeyboardView=new Ld(this.currentKeyboardModel,c,b);b=this._getKeyAnimation();this.cfcaKeyboardView.N=b;this.cfcaKeyboardView.ea=this.vb;this.cfcaKeyboardView.ca=this.ca;this.isInitializing=!1;this.triggerEvent("keyboardInit",{sipId:this.data.sipId});Be(this)}})},_initKeyboard(a){this.keyboardModels=De(this,this.data.keyboardType,this.data.displayMode,a.isPad);this.currentKeyboardModel=this.keyboardModels[0]},_updateOrderType(a){this._setRandomType(a)},_setRandomType(a){this.cfcakeyboardProperty.disorderType=
a;return 0},_getRandomType(){return this.cfcakeyboardProperty.disorderType},_setDisplayMode(a){const b=this.cfcakeyboardProperty;b.displayMode!==a&&(b.displayMode=a,a=this.getKeyboardProperty(),this.keyboardModels=De(this,this.data.keyboardType,a.displayMode,this.deviceInfo.isPad),this.currentKeyboardModel=this.keyboardModels[0],this.cfcaKeyboardView&&(wd(this.currentKeyboardModel,this._getRandomType()),ze(this,!0)),this.setData({backgroundColor:this.currentKeyboardModel.m.backgroundColor}))},_setOperationKeyboardBackground(a){const b=
this.cfcakeyboardProperty;if(1==a.length&&"all"==a[0].keyType){const c=a[0].normalColor;a=a[0].pressColor;b.functionBackgroundList.set("capslock",[c,a]);b.functionBackgroundList.set("delete",[c,a]);b.functionBackgroundList.set("switch",[c,a]);b.functionBackgroundList.set("back",[c,a]);b.functionBackgroundList.set("done",[c,a]);b.functionBackgroundList.set("space",[c,a])}else a.forEach(c=>{b.functionBackgroundList.set(c.f,[c.bc,c.cc])});this.keyboardModels.forEach(c=>{const d=c.u;c=c.La;var e=this.cfcakeyboardProperty.functionBackgroundList;
if(0<e.size&&0<c.size)for(const g of e){const [h,l]=g;e=c.get(h);e=d.get(e);if(e instanceof ad){var f=l;e.j.backgroundColor[0]=f[0];e.j.backgroundColor[1]=f[1]}}})},_setDoneKeyText(a,b,c){const d=this.cfcakeyboardProperty;a&&(d.doneText=a);b&&c&&(d.doneTextNormalColor=b,d.doneTextPressColor=c);this.keyboardModels.forEach(e=>{var f=e.u;e=e.La.get("done");f=f.get(e);f instanceof ad&&(f.j.title=d.doneText,d.doneTextNormalColor&&(e=d.doneTextPressColor,f.j.color[0]=d.doneTextNormalColor,f.j.color[1]=
e))})},_setBackKeyText(a,b,c){const d=this.cfcakeyboardProperty;a&&(d.backText=a);b&&c&&(d.backTextNormalColor=b,d.backTextPressColor=c);this.keyboardModels.forEach(e=>{var f=e.u;e=e.La.get("back");f=f.get(e);f instanceof ad&&(f.j.title=d.backText,d.backTextNormalColor&&(e=d.backTextPressColor,f.j.color[0]=d.backTextNormalColor,f.j.color[1]=e))})},_showLastCharacter(a){this.cfcakeyboardProperty.lastShow=a},_isShowLastCharacter(){return this.cfcakeyboardProperty.lastShow},_setKeyAnimation(a){this.cfcakeyboardProperty.keyAnimation=
a},_getKeyAnimation(){return this.cfcakeyboardProperty.keyAnimation},_setServerRandom(a){if(this.s){var b=this.s.C;if("undefined"===typeof W[b])b=4098;else{var c=yb(a),d,e=[];for(d=0;2*d<c.length;++d)e[d]=parseInt(c.substring(2*d,2*d+2),16);16>e.length?(W[b].J=null,b=4101):(W[b].J=e,b=0)}if(0!=b)this.onError(b);this.cfcakeyboardProperty.serverRandom=a;return 0}},_getServerRandom(){return this.cfcakeyboardProperty.serverRandom},_setEncryptState(a){this.clearInputValue();this.cfcakeyboardProperty.isEncrypt=
a},getEncryptState(){return we(this)},_setCipherType(a){var b=this.s.C;"undefined"===typeof W[b]?b=4098:1!=a&&0!=a?b=4097:(W[b].W=a,b=0);if(0!=b)this.onError(b);this.cfcakeyboardProperty.cipherType=a},_getCipherType(){return this.cfcakeyboardProperty.cipherType},_setOutputType(a){var b=this.s.C;"undefined"===typeof W[b]?b=4098:1!=a&&2!=a?b=4097:(W[b].va=a,b=0);if(0!=b)this.onError(b);this.cfcakeyboardProperty.outputType=a;return 0},_getOutputType(){return this.cfcakeyboardProperty.outputType},_setMaxLength(a){var b=
this.s.C;"undefined"===typeof W[b]?b=4098:0>a?b=4097:(W[b].maxLength=a,b=0);if(0!=b)this.onError(b);this.cfcakeyboardProperty.maxLength=a;return 0},_getMaxLength(){return this.cfcakeyboardProperty.maxLength},_setMinLength(a){var b=this.s.C;"undefined"===typeof W[b]?b=4098:0>a?b=4097:(W[b].cb=a,b=0);if(0!=b)this.onError(b);this.cfcakeyboardProperty.minLength=a;return 0},_getMinLength(){return this.cfcakeyboardProperty.minLength},_setInputRegex(a){var b=this.s.C;"undefined"===typeof W[b]?b=4098:(null==
a||"string"==typeof a&&0==a.length?W[b].Na=null:W[b].Na=a,b=0);if(0!=b)this.onError(b);this.cfcakeyboardProperty.inputRegex=a;return 0},_getInputRegex(){return this.cfcakeyboardProperty.inputRegex},_setKeywords(a){var b=this.s.C;"undefined"===typeof W[b]?b=4098:null==a||""==a?b=4097:(W[b].fa=[].concat(a),b=0);if(0!=b)this.onError(b);this.cfcakeyboardProperty.keywords=a},getKeywords(){return this.cfcakeyboardProperty.keywords},clearInputValue(){null!=this.lastCharacterTimeout&&(clearTimeout(this.lastCharacterTimeout),
this.lastCharacterTimeout=null);this.s.clear();this.setData({text:""})},getKeyboardProperty(){return this.cfcakeyboardProperty},getEncryptedInputValue(){const a=Qc(this.s.C);return{errorCode:this.getSipHandleErrorCode(),data:a}},getEncryptedClientRandom(){const a=this.s.getEncryptedClientRandom();return{errorCode:this.getSipHandleErrorCode(),data:a}},getEncodedCipher(){const a=this.s.getEncodedCipher(JSON.stringify({sdkVersion:"WeChat_*******"}));return{errorCode:this.getSipHandleErrorCode(),data:a}},
checkInputValueMatch(a){return Sc(this.s.C,a)},getCipherAttributes(){if(we(this)){var a=this.s.C;if("undefined"===typeof W[a])var b=4098;else{b=0<W[a].Sa;var c=0<W[a].Oa,d=0<W[a].Pa,e=0<W[a].Ga,f=2==W[a].sa.length||2==W[a].ta.length,g=2==W[a].wa.length,h=Tc(a,W[a].sa),l=Tc(a,W[a].ta);h=Math.max(h,l);a=Tc(a,W[a].wa);b=[c,b,d,e,f,g,h,a]}}else b=[];return b},getSipHandleErrorCode(){var a=this.s.C;return"undefined"===typeof W[a]?4098:W[a].errorCode},onError(a){this.triggerEvent("onError",{errorCode:a})},
getKeyboardHeight(){return this.data.bottomPadding+this.data.canvasHeight},getInputLength(){return this.data.text===this.data.placeholder?0:this.data.text.length},getInputValue(){return we(this)?"":this.data.text},getVersion(){return"*******"}},observers:{sipId:function(a){a&&(this.s=new Uc(this.data.sipId),this.setData({canvasId:a+"canvas"}))},serverRandom:function(a){a&&this._setServerRandom(a)},cipherType:function(a){0!=a&&1!=a||this._setCipherType(a)},outputType:function(a){1!=a&&2!=a||this._setOutputType(a)},
inputRegex:function(a){a&&this._setInputRegex(a)},keyword:function(a){0<a.length&&this._setKeywords(a)},isEncryptState:function(a){this._setEncryptState(a)},displayMode:function(a){if(1==a||0==a)this._setDisplayMode(a),1==a?this.setData({bubbleClass:"bubble-dark"}):this.setData({bubbleClass:"bubble"})},orderType:function(a){2!=a&&0!=a&&1!=a||this._updateOrderType(a)},minLength:function(a){0<a&&this._setMinLength(a)},maxLength:function(a){0<a&&this._setMaxLength(a)},isShowKeyboard:function(a){a?this.setData({keyboardTop:""}):
(this.triggerEvent("hideKeyboard",{sipId:this.data.sipId,keyboardHeight:this.getKeyboardHeight()}),this.setData({textClass:this.data.placeholder?"sip-input-placeholder placeholder-class":"sip-input-normal text-class",keyboardTop:"top:9999rpx;"}))},operationKey:function(a){0<a.length&&this._setOperationKeyboardBackground(a)},doneKey:function(a){a&&this._setDoneKeyText(a.text,a.normalColor,a.pressColor)},backKey:function(a){a&&this._setBackKeyText(a.text,a.normalColor,a.pressColor)},isKeyAnimation:function(a){this._setKeyAnimation(a)},
allowScreenRecord:function(a){this.cfcakeyboardProperty.allowScreenRecord=a},isKeyFeedBack:function(a){this.cfcakeyboardProperty.isKeyFeedBack=a},showLastCharacter:function(a){this._showLastCharacter(a)},precision:function(a){if("number"!==typeof a||!a&&0!==a||0>a)this.onError(4097);this.precision=a},text:function(a){a&&a===this.data.placeholder&&this.setData({textClass:"sip-input-placeholder placeholder-class"})}}});if(void 0==Z)var Z={};Z.O={};Z.O.jb={};Z.O.jb.stringify=function(a){for(var b=[],c=0;c<a.length;c++)b.push((a[c]>>>4).toString(16)),b.push((a[c]&15).toString(16));return b.join("")};Z.O.jb.parse=function(a){for(var b=[],c=0;c<a.length;c+=2)b.push(parseInt(a.substring(c,c+2),16));return b};Z.O.Ia={};Z.O.Ia.stringify=function(a){for(var b=[],c=0;c<a.length;c++)b.push(String.fromCharCode(a[c]));return b.join("")};Z.O.Ia.parse=function(a){for(var b=[],c=0;c<a.length;c++)b.push(a.charCodeAt(c)&255);return b};
Z.O.lb={};Z.O.lb.stringify=function(a){try{return decodeURIComponent(escape(Z.O.Ia.stringify(a)))}catch(b){throw Error("Malformed UTF-8 data");}};Z.O.lb.parse=function(a){return Z.O.Ia.parse(unescape(encodeURIComponent(a)))};
module.exports = {...goog.global};