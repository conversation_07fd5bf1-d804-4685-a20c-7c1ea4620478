import { useEffect, useRef, useState } from 'react';
import { View } from '@tarojs/components';
import Popup from '~components/Popup';
import styles from './index.module.scss';
import CySafeKeyboard, { CySafeKeyboardRef } from '~components/CySafeKeyboard';
import { getRandomCode } from '~services/password';
import { router } from '~utils/common';
import Taro, { useDidShow } from '@tarojs/taro';

interface PasswordCompleteParams {
  tradePassword: string;
  tradeRandomCode: string;
}

interface PasswordProps {
  visible: boolean;
  onClose?: () => void;
  onComplete?: (params: PasswordCompleteParams) => void;
}


export default ({ visible, onComplete, onClose }: PasswordProps) => {
  const keyboardRef = useRef<CySafeKeyboardRef>(null);

  const [passwordRandomCode, setPasswordRandomCode] = useState(''); // 密码键盘随机码

  // 获取密码键盘随机码
  const getRandom = async () => {
    try {
      const res = await getRandomCode();
      setPasswordRandomCode(res);
    } catch (error) {
      console.log('请求错误', error);
    }
  };

  useEffect(() => {
    getRandom()
  }, []);

  // 使用 useDidShow 钩子监听页面显示事件
  useDidShow(() => {
    if (visible) {
      getRandom();
    }
  });

  const goResetPassword = () => {
    router.push({
      url: '/modules/user/set-password-verifycode/index?type=reset&immediate=true'
    });
  }

  const onInputChangeCallBack = (_: string) => {
    const res = keyboardRef.current?.getEncryptedInputValue();
    const randomCode = keyboardRef.current?.getEncryptedClientRandom();
    if (res?.data) {
      onComplete?.({
        tradePassword: res.data,
        tradeRandomCode: randomCode?.data || '',
      })
      setTimeout(() => {
        keyboardRef.current?.clearInputValue()
      }, 1000)
    }
  };

  const handleClose = () => {
    keyboardRef.current?.clearInputValue()
    onClose?.();
  };

  return (
    <Popup
      visible={visible}
      title='交易密码'
      showClose
      onClose={handleClose}
      closeOnMaskClick={false}
      bodyStyle={{ height: '75vh' }}
    >
      <View className={styles.passwordWarp}>
        <View className={styles.passwordContainer}>
          <View className={styles.passwordTitle}>请输入交易密码</View>
          <View className={styles.passwordNote}>请输入6位交易密码，保障资金安全</View>
        </View>
        <View className={styles.passwordInput}>
          <CySafeKeyboard
            style={{ width: '100%', height: '100rpx', lineHeight: '100rpx', background: '#F6F6F6', fontSize: '32rpx' }}
            id='password'
            sipId='password'
            ref={keyboardRef}
            isGrid
            placeholder='请输入交易密码'
            serverRandom={passwordRandomCode}
            // onDoneClick={onDoneClick}
            onInputChangeCallBack={onInputChangeCallBack}
            displayMode={0}
            maxLength={6}
            keyboardType={0}
            orderType={1} />
        </View>
        <View className={styles.passwordBottom}>不记得交易密码?<View onClick={goResetPassword} className={styles.forgetPassowrd}>忘记密码</View></View>
      </View>
    </Popup>
  )
};