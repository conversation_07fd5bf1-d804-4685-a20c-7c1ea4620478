import Taro from '@tarojs/taro';
import {AlipayReqParams, WXPayReqParams} from '.';
import {showToast} from '../common';

export default {
  wxPay: async (wxPayReqParams: WXPayReqParams) => {
    return new Promise((resolve, reject) => {
      Taro.requestPayment({
        timeStamp: wxPayReqParams.timeStamp,
        nonceStr: wxPayReqParams.nonceStr,
        package: wxPayReqParams.prepayId,
        paySign: wxPayReqParams.sign,
        signType: 'RSA',
        success(res) {
          resolve(res);
        },
        fail(res) {
          reject(res);
        },
      });
    });
  },
  alipayPay: async (alipayParams: AlipayReqParams) => {
    showToast('该端暂未实现');
    console.log('alipayPay');
    return;
  },
};
