import {View} from '@tarojs/components';
import Card from '~components/Card';
import MoneyDisplay from '~components/MoneyDisplay';
import {InfoItem} from '../LoanInfo/LoanInfo';
import styles from './RepaymentInfo.module.scss';

interface RepaymentInfoProps {
  overdueDays: number;
  expireInterest: number;
  expirePenalty: number;
}

const RepaymentInfo = ({overdueDays, expireInterest, expirePenalty}: RepaymentInfoProps) => {
  return (
    <Card
      title='逾期信息'
      className={styles.repaymentInfo}
      collapsible={true}
      type={'error'}
      defaultExpanded={true}
      titleStyle={{color: '#F5222D'}}>
      <View className={styles.content}>
        <InfoItem label='剩余天数'>
          <View className={styles.totalDays}>
            逾期
            <View style={{color: '#F5222D', marginLeft: '5px', marginRight: '5px'}}>{overdueDays ?? 0}</View>天
          </View>
        </InfoItem>
        <InfoItem label='本金逾期后利息'>
          <MoneyDisplay money={expireInterest} size='form' currency={true} />
        </InfoItem>
        <InfoItem label='逾期罚息'>
          <MoneyDisplay money={expirePenalty} size='form' currency={true} />
        </InfoItem>
      </View>
    </Card>
  );
};

export default RepaymentInfo;
