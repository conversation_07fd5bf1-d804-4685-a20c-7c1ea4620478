import {FC, ForwardRefRenderFunction, ReactNode, forwardRef, useImperativeHandle} from 'react';
import {FlatListProps} from 'react-native';
import {ScrollView, View} from '@tarojs/components';
import './index.scss';

interface Props {
  data?: any[];
  ListFooterComponent?: ReactNode;
  ListEmptyComponent?: ReactNode;
}
export interface CyFlatListRef {
  scrollToTop: () => void;
}
/**
 * 组件名称：长列表
 * 组件描述：长列表
 */
const CyFlatList: ForwardRefRenderFunction<CyFlatListRef, Props & FlatListProps<any>> = (props, ref) => {
  useImperativeHandle(ref, () => ({
    scrollToTop: () => {},
  }));
  return (
    <ScrollView>
      {props.data &&
        props.data.map((item, index) => (
          <View key={props.keyExtractor ? props.keyExtractor(item, index) : index}>
            {props.renderItem &&
              props.renderItem({
                item,
                index,
                separators: {
                  highlight: () => {},
                  unhighlight: () => {},
                  updateProps: (select: 'leading' | 'trailing', newProps: any) => {},
                },
              })}
          </View>
        ))}
      {props.data?.length === 0 && props.ListEmptyComponent}
      {props.ListFooterComponent}
    </ScrollView>
  );
};

export default forwardRef(CyFlatList);
