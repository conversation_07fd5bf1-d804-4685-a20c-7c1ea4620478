.bankContainer {
  padding: 0 20px 20px 20px;
  background-color: #fff;
}
.tableHeader {
  position: sticky;
  top: 0;
  display: flex;
  align-items: center;
  width: 100%;
  height: 80px;
  background-color: #eee;
  color: $color-text-secondary;
  font-size: $font-size-sm;
}
.tableBody {
  .tableRow {
    display: flex;
    align-items: center;
    color: $color-text-normal;
    font-size: $font-size-normal;
    width: 100%;
    height: 100px;
    &:nth-child(odd) {
      background-color: #F6F8FF;
      border-radius: 8px;
    }
    .bankImg {
      width: 40px;
      height: 40px;
      margin-right: 11px;
    }
  }
}

.rowFirst {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 42%;
}
.rowSencond {
  width: 29%;
  text-align: center;
}
.rowName {
  justify-content: flex-start;
  padding-left: 40px;
}
.bankName {
  justify-content: flex-start;
  padding-left: 70px;

}