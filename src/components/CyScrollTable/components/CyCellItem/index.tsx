import {FC, isValidElement, memo} from 'react';
import {Image, StyleProp, StyleSheet, Text, TouchableOpacity, View} from 'react-native';
import Taro from '@tarojs/taro';
import {IColumns} from '../../index.rn';

type Props = {
  content: string | number | React.ReactNode; //显示内容
  isImage?: boolean; //是否为纯图片
  cellHeight?: number; //高度
  cell: IColumns;
  dataSource: any;
  contentStyle?: StyleProp<any>; //内容style
  customerStyle?: StyleProp<any>; //自定义style
  rowIndex?: number; //行数
  onColumnsClick?: (dateIndex: string) => void; //头部点击
  onAction?: (info: Record<string, number | string | boolean>, flag: string, index: number) => void; //cell点击
};
const CyCellItem: FC<Props> = props => {
  const {
    content,
    isImage,
    cellHeight = 50,
    cell,
    dataSource: item,
    contentStyle,
    customerStyle,
    rowIndex,
    onColumnsClick,
    onAction,
  } = props;
  //点击事件
  const cellItemClick = () => {
    if (onColumnsClick) {
      onColumnsClick && onColumnsClick(cell.dataIndex);
    }

    if (onAction) {
      if (cell.isAction) {
        // 如果是操作事件

        let canAction = false;
        if (cell.dataIndex !== '') {
          const auditFlag = cell.dataIndex;
          if (!item[auditFlag]) {
            canAction = true;
          }
        } else {
          canAction = true;
        }
        canAction && onAction && onAction(item, cell.actionCode!, rowIndex!);
        return;
      }

      if (cell.canCall) {
        // if (encrypt || !isShow(rowIndex)) {
        //   return;
        // }
        // 拨打电话
        const phoneNumber = item[cell.dataIndex];
        Taro.makePhoneCall({
          phoneNumber,
        });
        return;
      }

      if (cell.canDetail) {
        console.log('详情页');
        onAction && onAction(item, 'detail', rowIndex!);
        return;
      }
    }
  };

  const getRenderResult = () => {
    if (isValidElement(content)) {
      return content;
    } else {
      if (isImage && typeof content === 'number') {
        return <Image source={content} style={{...contentStyle, ...customerStyle}} />;
      } else {
        return <Text style={{...contentStyle, ...customerStyle}}>{content}</Text>;
      }
    }
  };
  return (
    <TouchableOpacity activeOpacity={1} onPress={cellItemClick}>
      <View
        style={[
          styles.tableCell,
          {
            width: cell.width ? cell.width / 2 : 90,
            height: cellHeight,
          },
        ]}>
        {getRenderResult()}
      </View>
    </TouchableOpacity>
  );
};

export default memo(CyCellItem);
const styles = StyleSheet.create({
  tableCell: {
    // padding: 10,
    justifyContent: 'center',
    alignItems: 'center',
    height: 50,
    color: '#fff',
    borderRightColor: '#fff',
    borderRightWidth: 1,
  },

  actionText: {
    color: '#4D7BFF',
  },
});
