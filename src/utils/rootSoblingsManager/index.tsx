import React, {FC, ReactNode} from 'react';
import RnRootSiblingsManager, {RootSiblingParent as RnRootSiblingParent} from 'react-native-root-siblings';
import {View} from '@tarojs/components';
/*  #ifdef  WEAPP */
const WeAppRootSiblingParent = ({children}: {children: ReactNode}) => {
  return <>
    <View>{children}</View>
    <View>其他</View>
  </>;
};


class WeAppRootSiblingsManager{
  constructor(element: ReactNode, callback?: (() => void) | undefined) {
    console.log('RootSiblingsManager');
    return
  }
}
/*  #endif  */

const RootSiblingParent = WeAppRootSiblingParent || RnRootSiblingParent;
const RootSiblingsManager = WeAppRootSiblingsManager || RnRootSiblingsManager;
export default RnRootSiblingsManager;
export {RootSiblingParent};
