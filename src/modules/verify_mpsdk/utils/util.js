const regeneratorRuntime = require("./regenerator-runtime/runtime");

/**
 * 比较两个版本号
 * @param {string} v1 - 第一个版本号
 * @param {string} v2 - 第二个版本号
 * @returns {number} 比较结果：1 如果 v1 > v2, -1 如果 v1 < v2, 0 如果相等
 */
function compareVersion(v1, v2) {
  v1 = v1.split(".");
  v2 = v2.split(".");
  const len = Math.max(v1.length, v2.length);

  while (v1.length < len) {
    v1.push("0");
  }
  while (v2.length < len) {
    v2.push("0");
  }

  for (let i = 0; i < len; i++) {
    const num1 = parseInt(v1[i]);
    const num2 = parseInt(v2[i]);

    if (num1 > num2) {
      return 1;
    } else if (num1 < num2) {
      return -1;
    }
  }

  return 0;
}

/**
 * 发送请求Promise
 * @param {Object} options - 请求选项
 * @param {string} options.url - 请求URL
 * @param {string} [options.method="POST"] - 请求方法
 * @param {Object} [options.data] - 请求数据
 * @param {Object} [options.header={"Content-Type": "application/json"}] - 请求头
 * @returns {Promise} 请求Promise
 */
let requestPromise = function (options) {
  let { url, method = "POST", data, header = { "Content-Type": "application/json" } } = options;
  console.log("requestPromise start:", url, data);
  return new Promise((resolve, reject) => {
    wx.request({
      url,
      method,
      data,
      header,
      success(res) {
        console.log("requestPromise success:", res);
        resolve(res);
      },
      fail(err) {
        console.log("requestPromise error:", err);
        reject(err);
      },
    });
  });
};

/**
 * 发送请求
 * @param {Object} options - 请求选项
 * @param {string} options.url - 请求URL
 * @param {string} [options.method="POST"] - 请求方法
 * @param {Object} [options.data] - 请求数据
 * @param {Object} [options.header={"Content-Type": "application/json"}] - 请求头
 * @param {Function} callback - 回调函数
 */
function request(options, callback) {
  let { url, method = "POST", data, header = { "Content-Type": "application/json" } } = options;
  console.log("requestPromise start:", url, data);
  try {
    wx.request({
      url,
      method,
      data,
      header,
      success(res) {
        console.log("request success:", res);
        if (res.statusCode === 200 && res.data) {
          if (res.data.ErrorCode === 0) {
            callback({ ErrorCode: 0, Data: res.data.Data });
          } else {
            callback({
              ErrorCode: res.data.ErrorCode,
              ErrorMsg: res.data.ErrorMsg,
              Data: res.data.Data,
            });
          }
        } else {
          callback({ ErrorCode: -107, ErrorMsg: "request请求异常，请稍后重试" });
        }
      },
      fail(err) {
        console.log("request error:", err);
        if (
          err.errMsg.indexOf("request:fail Unable to resolve host") >= 0 ||
          err.errMsg.indexOf("request:fail 似乎已断开与互联网的连接") >= 0
        ) {
          callback({ ErrorCode: 101, ErrorMsg: "网络异常，请稍后重试" });
        } else {
          callback({ ErrorCode: -107, ErrorMsg: "request请求异常，请稍后重试" });
        }
      },
    });
  } catch (err) {
    console.log("request error:", err);
    callback({ ErrorCode: -107, ErrorMsg: "request请求异常" });
  }
}

/**
 * 上传文件
 * @param {Object} options - 上传选项
 * @param {string} options.url - 上传URL
 * @param {string} options.filePath - 文件路径
 * @param {Object} options.data - 表单数据
 * @param {Function} callback - 回调函数
 */
function uploadFile(options, callback) {
  console.log(options);
  wx.uploadFile({
    url: options.url,
    filePath: options.filePath,
    name: "file",
    formData: options.data,
    success: (res) => {
      if (console.log("uploadFile| ", res), res.statusCode === 200) {
        console.log(res);
        let resTemp = JSON.parse(res.data);
        console.log("resTemp");
        console.log(resTemp);
        if (resTemp.ErrorCode === 0) {
          console.log(this.data);
          callback({ ErrorCode: 0, Data: resTemp });
        } else {
          callback({
            ErrorCode: resTemp.ErrorCode,
            ErrorMsg: "上传视频失败，" + resTemp.ErrorMsg,
          });
        }
      } else {
        callback({ ErrorCode: 101, ErrorMsg: "上传视频失败 " + res.statusCode });
      }
    },
    fail: (err) => {
      console.log("upload img fail", err);
      callback({ ErrorCode: 101, ErrorMsg: "上传视频失败, " + err.errMsg });
    },
  }).onProgressUpdate((res) => {
    this.setData({
      "livingbody.uploadProcess": res.progress - 10 < 0 ? 0 : res.progress - 10,
    });
    console.log("progress", res.progress);
    console.log("already upload data", res.totalBytesSent);
    console.log("all upload data", res.totalBytesExpectedToSend);
  });
}

/**
 * 验证输入
 * @param {string} value - 输入值
 * @param {string} type - 验证类型
 * @returns {boolean} 验证结果
 */
function validate(value, type) {
  switch (type) {
    case "signature":
      return /^\S{74}={2}$/.test(value);
    case "appid":
      return typeof value === "string" && /^\d{4}$/.test(value);
    case "uid":
      return value;
    case "sms_phone":
      return /^1\d{10}$/.test(value);
    case "sms_verifyCode":
      return /^\d{4}$/.test(value);
    case "idcard":
      return IDNumberValid(value);
    case "idname":
      return value && !value.match(/[A-z0-9]/g);
    case "idaddress":
      return !!value;
    case "end_path":
      return /^\//.test(value);
    case "token":
      return /^[a-zA-Z0-9-]{36}$/.test(value);
  }
}

/**
 * 显示模态框
 * @param {string} title - 标题
 * @param {string} content - 内容
 */
function showModal(title, content) {
  wx.showModal({
    title: title,
    content: content.replace(/(^\s*)|(\s*$)/g, ""),
    showCancel: false,
    confirmText: "我知道了",
    confirmColor: "#2d72f1",
    success: function (res) {
      res.confirm || res.cancel;
    },
  });
}

/**
 * 验证身份证号
 * @param {string} id - 身份证号
 * @returns {boolean} 验证结果
 */
function IDNumberValid(id) {
  if (!id || !/^\d{6}(18|19|20)?\d{2}(0[1-9]|1[0-2])(0[1-9]|[12]\d|3[01])\d{3}(\d|X)$/i.test(id)) {
    return false;
  }
  if (
    !{
      11: "北京",
      12: "天津",
      13: "河北",
      14: "山西",
      15: "内蒙古",
      21: "辽宁",
      22: "吉林",
      23: "黑龙江 ",
      31: "上海",
      32: "江苏",
      33: "浙江",
      34: "安徽",
      35: "福建",
      36: "江西",
      37: "山东",
      41: "河南",
      42: "湖北 ",
      43: "湖南",
      44: "广东",
      45: "广西",
      46: "海南",
      50: "重庆",
      51: "四川",
      52: "贵州",
      53: "云南",
      54: "西藏 ",
      61: "陕西",
      62: "甘肃",
      63: "青海",
      64: "宁夏",
      65: "新疆",
      71: "台湾",
      81: "香港",
      82: "澳门",
      91: "国外",
    }[id.substr(0, 2)]
  ) {
    return false;
  }
  if (id.length === 18) {
    id = id.split("");
    const factor = [7, 9, 10, 5, 8, 4, 2, 1, 6, 3, 7, 9, 10, 5, 8, 4, 2];
    const parity = [1, 0, "X", 9, 8, 7, 6, 5, 4, 3, 2];
    let sum = 0;
    let ai = 0;
    let wi = 0;
    for (let i = 0; i < 17; i++) {
      ai = id[i];
      wi = factor[i];
      sum += ai * wi;
    }
    let last = parity[sum % 11];
    if (id[17] === "x" || id[17] === "X") {
      return last === id[17].toUpperCase();
    }
    if (last !== parseInt(id[17])) {
      return false;
    }
  }
  return true;
}

/**
 * 开始原生验证
 * @param {boolean} uploadVideo - 是否上传视频
 * @param {number} checkAliveType - 检查类型
 * @param {string} baseUrl - 基础URL
 * @param {string} bizToken - BizToken
 * @param {Function} onError - 错误回调
 * @param {Function} onSuccess - 成功回调
 */
const startNativeVerify = (uploadVideo, checkAliveType, baseUrl, bizToken, onError, onSuccess, onCancel) => {
  const method = uploadVideo ? "startFacialRecognitionVerifyAndUploadVideo" : "startFacialRecognitionVerify";
  getUserIdKey(baseUrl, bizToken, onError, (userIdKey) => {
    console.log("获取userIdKey成功:", userIdKey);
    let verifyFunc = wx.startFacialRecognitionVerify;
    if (uploadVideo) {
      verifyFunc = wx.startFacialRecognitionVerifyAndUploadVideo;
    }
    verifyFunc({
      userIdKey: userIdKey,
      checkAliveType: checkAliveType,
      success(res) {
        getWxResult(baseUrl, bizToken, res.verifyResult, res.errCode, res.errMsg, (result) => {
          console.log(result);
          onSuccess(result);
        });
        reportMonitor(bizToken, method, 0);
      },
      fail(err) {
        console.log(err);
        reportError(bizToken, method, err);
        if (err.errCode === 90100) {
            onCancel?.();
            return false;
        }
        if (err.errCode) {
          getWxResult(baseUrl, bizToken, err.verifyResult, err.errCode, err.errMsg, (result) => {
            reportMonitor(bizToken, method, result.ErrorCode || -1);
            onSuccess(result);
          });
        } else {
          wx.showModal({
            title: "提示",
            content: err.errMsg,
            showCancel: false,
          });
        }
      },
    });
  });
};

/**
 * 获取UserIdKey
 * @param {string} baseUrl - 基础URL
 * @param {string} bizToken - BizToken
 * @param {Function} onError - 错误回调
 * @param {Function} onSuccess - 成功回调
 */
async function getUserIdKey(baseUrl, bizToken, onError, onSuccess) {
  try {
    let options = { url: `${baseUrl}/api/liveness/getWxUserIdKey?BizToken=${bizToken}` };
    wx.showLoading({ title: "加载中...", mask: true });
    let userIdKey = await getUserIdKeyRequest(options);
    wx.hideLoading();
    onSuccess(userIdKey);
  } catch (err) {
    console.log(err);
    wx.hideLoading();
    if (err.ErrorCode === 15 || err.ErrorCode === 14) {
      if (err.ErrorCode === 15) err.ErrorMsg = "当前BizToken已过期，请重试";
      else if (err.ErrorCode === 14) err.ErrorMsg = "当前BizToken已验证完成";
      onError({ BizToken: bizToken, ErrorCode: err.ErrorCode, ErrorMsg: err.ErrorMsg });
    } else if (err.ErrorCode === -1) {
      wx.showModal({ title: "提示", content: err.ErrorMsg, showCancel: false });
    } else {
      wx.showModal({
        title: "提示",
        content: err.ErrorMsg,
        confirmText: "重试",
        confirmColor: "#2d72f1",
        success: (res) => {
          if (res.confirm) getUserIdKey(baseUrl, bizToken, onError, onSuccess);
        },
      });
    }
  }
}

/**
 * 获取UserIdKey请求
 * @param {Object} options - 请求选项
 * @param {string} options.url - 请求URL
 * @returns {Promise} 请求Promise
 */
function getUserIdKeyRequest(options) {
  console.log(`请求 ${options.url}`);
  return new Promise((resolve, reject) => {
    try {
      wx.request({
        url: options.url,
        method: "POST",
        data: {},
        success(res) {
          console.log("request success:", res.data);
          if (res.data.ErrorCode === 0) {
            resolve(res.data.Data.UserIdKey);
          } else {
            reject(res.data);
          }
        },
        fail(err) {
          console.log("requestPromise error:", err);
          if (
            err.errMsg.indexOf("request:fail Unable to resolve host") >= 0 ||
            err.errMsg.indexOf("request:fail 似乎已断开与互联网的连接") >= 0
          ) {
            reject({ ErrorCode: 101, ErrorMsg: "网络异常，请稍后重试" });
          } else if (err.errMsg === "request:fail url not in domain list") {
            reject({
              ErrorCode: -1,
              ErrorMsg: "接口还未添加到服务器域名，请点击右上角三个点，打开调试模式再试",
            });
          } else {
            reject({ ErrorCode: 101, ErrorMsg: err.errMsg });
          }
        },
      });
    } catch (err) {
      console.log(Raven.captureException(err) + "捕获error");
    }
  });
}

/**
 * 获取微信结果
 * @param {string} baseUrl - 基础URL
 * @param {string} bizToken - BizToken
 * @param {string} verifyResult - 验证结果
 * @param {number} errCode - 错误码
 * @param {string} errMsg - 错误消息
 * @param {Function} onSuccess - 成功回调
 */
async function getWxResult(baseUrl, bizToken, verifyResult, errCode, errMsg, onSuccess) {
  try {
    wx.showLoading({ title: "加载中...", mask: true });
    let result = await getWxResultRequest(baseUrl, bizToken, verifyResult, errCode, errMsg);
    wx.hideLoading();
    onSuccess(result);
  } catch (err) {
    console.log(err);
    wx.hideLoading();
    if (err.ErrorCode === -1) {
      wx.showModal({ title: "提示", content: err.ErrorMsg, showCancel: false });
    } else {
      wx.showModal({
        title: "提示",
        content: err,
        confirmText: "重试",
        confirmColor: "#2d72f1",
        showCancel: false,
        success: (res) => {
          if (res.confirm) getWxResult(baseUrl, bizToken, verifyResult, errCode, errMsg, onSuccess);
        },
      });
    }
  }
}

/**
 * 获取微信结果请求
 * @param {string} baseUrl - 基础URL
 * @param {string} bizToken - BizToken
 * @param {string} verifyResult - 验证结果
 * @param {number} errCode - 错误码
 * @param {string} errMsg - 错误消息
 * @returns {Promise} 请求Promise
 */
function getWxResultRequest(baseUrl, bizToken, verifyResult, errCode, errMsg) {
  console.log(`请求 ${baseUrl}/api/liveness/getWxResult?BizToken=${bizToken}`);
  return new Promise((resolve, reject) => {
    wx.request({
      url: `${baseUrl}/api/liveness/getWxResult?BizToken=${bizToken}`,
      method: "POST",
      data: {
        VerifyResult: verifyResult || "",
        ErrCode: errCode.toString(),
        ErrMsg: errMsg,
      },
      success(res) {
        console.log("request success:", res.data);
        res.data.ErrorCode;
        resolve(res.data);
      },
      fail(err) {
        console.log("requestPromise error:", err);
        if (
          err.errMsg.indexOf("request:fail Unable to resolve host") >= 0 ||
          err.errMsg.indexOf("request:fail 似乎已断开与互联网的连接") >= 0
        ) {
          reject({ ErrorCode: 101, ErrorMsg: "网络异常，请稍后重试" });
        } else if (err.errMsg === "request:fail url not in domain list") {
          reject({
            ErrorCode: -1,
            ErrorMsg: "接口还未添加到服务器域名，请点击右上角三个点，打开调试模式再试",
          });
        } else {
          reject({ ErrorCode: 101, ErrorMsg: err.errMsg });
        }
      },
    });
  });
}

/**
 * 上报错误
 * @param {string} token - Token
 * @param {string} tag - 标签
 * @param {Object} error - 错误对象
 * @param {Object} [extra] - 额外信息
 * @returns {Promise} 上报Promise
 */
const reportError = (token, tag, error, extra) => {
  const errorData = { tag, error, source: "miniprogram" };
  try {
    errorData.system = wx.getSystemInfoSync();
  } catch (e) {}
  if (extra) errorData.extra = extra;
  console.log("上报错误：", errorData);
  return new Promise((resolve, reject) => {
    wx.request({
      url: `${wx.verifyBaseUrl}/api/report/reportError`,
      method: "POST",
      data: { token, errorData: JSON.stringify(errorData) },
      success(res) {
        resolve(res);
      },
      fail(err) {
        reject(err);
      },
    });
  });
};

/**
 * 上报监控
 * @param {string} token - Token
 * @param {string} pathName - 路径名
 * @param {number} errorCode - 错误码
 * @returns {Promise} 上报Promise
 */
const reportMonitor = (token, pathName, errorCode) =>
  new Promise((resolve, reject) => {
    wx.request({
      url: `${wx.verifyBaseUrl}/api/report/reportMonitor`,
      method: "POST",
      data: { token, pathName, platForm: 2, errorCode: String(errorCode) },
      success(res) {
        resolve(res);
      },
      fail(err) {
        reject(err);
      },
    });
  });

/**
 * 检查是否支持人脸识别
 * @param {string} token - Token
 * @param {number} checkAliveType - 检查类型
 * @param {Function} onSuccess - 成功回调
 */
const checkIsSupportFacialRecognition = (token, checkAliveType, onSuccess) => {
  wx.checkIsSupportFacialRecognition({
    checkAliveType: checkAliveType,
    success() {
      onSuccess && onSuccess();
    },
    fail(err) {
      wx.showModal({
        title: "提示",
        content: err.errMsg || "当前设备不支持人脸核身操作，请更换设备后重试",
        showCancel: false,
      });
      if (err.errMsg && err.errMsg.indexOf("开发者工具") === -1) {
        reportError(token, "checkIsSupportFacialRecognition", err, { checkAliveType });
      }
    },
  });
};

module.exports = {
  requestPromise,
  validate,
  compareVersion,
  showModal,
  request,
  uploadFile,
  startNativeVerify,
  reportError,
  reportMonitor,
  checkIsSupportFacialRecognition,
};