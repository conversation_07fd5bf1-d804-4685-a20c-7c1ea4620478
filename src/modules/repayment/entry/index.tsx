import {useEffect, useMemo, useState} from 'react';
import {ITouchEvent, Image, ScrollView, Text, View} from '@tarojs/components';
import Taro from '@tarojs/taro';
import classNames from 'classnames';
import MarqueeTip from '~components/MarqueeTip';
import PageWrap from '~components/PageWrap';
import Popup from '~components/Popup';
import arrowUpIcon from '~images/icon/<EMAIL>';
import arrowDownIcon from '~images/icon/<EMAIL>';
import {getRepaymentList, getRepaymentPlanDetail, getRepaymentPlanList, getRepaymentSummary} from '~services/repayment';
import {router} from '~utils/common';
import RepaymentCard from '../components/RepaymentCard';
import RepaymentDetail from '../components/RepaymentDetail';
import SchedulerBar, {SchedulerBarItem} from '../components/SchedulerBar';
import styles from './index.module.scss';
import { getLoanList } from '~services/bill';


export default () => {
  const [schedulerVisible, setSchedulerVisible] = useState(false);
  const [detailVisible, setDetailVisible] = useState(false);
  const [repaymentSummary, setRepaymentSummary] = useState<API.RepaymentSummaryRes>();
  const [repaymentPlanList, setRepaymentPlanList] = useState<API.RepaymentPlanListItem[]>([]);
  const [repaymentPlanDetailList, setRepaymentPlanDetailList] = useState<API.RepaymentPlanDetailListItem[]>([]);
  const [canScroll, setCanScroll] = useState(true);
  const [loanList, setLoanList] = useState<API.LoanDetail[]>([]);

  const showDetailPop = async (e: ITouchEvent, item: SchedulerBarItem) => {
    const {list} = await getRepaymentPlanDetail({dueDate: item.dueDate});
    console.log('detail list', list);
    setRepaymentPlanDetailList(list || []);
    setDetailVisible(true);
    setCanScroll(false);
    e.stopPropagation();
  };

  useEffect(() => {
    Taro.showLoading({ title: '加载中...', mask: true });
    getRepaymentSummary().then(res => {
      setRepaymentSummary(res);
    }).finally(() => {
      Taro.hideLoading();
    });
    getLoanList().then(res => {
      setLoanList(res.list || []);
    })
  }, []);

  const currentRepaymentAmount = useMemo(() => {
    let sum = 0;
    // 逾期
    if (repaymentSummary?.overdueLoanNum && repaymentSummary?.overdueLoanNum > 0) {
      sum = sum + repaymentSummary.overdueAmount;
    }
    // 当期
    if (repaymentSummary?.todayRepayableNum && repaymentSummary?.todayRepayableNum > 0) {
      sum = sum + repaymentSummary.todayRepayableAmount;
    }
    // 下期
    if (repaymentSummary?.nextRepaymentAmount && sum === 0) {
      sum = repaymentSummary.nextRepaymentAmount;
    }
    return sum;
  }, [repaymentSummary]);

  // 还款状态判断及返回各状态参数
  const repayementStatusObj = useMemo(() => {
    let obj = {
      badgeText: '',
      statusMain: undefined as unknown as API.OriginSummaryCategory,
    };
    if (repaymentSummary?.overdueLoanNum && repaymentSummary?.overdueAmount > 0) {
      obj.badgeText = '已逾期，请尽快还款';
      obj.statusMain = 'OVERDUE';
    } else if (repaymentSummary?.todayRepayableNum && repaymentSummary?.todayRepayableNum > 0) {
      obj.badgeText = '今天是还款日哦';
      obj.statusMain = 'TODAY';
    } else if (repaymentSummary?.nextRepaymentDate) {
      obj.badgeText = `${repaymentSummary?.nextRepaymentDate}可还款`;
      obj.statusMain = 'AHEAD';
    }
    return obj;
  }, [repaymentSummary]);

  const goRepaymentList = () => {
    router.push({
      url: '/modules/repayment/repayment-periods/index?type=' + repayementStatusObj.statusMain,
    });
  };

  const showCurrentDetail = async () => {
    try {
      Taro.showLoading({title: '查询中...', mask: true});
      const {list, ...others} = await getRepaymentList({repayableSummaryCategory: 'ACTIVE'});
      // 将list中的数据转化为RepaymentDetail所需的数据格式
      const btList = list?.map(item => {
        return {
          dueDate: item.dueDate,
          totalAmount: item.repayableAmount + '',
          principal: item.repayablePrincipal + '',
          interest: item.repayableInterest + '',
          totalTerm: item.totalTerm + '',
          curTerm: item.curTerm + '',
          loanInitPrin: item.principal + '',
          penalty: item.repayablePenaltyInterest + '',
          loanNo: item.loanNo
        }
      })
      Taro.hideLoading();
      setRepaymentPlanDetailList(btList);
      setDetailVisible(true);
      setCanScroll(false);
    } catch (e) {
      console.log('getRepaymentListErr====', e);
      Taro.hideLoading();
    }
  };

  useEffect(() => {
    getRepaymentPlanList().then(res => {
      setRepaymentPlanList(res.list || []);
    });
  }, []);

  return (
    <ScrollView scrollY={canScroll} style={{maxHeight: '100vh'}}>
      <PageWrap className={styles.entryPage}>
        <MarqueeTip
          darkIcon
          content='本公司的非交易时间段为23:00-3:00（第二天）'
          duration={0}
          color='#616C92'
          loopNoGap={false}
        />
        <View className={styles.schedulerCard}>
          <RepaymentCard
            overdueDays={repaymentSummary?.maxOverdueDays}
            overdueNumer={repaymentSummary?.overdueLoanNum}
            money={currentRepaymentAmount}
            composition={{
              principal: repaymentSummary?.curRepayPrincipal || 0.0,
              interest: repaymentSummary?.curRepayInterest || 0.0,
              penalty: repaymentSummary?.curRepayPenalty || 0.0,
            }}
            status={repayementStatusObj.statusMain}
            repaymentBadge={repayementStatusObj.badgeText}
            repaymentNumber={(repaymentSummary?.todayRepayableNum || 0) + (repaymentSummary?.overdueLoanNum || 0)}
            handleToRepayment={goRepaymentList}
            handleToDetail={showCurrentDetail}
            repaymentDate={repaymentSummary?.nextRepaymentDate}
          />
        </View>
        <View className={styles.schedulerCard}>
          <View
            className={styles.schedulerCardTitle}
            onClick={() => setSchedulerVisible(schedulerVisible ? false : true)}>
            <Text>待还计划</Text>
            <View className={styles.schedulerCardTitleDesc}>
              <Text>共{repaymentPlanList.length}期</Text>
              <Image className={styles.arrowIcon} src={!schedulerVisible ? arrowDownIcon : arrowUpIcon} />
            </View>
          </View>
          <View
            className={classNames(
              styles.schedulerCardContent,
              schedulerVisible ? styles.schedulerCardContentVisible : '',
            )}>
            <SchedulerBar onDetailClick={showDetailPop} list={repaymentPlanList} />
          </View>
        </View>

      <Popup
        bodyStyle={{background: '#f6f6f6'}}
        title='还款明细'
        onClose={() => {
          setDetailVisible(false)
          setCanScroll(true)
        }}
        visible={detailVisible}>
        <RepaymentDetail
          list={repaymentPlanDetailList}
          loanList={loanList}
        />
      </Popup>
    </PageWrap>
    </ScrollView>
  );
};
