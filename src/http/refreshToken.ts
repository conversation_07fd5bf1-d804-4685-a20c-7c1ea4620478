import useUserStore from "~store/user";
import { router } from "~utils/common";

// token失效时刷新token并重试请求
let isRefreshing = false;
let refreshSubscribers: Array<() => void> = [];

// 执行队列中的请求
const onRefreshed = () => {
  refreshSubscribers.forEach(callback => callback());
  refreshSubscribers = [];
};

// 添加请求到队列
const addSubscriber = (callback: () => void) => {
  refreshSubscribers.push(callback);
};

// 刷新token的主函数
const refresh = async () => {
  if (!isRefreshing) {
    isRefreshing = true;
    try {
      // 登出当前用户
      useUserStore.getState().logout();
      // 重新获取token
      await useUserStore.getState().wechatLogin();
      onRefreshed();
      return;
    } catch (error) {
      console.error('Token刷新失败:', error);
      // 刷新失败时，清空队列
      refreshSubscribers = [];
      // 可能需要跳转到登录页面
      router.replace({
        url: `/modules/user/login/index`,
      });
      return Promise.reject(error); // 返回rejected promise以便Promise链可以捕获错误
    } finally {
      isRefreshing = false;
    }
  } else {
    // 如果已经在刷新中，返回一个Promise，等待刷新完成
    return new Promise((resolve, reject) => {
      addSubscriber(() => {
        resolve(true);
      });
    });
  }
};

export default refresh