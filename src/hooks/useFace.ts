// 人脸识别hooks
import {useEffect, useRef, useState} from 'react';
import {View} from '@tarojs/components';
import {document, location, window} from '@tarojs/runtime';
import Taro, {ENV_TYPE} from '@tarojs/taro';
import {z} from 'zod';
import CyWebFaceAgreement from '../components/CyWebFaceAgreement';
import {
  getAliyunCertifyurl,
  getFaceIdResult,
  getFaceIdResultWithCertid,
  getFaceRequired,
  getFaceType,
  getRegisterFaceIdBizToken,
  getTecentFaceBizToken,
} from '../services/face';
import {showModal, showToast} from '../utils/common';
import {stringToArrayBufferFallback} from '../utils/string';
import useMainStore from '~store/main';

// 错误编码枚举对象
export enum ErrorCode {
  FACE_TOKEN_NOT_FOUND = '10001', // 未找到面部识别的 BizToken
  FACE_URL_ERROR = '10002', // 未找到面部识别的 BizToken
  FACE_API_ERROR = '10003', // 接口调用失败
  UNKNOWN_ERROR = '10010', // 未知错误
}

export const enum FaceState {
  INIT,
  DOING,
  SUCCESS,
  FAIL,
}

export type UseFaceParams = {
  requestFrom?: string;
  certId?: string;
  name?: string;
  loanAmount?: string;
  faceSuccess?: (ticketId?: string) => void;
  faceFail?: () => void;
};

export enum FACE_TYPE {
  private = 'private',
  public = 'public',
}

export default (initialParams: UseFaceParams = {}) => {

  const env = Taro.getEnv();
  const canJumpFace = useRef<boolean>(false);
  const tencentBiztoken = useRef('');
  const [popupVisible, setPopupVisible] = useState(false);
  const [isPrivacyFace, setIsPrivacyFace] = useState<boolean>();
  const faceAgreementChecked = useRef(false);
  const {config} = useMainStore.getState();

  const paramsRef = useRef<UseFaceParams>(initialParams);

  const faceApiRes = useRef<{
    licence?: string;
    securityConfig?: string;
    ticketId?: string;
    certifyId?: string;
    certifyUrl?: string;
    requestId?: string;
    bizToken?: string;
  }>({
    licence: '',
    securityConfig: '',
    ticketId: '',
    certifyId: '',
    certifyUrl: '',
    requestId: '',
    bizToken: '',
  });
  const [isFacing, setIsFacing] = useState(false);

  const init = async () => {
    // 1.判断是否需要做活体
    try {
      const flag = await getFaceRequired({
        requestFrom: paramsRef.current.requestFrom || 'miniprogram',
        loanAmount: paramsRef.current.loanAmount,
      });
      console.log('flag====', flag);
      if (!flag) {
        canJumpFace.current = true;
        setIsFacing(false);
        paramsRef.current.faceSuccess?.();
        return;
      }
    } catch (e) {
      //
      console.log('face init error', e);
    }
  };

  const jumpProcess = async () => {
    try {
      const flag = await getFaceRequired({
        requestFrom: paramsRef.current.requestFrom || 'miniprogram',
        loanAmount: paramsRef.current.loanAmount,
      });
      console.log('flag====1', flag);
      if (!flag) {
        canJumpFace.current = true;
        setIsFacing(false);
        paramsRef.current.faceSuccess?.();
        return false;
      }
      return true;
    } catch (e) {
      return true;
    }
  };

  const startFace = async (overrides: Partial<UseFaceParams> = {}) => {
    paramsRef.current = { ...paramsRef.current, ...overrides };
    // 1. 跳过逻辑
    if (!(await jumpProcess())) return;
    console.log('startFace');
    // 2. 环境判断
    setIsFacing(true);
    if (env === ENV_TYPE.WEAPP) {
      startTencentFaceWeApp();
      return;
    }
    // 3. 类型查询
    const faceType = await getFaceTypeFn();
    if (env === ENV_TYPE.WEB) {
      if (faceType === FACE_TYPE.private) {
        startAliyunFace();
      } else {
        startTencentFaceWeb();
      }
    }
  };

  const startTencetFace = async () => {
    // 原生
    if (env === ENV_TYPE.RN) {
      // TODO: 调用原生接口实现拉起活体识别
      // startNativeFace({
      //   faceType: isPrivacyFace.current ? 'private' : 'public',
      //   faceSuccess: paramsRef.current.faceSuccess,
      //   faceFail: paramsRef.current.faceFail,
      //   faceApiRes,
      // });
    } else {
    }
  };

  // 获取活体识别应该使用的类型
  const getFaceTypeFn = async () => {
    try {
      const {faceType} = await getFaceType();
      setIsPrivacyFace(faceType === FACE_TYPE.private);
      return faceType;
    } catch (e) {
      // 默认走老版本活体识别
      return FACE_TYPE.public;
    }
  };

  // 老版本：腾讯活体检测-小程序版本
  const startTencentFaceWeApp = async () => {
    // 1. 获取活体识别的 BizToken
    try {
      let faceBiztokenRes;
      if(!paramsRef.current.certId) {
        faceBiztokenRes = await getTecentFaceBizToken({
          requestFrom: paramsRef.current.requestFrom || 'miniprogram',
        });
      } else {
        faceBiztokenRes = await getRegisterFaceIdBizToken({
          requestFrom: paramsRef.current.requestFrom ||'miniprogram',
          certId: paramsRef.current.certId,
          name: paramsRef.current.name,
        });
      }
      faceApiRes.current = faceBiztokenRes;
      if (faceBiztokenRes?.bizToken) {
        tencentBiztoken.current = faceBiztokenRes.bizToken;
      } else {
        tencentBiztoken.current = '';
      }
    } catch (e) {
      tencentBiztoken.current = '';
    }

    // 2. 调用腾讯活体识别
    if (tencentBiztoken.current) {
      console.log('wx.startVerify');
      wx.startVerify({
        data: {
          token: tencentBiztoken.current, // 必要参数，BizToken
        },
        success: successRes => {
          // TODO: 上报日志
          console.log('face scucess', successRes);
          setIsFacing(false);
          getFaceResult();
        },
        fail: (err: any) => {
          // 配置了不检查活体结果且是测试环境（mode不为空）的情况下尽可能还原真实环境去后端查询活体结果
          if(config.checkFaceResults === false && process.env.TARO_APP_MODE) {
            setIsFacing(false);
            getFaceResult();
          } else {
            // TODO: 上报日志
            console.log('face fail', err);
            err && showToast(err.ErrorMsg);
            setIsFacing(false);
          }
        },
        cancel: () => {
          // 小程序环境，取消情况下直接返回成功
          if(config.checkFaceResults === false && process.env.TARO_APP_MODE) {
            // paramsRef.current.faceSuccess?.();
            setIsFacing(false);
            getFaceResult();
          }
        }
      });
    } else {
      showToast('调用活体识别失败！');
      processErrorCb({msg: '接口调用错误, 02'});
      console.log(`${ErrorCode.FACE_TOKEN_NOT_FOUND}`);
    }
  };

  // 老版本：腾讯活体检测-web版
  const startTencentFaceWeb = async () => {
    await getFaceURL();
  };

  const getFaceURL = async () => {
    try {
      const res = await getAliyunCertifyurl({
        requestFrom: paramsRef.current.requestFrom || 'common',
      });
      if (res.certifyUrl) {
        faceApiRes.current = res;
      } else {
        processErrorCb({msg: '接口调用错误, 03', err: res});
        console.log(`${ErrorCode.FACE_URL_ERROR}`);
      }
    } catch (e) {
      processErrorCb({msg: '接口调用错误', err: e});
      console.log(e);
      console.log(`${ErrorCode.FACE_URL_ERROR}`);
    }
  };

  // 监听活体识别结果
  const startListen = () => {
    if (env !== ENV_TYPE.WEB) return;
    if (isPrivacyFace) {
      // 1. 私有化版本监听
      const listenCb = async (e: any) => {
        console.log('%cuseFaceInstance.ts line:66 ready', 'color: #007acc;', e.data);
        if (e.data.state === 'ready') {
          const webview = document?.getElementById('zoloz');
          const {licence, securityConfig} = faceApiRes.current;
          console.log('webview', webview);
          console.log('data', faceApiRes, {
            license: licence,
            securityConf: securityConfig,
            locale: 'zh-CN',
          });
          // @ts-ignore
          webview?.contentWindow?.postMessage(
            {
              license: licence,
              securityConf: securityConfig,
              locale: 'zh-CN',
            },
            '*',
          );
        }
        if (e.data.state === 'complete') {
          try {
            await getFaceResult();
          } catch (err) {
            processErrorCb({msg: '验证错误, 0001', err});
          }
        }
        if (e.data.state === 'interrupted') {
          processErrorCb({msg: `验证错误, 0001, e: ${JSON.stringify(e.data.reason)}`, err: e});
        }
      };
      window.addEventListener?.('message', listenCb);
    } else {
      // 2. 腾讯云版本监听
      let timer = setInterval(() => {
        const faceStorageStr = Taro.getStorageSync('__persisted__face__result');
        if (faceStorageStr) {
          const faceStorageObj = JSON.parse(faceStorageStr);
          // if (faceStorageObj[faceApiRes.ticketId]) {
          //   getFaceResult();
          //   clearInterval(timer);
          // }
        }
      }, 1000);
    }
  };

  // 新版本：阿里私有化活体检测
  const startAliyunFace = async () => {
    if (faceAgreementChecked.current) {
      await getFaceURL();
    }
  };

  useEffect(() => {
    startListen();
  }, [faceApiRes.current.securityConfig]);

  useEffect(() => {
    if (!isPrivacyFace && faceApiRes?.current.certifyUrl) {
      location.href = faceApiRes.current.certifyUrl;
    }
  }, [isPrivacyFace, faceApiRes?.current.certifyUrl]);

  const showFacePopup = () => {
    setPopupVisible(true);
  };

  // 调用过程中错误处理
  const processErrorCb = (err: {msg: string; err?: any}) => {
    setIsFacing(false);
    showToast(err.msg);
    console.log(`processErrorCb ${err.msg}`, err.err);
  };

  // 查询结果出错
  const resultErrorCb = (err: {msg?: string}) => {
    showModal({
      title: '提示',
      content: err.msg || '活体识别失败请稍候再试！',
      showCancel: false,
    });
    console.log(`resultErrorCb ${err.msg}`);
  };

  const getFaceResult = async () => {
    const api = paramsRef.current.certId ? getFaceIdResultWithCertid : getFaceIdResult
    api({
      bizToken: faceApiRes.current.bizToken || '',
      requestId: faceApiRes.current.requestId || '',
      requestFrom: paramsRef.current.requestFrom || 'miniprogram',
      certId: paramsRef.current.certId || '',
    })
      .then((res: any) => {
        setIsFacing(false);
        paramsRef.current.faceSuccess?.(faceApiRes.current.ticketId);
      })
      .catch((e: any) => {
        setIsFacing(false);
        resultErrorCb(e);
        paramsRef.current.faceFail?.();
      });
  };

  const continueFace = startAliyunFace;

  return {init, startFace, isFacing, isPrivacyFace, faceApiRes, faceAgreementChecked, continueFace};
};
