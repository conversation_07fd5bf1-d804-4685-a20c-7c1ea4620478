import {FC} from 'react';
import {Image, View} from '@tarojs/components';
import clearPng from '~images/icon/clear.png';
import PDFView from '../CyPdfView';
import './index.scss';

interface Props {
  pdfBase64: string;
  onReject: () => void;
  onClose: () => void;
  onSubmit: () => void;
}

/**
 * 组件名称：协议弹出框
 * 组件描述：弹出协议签订
 */
const CyPdfAgreementPopup: FC<Props> = ({onClose, pdfBase64, onReject, onSubmit}) => {
  const updateStatus = (flag: string) => {
    if (flag === 'reject') {
      onReject();
    } else {
      onSubmit();
    }
    onClose();
  };

  return (
    <View className='comp-cy-pdf-agreement-popup'>
      <View className='comp-cy-pdf-agreement-popup-scroll'>
        <Image
          className='comp-cy-pdf-agreement-popup-icon'
          src={clearPng}
          onClick={() => {
            onClose();
          }}></Image>
        <PDFView
          style={{flex: 1}}
          resource={pdfBase64}
          resourceType='base64'
          enableAnnotations
          onError={error => console.log('Cannot render PDF', error)}
        />
      </View>

      <View className='comp-cy-pdf-agreement-popup-bottom'>
        <View className='comp-cy-pdf-agreement-popup-btn' onClick={() => updateStatus('reject')}>
          回退
        </View>
        <View className='comp-cy-pdf-agreement-popup-btn' onClick={() => updateStatus('submit')}>
          完成
        </View>
      </View>
    </View>
  );
};

export default CyPdfAgreementPopup;
