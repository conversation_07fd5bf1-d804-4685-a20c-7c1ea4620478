buildscript {
    ext {
        buildToolsVersion = "34.0.0"
        minSdkVersion = 23
        compileSdkVersion = 34
        targetSdkVersion = 34
        ndkVersion = "25.1.8937393"
        kotlinVersion = "1.6.10"
    }
    repositories {
        // 添加阿里云 maven 地址\
        maven { url "https://mirrors.huaweicloud.com/repository/maven/" }
        maven { url 'https://maven.aliyun.com/repository/central' }
        maven { url 'https://maven.aliyun.com/repository/public' }
        maven { url 'https://maven.aliyun.com/repository/gradle-plugin' }
        maven { url "https://maven.aliyun.com/repository/apache-snapshots" }
        maven { url "https://maven.aliyun.com/nexus/content/repositories/google" }
        maven { url 'https://maven.aliyun.com/nexus/content/groups/public/' }
        maven { url 'https://maven.aliyun.com/nexus/content/repositories/jcenter/' }
        maven { url 'https://maven.aliyun.com/nexus/content/repositories/releases/' }
        maven { url "https://oss.sonatype.org/content/repositories/snapshots/" }
        maven { url 'https://maven.aliyun.com/repository/jcenter' }
        maven { url 'https://maven.aliyun.com/repository/google' }
        maven { url 'https://maven.aliyun.com/repository/gradle-plugin' }

        mavenLocal()
        mavenCentral()
        google()
    }
    dependencies {
        classpath("com.android.tools.build:gradle")
        classpath("com.facebook.react:react-native-gradle-plugin")
        classpath("org.jetbrains.kotlin:kotlin-gradle-plugin")
    }
}

allprojects {
    repositories {
        // 添加阿里云 maven 地址\
        maven { url "https://mirrors.huaweicloud.com/repository/maven/" }
        maven { url 'https://maven.aliyun.com/repository/central' }
        maven { url 'https://maven.aliyun.com/repository/public' }
        maven { url 'https://maven.aliyun.com/repository/gradle-plugin' }
        maven { url "https://maven.aliyun.com/repository/apache-snapshots" }
        maven { url "https://maven.aliyun.com/nexus/content/repositories/google" }
        maven { url 'https://maven.aliyun.com/nexus/content/groups/public/' }
        maven { url 'https://maven.aliyun.com/nexus/content/repositories/jcenter/' }
        maven { url 'https://maven.aliyun.com/nexus/content/repositories/releases/' }
        maven { url "https://oss.sonatype.org/content/repositories/snapshots/" }
        maven { url 'https://maven.aliyun.com/repository/jcenter' }
        maven { url 'https://maven.aliyun.com/repository/google' }
        maven { url 'https://maven.aliyun.com/repository/gradle-plugin' }

        mavenLocal()
        mavenCentral()
        google()

        maven {
            // expo-camera bundles a custom com.google.android:cameraview
            url "$rootDir/../node_modules/expo-camera/android/maven"
        }
    }
}
apply plugin: "com.facebook.react.rootproject"