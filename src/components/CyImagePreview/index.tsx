import { Image, Text, View } from '@tarojs/components';
import Taro from '@tarojs/taro';
import { CyPopup } from 'hncy58-taro-components';
import styles from './index.module.scss';

interface ImageItem {
  name: string;
  content: string;
}

interface CyImagePreviewProps {
  // 是否显示弹出层
  visible: boolean;
  // 图片数据
  images: ImageItem[];
  // 标题
  title?: string;
  // 关闭弹出层的回调函数
  onClose?: () => void;
  // 图片容器高度
  height?: number | string;
  // 图片显示模式
  mode?: 'aspectFit' | 'widthFix' | 'aspectFill' | 'scaleToFill';
}

const CyImagePreview = ({
  visible,
  images,
  title,
  onClose,
  height = '100%',
  mode = 'widthFix',
}: CyImagePreviewProps) => {
  if (!images || images.length === 0) {
    return null;
  }

  const handleImageClick = (current: string) => {
    Taro.previewImage({
      current,
      urls: images.map(item => item.content),
    });
  };

  return (
    <CyPopup title={title || images[0].name} visible={visible} onClose={onClose}>
      <View className={styles.imageContainer} style={{ height }}>
        {images.map((image, index) => (
          <Image
            key={index}
            onClick={() => handleImageClick(image.content)}
            mode={mode}
            src={image.content}
            className={styles.image}
            style={{ width: '100%' }}
          />
        ))}
        {images.length > 1 && (
          <View className={styles.pagination}>
            <Text className={styles.paginationText}>{`1/${images.length}`}</Text>
          </View>
        )}
      </View>
    </CyPopup>
  );
};

export default CyImagePreview; 