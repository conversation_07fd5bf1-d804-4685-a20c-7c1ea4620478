.content {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    flex-wrap: wrap;
}

.serviceItem {
    display: flex;
    flex-direction: row;
    align-items: center;
    width: 320px;
    height: 120px;
    border-radius: 16px;
    margin-bottom: 16px;
    padding-left: 44px;
    box-sizing: border-box;
    font-size: 28px;
    color: #333;
    font-weight: 500;
}

.aboutus {
    background: url('../../../../assets/images/index/aboutus-entry-bg.png');
    background-size: 100%;
}
.production {
    background: url('../../../../assets/images/index/product-intro-bg.png');
    background-size: 100%;
}
.app {
    background: url('../../../../assets/images/index/app-entry-bg.png');
    background-size: 100%;
}
.kefu {
    background: url('../../../../assets/images/index/kefu-entry-bg.png');
    background-size: 100%;
}