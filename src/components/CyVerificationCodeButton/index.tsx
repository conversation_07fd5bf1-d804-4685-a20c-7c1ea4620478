import React, {FC, useEffect, useImperativeHandle, useState, forwardRef} from 'react';
import {View} from '@tarojs/components';
import {useUnload} from '@tarojs/taro';
import classNames from 'classnames';
import './index.scss';

type Props = {
  className?: string;
  sendSuccess: boolean; // 监听发送成功
  onSend?: () => void; // 发送验证码
  onCountdownEnd?: () => void; // 倒计时结束
  interval?: number; // 倒计时秒数，默认为60秒
  type?: 'button' | 'text';
};

export interface CyVerificationRef {
  resetTimer: () => void;
  handleSend: () => void;
}

/**
 * 组件名称：发送验证码按钮
 * 组件描述：发送验证码
 */
const CyVerificationCodeButton = forwardRef<CyVerificationRef, Props>((props, ref) => {
  const {className, sendSuccess = false, onSend, onCountdownEnd, interval = 60, type = 'button'} = props;
  const [remainingTime, setRemainingTime] = useState(interval);
  const [disabled, setDisabled] = useState(false);

  // 暴露方法给父组件
    useImperativeHandle(ref, () => ({
      resetTimer: () => {
        clearInterval(countdownRef.current);
        setDisabled(false);
        setRemainingTime(interval);
      },
      handleSend
    }));

  // 监听验证码发送成功事件，当组件挂载时，以及sendSuccess的值发生变化时，调用该方法
  useEffect(() => {
    if (sendSuccess) {
      startTimer();
    }
  }, [sendSuccess]);

  const sendBtnName = classNames('verification-button', {
    'verification-button--disabled': disabled,
  });

  //定时器
  const countdownRef = React.useRef<undefined | NodeJS.Timeout>(undefined);

  useUnload(() => {
    clearInterval(countdownRef.current); // 清除定时器
  });

  const startTimer = () => {
    clearInterval(countdownRef.current);
    setDisabled(true);
    setRemainingTime(interval);
    countdownRef.current = setInterval(cutDown, 1000);
  };

  const cutDown = () => {
    setRemainingTime(flag => {
      if (flag <= 0) {
        clearInterval(countdownRef.current);
        setDisabled(false);
        onCountdownEnd?.();
        return interval;
      }
      return flag - 1;
    });
  };

  const handleSend = () => {
    if (!disabled) {
      onSend?.();
    }
  };

  return (
    <View className={className}>
      {
        type === 'button' &&
      <View className={sendBtnName} onClick={handleSend}>
        {disabled ? `${remainingTime}s后重试` : '获取验证码'}
      </View>
      }
      {
        type === 'text' && 
        <View className='codeText' onClick={handleSend}>
          {disabled ? `重新获取(${remainingTime}s)` : '获取验证码'}
        </View>
      }
    </View>
  );
});

export default CyVerificationCodeButton;
