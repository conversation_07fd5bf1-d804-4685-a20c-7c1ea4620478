// src/utils/mockManagers.ts
// 测试数据：客户经理列表（姓名、手机、位置经纬度）

export type Manager = {
  name: string;
  phone: string;
  location: {
    latitude: number;
    longitude: number;
    name: string;
    addressDesc: string; // 位置详细描述
  };
};

export const mockManagers: Manager[] = [
  {
    name: '张经理',
    phone: '13812345908',
    location: { 
      latitude: 28.2282, 
      longitude: 112.9389, 
      name: '长沙市岳麓区', 
      addressDesc: '位于长沙市西侧，靠近湖南大学和中南大学，交通便利，适合学生和科技从业者。' 
    },
  },
  {
    name: '李经理',
    phone: '13987654481',
    location: { 
      latitude: 28.1927, 
      longitude: 112.9865, 
      name: '长沙市天心区', 
      addressDesc: '长沙市中心区域，商业发达，毗邻橘子洲头和太平街，生活便利。' 
    },
  },
  {
    name: '王经理',
    phone: '13711112132',
    location: { 
      latitude: 28.1931, 
      longitude: 113.0163, 
      name: '长沙市芙蓉区', 
      addressDesc: '长沙市东部，靠近火车站和万家丽广场，交通枢纽，便于出行。' 
    },
  },
  {
    name: '赵经理',
    phone: '13655556641',
    location: { 
      latitude: 28.2559, 
      longitude: 112.9855, 
      name: '长沙市开福区', 
      addressDesc: '长沙市北部，靠近湘江，环境优美，适合家庭居住。' 
    },
  },
  {
    name: '孙经理',
    phone: '13544443213',
    location: { 
      latitude: 28.1355, 
      longitude: 113.0070, 
      name: '长沙市雨花区', 
      addressDesc: '长沙市南部，工业和住宅区发达，靠近高铁南站，便捷连接周边城市。' 
    },
  },
]; 