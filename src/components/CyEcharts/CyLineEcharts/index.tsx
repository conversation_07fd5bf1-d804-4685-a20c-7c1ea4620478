import {CSSProperties, FC, useEffect, useState} from 'react';
import type {LineSeriesOption} from 'echarts/charts';
import {ComposeOption} from 'echarts/core';
import CyEcharts from '..';

interface Props {
  width: number;
  height: number;
  customerToolTip?: (data: any) => React.ReactNode; //自定义tooltip
}
/**
 * 组件名称：图表
 * 组件描述：图表
 */
const CyLineEcharts: FC<Props & ComposeOption<LineSeriesOption>> = props => {
  const [options, setOptions] = useState<ComposeOption<LineSeriesOption>>({});
  useEffect(() => {
    setupOptions();
  }, [props]);
  const setupOptions = () => {
    const _options = {
      tooltip: props.tooltip,
      legend: props.legend,
      //网格
      grid: props.grid,
      xAxis: props.xAxis,
      yAxis: props.yAxis,
      series: props.series,
      backgroundColor: props.backgroundColor ?? 'transparent',
    };
    setOptions(_options);
  };
  return (
    <CyEcharts option={options} width={props.width} height={props.height} customerToolTip={props.customerToolTip} />
  );
};

export default CyLineEcharts;
