.billContainer {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.unSelectAble {
  opacity: .5;
}
.billItem {
  display: flex;
  background-color: #fff;
  border-radius: 8px;
  flex: 1;
}

.billItemMain {
  flex: 1;
  padding: 32px 24px 24px 0;
  .billItemTop {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 32px;
    .billItemLeft {
      display: flex;
      align-items: center;
      line-height: 1;

    }
    .billAmountDesc {
      color: $color-text-secondary;
      font-size: $font-size-sm;
      margin-left: 26px;
    }
    .billArrow {
      width: 24px;
      height: 24px;
    }
  }
  .billInfo {
    background-color: #F6F7FB;
    color: $color-text-secondary;
    font-size: $font-size-sm;
    padding: 12px 0 12px 30px;
    line-height: 1;
    border-radius: 8px;
  }
}

.billItemCheck {
  display: flex;
  align-items: center;
  margin-left: 15px;
}

.comp-cy-checkbox .cy-checkbox-item {
  margin-bottom: 20px;
}

.billArrow {
  width: 24px;
  height: 24px;
  margin-right: 12px;
}
