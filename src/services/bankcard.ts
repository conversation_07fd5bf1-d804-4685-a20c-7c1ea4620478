import request from '../http/request';

/** 获取用户可用银行卡列表 */
export const getBankCardList = (params?: {requestFrom?: string}) => {
  return request<API.IBankCardInfo[]>({
    url: '/api/bank-card/list',
    method: 'GET',
    params
  });
};

/** 绑定银行卡申请 */
export const bankCardApply = (data: API.bankcardBindInfo) => {
  return request<API.IBankCardApplyRes>({
    url: '/api/bank-card/apply',
    method: 'POST',
    data,
  });
}

/** 绑定银行卡确认 */
export const bankCardConfirm = (data: API.bankcardBindConfirm) => {
  return request<{}>({
    url: '/api/bank-card/confirm',
    method: 'POST',
    data,
  });
}

/** 查询可绑银行卡列表 */
export const getSupporBanks = () => {
  return request<API.IBankCardInfo[]>({
    url: '/api/bank-card/support-banks',
    method: 'GET',
  });
}

/** 通过银行卡号查询银行名称 */
export const getBankNameByCardNo = ({ cardNo }: { cardNo: string }) => {
  return request<string>({
    url: 'api/bank-card/queryBankNameByCardNo',
    method: 'GET',
    params: { cardNo },
  })
}

/** 查询第三方可绑卡渠道信息 */
export const getUnsignedChannel = ({ cardNo, defaultBank, type }: { cardNo: string, defaultBank: boolean, type: string }) => {
  return request<{
    seqNo: string;
    unsignedChannel: string;
    unsignedChannelUrl: string;
  }>({
    url: 'api/bank-card/queryUnsignedChannel',
    method: 'POST',
    data: { cardNo, defaultBank, type },
  })
}

/** 查询第三方可绑卡结果 */
export const getUnsignedChannelState = ({ seqNo }: { seqNo: string}) => {
  return request<{
    result: boolean
  }>({
    url: 'api/bank-card/queryUnsignedChannelState',
    method: 'POST',
    data: { seqNo },
  })
}

/** 删除银行卡 */
export const deleteBankCard = (data: { accountId: string }) => {
  return request<{
    result: boolean
  }>({
    url: 'api/bank-card/delete',
    method: 'POST',
    params: data,
  })
}

/** 删除银行卡 */
export const setDefaultBankCard = (data: { accountId: string }) => {
  return request<{
    result: boolean
  }>({
    url: 'api/bank-card/default',
    method: 'POST',
    params: data,
  })
}

/** 升级银行卡-发送短信 */
export const sendUpgradeBankCardSms = (data: { acctId: string; }) => {
  return request<{
    applyNo: string;
    needSupplyDocTypes: string[];
  }>({
    url: 'api/bank-card/upgrade/send-code',
    method: 'POST',
    data,
  })
}

/** 升级银行卡-确认 */
export const confirmUpgradeBankCard = (data: { applyNo: string; acctId: string; verificationCode: string; }) => {
  return request<any>({
    url: 'api/bank-card/upgrade',
    method: 'POST',
    data,
  })
}

/** 银行卡OCR */
export const ocrBankcard = (data: { base64Picture: string }) => {
  return request<string>({
    url: 'api/bank-card/ocrBankCard',
    method: 'POST',
    data,
  })
}
