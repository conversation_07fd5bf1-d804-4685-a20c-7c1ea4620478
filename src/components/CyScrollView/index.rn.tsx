import React, {CSSProperties, FC, ForwardRefRenderFunction, ReactNode} from 'react';
import {ScrollView, StyleProp, ViewStyle} from 'react-native';
import {BaseEventOrig, ScrollViewProps} from '@tarojs/components';
import './index.scss';

interface Props {
  className?: string;
  style?: string | CSSProperties;
  children?: ReactNode;
  enhanced?: boolean;
  pagingEnabled?: boolean;
  showScrollbar?: boolean;
  scrollX?: boolean;
  scrollY?: boolean;
}

/**
 * 组件名称：滚动视图
 * 组件描述：滚动视图
 */
const MyCyScrollView: ForwardRefRenderFunction<unknown, Props & ScrollViewProps> = (props, ref) => {
  const {style, children} = props;
  const customStyle = style as StyleProp<ViewStyle>;
  React.useImperativeHandle(ref, () => ({
    scrollToOffset: (x: number, y: number) => {
      if (scrollViewRef.current) {
        scrollViewRef.current.scrollTo({x, y, animated: props.scrollWithAnimation ?? false});
      }
    },
  }));
  const scrollViewRef = React.useRef<ScrollView>(null);
  return (
    <ScrollView
      showsHorizontalScrollIndicator={props.showScrollbar ?? true}
      showsVerticalScrollIndicator={props.showScrollbar ?? true}
      pagingEnabled={props.pagingEnabled}
      horizontal={props.scrollX ?? false}
      ref={scrollViewRef}
      style={customStyle}
      directionalLockEnabled
      nestedScrollEnabled
      onScroll={e => {
        const detail = {
          detail: {scrollLeft: e.nativeEvent.contentOffset.x, scrollTop: e.nativeEvent.contentOffset.y},
        } as BaseEventOrig<ScrollViewProps.onScrollDetail>;
        props.onScroll && props.onScroll(detail);
      }}
      scrollEventThrottle={16}>
      {children}
    </ScrollView>
  );
};
const CyScrollView = React.forwardRef(MyCyScrollView);
export default CyScrollView;
