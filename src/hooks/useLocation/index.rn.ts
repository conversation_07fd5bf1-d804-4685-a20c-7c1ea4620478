import {useEffect, useRef, useState} from 'react';
import AMapLoader from '@amap/amap-jsapi-loader';
import Taro from '@tarojs/taro';
import {PositionType, UseLocationParams} from '.';

export default function useH5Location(params?: UseLocationParams) {
  const [position, setPosition] = useState<PositionType>({
    lat: undefined,
    lng: undefined,
  });
  const startLocation = async ({onSuccess, onFail, isRetry = true}: UseLocationParams) => {
    //
  };
  return {startLocation, position};
}
