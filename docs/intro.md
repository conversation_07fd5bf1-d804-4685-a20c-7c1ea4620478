# 项目介绍

## 背景

前端组的系统往往都存在多端实现，如 城一代iOS/微信公众号/城一代Android 基础功能都类似，但是目前开发需要多端独立开发，逻辑无法复用，一个需求的开发周期过长。且存在可能业务实现有差异的情况，较难去管理。

## 目标

实现一套代码能够编译到以下平台，一次开发多端使用保持多端一致：
-  iOS
-  Android
-  Harmony：鸿蒙
-  weapp：小程序
-  web：网页

## 技术选型

目前前端的跨端方案较流行的有以下几种：

- :x: uniapp：不支持鸿蒙
- :white_check_mark: taro 4.x: 都支持
- :x: react-native：不支持鸿蒙/小程序
- :x: flutter：不支持鸿蒙/小程序

故最终选择了[taro 4.x](https://taro-docs.jd.com/docs/next/)

## 相关技术栈

1. Taro v4.x
2. React
3. typescript
4. Taro-ui
5. Taro-hooks
6. zustand
7. ReactNative
8. axios
9. classnames
10. eslint + prettier

## 文件结构

```
.
├─ .vscode # vscode编辑器配置
├─ android # android 壳文件
├─ ios # ios 壳文件
├─ scripts # 自定义的npm脚本
├─ config # taro项目各环境配置文件
├─ dist # h5产物
├─ docs # 文档
├─ src 
│  ├─ http  # 请求工具类
│  ├─ components  # 全局组件
│  ├─ pages  # 页面
|     ├─ index.scss # 页面样式
|     ├─ index.tsx # 页面代码
|     ├─ index.config.ts # 页面配置
│     └─ components # 页面级组件
│  ├─ services  # 接口请求
│  ├─ store  # 全局状态管理
│  ├─ styles  # 公共样式
│  ├─ utils  # 公共工具函数
│  ├─ app.config.ts # 项目页面配置
│  ├─ app.ts # 页面渲染入口
│  └─ index.html
├─ typings # 全局ts类型
├─ .env.[mode] # mode环境的环境变量
├─ .env # 默认环境变量
├─ tsconfig.json
├─ .eslintrc
├─ .gitignore
├─ .watchmanconfig
├─ project.config.json  # 微信配置文件
├─ .prettier.config.js
├─ babel.config.js
└─ package.json
```