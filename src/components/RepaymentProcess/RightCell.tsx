import {View} from '@tarojs/components';
import styles from './index.module.scss';
import {formatToFixedTwo} from '~utils/number';

interface RightCellProps {
  totalAmount: string | number;
  principal: string | number;
  interest: string | number;
}

const RightCell = ({totalAmount, principal, interest}: RightCellProps) => {
  return (
    <View className={styles.repayment_cell_right}>
      <View className={styles.repayment_cell_money}>{totalAmount}元</View>
      <View className={styles.repayment_cell_note}>{`含本金${formatToFixedTwo(principal)}+利息${formatToFixedTwo(interest)}`}</View>
    </View>
  );
};

export default RightCell; 