import {ReactNode} from 'react';
import {View} from '@tarojs/components';

type LinearGradientProps = {
  direction: 'to top' | 'to right' | 'to bottom' | 'to left';
  colors: string[];
  children: ReactNode;
  className?: string;
};

const CyLinearGradient = (props: LinearGradientProps) => {
  const {colors, direction, className} = props;
  return (
    <View
      className={className}
      style={{
        background: `linear-gradient(${direction}, ${colors[0]} 0%, ${colors[1]} 100%)`,
      }}>
      {props.children}
    </View>
  );
};

export default CyLinearGradient;
