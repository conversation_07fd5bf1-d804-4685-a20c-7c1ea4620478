.comp-cy-collapse {

}

.panel {
  &-header {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    font-size: 28px;
    color: #333;
    padding: 0 43px;
  }

  &-icon {
    width: 32px;
    height: 32px;
  }

  &-text {
    font-size: 28px;
    color: #333;
  }
  &-content {
    display: none;
  }
}

.panel-active {
  display: block;
}

.up {
  transform: rotate(180deg);
}