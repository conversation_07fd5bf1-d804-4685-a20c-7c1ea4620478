import {Image, Text, View} from '@tarojs/components';
import classNames from 'classnames';
import CyCheckbox from '~components/CyCheckbox';
import MoneyDisplay from '~components/MoneyDisplay';
import arrowRight from '~images/icon/<EMAIL>';
import {formattedMoney} from '~utils/common';
import {router} from '~utils/common';
import styles from './index.module.scss';
import dayjs from 'dayjs';

interface IRepaymentBillProps {
  amount: number;
  date?: string;
  onChange?: (e: any) => void;
  remainTerms?: number;
  principal: number;
  status: API.LoanStatus;
  unSelectAble?: boolean;
  loanNo: string;
  overdueDays?: number;
}
const statusTextMap = {
  OVERDUE: '已逾期',
  TODAY: '已出账',
  // 'AHEAD': '未出账',
  UNEXPIRED: '未出账',
};
const statusClassMap = {
  OVERDUE: 'tag-error',
  TODAY: 'tag-primary',
  UNEXPIRED: 'tag-disabled',
};
const RepaymentBill = ({
  amount,
  date,
  onChange,
  remainTerms,
  principal,
  status,
  unSelectAble,
  loanNo,
  overdueDays,
}: IRepaymentBillProps) => {
  // const handleCheckboxChange = (e: any) => {
  //   onChange?.(e);
  // };

  const goToBillDetails = (e: any, loanNo: string) => {
    e.stopPropagation();
    router.push({
      url: `/modules/bill/details/index?loanNo=${loanNo}`,
    });
  };

  return (
    <View
      className={classNames(styles.billContainer, unSelectAble ? styles.unSelectAble : '')}
      onClick={e => goToBillDetails(e, loanNo)}>
      <View className={styles.billItem}>
        <View className={styles.billItemMain}>
          <View className={styles.billItemTop}>
            <View className={styles.billItemLeft}>
              <MoneyDisplay money={amount} />
              {/* <Text className={styles.billAmountDesc}>剩余 {remainTerms} 期可还完</Text> */}
            </View>
            <View className={styles.billItemRight}>
              <Text className={statusClassMap[status] || 'tag-disabled'}>
                {(statusTextMap[status] || '未入账') + (overdueDays ? `${overdueDays}天` : '')}
              </Text>
              <Image className={styles.billArrow} src='/assets/icons/arrow-down.png'></Image>
            </View>
          </View>
          <View className={styles.billInfo}>
            {dayjs(date).format('YYYY-MM-DD')} 借款{formattedMoney(`${principal}`, {symbol: ''})}元
          </View>
        </View>
      </View>
      <Image src={arrowRight} className={styles.billArrow}></Image>
    </View>
  );
};

export default RepaymentBill;
