import {CyB<PERSON>on, CySafeArea} from 'hncy58-taro-components';
import {View, ViewProps} from '@tarojs/components';
import styles from './index.module.scss';
import RepaymentBadge from '../RepaymentBadge';

interface RepaymentFooterBarProps extends ViewProps {
  // 右侧按钮文字
  btnText: string;
  // 右侧按钮点击事件
  onBtnClick?: () => void;
  // 左侧内容
  content?: React.ReactNode;
  disabled?: boolean;
  tips?: string;
}
const RepaymentFooterBar = (props: RepaymentFooterBarProps) => {
  const {content, btnText, onBtnClick, disabled, tips} = props;
  return (
    <View className={styles.footerBar_container}>
      {
        tips ? <View className={styles.error_tips}>{tips}</View> : null
      }
      <View className={styles.footerBar_main}>
        <View className={styles.footerBar_left}>{content}</View>
        <View className={styles.footerBar_right}>
          <CyButton disabled={disabled} round type='primary' className={styles.footerBar_btn} onClick={onBtnClick}>
            {btnText}
          </CyButton>
        </View>
      </View>
      <CySafeArea position='bottom'></CySafeArea>
    </View>
  );
};

export default RepaymentFooterBar;
