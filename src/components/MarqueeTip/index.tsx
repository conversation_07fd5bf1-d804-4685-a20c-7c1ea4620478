import React from 'react';
import {Image, View, ViewProps} from '@tarojs/components';
import classNames from 'classnames';
import noticeIcon from '~icons/icon-notice.png';
import noticeIconDark from '~images/icon/<EMAIL>'
import styles from './index.module.scss';

interface MarqueeTipProps extends ViewProps {
  // 走马灯内容
  content: string;
  // 内容区域自定义style
  contentStyle?: React.CSSProperties;
  // 图标自定义style
  iconStyle?: React.CSSProperties;
  // 背景颜色
  backgroundColor?: string;
  // 动画持续时间（秒）
  duration?: number;
  // 字体颜色
  color?: string;
  // 是否无缝滚动
  loopNoGap?: boolean;
  // 是否使用深色图标
  darkIcon?: boolean;
}

const MarqueeTip: React.FC<MarqueeTipProps> = ({
  content,
  contentStyle,
  iconStyle,
  backgroundColor = '#E9EDFF',
  duration = 10,
  color = '#bbb',
  loopNoGap = true,
  darkIcon = false,
  ...restProps
}) => {
  return (
    <View className={styles.marquee_tip} style={{color, backgroundColor}} {...restProps}>
      <View className={styles.tip_icon} style={{backgroundColor}}>
        <Image className={styles.horn_img} src={darkIcon ? noticeIconDark : noticeIcon} style={iconStyle} />
      </View>
      <View className={classNames(styles.marquee, styles.loopNoGap)} style={{animationDuration: `${duration}s`}}>
        <View className={styles.marquee_content} style={contentStyle}>
          {content}
        </View>
        {loopNoGap ? (
          <View className={styles.marquee_content} style={contentStyle}>
            {content}
          </View>
        ) : null}
      </View>
    </View>
  );
};

export default MarqueeTip;
