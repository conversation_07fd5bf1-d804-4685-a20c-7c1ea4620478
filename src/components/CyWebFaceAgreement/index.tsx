import React, {useRef} from 'react';
import {View} from '@tarojs/components';
import {showToast} from '../../utils/common';

export default () => {
  const contracts = [];
  const isContractChecked = useRef(false);
  const url = useRef('');
  const inFaceView = useRef(false);

  const goStartFace = () => {
    if (!isContractChecked.current) {
      showToast('请勾选同意《人脸识别服务用户授权书》');
      return;
    }
    inFaceView.current = true;
  };
  return (
    <>
      <iframe
        id='zoloz'
        src={url.current}
        progress='false'
        allow='microphone;camera;midi;encrypted-media;'
        allowusermedia
        allowfullscreen='true'
        webkitallowfullscreen='true'
        width='100%'
        height='100%'></iframe>
      <View className='face-agreement-page'>
        <View className='title'>人脸识别</View>
        <View className='top-tips'>为保障您的个人信息安全，请按照提示进行验证</View>
      </View>
    </>
  );
};
