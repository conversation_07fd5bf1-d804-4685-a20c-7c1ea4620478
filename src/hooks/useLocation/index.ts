import {useEffect, useRef, useState} from 'react';
import AMapLoader from '@amap/amap-jsapi-loader';
import Taro from '@tarojs/taro';

export type PositionType = {
  lat: string | undefined;
  lng: string | undefined;
};
export type UseLocationParams = {
  onSuccess: (data: any) => void;
  onFail: (error: any) => void;
  isRetry: boolean;
};

export default function useH5Location(params?: UseLocationParams) {
  const [position, setPosition] = useState<PositionType>({
    lat: undefined,
    lng: undefined,
  });

  const aMap = useRef<any>();

  useEffect(() => {
    loadAMap();
  }, []);

  const loadAMap = async () => {
    return await AMapLoader.load({
      // 申请好的Web端开发者Key，首次调用 load 时必填 生产环境70b6d3b8d7234408b087575e6d4c66b8
      key: process.env.TARO_APP_AMAP_CODE || '',
      version: '2.0', // 指定要加载的 JSAPI 的版本，缺省时默认为 1.4.15
      plugins: ['AMap.Geolocation'], // 需要使用的的插件列表，等
    }).then(amap => {
      aMap.current = amap;
    });
  };

  const startLocation = async ({
    onSuccess,
    onFail,
    isRetry = true,
  }: {
    onSuccess?: (data: any) => void;
    onFail?: (error: any) => void;
    isRetry?: boolean;
  }) => {
    if (!aMap.current) {
      await loadAMap();
      return;
    }
    const AMap = aMap.current;
    AMap.plugin('AMap.Geolocation', function () {
      const geolocation = new AMap.Geolocation({
        // 是否使用高精度定位，默认：true
        enableHighAccuracy: true,
        // 设置定位超时时间，默认：无穷大
        timeout: 10000,
        // 定位按钮的停靠位置的偏移量
        offset: [10, 20],
        //  定位成功后调整地图视野范围使定位位置及精度范围视野内可见，默认：false
        zoomToAccuracy: true,
        //  定位按钮的排放位置,  RB表示右下
        position: 'RB',
      });

      geolocation.getCurrentPosition(function (status: string, result: any) {
        if (status === 'complete') {
          setPosition({
            lat: `${result.position.lat}`,
            lng: `${result.position.lng}`,
          });
          onSuccess?.(result);
        } else {
          console.log('定位失败', result);
          onFail?.(result);
          showAlert(() => {
            if (isRetry) {
              startLocation({onSuccess, onFail, isRetry});
            }
          });
        }
      });
    });
  };

  const showAlert = (cb: () => void) => {
    Taro.showModal({
      title: '提示',
      content: '长银五八需要获得您的地理位置信息！请打开手机设置中的位置信息权限！',
      success: res => {
        cb();
      },
    });
  };

  return {startLocation, position};
}
