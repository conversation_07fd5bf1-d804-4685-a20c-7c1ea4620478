import React, { FC, ForwardRefRenderFunction, Ref, forwardRef, useEffect, useImperativeHandle, useMemo, useRef, useState } from 'react';
import {
  CommonEventFunction,
  PickerView,
  PickerViewColumn,
  PickerViewProps,
  Text,
  View,
  ViewProps,
} from '@tarojs/components';
import { CyPopup } from 'hncy58-taro-components';

interface CyPickerOption {
  label: string;
  value: string;
}
interface Props extends ViewProps {
  defaultValue?: number[] | undefined;
  options: CyPickerOption[] | CyPickerOption[][];
  visible?: boolean;
  onChange: (value: any, label: any, options: any[]) => void;
  title?: string;
}

const isValidDefaultVal = (val: number | number[] | undefined) => {
  if (Array.isArray(val)) {
    return val.every(item => typeof item === 'number' && item >= 0);
  }
  return typeof val === 'number' && val >= 0;
}
/**
 * 组件名称：选择器组件
 * 组件描述：对选择器组件做包装，用来兼容react-hook-formreact-hook-form或后续其他逻辑
 */
const CyPicker: ForwardRefRenderFunction<unknown, Props> = (
  { options, visible, title, children, onChange, ...props },
  ref,
) => {
  const currentVal = useRef(isValidDefaultVal(props.defaultValue) || [0])
  const [popVisible, setPopVisible] = useState(visible || false);

  useImperativeHandle(
    ref,
    () => ({

    }),
    [],
  );

  useEffect(() => {
    setPopVisible(visible || false);
  }, [visible])

  const confirm = () => {
    console.log('confirm', currentVal.current);
    const val = currentVal.current;
    if (isSinglePicker) {
      const index = Array.isArray(val) ? val[0] : val ? 0 : 0;
      const item = (options as CyPickerOption[])?.[index];
      onChange?.(item.value, item.label, options);
    } else {
      const indexes = val as number[];
      const selectedItems = options.map((col, i) => {
        const selectedItem = (col as CyPickerOption[])[indexes[i] ?? 0];
        return selectedItem;
      });
      const values = selectedItems.map(item => item.value);
      const labels = selectedItems.map(item => item.label);
      onChange?.(values, labels, selectedItems);
    }
    setPopVisible(false);
  }

  const handleChange: CommonEventFunction<PickerViewProps.onChangeEventDetail> = (e) => {
    console.log('picker change', e.detail.value);
    currentVal.current = e.detail.value;
  };

  const isSinglePicker = useMemo(() => {
    return !Array.isArray(options[0]);
  }, [options]);

  return (
    <>
      <View onClick={() => setPopVisible(true)}>{children}</View>
      <CyPopup visible={popVisible} align='bottom' onClose={() => setPopVisible(false)}>
        <View className='cy-picker-header'>
          <Text className='cy-picker-cancel' onClick={() => setPopVisible(false)}>取消</Text>
          <Text className='cy-picker-title'>{title}</Text>
          <Text className='cy-picker-confirm' onClick={confirm}>确认</Text>
        </View>
        <PickerView {...props} indicatorClass='cy-picker-indicator-item' style='height: 250px; padding: 0 24px' onChange={handleChange}>
          {
            !isSinglePicker ? options.map((optionCol, i) => {
              return (
                <PickerViewColumn key={i}>
                  {
                    (optionCol as CyPickerOption[]).map((item, idx) =>
                      <View className='cy-picker-item' key={idx}>{item.label}</View>
                    )
                  }
                </PickerViewColumn>
              )
            }) : (
              <PickerViewColumn>
                {
                  (options as CyPickerOption[]).map((item, idx) =>
                    <View className='cy-picker-item' key={idx}>{item.label}</View>
                  )
                }
              </PickerViewColumn>
            )
          }
        </PickerView>
      </CyPopup>
    </>
  );
};

export default forwardRef(CyPicker);
