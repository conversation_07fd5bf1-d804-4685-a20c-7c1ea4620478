
.item {
  position: relative;
  display: flex;

  &:last-child {
    .line {
      display: none;
    }
  }
}

.dot {
  position: absolute;
  left: 0;
  top: 34px; // 与标题文本中心对齐
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background-color: $color-primary;
  z-index: 2;
}

.line {
  position: absolute;
  left: 6px;
  top: 34px; // 与标题文本底部对齐
  height: 100%;
  background-color: $color-border-normal;
  width: 2px;
}


.content {
  flex: 1;
  margin-left: 46px;
  position: relative;
}

// 最后一项不需要下延线
.item:last-child .content::after {
  display: none;
}

.header {
  display: flex;
  align-items: center;
  cursor: pointer;
  height: 80px;
}

.detail {
  border-radius: 8px;
  margin: 0;
  color: $color-text-secondary;
  font-size: 26px;
  max-height: 0;
  line-height: 46px;
  overflow: hidden;
  transition: max-height 0.3s;
}

.detailOpen {
  max-height: 400px; // 适当调整
}