# 业务说明

## 首页 pages/home/<USER>


### 接口
- api/statistics/customer/acceptance/summary 进件情况统计
- api/employee/allmenu 所有菜单
- api/employee/mymenu 我的菜单 

## 认领客户 pages/claim-customer/claim-customer

### 接口
- api/customer/claim 认领客户

## 我的二维码 pages/my-app-qrcode/my-app-qrcode

### 特别说明
先调用高德定位功能获取经纬度；在调用高德的逆地理位置API获取位置信息;在调用接口获取我的二维码

### 接口
- api/employee/qrCode 我的二维码

## APP二维码 pages/my-qrcode/my-qrcode?type=app

### 接口
- api/employee/qrCode/generate app二维码

## 我的薪酬 pages/my-salary/my-salary

### 接口
- api/employee/permission 获取部门权限
- api/hr/salary 获取薪酬信息

## 逾期查询 pages/overdue-query/overdue-query

### 特别说明
- 客户借据的溢缴款信息是从api/postloan/loaninfo/unsettled接口中获取的，再将溢缴款信息合并到api/postloan/overview接口返回的的billList中进行展示
- 照片的获取过程是先获取照片的路径信息，在调用接口获取照片的base64进行展示
### 接口
- api/customer/search 搜索客户
- api/postloan/overview 逾期信息查询

客户信息
- api/postloan/overCustomer/historyAddressList  历史位置信息
- api/postloan/photos 获取照片信息
- api/postloan/photo 获取照片的base64

逾期信息
- api/postloan/collectRecord 催收记录
- api/postloan/delayCus/lawSituation 诉讼信息
- api/postloan/lawSuit/byCertId 司法分期客户
- api/postloan/mobileList/byCertId 联系记录
- api/postloan/loaninfo/unsettled 借据信息

## 分期试算 pages/stage-trial/stage-trial

### 接口
- api/postloan/byStage/tryBalance 分期试算

## 分期试算结果 pages/stage-trial-result/stage-trial-result

