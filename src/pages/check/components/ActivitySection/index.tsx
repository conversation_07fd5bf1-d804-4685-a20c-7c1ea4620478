import {Image, Text, View} from '@tarojs/components';
import {showToast} from '~utils/common';
import Section from '../Section';
import styles from './index.module.scss';

const activities = [
  {
    image: 'https://hncy58test-cyd.oss-cn-beijing.aliyuncs.com/multi-platform/activity/activity-birthday.png',
    link: '',
  },
  {
    image: 'https://hncy58test-cyd.oss-cn-beijing.aliyuncs.com/multi-platform/activity/activity-new-user.png',
    link: '',
  },
];

export default () => {
  const goLink = (link: string) => {
    // TODO: 跳转
    showToast('功能开发中');
  };
  return (
    <Section title='热门活动'>
      <View className={styles.content}>
        {activities.map((item, index) => (
          <View className={styles.activityBanner} key={index} onClick={() => goLink(item.link)}>
            <Image className={styles.activityImage} src={item.image}></Image>
          </View>
        ))}
      </View>
    </Section>
  );
};
