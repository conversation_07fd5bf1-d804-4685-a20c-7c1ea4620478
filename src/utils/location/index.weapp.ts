import Taro from '@tarojs/taro';
import {getLocationRegeo} from '../../services/location';
import {showModal, showToast} from '../common';
import {isAndriod, isIos} from '../platform';
import { CyModal } from 'hncy58-taro-components';
import { saveCustomerLocationInfo } from '~services/user';

type GetLocationOption = {
  onSuccess: (res: API.LocationPoint) => void;
  onFail?: (error: {message: string}) => void;
  usage: string;
};

const Setting = 'scope.userFuzzyLocation';

const openLocationSetting = (cb: () => void) => {
  Taro.openSetting({
    success (res) {
      console.log('原生设置页回调->', res)
      const { authSetting } = res || {}
      // 已开启位置授权
      if (authSetting.hasOwnProperty(Setting) && authSetting[Setting]) {
        console.log('已成功开启位置服务->But这里没有返回任何位置信息相关信息')
        // 再次手动获取用户位置信息
        cb()
      }
    },
    fail (err) {
      showToast('获取位置信息失败')
      console.log('打开设置页失败->', err)
    }
  })
}


//获取单次定位位置
export const getLocation = async ({onSuccess, onFail, usage = ''}: GetLocationOption) => {
  try {
    const start = () => {
      Taro.getSetting({
        success: (res) => {
          const setting = 'scope.userFuzzyLocation'
          console.log('getSetting res=====', res.authSetting);
          const { authSetting } = res || {}
          // 已开启位置授权
          if (authSetting.hasOwnProperty(Setting) && authSetting[Setting]) {
            startGetLocation()
          } else {
            CyModal.create({
              title: '提示',
              content: `“长银五八消费金融”想使用您的当前位置${usage ? ',' + usage : ''}`,
              onConfirm: () => {
                startGetLocation()
              },
              isShowCancel: true
            })
          }
        }
      });
    }
    const startGetLocation = () => Taro.getLocation({
      type: 'gcj02',
      success: async data => { 
         console.log('获取地理位置结果', data);
        const latitude = `${data.latitude}`;
        const longitude = `${data.longitude}`;
        console.log('获取定位', {latitude, longitude});
        if (!isValid(latitude, longitude)) {
          onFail && onFail({message: '获取位置失败'});
          return;
        }
        try {
          saveCustomerLocationInfo({
            longitude: longitude + '',
            latitude: latitude + '',
          })
        } catch (e) {
          console.error('保存地理位置信息失败', e)
        }
        onSuccess({latitude, longitude})
      },
      fail: () => {
        console.log('获取地理位置失败');
        Taro.showModal({
          title: '获取位置信息失败',
          content: '请检查是否已开启定位服务',
          showCancel: true,
          confirmText: '去设置',
          success: () => {
            openLocationSetting(startGetLocation)
          },
        })
      },
    })
    start()
    // Taro.getLocation({
    //   type: 'gcj02',
    //   success: async data => {
    //     console.log('获取地理位置结果', data);
    //     const latitude = `${data.latitude}`;
    //     const longitude = `${data.longitude}`;
    //     console.log('获取定位', {latitude, longitude});
    //     if (!isValid(latitude, longitude)) {
    //       onFail && onFail({message: '获取位置失败'});
    //       return;
    //     }

    //     if (regeo) {
    //       await getLocationDetail({lon: longitude, lat: latitude, onSuccess, onFail});
    //     } else {
    //       const address: API.AddressComponent = {
    //         latitude,
    //         longitude,
    //         country: '',
    //         countryCode: '',
    //         province: '',
    //         city: '',
    //         citycode: '',
    //         district: '',
    //         streetNumber: {
    //           street: '',
    //           number: '',
    //         },
    //         adcode: '',
    //       };
    //       onSuccess(address);
    //     }
    //   },
    //   fail: () => {
    //     console.log('获取地理位置失败');
    //   },
    // });
  } catch (error) {
    console.log('error=', error);
    onFail && onFail({message: '获取位置失败'});
  }
};

// const getLocationDetail = async ({
//   lon,
//   lat,
//   onSuccess,
//   onFail,
// }: {
//   lon: string;
//   lat: string;
//   onSuccess: (res: API.LocationPoint) => void;
//   onFail?: (error: {message: string}) => void;
// }) => {
//   try {
//     const data = await getLocationRegeo({
//       lon,
//       lat,
//     });
//     if (!data.regeocode.addressComponent.countryCode) {
//       data.regeocode.addressComponent.countryCode = '156';
//     }
//     const address = controlAddressForNotArray(data.regeocode.addressComponent);
//     address.latitude = lat;
//     address.longitude = lon;
//     onSuccess(address);
//   } catch (error) {
//     console.log('error=', error);
//     onFail && onFail({message: '网络错误'});
//   }
// };

//开始持续地位
export const startUpdatingLocation = () => {};

//关闭持续地位
export const stopUpdatingLocation = () => {};

//获取持续定位地址信息
// export const getUpdatingLocation = async ({onSuccess, regeo = true, onFail}: GetLocationOption) => {
//   try {
//     let {latitude, longitude}: {latitude: string; longitude: string} = await LocationManager.getUpdatingLocation();

//     if (!isValid(latitude, longitude)) {
//       onFail && onFail({message: '获取位置失败'});
//       return;
//     }
//     latitude = `${latitude}`;
//     longitude = `${longitude}`;
//     if (regeo) {
//       await getLocationDetail({lon: longitude, lat: latitude, onSuccess, onFail});
//     } else {
//       const address: API.AddressComponent = {
//         latitude,
//         longitude,
//         country: '',
//         countryCode: '',
//         province: '',
//         city: '',
//         citycode: '',
//         district: '',
//         streetNumber: {
//           street: '',
//           number: '',
//         },
//         adcode: '',
//       };
//       onSuccess(address);
//     }
//   } catch (error) {
//     console.log('error=', error);
//     onFail && onFail({message: '获取位置失败'});
//   }
// };

//获取定位是否可用
export const getLocationAuthStatusIsValid: () => boolean = () => {
  return false;
};

//当前应用是否可以使用/申请定位服务
export const getLocationServiceIsValid: () => boolean = () => {
  return false;
};

//获取定位权限状态
export const getAuthorizationStatus: () => string = () => {
  return '0';
};

/**
 * 获取定位权限
 * @param openSeting 权限不足时是否提示打开设置
 * @returns
 */
export const requestLocationPermission = async (openSeting = true) => {
  try {
    return false;
  } catch (error) {
    console.log('Location Accuracy Permission Error: ', error);
    return false;
  }
};
//判断经纬度有效
const isValid = (latitude: string | number, longitude: string | number) => {
  if (latitude === undefined || latitude === null || longitude === undefined || longitude === null) {
    return false;
  }

  if (
    (typeof latitude !== 'number' && typeof latitude !== 'string') ||
    (typeof longitude !== 'number' && typeof longitude !== 'string')
  ) {
    return false;
  }
  if (typeof latitude === 'number') {
    if (Number.isNaN(latitude) || latitude < 0) {
      return false;
    }
  }

  if (typeof longitude === 'number') {
    if (Number.isNaN(longitude) || longitude < 0) {
      return false;
    }
  }

  if (typeof latitude === 'string') {
    if (latitude.length === 0 || Number.isNaN(parseFloat(latitude)) || parseFloat(latitude) < 0) {
      return false;
    }
  }

  if (typeof longitude === 'string') {
    if (longitude.length === 0 || Number.isNaN(parseFloat(longitude)) || parseFloat(longitude) < 0) {
      return false;
    }
  }
  return true;
};

/**
 * 处理高德逆地理位置问题（在没值的时候返回的是空数组，而不是空字符串。处理返回空字符串）
 * @param address
 * @returns
 */
// const controlAddressForNotArray: (address: API.AddressComponent) => API.AddressComponent = address => {
//   console.log('处理前=', address);
//   const newAddress = {...address};
//   newAddress.country = controlWord(newAddress.country);
//   newAddress.countryCode = controlWord(newAddress.countryCode);
//   newAddress.province = controlWord(newAddress.province);
//   newAddress.city = controlWord(newAddress.city);
//   newAddress.citycode = controlWord(newAddress.citycode);
//   newAddress.district = controlWord(newAddress.district);
//   newAddress.adcode = controlWord(newAddress.adcode);
//   if (newAddress.streetNumber) {
//     newAddress.streetNumber.number = controlWord(newAddress.streetNumber.number);
//     newAddress.streetNumber.street = controlWord(newAddress.streetNumber.street);
//     newAddress.streetNumber.direction = controlWord(newAddress.streetNumber.direction);
//     newAddress.streetNumber.distance = controlWord(newAddress.streetNumber.distance);
//   }
//   newAddress.towncode = controlWord(newAddress.towncode);
//   newAddress.township = controlWord(newAddress.township);
//   console.log('处理后=', newAddress);
//   return newAddress;
// };

//当为空时，和1.0保持一致，传[]字符串
const controlWord: (name: any) => string = name => {
  if (name === undefined) return '[]';
  if (typeof name === 'string') {
    return name;
  } else if (Array.isArray(name)) {
    if (name.length > 0) {
      return controlWord(name[0]);
    } else {
      return '[]';
    }
  } else {
    return '[]';
  }
};
