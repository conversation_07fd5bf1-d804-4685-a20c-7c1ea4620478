import { canRaiseAmount } from "~services/raiseAmount"
import { router } from "./common";
const toRaiseAmountCheck = async () => { 
  try {
    const { resultCode, nextApplyDate = '' } = await canRaiseAmount()
    if (['pass', 'unprocessed', 'unactived'].includes(resultCode) || !resultCode) {
      router.push({
        url: `/modules/raise_amount/index/index?state=${resultCode}`,
      });
    } else {
      router.push({
        url: `/modules/raise_amount/state/index?state=${resultCode}&nextApplyDate=${nextApplyDate}`,
      });
    }
  } catch (error) {
    console.log('error', error)
  }
}

export default toRaiseAmountCheck