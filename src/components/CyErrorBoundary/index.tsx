import React from 'react';
import {showModal, showToast} from '../../utils/common';
import './index.scss';

interface ErrorBoundaryState {
  hasError: boolean;
  error?: Error;
}

interface ErrorBoundaryProp {
  children?: React.ReactNode;
}

/**
 * 组件名称：错误边界处理组件
 * 组件描述：错误处理
 */
class CyErrorBoundary extends React.Component<ErrorBoundaryProp, ErrorBoundaryState> {
  constructor(props: ErrorBoundaryProp) {
    super(props);
    this.state = {hasError: false};
  }

  static getDerivedStateFromError(error: Error): ErrorBoundaryState {
    // 当渲染进程中发生错误时，更新state
    return {hasError: true, error};
  }

  async componentDidCatch(error: Error, info: React.ErrorInfo): Promise<void> {
    showToast(error.message || '未知错误');

    showModal({
      title: '应用错误',
      content: error.message || '未知错误',
      showCancel: false,
    });
    console.log('ErrorBoundary caught an error:', error, info);
  }

  render(): React.ReactNode {
    // if (this.state.hasError) {
    //   // 你可以渲染任意的UI来展示错误状态
    //   return (
    //     <View className='comp-cy-error-boundary'>
    //       <Text>Something went wrong: {this.state.error?.message || '未知错误'}</Text>
    //     </View>
    //   );
    // }
    return this.props.children;
  }
}

export default CyErrorBoundary;
