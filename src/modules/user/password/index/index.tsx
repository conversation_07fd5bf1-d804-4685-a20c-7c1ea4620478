import PageWrap from '~components/PageWrap';
import styles from './index.module.scss';
import { List } from '@taroify/core';
import ListItem from '~components/List/ListItem';
import { router } from '~utils/common';
import { useMemo } from 'react';
import useUserStore from '~store/user';
import { useLendingProgress } from '~hooks/useLendingProgress';
import Cy<PERSON>pinner from '~components/CySpinner';
import setIcon from '~images/password/<EMAIL>';
import editIcon from '~images/password/<EMAIL>';
import resetIcon from '~images/password/<EMAIL>';
import { Image, View } from '@tarojs/components';

export default () => {
  const { user } = useUserStore();
  const [stateMap] = useLendingProgress();

  const goChangePassword = () => {
    router.push({
      url: '/modules/user/change-password/index',
    });
  };

  const goResetPassword = (isReset = false) => {
    router.push({
      url: isReset
        ? '/modules/user/set-password-verifycode/index?type=reset'
        : '/modules/user/set-password-verifycode/index',
    });
  };

  const canUpdatePassword = useMemo(() => {
    return user.registerFlag && stateMap.needTradePassword !== undefined && !stateMap.needTradePassword;
  }, [user, stateMap]);

  // if (stateMap.needBankCard === undefined) return <CySpinner />;

  return (
    <PageWrap className={styles.passwordPage}>
      <View className={styles.header}>
        <View className={styles.title}>密码管理</View>
        <View className={styles.subtitle}>设置或修改您的交易密码</View>
      </View>
      <View className={styles.tip}>为保障您的资金安全，请勿向他人透露交易密码</View>
      {
        stateMap.needBankCard === undefined ? <CySpinner /> : (
          <List className={styles.listContainer}>
            {
              canUpdatePassword ? (
                <>
                  <View onClick={() => goChangePassword()} className={styles.listItem}>
                    <Image src={editIcon} className={styles.listThumb} />
                    <View>
                      <View className={styles.listTitle}>修改交易密码</View>
                      <View className={styles.listSubtitle}>修改您当前的交易密码</View>
                    </View>
                  </View>
                  <View onClick={() => goResetPassword(true)} className={styles.listItem}>
                    <Image src={resetIcon} className={styles.listThumb} />
                    <View>
                      <View className={styles.listTitle}>重置交易密码</View>
                      <View className={styles.listSubtitle}>当前密码遗失重新设置交易密码</View>
                    </View>
                  </View>
                </>
              ) : 
                <View onClick={() => goResetPassword()} className={styles.listItem}>
                  <Image src={setIcon} className={styles.listThumb} />
                  <View>
                    <View className={styles.listTitle}>设置交易密码</View>
                    <View className={styles.listSubtitle}>使用借还款功能需设置您的交易密码</View>
                  </View>
                </View>
            }
          </List>
        )
      }
    </PageWrap>
  );
};
