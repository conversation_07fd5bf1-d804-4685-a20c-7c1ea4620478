.loan-certificate-wrapper {
  display: flex;
  flex-direction: column;
  padding: 20px;
}

.loan-certificate-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 10px;
  width: 100%;
  &-button {
    margin-top: 10px;
    width: 100%;
  }
  &-image {
    flex: 1;
    display: flex;
    flex-direction: column;
    border-radius: 8px;
  }
  &-content {
    font-size: 28px;
    padding: 20px;
    color: #333;
    border-radius: 8px;
    background-color: #fff;
    display: flex;
    flex-direction: column;
    gap: 20px;
    &-item {
      display: flex;
      flex-direction: row;
      justify-content: space-between;
      align-items: center;
      &-left{
        flex: 1;
      }
      &-left-value {
        color: $color-primary;
      }
      &-text {
        color: #999;
      }
    }
  }
  &-button-text {
    color: #999;
    text-align: center;
  }
}