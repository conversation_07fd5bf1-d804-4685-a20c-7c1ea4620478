import Taro from '@tarojs/taro';
import { produce } from 'immer';
import { create } from 'zustand';
import { devtools, persist, subscribeWithSelector } from 'zustand/middleware';
import { getAccessToken } from '~services/user';
import useUserStore from './user';
import { getSystemConfig } from '~services/system';

type CompanyInfoState = {
    companyInfo: API.UpdateCompanyInfoReqParams;
    isCompanyInfoUpdate: boolean;
    setCompanyInfo: (companyInfo: API.UpdateCompanyInfoReqParams) => void;
    setCompanyInfoUpdate: (val: boolean) => void;
};

const useCompanyInfoStore = create(
    subscribeWithSelector(
        devtools<CompanyInfoState>((set, get) => ({
            companyInfo: {
                workName: '',
                workType: 'work',
                monthInCome: '',
                companyName: '',
            },
            isCompanyInfoUpdate: false,
            setCompanyInfo: (companyInfo) => {
                set({
                    companyInfo: companyInfo,
                });
            },
            setCompanyInfoUpdate: (val: boolean) => {
                set({
                    isCompanyInfoUpdate: val,
                });
            }
        })),
    )
);


export default useCompanyInfoStore;