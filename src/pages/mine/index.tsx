import {Image, View} from '@tarojs/components';
import Taro, {useDidShow} from '@tarojs/taro';
import PageWrap from '~components/PageWrap';
import cpscIcon from '~images/mine/<EMAIL>';
import gdgnIcon from '~images/mine/<EMAIL>';
import jqzmIcon from '~images/mine/<EMAIL>';
import kfzxIcon from '~images/mine/<EMAIL>';
import kqbIcon from '~images/mine/<EMAIL>';
import qbdhIcon from '~images/mine/<EMAIL>';
import szIcon from '~images/mine/<EMAIL>';
import teIcon from '~images/mine/<EMAIL>';
import wderIcon from '~images/mine/<EMAIL>';
import wdhtIcon from '~images/mine/<EMAIL>';
import wdzlIcon from '~images/mine/<EMAIL>';
import yhkIcon from '~images/mine/<EMAIL>';
import {getCustomerInfo, signout} from '~services/user';
import useMainStore from '~store/main';
import useUserStore from '~store/user';
import Card from '../../components/Card';
import {goWebviewLinkOrRoute, router, showToast} from '../../utils/common';
import ActivitySwiper from '../check/components/ActivitySwiper';
import EntryBar from '../check/components/EntryBar';
import AvatarBar from './components/AvatarBar';
import BillCard from './components/BillCard';
import styles from './index.module.scss';
import toRaiseAmountCheck from '~utils/toRaiseAmountCheck';
import { goCusServiceCenter } from '~utils/customerCenterUrlCache';
import NoticeBar from './components/NoticeBar';
import { useUserRegisterInterceptor } from '~hooks/useUserRegisterInterceptor';
import logo from '~images/mine/gr-dt.png';
import bg from '~images/mine/grbj.png';
import useRepaymentStore from '~store/repayment';
const Mine = () => {
  /** 1. 变量定义 */
  const {user, logout, setUser, isShowRaiseAmount} = useUserStore();
  const {goLinkWithCreditInterceptor, goLinkWithLoginInterceptor} = useUserRegisterInterceptor();

  const goSettingPage = () => {
    if(!user.mobile) {
      router.push({
        url: '/modules/user/login/index',
      });
      return;
    }
    router.push({
      url: '/modules/user/setting/index',
    });
  };
  
  const fastEntries = [
    {icon: wderIcon, name: '我的额度', onClick: () => goLinkWithCreditInterceptor('/modules/user/amount/index')},
    {icon: wderIcon, name: '借还记录', onClick: () => goLinkWithCreditInterceptor('/modules/loanAndRepayment/index/index')},
    {icon: qbdhIcon, name: '全部待还', onClick: () => goLinkWithCreditInterceptor('/modules/repayment/repayment-all-list/index')},
    {icon: qbdhIcon, name: '密码管理', onClick: () => goLinkWithCreditInterceptor('/modules/user/password/index/index')},
    // {icon: kqbIcon, name: '卡券包', onClick: goLink},
  ];

  const serviceEntries = [
    {icon: yhkIcon, name: '银行卡', onClick: () => goLinkWithCreditInterceptor('/modules/bankcard/bankcard-list/index')},
    {icon: jqzmIcon, name: '结清证明', onClick: () => goLinkWithCreditInterceptor('/modules/loan_certificate/index/index')},
    // {icon: cpscIcon, name: '产品手册', onClick: goLink},
    {icon: wdzlIcon, name: '我的资料', onClick: () => goLinkWithCreditInterceptor('/modules/user/edit-message/index')},
    {icon: kfzxIcon, name: '客服中心', onClick: goCusServiceCenter},
    // {icon: teIcon, name: '提额', onClick: goRaiseAmount, isShow: isShowRaiseAmount},
    // {icon: wdhtIcon, name: '我的合同', onClick: goLink},
    // {icon: gdgnIcon, name: '更多功能', onClick: goLink},
  ];

  /** 3. effects */
  // 更新tabbar
  useDidShow(async () => {
    const {setTabbarIndex} = useMainStore.getState();
    setTabbarIndex(1); // 我的页面索引
    useUserStore.getState().updateUserInfo();
    useRepaymentStore.getState().updateRepaymentSummary();
  });

  /** 4. 方法定义 */
  const handleSignout = async () => {
    if (!user.mobile) {
      return;
    }
    await signout();
    logout();
    showToast('退出登录成功');
  };

  /** 5. ui */
  return (
    <PageWrap className={styles['page-mine']} style={{backgroundImage: `url(${bg})`, backgroundSize: 'cover', backgroundRepeat: 'no-repeat',}}>
      <View className={styles['page-content']}>
        <View className={styles['page-header']} style={{paddingTop: Taro.getWindowInfo().statusBarHeight}}>
          <Image className={styles.szIcon} src={szIcon} onClick={goSettingPage} />
        </View>
        <AvatarBar name={user.name} mobile={user.mobile} />
        {
          user.mobile && !user.registerFlag ? (
            <NoticeBar notice='完成实名认证，领取借钱额度' actionText='去认证' onClick={() => {router.push({url: '/modules/credit_authorization/identify/index'})}} />
          ) : null
        }
        <View className='vertical-gap'></View>
        <Card>
          <EntryBar entries={fastEntries} entryItemStyle={{marginTop: 0}}/>
        </Card>
        <View className='vertical-gap'></View>
        <BillCard />
        <View className='vertical-gap'></View>
        <Card title='常用服务'>
          <EntryBar size='small' entries={serviceEntries} />
        </Card>
        <View className='vertical-gap'></View>
        <ActivitySwiper />
        {
          (!user.registerFlag) && (
            <View className={styles['page-footer']}>
            <Image src={logo} className={styles['page-footer-logo']}/>
          </View>
          ) 
        }
      </View>
    </PageWrap>
  );
};

export default Mine;
