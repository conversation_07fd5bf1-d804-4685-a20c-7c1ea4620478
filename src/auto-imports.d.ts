/* eslint-disable */
/* prettier-ignore */
// @ts-nocheck
// noinspection JSUnusedGlobalSymbols
// Generated by unplugin-auto-import
export {}
declare global {
  const useAPICheck: typeof import('taro-hooks')['useAPICheck']
  const useAccountInfo: typeof import('taro-hooks')['useAccountInfo']
  const useActionSheet: typeof import('taro-hooks')['useActionSheet']
  const useAlertBeforeUnload: typeof import('taro-hooks')['useAlertBeforeUnload']
  const useApp: typeof import('taro-hooks')['useApp']
  const useAppBaseInfo: typeof import('taro-hooks')['useAppBaseInfo']
  const useArrayBuffer: typeof import('taro-hooks')['useArrayBuffer']
  const useAudio: typeof import('taro-hooks')['useAudio']
  const useAuthorize: typeof import('taro-hooks')['useAuthorize']
  const useBackground: typeof import('taro-hooks')['useBackground']
  const useBattery: typeof import('taro-hooks')['useBattery']
  const useBluetooth: typeof import('taro-hooks')['useBluetooth']
  const useBoolean: typeof import('taro-hooks')['useBoolean']
  const useBrightness: typeof import('taro-hooks')['useBrightness']
  const useCamera: typeof import('taro-hooks')['useCamera']
  const useChooseAddress: typeof import('taro-hooks')['useChooseAddress']
  const useClipboardData: typeof import('taro-hooks')['useClipboardData']
  const useCreation: typeof import('taro-hooks')['useCreation']
  const useDebounce: typeof import('taro-hooks')['useDebounce']
  const useDebounceFn: typeof import('taro-hooks')['useDebounceFn']
  const useDeviceInfo: typeof import('taro-hooks')['useDeviceInfo']
  const useEnterOptions: typeof import('taro-hooks')['useEnterOptions']
  const useEnv: typeof import('taro-hooks')['useEnv']
  const useEvent: typeof import('taro-hooks')['useEvent']
  const useFile: typeof import('taro-hooks')['useFile']
  const useFrom: typeof import('taro-hooks')['useFrom']
  const useImage: typeof import('taro-hooks')['useImage']
  const useInvoice: typeof import('taro-hooks')['useInvoice']
  const useKeyboard: typeof import('taro-hooks')['useKeyboard']
  const useLatest: typeof import('taro-hooks')['useLatest']
  const useLaunchOptions: typeof import('taro-hooks')['useLaunchOptions']
  const useLoading: typeof import('taro-hooks')['useLoading']
  const useLocation: typeof import('taro-hooks')['useLocation']
  const useLogin: typeof import('taro-hooks')['useLogin']
  const useManualPullDownRefresh: typeof import('taro-hooks')['useManualPullDownRefresh']
  const useMap: typeof import('taro-hooks')['useMap']
  const useMemoizedFn: typeof import('taro-hooks')['useMemoizedFn']
  const useMenuButtonBoundingClientRect: typeof import('taro-hooks')['useMenuButtonBoundingClientRect']
  const useModal: typeof import('taro-hooks')['useModal']
  const useMotion: typeof import('taro-hooks')['useMotion']
  const useMount: typeof import('taro-hooks')['useMount']
  const useNavigationBar: typeof import('taro-hooks')['useNavigationBar']
  const useNetworkType: typeof import('taro-hooks')['useNetworkType']
  const useOnline: typeof import('taro-hooks')['useOnline']
  const usePage: typeof import('taro-hooks')['usePage']
  const usePreload: typeof import('taro-hooks')['usePreload']
  const usePromise: typeof import('taro-hooks')['usePromise']
  const useRecord: typeof import('taro-hooks')['useRecord']
  const useRendererUserAgent: typeof import('taro-hooks')['useRendererUserAgent']
  const useRequest: typeof import('taro-hooks')['useRequest']
  const useRequestSubscribeMessage: typeof import('taro-hooks')['useRequestSubscribeMessage']
  const useRouter: typeof import('taro-hooks')['useRouter']
  const useScanCode: typeof import('taro-hooks')['useScanCode']
  const useSelectorQuery: typeof import('taro-hooks')['useSelectorQuery']
  const useStorage: typeof import('taro-hooks')['useStorage']
  const useSystemInfo: typeof import('taro-hooks')['useSystemInfo']
  const useTabBar: typeof import('taro-hooks')['useTabBar']
  const useThrottle: typeof import('taro-hooks')['useThrottle']
  const useThrottleFn: typeof import('taro-hooks')['useThrottleFn']
  const useToast: typeof import('taro-hooks')['useToast']
  const useToggle: typeof import('taro-hooks')['useToggle']
  const useTopBarText: typeof import('taro-hooks')['useTopBarText']
  const useUnmount: typeof import('taro-hooks')['useUnmount']
  const useUpdate: typeof import('taro-hooks')['useUpdate']
  const useUpdateEffect: typeof import('taro-hooks')['useUpdateEffect']
  const useUpdateManager: typeof import('taro-hooks')['useUpdateManager']
  const useUserInfo: typeof import('taro-hooks')['useUserInfo']
  const useVibrate: typeof import('taro-hooks')['useVibrate']
  const useVideo: typeof import('taro-hooks')['useVideo']
  const useVisible: typeof import('taro-hooks')['useVisible']
  const useWeRun: typeof import('taro-hooks')['useWeRun']
  const useWebp: typeof import('taro-hooks')['useWebp']
  const useWindowInfo: typeof import('taro-hooks')['useWindowInfo']
}
