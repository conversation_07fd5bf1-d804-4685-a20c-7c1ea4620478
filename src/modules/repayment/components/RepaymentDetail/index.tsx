import React, {useState} from 'react';
import {Image, Text, View} from '@tarojs/components';
import styles from './index.module.scss';
import MoneyDisplay from '~components/MoneyDisplay';
import { formattedMoney } from '~utils/common';
import currency from 'currency.js';
import dayjs from 'dayjs';
import arrowDownIcon from '~images/icon/<EMAIL>';
import arrowUpIcon from '~images/icon/<EMAIL>';



interface RepaymentDetailProps {
  list: API.RepaymentPlanDetailListItem[];
  loanList: API.LoanDetail[];
}

const RepaymentDetail: React.FC<RepaymentDetailProps> = ({list, loanList}) => {
  const [expandedItems, setExpandedItems] = useState<Record<number, boolean>>({});

  const toggleExpand = (index: number) => {
    setExpandedItems(prev => ({
      ...prev,
      [index]: !prev[index],
    }));
  };

  return (
    <View className={styles.container}>
      {list.map((item, index) => (
        <View key={index} className={styles.itemContainer}>
          <View className={styles.itemHeader} onClick={() => toggleExpand(index)}>
            <View className={styles.amountSection}>
              <Text className={styles.amount}>{formattedMoney(item.totalAmount)}</Text>
              <Image
                className={`${styles.arrow} ${expandedItems[index] ? styles.arrowUp : ''}`}
                src={expandedItems[index] ? arrowUpIcon : arrowDownIcon}
              />
            </View>
            <Text className={styles.period}>{`${item.curTerm}/${item.totalTerm}期`}</Text>
          </View>

          <View className={`${styles.itemDetail} ${expandedItems[index] ? styles.expanded : ''}`}>
            {item.principal && (
              <View className={styles.detailRow}>
                <Text className={styles.detailLabel}>本金：</Text>
                <Text className={styles.detailValue}>¥{item.principal}</Text>
              </View>
            )}

            {item.interest && (
              <View className={styles.detailRow}>
                <Text className={styles.detailLabel}>利息：</Text>
                <Text className={styles.detailValue}>¥{item.interest}</Text>
              </View>
            )}

            {item.penalty && (
              <View className={styles.detailRow}>
                <Text className={styles.detailLabel}>罚息：</Text>
                <Text className={styles.detailValue}>¥{item.penalty}</Text>
              </View>
            )}
          </View>
          {(item.loanInitPrin || item.principal) && (
            <View className={styles.detailSortSummary}>
              {loanList.find(loan => loan.loanNo === item.loanNo)?.startDate}  借款{formattedMoney(item.loanInitPrin || item.principal, {precision: 2, symbol: ''})}元
            </View>
          )}
        </View>
      ))}
    </View>
  );
};

export default RepaymentDetail;
