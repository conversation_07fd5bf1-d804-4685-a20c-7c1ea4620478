.skeletonCard {
  background: #fff;
  border-radius: 12px;
  padding: 0;
  margin: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  position: relative;
}

.topTag {
  position: absolute;
  top: 0;
  left: 0;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 8px 16px;
  border-radius: 0 0 12px 0;
}

.mainContent {
  padding: 40px 20px 20px;
}

.titleSection {
  margin-bottom: 8px;
}

.amountSection {
  margin-bottom: 24px;
}

.buttonSection {
  display: flex;
  gap: 12px;
  margin-bottom: 24px;
}

.skeletonButton {
  height: 36px;
  border-radius: 18px;
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: shimmer 1.5s infinite;
}

.bottomInfo {
  display: flex;
  justify-content: space-between;
  margin-bottom: 16px;
}

.infoItem {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
}

.bottomTip {
  display: flex;
  justify-content: center;
}

.skeletonRect {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: shimmer 1.5s infinite;
  border-radius: 4px;
}

@keyframes shimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

/* 响应式适配 */
@media (max-width: 375px) {
  .skeletonCard {
    margin: 12px;
  }
  
  .mainContent {
    padding: 36px 16px 16px;
  }
  
  .buttonSection {
    gap: 8px;
  }
  
  .skeletonButton {
    height: 32px;
    border-radius: 16px;
  }
}