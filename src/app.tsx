import React, {PropsWithChildren, useEffect, useState} from 'react';
import {CyModal, CyTheme} from 'hncy58-taro-components';
import Taro, { useDidShow, useLaunch, useReady } from '@tarojs/taro';
import {getUserRegisterProtocal} from '~services/agreement';
import {getAccessToken, getCustomerInfo, getUserCreditInfo, queryScanQrCode} from '~services/user';
import useUserStore from '~store/user';
import './app.scss';
import CyErrorBoundary from './components/CyErrorBoundary';
import CySafeAreaProvider from './components/CySafeAreaProvider';
// import './styles/custom-variables.scss';
import './styles/global.global.scss';
import './styles/themes/index.scss';
import {isInWeApp, isInWeb} from './utils/env';
import {lockToPortrait} from './utils/orientation';
import SplashScreen from './utils/splashScreen/splashScreen';
import useMainStore from '~store/main';
import { getSystemConfig } from '~services/system';
import { navigator } from '@tarojs/runtime';
import useRepaymentStore from '~store/repayment';

const App = ({children}: PropsWithChildren) => {
  const {updateUserInfo, updateIsShowRaiseAmount, wechatLogin} = useUserStore();
  const {updateConfig} = useMainStore();
  const {updateRepaymentSummary} = useRepaymentStore();

  //只在测试环境可以配置自定义环境
  const appInit = async () => {
    console.log('mode::::', process.env.TARO_APP_MODE) 
    // 限制小程序pc端
    banMiniprogramPC();
    // 放开能够截图
    openCapture();
    // 初始化活体识别
    initVerify();

    // 登录 + 登录后获取全局配置、用户信息、是否显示提额
    await wechatLogin();
    updateUserInfo()
    updateConfig();
    updateRepaymentSummary();
  };
  
  useDidShow(() => {
    // 营销二维码扫码场景值获取
    recordScanScene()
  })


  // 初始化活体识别
  const initVerify = () => {
    if (isInWeApp()) {
      const verify = require('~modules/verify_mpsdk/main');
      verify.init();
    }
  }
  
  // 限制小程序pc端
  const banMiniprogramPC = () => {
    const systemInfo = Taro.getSystemInfoSync();
    console.log('systemInfo=====', systemInfo)
    if (!['android', 'ios', 'devtools'].includes(systemInfo.platform)) {
      const showTips = () => Taro.showModal({
        title: '温馨提示',
        content: '小程序暂不支持PC端，请使用手机扫码打开',
        showCancel: false,
        success: function (res) {
          showTips();
        },
      })
      showTips()
    }
  }

  // 放开能够截图
  const openCapture = () => {
    // 放开能够截图
    Taro.setVisualEffectOnCapture({
      visualEffect: 'none',
      success:(res) => {
        //
      },
    })
    
  }
  // 营销二维码扫码场景值获取
  const recordScanScene = () => {
    const options = Taro.getLaunchOptionsSync()
    console.log('获取到场景码::::begain', options, options.scene, options.query.scene)
    // 检查场景值，判断是否从二维码进入
    if ([1047, 1048, 1049].includes(options.scene)) {
      if(options.query.scene) {
        console.log('获取到场景码::::options.scene', options.query.scene)
        queryScanQrCode({scene: options.query.scene})
      }
    }
  }

  useEffect(() => {
    // 初始化
    appInit();
  }, []);


  return (
    <>
      <CyErrorBoundary>
        <CyTheme.Provider>{children}</CyTheme.Provider>
      </CyErrorBoundary>
    </>
  );
};
export default App;
