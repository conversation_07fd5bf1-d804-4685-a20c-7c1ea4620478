import Sm4js from '~utils/sm4js';
import {getUserDefaultMap} from './selecteEnv';

const testKey = 'qj6G4VAoEnmKGxKs';
const releaseKey = 'CVRa0IrAeHcyZHTG';

let key = process.env.TARO_APP_MODE === 'bbit' || process.env.TARO_APP_MODE === 'sit' ? testKey : releaseKey;
console.log('getUserDefaultMap=使用');
const map = getUserDefaultMap();
if (map.TARO_APP_API && map.TARO_APP_API.length > 0) {
  key = map.TARO_APP_MODE === 'bbit' || map.TARO_APP_MODE === 'sit' ? testKey : releaseKey;
}
let sm4 = new Sm4js({
  key, // 密钥，前后端一致即可，通常由后端提供
  // iv: '你的初始化向量', // 初始化向量，通常由后端提供
  mode: 'ecb', // 加密模式，例如 'ecb' 或 'cbc'
  cipherType: 'base64', // 输出类型，例如 'hex' 或 'base64'
});

export const cykey = key;
// 加密数据
export const encryptEcb = (source: string) => {
  return sm4.encrypt(source);
};

// 解密数据
export const decryptEcb = (source: string) => {
  return sm4.decrypt(source);
};
