declare namespace API {
  type AccessTokenResData = {
    access_token: string;
  };

  type CreditStates = 'REJECT' | 'PASS' | 'NO_AMOUNT' | 'PROCESSING' | 'FROZEN' | 'INIT' | 'REFUSE';
  type ProductCodes = 'P1109';
  type ProductName = 'PROD_CODE_SALARY' | 'PROD_CODE_GUIDE';

  type UserInfo = {
    name: string;
    username: string;
    mobile: string;
    certId: string;
    registerFlag: boolean;
    custUid: number | string;
  };

  type RepaymenModeItem = {
    repaymentType: string;
    repaymentTypeDesc: string;
    repaymentTypeName: string;
    terms: number[];
  };

  type AmountCreditInfo = {
    activated: boolean;
    availableAmount: number;
    certId: string;
    creditAmount: number;
    effAmount: number;
    expireDate: string;
    frozenAmount: number;
    loanCode: string;
    prodCode: ProductCodes;
    rate: number;
    state: CreditStates;
    stateDesc: string;
    usedAmount: number;
    repaymentModeDTOS: RepaymenModeItem[];
    needCreditApplySuccessView: boolean;
  };
  enum ContractEnum {
    lendingDebt = 'lendingDebt',
    privacy = 'privacy',
  }
  export type SendMsgReq = {
    mobile: string;
    ticket?: string;
    type: SMSTypes;
    randStr?: string;
  };
  // M1001("账户注销"),
  // M1002("双延固定利率协议分期申请"),
  // M1003("分期申请"),
  //  M1004("借款"),
  // M1005("设置交易密码"),
  // M1006("分期申请"),
  //  M1007("登录"),
  // M1008("H5营销活动"),
  //  M1009("银行卡还款"),
  // M1010("重新申请授信"),
  // M1011("申请续签");
  //  M2019 = 挂失解约
  // M1001 = 账户注销
  //  M2001 = 修改预留手机号
  // M2002 = 绑定邀请码 M2003 = 快手客户表单收集 M2004 = 调整预授信额度 M1004 = 借钱 M2005 = 可降息 M2006 = 分期还款 M2007 = 分期还款计划 M2008 = 请选择固定还款日 M2009 = 确认续签 M2010 = 提前续签-确认续签 M1005 = 设置交易密码 M2011 = 重置密码 M2012 = 密码设置 M2013 = 验证手机号 M2014 = 延期还款-联系信息填写 M2015 = 延期还款原因 M2016 = 请选择延长期限 M2017 = 新冠场景-请选择具体原因 M2018 = 关闭额度
  export type SMSTypes =
    | 'M1001'
    | 'M1002'
    | 'M1003'
    | 'M1004'
    | 'M1005'
    | 'M1006'
    | 'M1007'
    | 'M1008'
    | 'M1009'
    | 'M1010'
    | 'M1011';
  type UpdateCompanyInfoReqParams = {
    workType: 'work' | 'free';
    companyName?: string;
    workName?: string;
    monthInCome: string;
  };
  type RegisterReqParams = {
    certId: string;
    name: string;
    mobile: string;
    nationality?: string;
    certStartDate?: string;
    certExpireDate?: string;
    certAddress?: string;
    issueAuthority?: string;
    type?: string;
    productCode?: ProductName;
    appRegisterId?: string;
    ticketId?: string;
    companyInfo?: UpdateCompanyInfoReqParams;
  };
  type ApplyCreidtParams = {
    longitude: string;
    latitude: string;
  };
  type LoginByJscodeReqParams = {
    code: string;
  };

  type getFaceBizTokenParams = {
    certId?: string;
    name?: string;
    requestFrom: string;
  };

  type getFaceRequiredParams = {
    requestFrom: string;
    loanAmount?: number | string;
  };

  type AddressInfoType = {
    /** 省 */
    province?: string;
    /** 市 */
    city?: string;
    /** 区 */
    district?: string;
    /** 详细地址 */
    detailAddress?: string;
  };
  export type baseInfoParams = {
    /** 婚姻状况 */
    maritalStatus: string;
    /** 最高学历 */
    highestEducation: string;
    /** 最高学位 */
    highestDegree: string;
    /** 邮政编码 */
    postCode?: string;
    /** 居住地址 */
    homeAddress: AddressInfoType;
    /** 门牌号 */
    houseNumber?: string;
    /** 居住状况 */
    addressStatus: string;
    detailAddress?: string;
    custUid?: number | string;
  };

  export type IOccupation = {
  code: string;
  name: string;
  level: number;
  children?: IOccupation[];
}

  export type companyInfoParams = {
    /** 就业状况 */
    employeeStatus: string;
    /** 单位名称 */
    company: string;
    /** 单位性质 */
    enterpriseType: string;
    /** 行业类别 */
    industry: string;
    /** 单位地址 */
    companyAddress: AddressInfoType;
    detailAddress: string;
    /** 一级职业 */
    occupation: string;
    /** 二级职业 */
    middleOccupation: string;
    /** 三级职业 */
    littleOccupation: string;
    /** 职务 */
    duty: string;
    /** 职称 */
    positionalTitle: string;
    custUid?: number | string;
    occupationName?: string;
  };

  export type contactInfoParams = {
    /** 联系人姓名 */
    name?: string;
    /** 联系人关系 */
    relation?: string;
    /** 联系人手机号 */
    mobileNo?: string;
  };
  export type contactInfoFormData = {
    contact1Relation: string;
    contact1Name: string;
    contact1Mobile: string;
    contact2Relation: string;
    contact2Name: string;
    contact2Mobile: string;
    custUid?: number | string;
  };

  export type bankcardBindInfo = {
    /** 银行卡号 */
    accountNo: string;
    /** 银行卡预留手机号 */
    bankMobileNo: string;
  };

  export type bankcardBindConfirm = {
    /** 绑卡申请号 */
    applyNo: string;
    /** 验证码 */
    verificationCode: string;
    /** 银行卡号 */
    accountNo: string;
    /** 是否默认银行卡 */
    defaultFlag?: boolean;
  };

  // type FaceType = '1' | '2';

  export enum FACE_TYPE {
    private = 'private',
    public = 'public',
  }

  export type IFaceRequestInfo = {
    requestFrom: string;
  };

  export type IFaceResponseInfo = {
    certifyUrl: string;
    licence?: string;
    securityConfig?: string;
    ticketId: string;
    certifyId?: string;
    bizToken?: string;
    requestId?: string;
  };

  /**
   * 身份证正反面标识
   * 1：正面
   * 2：反面
   */
  export type DiscernIdcardIndex = '1' | '2';

  export type IDiscernIdcardReq = {
    base64Picture: string;
    idCardIndex: DiscernIdcardIndex;
    photoEventType: string;
  };

  export type IDiscernIdcardRes = {
    certId?: string;
    name?: string;
    validity?: string;
    address?: string;
    sex?: string;
    people?: string;
    birthday?: string;
    issueAuthority?: string; // 签发机关
  };

  export type ISaveIdcardInfoReq = {
    certAddress?: string;
    sex?: 0 | 1; // 0: 女 1: 男
    birthDay?: string;
    certValidPeriodStart?: string;
    certValidPeriodEnd?: string;
    name?: string;
    issueAuthority?: string;
    certId?: string;
  };

  export type IValidateIdcardReq = {
    certId?: string;
    custName?: string;
    validity?: string;
  };

  export type RepaymentSummaryRes = {
    curRepayInterest: number;
    curRepayPenalty: number;
    curRepayPrincipal: number;
    curRepayTotAmt: number;
    totalPrincipal: number;
    aheadPaymentLoanNum: number;
    totalUnsettledLoanNum: number;
    overdueLoanNum: number;
    overdueAmount: number;
    todayRepayableAmount: number;
    todayRepayableNum: number;
    nextRepaymentAmount: number;
    nextRepaymentDate: string;
    nextRepaymentNum: number;
    maxOverdueDays: number;
  };

  export type OriginSummaryCategory = 'OVERDUE' | 'TODAY' | 'AHEAD';
  export type RepayableSummaryCategory = 'OVERDUE' | 'TODAY' | 'AHEAD' | 'ACTIVE' | 'ALL';

  export type RepaymentListReq = {
    repayableSummaryCategory: RepayableSummaryCategory;
  };

  export type LoanStatus = 'OVERDUE' | 'TODAY' | 'UNEXPIRED';

  export interface ListLoanDetail {
    allowAheadRepayment: boolean; // 是否允许提前还款
    allowLeastRepaymentPrincipal: number; // 最低还款本金
    allowSettleInterest: boolean; // 是否允许结算利息
    canCombine: boolean; // 是否可以合并
    canRepayment: boolean; // 是否可以还款
    createDate: string; // 创建日期
    dueDate: string; // 到期日期
    endDate: string; // 结束日期
    expireInterest: number; // 到期利息
    expirePenalty: number; // 到期罚息
    loanNo: string; // 贷款编号
    overdueDays: number; // 逾期天数
    overduePrincipal: number; // 逾期本金
    principal: number; // 本金总额
    remainPrincipal: number; // 剩余本金
    remainTerms: number; // 剩余期数
    repayableAmount: number; // 可还款总额
    repayableFee: number; // 可还款费用
    repayableGracePeriodInterest: number; // 可还款宽限期利息
    repayableInterest: number; // 可还款利息
    repayablePenaltyInterest: number; // 可还款罚息
    repayablePrincipal: number; // 可还款本金
    startDate: string; // 开始日期
    termIssuedInterest: number; // 期发放利息
    totalTerm: number; // 总期数
    curTerm: number; // 当前期数
    loanStatus: LoanStatus; // 贷款状态
    // TODO: 待确认补充
    loanInitPrin: string;
  }

  export type RepaymentListRes = {
    /** 列表总额 */
    sumAmount: number;
    /** 列表 */
    list: ListLoanDetail[];
    /** 当前时间是否允许还款 */
    enableRepay: boolean;
    /** 不可还款开始时间 */
    startTime: string;
    /** 不可还款结束时间 */
    endTime: string;
    /** 合并还款最多可勾选条数 */
    maxRecordNum: number;
  };

  export type AheadTrailReq = {
    loanNo?: string;
    loanNos?: string; // 合并还款
    paymentMethod?: string;
    repayPrincipal: number;
    customerGoodsId?: string; //  优惠券id
    customerGoodsIds?: string; //  叠加优惠券id
  };

  export type AheadTrailRes = {
    /** 还款金额 */
    repayAmount: number;
    /** 应还款总额 */
    repayTotalAmount: number;
    /** 应还款手续费 */
    repayFee: number;
    /** 实际优惠金额 */
    actuallyCoounValue: number;
    /**  应还计提利息 */
    repayInterest: number;
    /**  还款本金 */
    repayPrincipal: number;
  };

  export type ActiveTrailReq = {
    loanNo?: string;
    loanNos?: string; // 合并还款
    paymentMethod?: string;
    repayAmount: number;
    customerGoodsId?: string; //  优惠券id
    customerGoodsIds?: string; //  叠加优惠券id
  };

  export type ActiveTrailRes = {
    /** 还款金额 */
    repayAmount: number;
    /** 应还款总额 */
    repayTotalAmount: number;
    /** 应还款手续费 */
    repayFee: number;
    /** 实际优惠金额 */
    actuallyCoounValue: number;
  };

  export type PayWayReq = {
    principalAcru: number;
  };

  export type PayWayRes = {
    /** 还款金额 */
    bankCardInfo: IBankCardInfo;
    /** 还款方式 */
    payWayList: TPayWay[];
  };

  /**
   *  还款方式
   */
  export type TPayWay = {
    cashierCode: string;
    collectFee: number;
    payIconImgUrl: string;
    singleAmountLimit: number;
    status: boolean;
  };

  /**
   *  银行卡信息
   */
  export interface IBankCardInfo {
    accountNo: string;
    acctId: string;
    available: boolean;
    bankBackgroundImgUrl: string;
    bankCode: string;
    bankIconImgUrl: string;
    bankMobileNo: string;
    bankName: string;
    canUpgrade: boolean;
    dayLimit: number;
    dayPayLimit: number;
    defaultFlag: boolean;
    singleLimit: number;
    singlePayLimit: number;
    maintenanceEndTime: string;
    maintenanceStartTime: string;
    desensitizeAccountNo: string;
    desensitizeBankMobileNo: string;
  }

  export type BankcardPayReq = {
    /** 还款类型 */
    repaymentCategory: string;
    /** 还款账户id */
    acctId: string;
    /** 还款账单id */
    loanNo?: string;
    /** 合并还款账单id */
    loanNos?: string;
    /** 验证码 */
    verificationCode: string;
    /** 分期还款标识 */
    installment?: string;
    /** 叠加优惠券id */
    customerGoodsIds?: string;
    /** 还款金额 */
    repayAmount: number;
    /** 验证码场景值 */
    smsSceneType: string;
  };

  export type BankcardPayRes = {
    /** 还款id：用于轮询还款状态 */
    repaymentRegisterId: 'xxxxxx';
    /**  */
    needGuideApplyGrace: boolean;
  };

  export type getRepaymentStateReq = {
    repaymentRegisterId: string;
  };

  export type RepaymentStatusCode = 'SUCCESS' | 'FAILED' | 'PROCESSING';

  export type getRepaymentStateRes = {
    /** 还款状态码：SUCCESS;FAILED;PROCESSING */
    state: RepaymentStatusCode;
    /** 还款状态描述 */
    statusDesc: string;
    /** 返回消息 */
    message: string;
  };

  export type RepaymentPlanListItem = {
    totalAmount: string;
    dueDate: string;
  };

  export type RepaymentPlanListRes = {
    sumAmount: string;
    list: RepaymentPlanListItem[];
  };
  export type RepaymentPlanDetailListItem = {
    totalTerm: string;
    curTerm: string;
    dueDate: string;
    loanInitPrin: string;
    totalAmount: string;
    principal: string;
    interest: string;
    penalty: string;
    loanNo: string;
  };
  export type RepaymentPlanDetailRes = {
    list: RepaymentPlanDetailListItem[];
    sumAmount: string;
  };

  export type PrepareLoanInfoReq = {
    loanNos: string;
    repayableSummaryCategory: RepayableSummaryCategory;
  };

  export type PrepareLoanInfoRes = {
    repayableAmount: number;
    principal: number;
    allowLeastRepaymentPrincipal: number;
    remainPrincipal: number;
    expireInterest: number;
    expirePenalty: number;
    repayableFee: number;
    repayablePrincipal: number;
    repayableInterest: number;
  };

  export type IPosition = {
    longitude: string;
    latitude: string;
  };
  
  // E01：身份证 E02：银行卡 E03: 与客户经理合影 E12: 手持身份证照片 E21：补录资料 E28: 资产信息 E29：外部结清证明，E33：优化建议， E20: 客户信息更新资料上传
  export type DocType = 'E01' | 'E02' | 'E03' | 'E12' | 'E21' | 'E28' | 'E29' | 'E33' | 'E20' ;
  

  export type UploadDocumentReq = {
    files: string[];
    eventCode: string;
    docType?: DocType;
  };

  /**
   * 借款模块
   */
  export type ILendingPreCheckRes = {
    existWaitLending: boolean;
    canFixHistoryLoanDueDate: boolean;
    fixedDueDate: number;
    fixedDueCalendar: string;
    needSupplyDocTypes: string[];
  };

  export type ILendingPreProgressRes = {
    progressCode: string;
    progressDesc: string;
    needBankCard: boolean;
    needCertInfo: boolean;
    needCertPic: boolean;
    needContactInfo: boolean;
    needRefillAddress: boolean;
    needSupplementInfo: boolean;
    needTradePassword: boolean;
  }

  export type IRepaymentTrailRes = {
    schedules: ISchedulesItem[];
  };
  export type ISchedulesItem = {
    currentTerm: number;
    currentStatus: boolean;
    totalAmount: number;
    principal: number;
    interest: number;
    cutInterest: number;
    beforeCutInterest: number;
    fee: number;
    dueDate: string;
    startDate: string;
    days: number;
    delayInterestFlag: boolean;
  };

  export type ILendingApplyRes = {
    billNo: string;
    scaleControlFlag: boolean;
  };

  export type ILendingStatusCode = 'SUCCESS' | 'FAILED' | 'PROCESSING' | 'CREDIT_REAPPRAISE_PROCESSING' | 'OVERTIME' | 'PHONE_VERIFY' | 'LOAN_BLOCKING';

  export type ILendingStatusRes = {
    state: ILendingStatusCode;
    stateDesc: string;
    loanAmount: number;
    loanNo: string;
    message: string;
    firstPaymentDate: string;
    firstPaymentAmount: number;
  };

  /**
   * 银行卡模块
   */
  export type IBankCardApplyRes = {
    applyNo: string;
    needSupplyDocTypes: string[];
  };

  export type SetTradeReq = {
    keyboardType: string;
    tradePassword: string;
    randomCode: string;
  };

  export type UpdateTradeReq = {
    keyboardType: string;
    oldTradePassword: string;
    oldRandomCode: string;
    newTradePassword: string;
    newRandomCode: string;
  };

  export type ResetTradeReq = {
    keyboardType: string;
    tradePassword: string;
    randomCode: string;
  };

  type LoanReq = {
    loanNo?: string; //借据号
    billNo?: string; //放款流水号
    lscStatus?: string; //管控状态 - 管控才会有
    contractNo?: string; //合同号
  };

  // 联合贷款客户信息
  interface Company {
    partnerCode: string; // 合作方编码
    partnerName: string; // 合作方机构名称
    partnerLoanAmt: number; // 放款金额
    unionLoanRate: number; // 放款比例
    remainPrcp: number; // 未还金额
  }

  // 分期本金还款比例信息
  interface InstallmentRatio {
    startTerm: number; // 开始期数
    endTerm: number; // 截止期数
    ratio: number; // 当期本金比例
  }

  // 调息记录
  interface ReduceRate {
    adjustTime: string; //调整时间
    beforeRate: number; //原利率
    afterRate: number; // 现利率
  }

  // 申请记录
  interface LoanApplyRecord {
    applyType: string; // 申请类型
    status: string; // 申请状态
    date: string; // 申请时间
  }

  // 退货记录
  interface CreditPayBack {
    tradeDate: string; // 交易日期
    tradeAmt: number; // 交易金额
    systemDate: string; // 入账日期
    systemAmt: number; // 入账金额
    tradeStatus: string; // 交易状态
  }
  interface Loan {
    loanNo: string; // 借据号
    loanProdCode: string; // 产品编码
    loanProdName: string; // 产品名称
    certId: string; // 身份证号
    name: string; // 用户名称
    contractNo: string; // 合同编号
    loanStatus: string; // 借据状态
    loanStatusName: string; // 借据状态名称
    principal: number; // 借款本金
    remainPrincipal: number; // 剩余未还本金
    companies: Company[]; // 联合贷款客户信息
    paidPrincipal: number; // 已还金额成分
    paidInterest: number; // 已还利息
    paidPenalty: number; // 已还罚息
    termIssuedInterest: number; // 未出账单应还利息
    mountRepayableInterest: number; // 挂载期未还利息
    allowSettleInterest: boolean; // 挂载期全额还清利息
    rate: number; // 日利率
    term: number; // 期数
    repaymentType: string; // 还款方式编码
    repaymentTypeName: string; // 还款方式名称
    startDate: string; // 借款开始时间
    endDate: string; // 借款结束时间
    purpose: string; // 借款用途编码
    purposeName: string; // 借款用途名称
    purposeConfirm: boolean; // 借款用途确认状态
    accountNo: string; // 收款银行卡号
    accountName: string; // 收款银行名称
    repayAccountNo: string; // 还款银行卡号
    repayAccountName: string; // 还款银行名称
    couponAmount: number; // 优惠价格
    dueDate: string; // 还款日
    overdueDays: number; // 逾期天数
    gracePeriodInterest: number; // 累计宽限期利息
    penaltyInterest: number; // 已废弃
    expireInterest: number; // 本金逾期利息
    expirePenalty: number; // 逾期罚息
    repayablePrincipal: number; // 当前应还本金
    repayableAmount: number; // 当前应还总额
    repayableInterest: number; // 当前应还利息
    repayableFee: number; // 当前应还费用
    reduceRateLists: ReduceRate[]; // 调息记录
    allowAheadRepayment: boolean; // 是否允许提前还款
    allowCurDayRepayment: boolean; // 是否允许当日还款
    installmentRepayment: boolean; // 分期还款需求新增
    installmentOverdueAmt: number; // 分期还款逾期金额
    installmentTerm: number; // 分期期数
    installmentRatios: InstallmentRatio[]; // 本金还款比例
    installmentStartDate: string; // 分期开始日期
    installmentEndDate: string; // 分期结束日期
    allowLeastRepaymentPrincipal: number; // 可最低还款本金
    failMsg: string; // 交易失败原因
    delayCount: number; // 延期次数
    delayTerm: number; // 延期期数
    delayInterestCount: number; // 延本延息次数
    delayInterestDate: string; // 延本延息到期日期
    delayExPrin: boolean; // 是否是本金延期
    repartitionClass: string; // 分期类型
    judicialStagesRepaymentFlag: boolean; // 可还款开关
    isJudicialStagesOverdue: boolean; // 是否是司法分期逾期
    legalInstallmentTip: string; // 司法分期提示信息
    delayJudicialStagesRepaymentFlag: boolean; // 双延司法分期还款开关
    isDelayJudicialStagesOverdue: boolean; // 是否双延司法分期超过最大逾期天数
    isDelayBreakMark: boolean; // 是否双延司法分期客户违约打标
    delayLegalInstallmentTip: string; // 双延司法分期提示信息
    loanApplyRecordList: LoanApplyRecord[]; // 申请记录列表
    isCanReservationRate: string; // 是否可以预约抵息
    currTerm: string; // 当前期数
    currTermIntAmt: number; // 当期应还利息
    isCreditPay: string; // 是否信用付
    tradeDesc: string; // 交易描述
    creditPayBackList: CreditPayBack[]; // 退货记录
  }

  interface LocationPoint {
    longitude: string;
    latitude: string;
  }

  interface LoanPlan {
    currentTerm: number; // 当前期数，非必须
    currentStatus: boolean; // 当前状态，true: 已还，false: 未还；当已还时，置灰显示
    totalAmount: number; // 应还总额，非必须
    principal: number; // 应还本金，非必须
    interest: number; // 预计实还利息，非必须
    cutInterest: number; // 预计优惠利息，非必须
    beforeCutInterest: number; // 优惠前利息，非必须
    fee: number; // 费用，非必须
    dueDate: string; // 应还日期，格式 yyyy/MM/dd 非必须
    startDate: string; // 开始日期，格式 yyyy/MM/dd 非必须
    days: number; // 当期天数，非必须
    delayInterestFlag: boolean; // 延本延息真空期，true: 是，false: 否 非必须
  }

  interface LoanPlanReq {
    schedules: LoanPlan[];
  }

  interface RepaymentRecord {
    amount: number; // 还款总额（必填）
    date: string; // 还款日期，格式yyyyMMdd（必填）
    failReason: string; // 失败原因（非必填）
    transactionTypeDesc: string; // 还款交易方式描述（非必填）
    principal: string; // 本金（非必填）
    interest: string; // 利息（非必填）
    penalty: string; // 罚息（非必填）
  }

  interface RepaymentRecordReq {
    list: RepaymentRecord[];
  }

  type LoanCategories = 'UNSETTLED' | 'PROGRESSING' | 'COMPLETED'
  interface LoanListReq {
    loanCategory?: LoanCategories;
    fieldName: string; // 排序字段
    loanType: 'all' | 'other' | 'creditpay' ; // 借据类型
    sort: 'asc' | 'desc'; // 排序方式
  }

  interface LoanListRes {
    list: LoanDetail[];
  }

  type LoanContractReq = {
    content: string; // 协议内容，JPG格式文件的base64编码，非必须
  };
  interface SearchCompanyListRes {
    num: number;
    items: {
      creditNo: string;
      title: string;
    }[];
  }

  interface ISystemConfigRes {
    checkContactNames: string;
    customerRepaymentHistoryMaxMonth: number;
    echoInfoEffectiveInterval: number;
    enableModifyRepaymentDate: boolean;
    forceReadSeconds: number;
    ocrAlbumSwitch: boolean;
    checkFaceResults: boolean; // 是否检查活体识别结果
  }

  type LoanContractListRes = {
    contId: string; //合同id
    contractCode: string; //合同编码
    contractName: string; //合同名称
    loanContractUrl: string; //文件路径
  };

  type SettleProofList = {
    /** 合同号 */
    contractCode: string;

    /** 开具时间 */
    settleEvidentiaryDate: string;

    /** 防伪码 */
    securityCode: string;

    /** 身份证id */
    certId: string;

    /** 文件路径 */
    filePath?: string;

    /** 客户合同状态 */
    status?: string;

    /** pdf编码字节 */
    pdfBase64: string;

    /** 图片编码字节 */
    imgBase64: string;
  };

  type AllListReq = {
    list: SettleProofList[];
  };

  type LoanRecord = {
    /** 借据号 */
    loanNo: string;

    /** 合同号 */
    contractCode: string;

    /** 借钱时间 */
    startDate: string;

    /** 贷款本金 */
    loanPrincipal: number;
  };

  type LendingListReq = {
    list: LoanRecord[];
  };

  type LoanDetail = {
    /** 客户姓名 */
    custName: string;

    /** 安全码 */
    securityCode: string;

    /** PDF 文件 Base64 编码字节数据 */
    pdfBase64: string;

    /** 图片 Base64 编码字节数据 */
    imgBase64: string;

    /** 借款开始时间 */
    startDate: string;

    /** 当前账单还清时间 */
    paidOutDate: string;

    /** 借据编号 */
    loanNo: string;

    /** 合同编号 */
    contractCode: string;

    /** 开具时间 */
    settleEvidentiaryDate: string;

    /** 身份证号 */
    certId: string;

    /** 贷款本金金额 */
    loanPrincipal: number;

    /** 借款合同编号 */
    loanContractCode: string;
  };

  /**
   * 提额
   */
  type RaiseAmountCode = 'pass' | 'reject' | 'unprocessed' | 'unactived' | 'nodate'
  type ShowRaiseAmountRes = {
    /** 是否显示提额 */
    menuVisible: boolean;
  }

  type CanRaiseAmountRes = {
    /** 提额状态码 */
    resultCode: RaiseAmountCode;
    /** 提额查询结果 */
    accessCheckResult: boolean;
    /** 下次申请日期 */
    nextApplyDate?: string;
  }

  type RaiseAmountRecordItem = {
    /** 提额申请类型 */
    applyType: string;
    /** 提额申请类型名称 */
    applyTypeName: string;
    /** 提额申请时间 */
    applyDate: string;
    /** 提额申请状态 */
    applyStatus: string;
    /** 提额申请状态名称 */
    applyStatusName: string;
    /** 提额申请记录ID */
    orderNo: string;
    /** 提额申请金额 */
    raisedAmount: number;
  }
}
