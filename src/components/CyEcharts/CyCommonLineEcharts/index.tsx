import {FC} from 'react';
import {View} from '@tarojs/components';

type Props = {
  legend_data?: string[];
  series_data: {
    name: string;
    //转折点样式
    itemStyle: {
      color: string;
    };
    data: string[] | number[];
  }[];
  y_axis_name: string[];
  x_axis_data: string[];
  x_axis_label_interval?: ((index: number, value: string) => void) | number;
  y_name_padding?: number[][];
  y_split_config?: string[];
  customerToolTip?: (data: any) => React.ReactNode; //自定义tooltip
};

/**
 * 组件名称：图表
 * 组件描述：图表
 */
const CyCommonLineEcharts: FC<Props> = props => {
  return <View></View>;
};

export default CyCommonLineEcharts;
