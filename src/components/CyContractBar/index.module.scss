.contract-bar {
  display: flex;
  flex-direction: row;
  align-items: flex-start;
  font-size: 24px;
  color: #999;
  padding: 0 12px;
  .taro-checkbox_checked {
    border-radius: 46px;
    top: 0;
  }

  .contract-bar-text {
    line-height: 38px;
    margin-left: 8px;
    font-size: 24px;
  }

}

.weui-cells_checkbox {
  display: flex;
  flex-direction: row;
  align-items: center;
}
.weui-icon-checked {
  width: 32px;
  height: 32px;
}
.weui-icon-checked:before {
  font-size: 32px!important;
}


.contract-bar__checkbox {
  display: flex;
  border-radius: 46px;
}

.comp-cy-link {
  color: #425BC3;
  font-size: 24px;
  white-space: wrap;
}
.comp-cy-inactive {
  font-size: 24px;
  color: $color-text-secondary;
}

.justify-flex-start {
  justify-content: flex-start;
}

.justify-center {
  justify-content: center;
  align-items: center;
}

.contractPopupFooter {
  display: flex;
  padding: 40px;
}

.contractContent {
  padding: 0 40px 40px 40px;
  color: $color-text-normal;
}

.cy-contractbar-confirm-btn {
  margin-left: 12px;
  flex: 1;
}