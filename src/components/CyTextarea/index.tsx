import {ComponentType, ForwardRefRenderFunction, forwardRef, useContext, useImperativeHandle, useRef} from 'react';
import {
  BaseEventOrig,
  CommonEventFunction,
  InputProps,
  Target,
  Textarea,
  TextareaProps,
  View,
  ViewProps,
} from '@tarojs/components';
import {ENV_TYPE, getEnv} from '@tarojs/taro';
import {FocusContext} from '../CyKeyboardAvoidingView';
import './index.scss';

interface HooksTarget extends Target {
  name?: string;
}
interface BaseHooksEventOrig<T> extends BaseEventOrig<T> {
  target: HooksTarget;
}
type eventType = BaseHooksEventOrig<InputProps.inputEventDetail>;

interface Props extends TextareaProps {
  onChange?: (event: unknown) => void;
}
/**
 * 组件名称：输入区组件
 * 组件描述：输入区域支持多行输入，从taro组件包装而来，为了支持CyForm
 */
const CyTextarea: ForwardRefRenderFunction<unknown, Props> = ({value, ...props}, ref) => {
  const myRef = useRef<ComponentType<ViewProps>>(null);
  const currentVal = useRef<string | undefined>(value);
  useImperativeHandle(ref, () => ({
    get value() {
      return currentVal.current;
    },
    set value(newValue) {
      currentVal.current = newValue;
    },
  }));
  let getAbsolutePosition: () => void;
  //  RN键盘做特殊处理
  if (getEnv() === ENV_TYPE.RN) {
    // 计算组件相对于屏幕的位置
    getAbsolutePosition = () => {
      if (myRef.current) {
        const current = myRef.current as any;
        if (current.measureInWindow) {
          current.measureInWindow((x: number, y: number, width: number, height: number) => {
            emitEvent({x, y});
          });
        }
      }
    };
  }

  const emitEvent = useContext(FocusContext);
  const onFocus: CommonEventFunction<TextareaProps.onFocusEventDetail> = e => {
    props.onFocus && props.onFocus(e);
    getAbsolutePosition && getAbsolutePosition();
  };

  let newOnInput = (e: eventType) => {};
  // 对RN端做特殊处理，因为RN端input组件无onChange事件且env target 与 h5端有差异
  if (getEnv() === ENV_TYPE.RN) {
    newOnInput = (e: eventType) => {
      currentVal.current = e.detail.value;
      const newVal = {
        target: {
          name: props.name,
          value: e.detail.value,
        },
        detail: {
          value: e.detail.value,
        },
      };
      props.onInput?.(e);
      props.onChange?.(newVal);
    };
  } else {
    newOnInput = (e: eventType) => {
      currentVal.current = e.detail.value;
      props.onInput?.(e);
      props.onChange?.(e);
    };
  }

  const onInput = (e: eventType) => {
    currentVal.current = e.detail.value;
    props.onInput?.(e);
  };
  return (
    <>
      {/* RN中只有View才能取到Ref，为了不影响style等传递定义一个空view */}
      {process.env.TARO_ENV === 'rn' && <View ref={myRef}></View>}
      <Textarea
        className='comp-cy-textarea'
        {...props}
        onInput={props.onInput ? onInput : newOnInput}
        defaultValue={value ?? currentVal.current}
        style={props.style}
        onFocus={e => {
          onFocus(e);
        }}></Textarea>
    </>
  );
};

export default forwardRef(CyTextarea);
