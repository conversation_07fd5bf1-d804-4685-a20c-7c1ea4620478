.container {
  padding: 20px;
  background-color: #f5f5f5;
}

.section {
  margin-bottom: 30px;
  
  &:last-child {
    margin-bottom: 0;
  }
}

.title {
  font-size: 28px;
  font-weight: 500;
  margin-bottom: 16px;
  color: #333;
}

.row {
  display: flex;
  flex-direction: row;
  gap: 16px;
  align-items: center;
}

.item {
  flex-shrink: 0;
}

.card {
  background-color: #fff;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.cardContent {
  padding: 16px;
}

.chatContainer {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.chatMessage {
  max-width: 80%;
  
  &:nth-child(even) {
    align-self: flex-end;
  }
} 