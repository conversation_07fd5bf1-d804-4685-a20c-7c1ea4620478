// @import "hncy58-taro-components/dist/style/themes/index.scss";
// flex 样式
.flex {
  display: flex;
  flex-direction: row;
  align-items: center;
}

.flex-between {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
}

.flex-center {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
}

.flex-column {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: space-between;
}

.flex-column-center {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.vertical-gap {
  margin-top: 32px;
}

.bottom-fixed {
  position: fixed;
  bottom: 0;
  width: 100%;
  left: 0;
  padding-left: 20px;
  padding-right: 20px;
  box-sizing: border-box;
  padding-bottom: 40px;
  padding-bottom: constant(safe-area-inset-bottom); /*兼容 IOS<11.2*/
  padding-bottom: env(safe-area-inset-bottom); /*兼容 IOS>11.2*/
  background-color: #fff;
}

.taroify-popup {
  z-index: 99999999 !important;
}

.taroify-picker-column {
  font-size: 28px !important;
}



.tag-error {
  @include tag(#F5222D, #FFF1F0);
}

.tag-primary {
  @include tag(#2F54EB, #E9EDFF);
}

.tag-disabled {
  @include tag(#bbb, #F6F6F6);
}