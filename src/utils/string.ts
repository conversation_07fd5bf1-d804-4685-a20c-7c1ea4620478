import CryptoJS from 'crypto-js';

// 添加星号
export const plusStar = (str?: string, frontL = 0, endL = 0) => {
  if (!str) return;
  let star = '***';
  return str.substring(0, frontL) + star + str.substring(str.length - endL);
};

/**千分符 */
export const formatNumber = (num?: string) => {
  if (!num || Number.isNaN(parseInt(num))) return 0;
  return parseFloat(num).toLocaleString();
};

//小数就保留两位小数并千分化
export const toLocaleString = (str?: string | number) => {
  if (!str) return '0';
  if (typeof str === 'number') {
    return str.toLocaleString();
  }
  return parseFloat(str).toLocaleString();
};

type QueryParams = {[key: string]: string | number | boolean | undefined | null};

export const objectToQueryString: (params: QueryParams) => string = params => {
  return Object.keys(params)
    .filter(key => params[key] !== undefined && params[key] !== null)
    .map(key => {
      const value = params[key];
      if (typeof value === 'object' && value !== null) {
        return objectToQueryString(value as QueryParams);
      } else if (Array.isArray(value)) {
        return value.map(val => `${encodeURIComponent(key)}=${encodeURIComponent(val)}`).join('&');
      } else {
        return `${encodeURIComponent(key)}=${encodeURIComponent(value ?? '')}`;
      }
    })
    .join('&');
};
/**
 * 移除图片base64字符串的前缀
 * @param base64String
 * @returns
 */
export const removeBase64Prefix: (base64String: string) => string = base64String => {
  // 使用正则表达式匹配并去除data URL scheme部分
  return base64String.replace(/^data:image\/[a-z]+;base64,/i, '');
};

// 定义一个函数来检查文件名是否为图片后缀
export const isImageFilename: (filename: string) => boolean = filename => {
  // 定义图片后缀集合
  const imageExtensions = new Set(['jpg', 'jpeg', 'png', 'gif', 'bmp', 'tiff', 'webp', 'svg']);

  // 获取文件的后缀名，使用split('.')方法分割文件名，并获取最后一个元素
  const extension = filename.split('.').pop()?.toLowerCase() ?? '';

  // 检查后缀名是否在图片后缀集合中
  return imageExtensions.has(extension);
};

/**
 * MD5加密
 * @param message
 * @returns
 */
export const md5: (message: string) => string = message => {
  return CryptoJS.MD5(message).toString();
};

/**
 * 常规MD5加密算法
 * @param message
 * @returns
 */
export const commonMd5: (message: string) => string = message => {
  const cykey = 'cykey';
  return md5(md5(message) + cykey);
};

/**
 * 字符串转ArrayBuffer
 */
export const stringToArrayBufferFallback = (str: string) => {
  let utf8Bytes = [];
  for (let i = 0; i < str.length; i++) {
    let charCode = str.charCodeAt(i);
    if (charCode < 128) {
      utf8Bytes.push(charCode);
    } else if (charCode < 2048) {
      utf8Bytes.push((charCode >> 6) | 192);
      utf8Bytes.push((charCode & 63) | 128);
    } else if (charCode < 65536) {
      utf8Bytes.push((charCode >> 12) | 224);
      utf8Bytes.push(((charCode >> 6) & 63) | 128);
      utf8Bytes.push((charCode & 63) | 128);
    } else {
      utf8Bytes.push((charCode >> 18) | 240);
      utf8Bytes.push(((charCode >> 12) & 63) | 128);
      utf8Bytes.push(((charCode >> 6) & 63) | 128);
      utf8Bytes.push((charCode & 63) | 128);
    }
  }
  let arrayBuffer = new ArrayBuffer(utf8Bytes.length);
  let arrayView = new Uint8Array(arrayBuffer);
  for (let i = 0; i < utf8Bytes.length; i++) {
    arrayView[i] = utf8Bytes[i];
  }
  return arrayBuffer;
};
