import {JSXElementConstructor, ReactElement} from 'react';
import RNLinearGradient from 'react-native-linear-gradient';
import {View} from '@tarojs/components';

type LinearGradientProps = {
  direction: 'to top' | 'to right' | 'to bottom' | 'to left';
  colors: string[];
  children: ReactElement<any, string | JSXElementConstructor<any>>[] | undefined;
};

const LinearGradientRn = (props: LinearGradientProps) => {
  let x1 = 0;
  let x2 = 0;
  let y1 = 0;
  let y2 = 0;
  switch (props.direction) {
    case 'to top':
      y2 = 1;
      break;
    case 'to right':
      x2 = 1;
      break;
    case 'to bottom':
      y1 = 1;
      break;
    case 'to left':
      x1 = 1;
      break;
  }
  return (
    <View className='gradient-container'>
      <RNLinearGradient {...props} start={{x: x1, y: y1}} end={{x: x2, y: y2}}>
        {props.children}
      </RNLinearGradient>
    </View>
  );
};
export default LinearGradientRn;
