import {ForwardRefRenderFunction, forwardRef, useEffect, useImperativeHandle, useState} from 'react';
import {Checkbox, Image, Text, View} from '@tarojs/components';
import CyCheckBox from '~components/CyCheckBox';
import FaceImage from '~images/face/aliyun-face-tips.png';
import useFace from '../../hooks/useFace';
import {showToast} from '../../utils/common';
import CyImage from '../CyImage';
import './index.scss';

interface CyFaceProps extends React.PropsWithChildren {
  requestFrom?: string;
  onSuccess?: () => void;
  onFail?: () => void;
}
export interface CyFaceRef {
  startFace: () => void; //显示
}
const CyFace: ForwardRefRenderFunction<CyFaceRef, CyFaceProps> = ({children, requestFrom, onSuccess, onFail}, ref) => {
  const {init, startFace, isPrivacyFace, faceApiRes, faceAgreementChecked, continueFace, isFacing} = useFace({
    requestFrom: requestFrom || 'common',
    certId: '110101199001011234',
    custName: '张三',
    faceRecognitionType: '1',
    faceSuccess:
      onSuccess ||
      (() => {
        showToast('活体识别成功');
      }),
    faceFail:
      onFail ||
      (() => {
        showToast('活体识别失败');
      }),
  });

  useImperativeHandle(ref, () => ({
    startFace,
  }));

  return (
    <View>
      {isPrivacyFace && !faceAgreementChecked.current && isFacing && (
        <View className='face-agreement-page' v-show='!inFaceView'>
          <View className='title'>人脸识别</View>
          <View className='top-tips'>为保障您的个人信息安全，请按照提示进行验证</View>
          <CyImage className='face-img' src={FaceImage}></CyImage>
          <View
            onClick={e => {
              faceAgreementChecked.current = true;
            }}>
            <Text>我已阅读并同意: {faceAgreementChecked.current ? 1 : 2}</Text>
          </View>
          <View onClick={continueFace}>开始认证</View>
        </View>
      )}
      {isPrivacyFace && faceApiRes?.certifyUrl && isFacing && (
        <View className='face-container'>
          <iframe
            id='zoloz'
            src={faceApiRes.certifyUrl}
            allow='microphone;camera;midi;encrypted-media;'
            allowusermedia='true'
            allowFullScreen
            webkitallowfullscreen='true'
            width='100%'
            height='100%'></iframe>
          <View className='face-agreement-page'>
            <View className='title'>人脸识别</View>
            <View className='top-tips'>为保障您的个人信息安全，请按照提示进行验证</View>
          </View>
        </View>
      )}
      {children && <View onClick={() => (startFace as () => Promise<void>)()}>{children}</View>}
    </View>
  );
};
export default forwardRef(CyFace);
