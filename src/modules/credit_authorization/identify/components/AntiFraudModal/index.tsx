import {useEffect, useState} from 'react';
import {CyModal} from 'hncy58-taro-components';
import {Text, View} from '@tarojs/components';
import useCountdown from '~hooks/useCountdown';
import styles from './index.module.scss';

export default () => {
  const {count: countdown, start} = useCountdown(5, 0);
  const [visible, setVisible] = useState(true);

  useEffect(() => {
    start();
  }, []);
  return (
    <CyModal
      visible={visible}
      title='温馨提示'
      content={
        <View className={styles.content}>
          <View>长银五八不与任何第三方融资中介合作！</View>
          <View>免费申请额度，除利息外不收取任何费用！</View>
          <View>任何个人或单位向您收取申请手续费均涉嫌欺诈！</View>
          <View>
            本人知晓并承诺：“贷款仅供本人日常消费使用（购车、购房、投资除外），不得转借他人或机构使用，否则由本人承担相应法律责任。”
          </View>
          <View>
            我司“消费贷”产品为有消费需求的特定客群提供的一款备用金，
            <Text className={styles.mark}>在校生请勿申请</Text>
          </View>
        </View>
      }
      footer={
        <View
          className={styles['modal-footer']}
          onClick={() => {
            if (countdown === 0) {
              visible && setVisible(false);
            }
          }}>
          {countdown > 0 ? <Text>请阅读({countdown}s)</Text> : <Text className={styles.mark}>已知悉</Text>}
        </View>
      }></CyModal>
  );
};
