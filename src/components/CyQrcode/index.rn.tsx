import {FC} from 'react';
import QrcodeSVG from 'react-native-qrcode-svg';
import {View} from '@tarojs/components';
import './index.scss';

type Props = {
  value?: string;
  size?: number;
};

/**
 * 组件名称：二维码组件
 * 组件描述：展示二维码
 */
const CyQrcode: FC<Props> = props => {
  return <View className='comp-cy-qrcode'>{props.value !== '' ? <QrcodeSVG {...props} /> : <></>}</View>;
};

export default CyQrcode;
