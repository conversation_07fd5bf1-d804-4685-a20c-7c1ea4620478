import {FC, useEffect, useState} from 'react';
import {BaseEventOrig, View} from '@tarojs/components';
import CyButton from '../CyButton';
import {EventType, FormField} from '../CyForm';
import './index.scss';

type Option = {
  value: string | number;
  label: string;
  desc?: string;
};

interface Props extends FormField {
  options: Option[];
  v?: string | number;
}

/**
 * 组件名称：单选组件
 * 组件描述：目前支持按钮形式
 */
const CyRadio: FC<Props> = ({options, onChange, name, v}) => {
  const [value, setValue] = useState<string | number>();
  const onSelect = (val: string | number) => {
    setValue(val);
    const e = {
      target: {
        name,
        value: val,
      },
      detail: {
        value: val,
      },
    };
    onChange?.(e as unknown as EventType);
  };

  useEffect(() => {
    setValue(v);
  }, [v]);
  return (
    <View className='comp-cy-radio'>
      {options.map(option => (
        <CyButton
          size='mini'
          key={option.value}
          onClick={e => onSelect(option.value)}
          type={value === option.value ? 'primary' : 'default'}
          className={value === option.value ? 'checked radio-item' : 'unchecked radio-item'}>
          {option.label}
        </CyButton>
      ))}
    </View>
  );
};

export default CyRadio;
