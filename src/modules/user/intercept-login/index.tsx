import { Image, Text, View } from "@tarojs/components"
import PageContainer from "~components/PageWrap"
import iconImg from '~images/login/<EMAIL>'
import wechatImg from '~images/login/Wechat.png'
import appImg from '~images/login/app.png'
import styles from './index.module.scss'
import { useLoad } from "@tarojs/taro"
import { useState } from "react"
import CustomerServiceLink from "~components/CustomerServiceLink"
type ICode = 'UCS_10101' | 'UCS_10102'

const InterceptLogin = () => {
  const code = {
    'UCS_10101': '您已是我司客户！',
    'UCS_10102': '非注册渠道！'
  }
  const note = {
    'UCS_10101': '如需使用更多功能，请进入“长银58金融”微信公众号 或者城一代APP查看！',
    'UCS_10102': '您是我司渠道客户，请使用注册渠道进行登录！',
  }
  const [resCode, setResCode] = useState<ICode |  undefined>('UCS_10101')
  useLoad(({code}) => {
    if (code) {
      setResCode(code)
    }
  })
  return (
    <PageContainer className={styles.pageContainer}>
      <Image className={styles.pageImg} src={iconImg} />
      <View className={styles.value}>
        <View className={styles.text}>{resCode && code[resCode]}</View>
        <View className={styles.note}>{resCode && note[resCode]}</View>
      </View>
      {resCode === 'UCS_10101' ? <View className={styles.qrcodeContainer}>
        <View className={styles.codeItem}>
          <Image className={styles.codeImg} src={appImg} showMenuByLongpress />
          <View>城一代APP</View>
        </View>
        <View className={styles.codeItem}>
          <Image className={styles.codeImg} src={wechatImg} showMenuByLongpress />
          <View>微信公众号</View>
        </View>
      </View> : null}
      <View className={styles.result_tip}>
        <CustomerServiceLink />
      </View>
    </PageContainer>
  )
}

export default InterceptLogin