import type {UserConfigExport} from '@tarojs/cli';

export default {
  logger: {
    quiet: false,
    stats: true,
  },
  mini: {
    postcss: {
      url: {
        enable: true,
        config: {
          url: 'inline',
          maxSize: 10,
          fallback: () => {
          },
        },
      },
    },
  },
  h5: {
    devServer: {
      proxy: {
        '/sit': {
          target: process.env.TARO_APP_API, // h5 sit 环境跨域
          changeOrigin: true,
          pathRewrite: {
            '^/sit': '',
          },
        },
        '/bbit': {
          target: process.env.TARO_APP_API, // h5 bbit 环境跨域
          changeOrigin: true,
          pathRewrite: {
            '^/bbit': '',
          },
        },
        '/uat': {
          target: process.env.TARO_APP_API, // h5 uat 环境跨域
          changeOrigin: true,
          pathRewrite: {
            '^/uat': '',
          },
        },
      },
      client: {
        overlay: false,
      },
    },
  },
} satisfies UserConfigExport<'webpack5'>;
