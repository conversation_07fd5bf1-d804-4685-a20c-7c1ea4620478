规则：

html

> 通过编辑器插件来实现

1. 标签都需要闭合，单标签都要闭合
2. html标签都需要小写
3. link script 不带type属性
4. 元素属性双引号
5. 块级元素独占1行，h1,p 元素只能嵌套内联元素

css

1. 首行写charset
2. 导入不使用 @import url()
3. 类名不使用大写
4. 选择器和大括号中间接空格

js

1. 使用const let 而非 var

```
no-var: "error"
```

2. 推荐字面量方式创建对象

```
"no-new-object": "error"
```

3. 推荐对象方法属性用简写

```
"object-shorthand": "warn",
```

4. 推荐使用push、rest运算符
5. 字符串单引号，链接字符串用模板字符串

```
  "prefer-template": "error"
  "quotes": ["error", "single"]
```

6. 单行代码块不使用大括号

7. One True Brace Style

```
  "brace-style": "error",
```

8. 在逗号后面使用空格，逗号前面不加空格

```
  "comma-spacing": ["warn", {"before": false, "after": true}],
```

9.  禁止使用链式赋值： 如const a = b = c = 1

```
"no-multi-assign": "error"
```

10. 禁止使用非三等号比较

```
eqeqeq: "error"
```

11. import 按三方模块和内部模块的顺序按序引入

```
plugins: [
  '@trivago/prettier-plugin-sort-imports'
],
rules: [
  importOrderSeparation: false,
  importOrderSortSpecifiers: true,
]
```

12. html 中 script 代码段也需校验

```
// 通过eslint-plugin-html
"plugins": ["html"],
```

React

1. 禁止直接修改 props
2. 调用 props 时尽量使用扩展符
3. 不要直接修改 state

```
"no-direct-state-mutation": {
      "severity": "error",
      "code": function (source, options) {
        const ast = source.getAST();
        const scopeManager = source.getScopeManager();

        function checkStateAssignments(node) {
          if (node.type === 'AssignmentExpression') {
            const { left, right } = node;
            if (left.type === 'MemberExpression') {
              const { name } = left.object;
              if (scopeManager.isVarDeclared(name, 'useState')) {
                source.report({
                  node: node,
                  message: 'Direct mutation of the state is not allowed. Use the updater function provided by useState.'
                });
              }
            }
          }
        }

        traverse(ast, {
          VariableDeclarator: {
            enter(path) {
              const { id } = path.node;
              if (id.type === 'Identifier' && scopeManager.isVarDeclared(id.name, 'useState')) {
                path.traverse({
                  AssignmentExpression: checkStateAssignments
                });
              }
            }
          }
        });
      }
    }
```
