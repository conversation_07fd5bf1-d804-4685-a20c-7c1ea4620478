import {CSSProperties, FC} from 'react';
import {StyleProp, Text, TextStyle} from 'react-native';
import {View, ViewProps} from '@tarojs/components';
import './index.scss';

type Props = {
  title: string;
};

/**
 * 组件名称：不换行控件
 * 组件描述：不换行控件
 */
const CyNoWarpView: FC<Props & ViewProps> = props => {
  const textStyle: CSSProperties | undefined = {};
  const style: CSSProperties | undefined = props.style as CSSProperties | undefined;
  if (style) {
    textStyle.fontSize = style.fontSize;
    textStyle.color = style.color;
    textStyle.fontWeight = style.fontWeight;
    textStyle.lineHeight = style.lineHeight;
    textStyle.textAlign = style.textAlign;
  }
  return (
    <View className='comp-cy-no-warp-view' {...props} style={props.style}>
      <Text numberOfLines={1} ellipsizeMode='tail' style={textStyle as StyleProp<TextStyle>}>
        {props.children}
      </Text>
    </View>
  );
};

export default CyNoWarpView;
