import React, { useCallback, useRef, useState } from 'react';
import { Text, View } from '@tarojs/components';
import Taro from '@tarojs/taro';
import dayjs from 'dayjs';
import Card from '~components/Card';
import CyImagePreview from '~components/CyImagePreview';
import { getLoanContracDetail, getLoanContractList } from '~services/agreement';
import { showToast } from '~utils/common';
import { twoNumberTimes } from '~utils/number';
import styles from './LoanInfo.module.scss';

interface LoanInfoProps {
  name: string;
  loanNo: string;
  loanProdName: string;
  rate: number;
  startDate: string;
  endDate: string;
  term: number;
  purposeName: string;
  contractNo: string;
  accountNo: string;
  accountName: string;
  repayAccountNo?: string;
  repayAccountName?: string;
  dueDate: string;
  repaymentTypeName: string;
  certId: string;
}
interface InfoItemProps {
  label: string;
  value?: string | number;
  showValue?: boolean;
  children?: React.ReactNode;
  buttonText?: string;
  onButtonClick?: () => void;
}

const formatRate = (rate: number, size: number) => {
  if (!rate) return '';
  return `${rate * size}%`;
};

export const InfoItem = ({ label, value, showValue = true, children, buttonText, onButtonClick }: InfoItemProps) => {
  return (
    <View className={styles.infoItem}>
      <Text className={styles.label}>{label}</Text>
      <View className={styles.value}>
        {children ? (
          children
        ) : (
          <>
            <Text>{(showValue && value) || ''}</Text>
            {buttonText && (
              <Text className={styles.confirmButton} onClick={onButtonClick}>
                {buttonText}
                {'>'}
              </Text>
            )}
          </>
        )}
      </View>
    </View>
  );
};

const LoanInfo = ({
  name,
  loanNo,
  loanProdName,
  rate,
  startDate,
  endDate,
  term,
  purposeName,
  contractNo,
  accountName,
  accountNo,
  repayAccountName,
  repayAccountNo,
  dueDate,
  repaymentTypeName,
  certId,
}: LoanInfoProps) => {
  const [contractVisible, setContractVisible] = useState(false);
  const [contract, setContract] = useState<{ name: string; content: string }[]>([]);
  const contractCacheRef = useRef<{
    loanNo: string;
    contractData: { name: string; content: string }[];
  } | null>(null);

  const onContractClose = () => {
    setContractVisible(false);
  };

  const contractClick = useCallback(async () => {
    try {
      // 如果缓存存在且loanNo相同，直接使用缓存数据
      if (contractCacheRef.current && contractCacheRef.current.loanNo === loanNo) {
        setContract(contractCacheRef.current.contractData);
        setContractVisible(true);
        return;
      }
      Taro.showLoading({
        title: '加载中',
      });
      const [{ contId, contractName }] = await getLoanContractList({ loanNo });
      if (contId) {
        const [{ content }] = await getLoanContracDetail({ loanNo, contId });
        Taro.hideLoading();
        const contractData = [
          {
            name: contractName,
            content: `data:image/jpeg;base64,${content}`,
          },
        ];

        // 缓存数据
        contractCacheRef.current = {
          loanNo,
          contractData,
        };

        setContract(contractData);
        setContractVisible(true);
      }
    } catch (error) {
      console.error('获取合同信息失败:', error);
      Taro.hideLoading();
      showToast('获取合同信息失败');
    }
  }, [loanNo]);

  return (
    <>
      <View className={styles.loanInfoHeader}>
        <Text>借款人：{name ?? '-'}</Text>
        <Text>身份证号：{certId ?? '-'}</Text>
      </View>
      <Card title='借据信息' className={styles.loanInfo} collapsible={true} defaultExpanded={true}>
        <View className={styles.content}>
          {/* <InfoItem label='借款人信息' value={name ?? '-'} />
          <InfoItem label='身份证号' value={certId ?? '-'} /> */}
          <InfoItem label='借据号' value={loanNo ?? '-'} />
          <InfoItem label='借款产品' value={loanProdName ?? '-'} />
          <InfoItem label='日利率' value={rate ? formatRate(rate, 1) : '-'} />
          <InfoItem label='年利率(单利)' value={rate ? `${twoNumberTimes(rate, 360)}%(日利率*360)` : '-'} />
          <InfoItem
            label='借款期限'
            value={startDate && endDate && term ? `${startDate} - ${endDate} (${term ?? ''}期)` : '-'}
          />
          <InfoItem label='借款用途' value={purposeName ?? '-'} />
          {
            contractNo && loanNo ? (
              <InfoItem
                label='借款合同'
                buttonText='点击查看'
                showValue={false}
                onButtonClick={contractClick}
              />
            ) : <InfoItem label='借款合同' value='-' />

          }
          <View className={styles.line}></View>
          {/* {couponInfo && (
          <InfoItem
            label='优惠券'
            value={couponInfo}
            buttonText='查看'
            onButtonClick={() => console.log('优惠券点击')}
          />
        )} */}
          <InfoItem
            label='收款账户'
            value={accountName && accountNo ? `已提现至${accountName}(${accountNo?.slice(-4) ?? ''})` : '-'}
          />
          <InfoItem
            label='还款银行卡'
            value={repayAccountName && repayAccountNo ? `${repayAccountName}(${repayAccountNo?.slice(-4) ?? ''})` : '-'}
          />
          <InfoItem label='还款方式' value={repaymentTypeName ?? '-'} />
          <InfoItem label='还款日' value={(dueDate && `每月${dayjs(dueDate).format('D日')}`) ?? '-'} />
          <InfoItem label='下次还款日' value={(dueDate && dayjs(dueDate).format('YYYY年MM月DD日')) ?? '-'} />
        </View>
        {contract.length > 0 && (
          <CyImagePreview title='借款合同' images={contract} visible={contractVisible} onClose={onContractClose} />
        )}
      </Card>
    </>
  );
};

export default LoanInfo;
