// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 54;
	objects = {

/* Begin PBXBuildFile section */
		430B25F1C173126523D9AFAC /* libPods-taroDemo-rnTests.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 47B3B47F7E95FF0D8ACA066B /* libPods-taroDemo-rnTests.a */; };
		59DB395FFE37E41AD7C3662A /* ExpoModulesProvider.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2D8552542C3EE5212019185A /* ExpoModulesProvider.swift */; };
		7489C91E2BA1611400DFEBD7 /* assets in Resources */ = {isa = PBXBuildFile; fileRef = 7489C91D2BA1611400DFEBD7 /* assets */; };
		74C4D4AD2BD7B84500BF9B74 /* ScreenShieldView.m in Sources */ = {isa = PBXBuildFile; fileRef = 74C4D4AC2BD7B84500BF9B74 /* ScreenShieldView.m */; };
		74C8E3DB2B9FEE77002C8E2D /* NativeLocationManager.m in Sources */ = {isa = PBXBuildFile; fileRef = 74C8E3D92B9FEE77002C8E2D /* NativeLocationManager.m */; };
		74C8E3DE2B9FEEA4002C8E2D /* LocationManager.m in Sources */ = {isa = PBXBuildFile; fileRef = 74C8E3DD2B9FEEA4002C8E2D /* LocationManager.m */; };
		74CA97662B8EDBB50042FA99 /* main.jsbundle in Resources */ = {isa = PBXBuildFile; fileRef = 74CA96E52B8EDBB40042FA99 /* main.jsbundle */; };
		74D26A022B872F2C00271CB7 /* LaunchScreen.storyboard in Resources */ = {isa = PBXBuildFile; fileRef = 74D269FC2B872F2C00271CB7 /* LaunchScreen.storyboard */; };
		74D26A032B872F2C00271CB7 /* main.m in Sources */ = {isa = PBXBuildFile; fileRef = 74D269FE2B872F2C00271CB7 /* main.m */; };
		74D26A042B872F2C00271CB7 /* Images.xcassets in Resources */ = {isa = PBXBuildFile; fileRef = 74D269FF2B872F2C00271CB7 /* Images.xcassets */; };
		74D26A052B872F2C00271CB7 /* AppDelegate.mm in Sources */ = {isa = PBXBuildFile; fileRef = 74D26A002B872F2C00271CB7 /* AppDelegate.mm */; };
		74E81F4F2BDBA260003A7E34 /* launch-bg.png in Resources */ = {isa = PBXBuildFile; fileRef = 74E81F4E2BDBA260003A7E34 /* launch-bg.png */; };
		BB818C29E87010F1B78FC091 /* ExpoModulesProvider.swift in Sources */ = {isa = PBXBuildFile; fileRef = F68AA472D724BC849D1425A6 /* ExpoModulesProvider.swift */; };
		BD331BC130EC2429045B6814 /* libPods-taroDemo.a in Frameworks */ = {isa = PBXBuildFile; fileRef = BF5F56BA36BA7EC8440EF69E /* libPods-taroDemo.a */; };
		C7555E663B8DE179065DE03C /* PrivacyInfo.xcprivacy in Resources */ = {isa = PBXBuildFile; fileRef = 10CF61EF27274C640DAC0936 /* PrivacyInfo.xcprivacy */; };
		D4FF510C42AB98E4D4F9784D /* ExpoModulesProvider.swift in Sources */ = {isa = PBXBuildFile; fileRef = B5B235670D36F73B4A5E4110 /* ExpoModulesProvider.swift */; };
/* End PBXBuildFile section */

/* Begin PBXContainerItemProxy section */
		00E356F41AD99517003FC87E /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 83CBB9F71A601CBA00E9B192 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 13B07F861A680F5B00A75B9A;
			remoteInfo = rn;
		};
/* End PBXContainerItemProxy section */

/* Begin PBXFileReference section */
		00E356EE1AD99517003FC87E /* rnTests.xctest */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; path = rnTests.xctest; sourceTree = BUILT_PRODUCTS_DIR; };
		10CF61EF27274C640DAC0936 /* PrivacyInfo.xcprivacy */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xml; name = PrivacyInfo.xcprivacy; path = ../PrivacyInfo.xcprivacy; sourceTree = "<group>"; };
		13B07F961A680F5B00A75B9A /* rn.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = rn.app; sourceTree = BUILT_PRODUCTS_DIR; };
		2D8552542C3EE5212019185A /* ExpoModulesProvider.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = ExpoModulesProvider.swift; path = "Pods/Target Support Files/Pods-taroDemo-rnTests/ExpoModulesProvider.swift"; sourceTree = "<group>"; };
		47B3B47F7E95FF0D8ACA066B /* libPods-taroDemo-rnTests.a */ = {isa = PBXFileReference; explicitFileType = archive.ar; includeInIndex = 0; path = "libPods-taroDemo-rnTests.a"; sourceTree = BUILT_PRODUCTS_DIR; };
		65238539801331C00D2C6061 /* Pods-taroDemo-rnTests.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-taroDemo-rnTests.debug.xcconfig"; path = "Target Support Files/Pods-taroDemo-rnTests/Pods-taroDemo-rnTests.debug.xcconfig"; sourceTree = "<group>"; };
		7489C91D2BA1611400DFEBD7 /* assets */ = {isa = PBXFileReference; lastKnownFileType = folder; path = assets; sourceTree = "<group>"; };
		74991E862D7AF42B00A85822 /* libPassGuardCtrl.a */ = {isa = PBXFileReference; lastKnownFileType = archive.ar; name = libPassGuardCtrl.a; path = "../node_modules/rn-safe-keyboard/ios/Vendor/iOSPGLib/libPassGuardCtrl.a"; sourceTree = "<group>"; };
		74991E872D7AF42B00A85822 /* libcrypto.a */ = {isa = PBXFileReference; lastKnownFileType = archive.ar; name = libcrypto.a; path = "../node_modules/rn-safe-keyboard/ios/Vendor/iOSPGLib/libcrypto.a"; sourceTree = "<group>"; };
		74C4D4AB2BD7B84500BF9B74 /* ScreenShieldView.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = ScreenShieldView.h; sourceTree = "<group>"; };
		74C4D4AC2BD7B84500BF9B74 /* ScreenShieldView.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = ScreenShieldView.m; sourceTree = "<group>"; };
		74C8E3D92B9FEE77002C8E2D /* NativeLocationManager.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = NativeLocationManager.m; sourceTree = "<group>"; };
		74C8E3DA2B9FEE77002C8E2D /* NativeLocationManager.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = NativeLocationManager.h; sourceTree = "<group>"; };
		74C8E3DC2B9FEEA4002C8E2D /* LocationManager.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = LocationManager.h; sourceTree = "<group>"; };
		74C8E3DD2B9FEEA4002C8E2D /* LocationManager.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = LocationManager.m; sourceTree = "<group>"; };
		74CA96502B8EDBB40042FA99 /* main.map */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = "sourcecode.module-map"; path = main.map; sourceTree = "<group>"; };
		74CA96E52B8EDBB40042FA99 /* main.jsbundle */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text; path = main.jsbundle; sourceTree = "<group>"; };
		74D269FC2B872F2C00271CB7 /* LaunchScreen.storyboard */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = file.storyboard; path = LaunchScreen.storyboard; sourceTree = "<group>"; };
		74D269FD2B872F2C00271CB7 /* AppDelegate.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = AppDelegate.h; sourceTree = "<group>"; };
		74D269FE2B872F2C00271CB7 /* main.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = main.m; sourceTree = "<group>"; };
		74D269FF2B872F2C00271CB7 /* Images.xcassets */ = {isa = PBXFileReference; lastKnownFileType = folder.assetcatalog; path = Images.xcassets; sourceTree = "<group>"; };
		74D26A002B872F2C00271CB7 /* AppDelegate.mm */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.objcpp; path = AppDelegate.mm; sourceTree = "<group>"; };
		74D26A012B872F2C00271CB7 /* Info.plist */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text.plist.xml; path = Info.plist; sourceTree = "<group>"; };
		74E81F4E2BDBA260003A7E34 /* launch-bg.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "launch-bg.png"; sourceTree = "<group>"; };
		ADEE9BCCC9FDDC5875A775AC /* Pods-taroDemo.adhoc.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-taroDemo.adhoc.xcconfig"; path = "Target Support Files/Pods-taroDemo/Pods-taroDemo.adhoc.xcconfig"; sourceTree = "<group>"; };
		B0D45856C5157B7783F2B4E0 /* Pods-taroDemo.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-taroDemo.debug.xcconfig"; path = "Target Support Files/Pods-taroDemo/Pods-taroDemo.debug.xcconfig"; sourceTree = "<group>"; };
		B36CE4314231ECA603F41640 /* Pods-taroDemo.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-taroDemo.release.xcconfig"; path = "Target Support Files/Pods-taroDemo/Pods-taroDemo.release.xcconfig"; sourceTree = "<group>"; };
		B5B235670D36F73B4A5E4110 /* ExpoModulesProvider.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = ExpoModulesProvider.swift; path = "Pods/Target Support Files/Pods-taroDemo/ExpoModulesProvider.swift"; sourceTree = "<group>"; };
		BD2ABFFC92A097FA92391F03 /* Pods-taroDemo-rnTests.adhoc.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-taroDemo-rnTests.adhoc.xcconfig"; path = "Target Support Files/Pods-taroDemo-rnTests/Pods-taroDemo-rnTests.adhoc.xcconfig"; sourceTree = "<group>"; };
		BF5F56BA36BA7EC8440EF69E /* libPods-taroDemo.a */ = {isa = PBXFileReference; explicitFileType = archive.ar; includeInIndex = 0; path = "libPods-taroDemo.a"; sourceTree = BUILT_PRODUCTS_DIR; };
		CBF4D5834AF18C49A5386AB9 /* Pods-taroDemo-rnTests.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-taroDemo-rnTests.release.xcconfig"; path = "Target Support Files/Pods-taroDemo-rnTests/Pods-taroDemo-rnTests.release.xcconfig"; sourceTree = "<group>"; };
		ED297162215061F000B7C4FE /* JavaScriptCore.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = JavaScriptCore.framework; path = System/Library/Frameworks/JavaScriptCore.framework; sourceTree = SDKROOT; };
		F68AA472D724BC849D1425A6 /* ExpoModulesProvider.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = ExpoModulesProvider.swift; path = "Pods/Target Support Files/Pods-rn-rnTests/ExpoModulesProvider.swift"; sourceTree = "<group>"; };
/* End PBXFileReference section */

/* Begin PBXFrameworksBuildPhase section */
		00E356EB1AD99517003FC87E /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = **********;
			files = (
				430B25F1C173126523D9AFAC /* libPods-taroDemo-rnTests.a in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		13B07F8C1A680F5B00A75B9A /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = **********;
			files = (
				BD331BC130EC2429045B6814 /* libPods-taroDemo.a in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		11E7537EE43B145CF6DD6512 /* ExpoModulesProviders */ = {
			isa = PBXGroup;
			children = (
				2D588BD5B41F25D0E207BE95 /* rnTests */,
				42CC1E7E0A3558A9C26E47CA /* taroDemo */,
			);
			name = ExpoModulesProviders;
			sourceTree = "<group>";
		};
		2D16E6871FA4F8E400B85C8A /* Frameworks */ = {
			isa = PBXGroup;
			children = (
				74991E872D7AF42B00A85822 /* libcrypto.a */,
				74991E862D7AF42B00A85822 /* libPassGuardCtrl.a */,
				ED297162215061F000B7C4FE /* JavaScriptCore.framework */,
				BF5F56BA36BA7EC8440EF69E /* libPods-taroDemo.a */,
				47B3B47F7E95FF0D8ACA066B /* libPods-taroDemo-rnTests.a */,
			);
			name = Frameworks;
			sourceTree = "<group>";
		};
		2D588BD5B41F25D0E207BE95 /* rnTests */ = {
			isa = PBXGroup;
			children = (
				F68AA472D724BC849D1425A6 /* ExpoModulesProvider.swift */,
				2D8552542C3EE5212019185A /* ExpoModulesProvider.swift */,
			);
			name = rnTests;
			sourceTree = "<group>";
		};
		42CC1E7E0A3558A9C26E47CA /* taroDemo */ = {
			isa = PBXGroup;
			children = (
				B5B235670D36F73B4A5E4110 /* ExpoModulesProvider.swift */,
			);
			name = taroDemo;
			sourceTree = "<group>";
		};
		7456AC182B9EE27E00D20EBA /* Module */ = {
			isa = PBXGroup;
			children = (
				74C8E3DC2B9FEEA4002C8E2D /* LocationManager.h */,
				74C8E3DD2B9FEEA4002C8E2D /* LocationManager.m */,
				74C8E3DA2B9FEE77002C8E2D /* NativeLocationManager.h */,
				74C8E3D92B9FEE77002C8E2D /* NativeLocationManager.m */,
			);
			path = Module;
			sourceTree = "<group>";
		};
		74C4D4AA2BD7B83300BF9B74 /* conponents */ = {
			isa = PBXGroup;
			children = (
				74C4D4AB2BD7B84500BF9B74 /* ScreenShieldView.h */,
				74C4D4AC2BD7B84500BF9B74 /* ScreenShieldView.m */,
			);
			path = conponents;
			sourceTree = "<group>";
		};
		74D269FB2B872F2C00271CB7 /* taroDemo */ = {
			isa = PBXGroup;
			children = (
				74C4D4AA2BD7B83300BF9B74 /* conponents */,
				74D269FD2B872F2C00271CB7 /* AppDelegate.h */,
				74D26A002B872F2C00271CB7 /* AppDelegate.mm */,
				74D269FF2B872F2C00271CB7 /* Images.xcassets */,
				74D26A012B872F2C00271CB7 /* Info.plist */,
				74D269FC2B872F2C00271CB7 /* LaunchScreen.storyboard */,
				74E81F4E2BDBA260003A7E34 /* launch-bg.png */,
				74D269FE2B872F2C00271CB7 /* main.m */,
				7456AC182B9EE27E00D20EBA /* Module */,
				10CF61EF27274C640DAC0936 /* PrivacyInfo.xcprivacy */,
			);
			path = taroDemo;
			sourceTree = "<group>";
		};
		832341AE1AAA6A7D00B99B32 /* Libraries */ = {
			isa = PBXGroup;
			children = (
			);
			name = Libraries;
			sourceTree = "<group>";
		};
		83CBB9F61A601CBA00E9B192 = {
			isa = PBXGroup;
			children = (
				74D269FB2B872F2C00271CB7 /* taroDemo */,
				74CA96E52B8EDBB40042FA99 /* main.jsbundle */,
				74CA96502B8EDBB40042FA99 /* main.map */,
				7489C91D2BA1611400DFEBD7 /* assets */,
				832341AE1AAA6A7D00B99B32 /* Libraries */,
				83CBBA001A601CBA00E9B192 /* Products */,
				2D16E6871FA4F8E400B85C8A /* Frameworks */,
				BBD78D7AC51CEA395F1C20DB /* Pods */,
				11E7537EE43B145CF6DD6512 /* ExpoModulesProviders */,
			);
			indentWidth = 2;
			sourceTree = "<group>";
			tabWidth = 2;
			usesTabs = 0;
		};
		83CBBA001A601CBA00E9B192 /* Products */ = {
			isa = PBXGroup;
			children = (
				13B07F961A680F5B00A75B9A /* rn.app */,
				00E356EE1AD99517003FC87E /* rnTests.xctest */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		BBD78D7AC51CEA395F1C20DB /* Pods */ = {
			isa = PBXGroup;
			children = (
				B0D45856C5157B7783F2B4E0 /* Pods-taroDemo.debug.xcconfig */,
				B36CE4314231ECA603F41640 /* Pods-taroDemo.release.xcconfig */,
				65238539801331C00D2C6061 /* Pods-taroDemo-rnTests.debug.xcconfig */,
				CBF4D5834AF18C49A5386AB9 /* Pods-taroDemo-rnTests.release.xcconfig */,
				ADEE9BCCC9FDDC5875A775AC /* Pods-taroDemo.adhoc.xcconfig */,
				BD2ABFFC92A097FA92391F03 /* Pods-taroDemo-rnTests.adhoc.xcconfig */,
			);
			path = Pods;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		00E356ED1AD99517003FC87E /* rnTests */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 00E357021AD99517003FC87E /* Build configuration list for PBXNativeTarget "rnTests" */;
			buildPhases = (
				A55EABD7B0C7F3A422A6CC61 /* [CP] Check Pods Manifest.lock */,
				398A020A6EED227A98F9950B /* [Expo] Configure project */,
				00E356EA1AD99517003FC87E /* Sources */,
				00E356EB1AD99517003FC87E /* Frameworks */,
				00E356EC1AD99517003FC87E /* Resources */,
				C59DA0FBD6956966B86A3779 /* [CP] Embed Pods Frameworks */,
				F6A41C54EA430FDDC6A6ED99 /* [CP] Copy Pods Resources */,
			);
			buildRules = (
			);
			dependencies = (
				00E356F51AD99517003FC87E /* PBXTargetDependency */,
			);
			name = rnTests;
			productName = rnTests;
			productReference = 00E356EE1AD99517003FC87E /* rnTests.xctest */;
			productType = "com.apple.product-type.bundle.unit-test";
		};
		13B07F861A680F5B00A75B9A /* taroDemo */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 13B07F931A680F5B00A75B9A /* Build configuration list for PBXNativeTarget "taroDemo" */;
			buildPhases = (
				C38B50BA6285516D6DCD4F65 /* [CP] Check Pods Manifest.lock */,
				0793EA18E731A0138928EFEB /* [Expo] Configure project */,
				13B07F871A680F5B00A75B9A /* Sources */,
				13B07F8E1A680F5B00A75B9A /* Resources */,
				00DD1BFF1BD5951E006B06BC /* Bundle React Native code and images */,
				00EEFC60759A1932668264C0 /* [CP] Embed Pods Frameworks */,
				E235C05ADACE081382539298 /* [CP] Copy Pods Resources */,
				13B07F8C1A680F5B00A75B9A /* Frameworks */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = taroDemo;
			productName = rn;
			productReference = 13B07F961A680F5B00A75B9A /* rn.app */;
			productType = "com.apple.product-type.application";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		83CBB9F71A601CBA00E9B192 /* Project object */ = {
			isa = PBXProject;
			attributes = {
				LastUpgradeCheck = 1210;
				TargetAttributes = {
					00E356ED1AD99517003FC87E = {
						CreatedOnToolsVersion = 6.2;
						TestTargetID = 13B07F861A680F5B00A75B9A;
					};
					13B07F861A680F5B00A75B9A = {
						LastSwiftMigration = 1120;
					};
				};
			};
			buildConfigurationList = 83CBB9FA1A601CBA00E9B192 /* Build configuration list for PBXProject "taroDemo" */;
			compatibilityVersion = "Xcode 12.0";
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
				Base,
			);
			mainGroup = 83CBB9F61A601CBA00E9B192;
			productRefGroup = 83CBBA001A601CBA00E9B192 /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				13B07F861A680F5B00A75B9A /* taroDemo */,
				00E356ED1AD99517003FC87E /* rnTests */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		00E356EC1AD99517003FC87E /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = **********;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		13B07F8E1A680F5B00A75B9A /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = **********;
			files = (
				74CA97662B8EDBB50042FA99 /* main.jsbundle in Resources */,
				7489C91E2BA1611400DFEBD7 /* assets in Resources */,
				74D26A022B872F2C00271CB7 /* LaunchScreen.storyboard in Resources */,
				74E81F4F2BDBA260003A7E34 /* launch-bg.png in Resources */,
				74D26A042B872F2C00271CB7 /* Images.xcassets in Resources */,
				C7555E663B8DE179065DE03C /* PrivacyInfo.xcprivacy in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXShellScriptBuildPhase section */
		00DD1BFF1BD5951E006B06BC /* Bundle React Native code and images */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = **********;
			files = (
			);
			inputPaths = (
				"$(SRCROOT)/.xcode.env.local",
				"$(SRCROOT)/.xcode.env",
			);
			name = "Bundle React Native code and images";
			outputPaths = (
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "\nset -e\n\nWITH_ENVIRONMENT=\"../node_modules/react-native/scripts/xcode/with-environment.sh\"\nREACT_NATIVE_XCODE=\"../node_modules/react-native/scripts/react-native-xcode.sh\"\n\n/bin/sh -c \"$WITH_ENVIRONMENT $REACT_NATIVE_XCODE\"\n";
		};
		00EEFC60759A1932668264C0 /* [CP] Embed Pods Frameworks */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = **********;
			files = (
			);
			inputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-taroDemo/Pods-taroDemo-frameworks-${CONFIGURATION}-input-files.xcfilelist",
			);
			name = "[CP] Embed Pods Frameworks";
			outputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-taroDemo/Pods-taroDemo-frameworks-${CONFIGURATION}-output-files.xcfilelist",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "\"${PODS_ROOT}/Target Support Files/Pods-taroDemo/Pods-taroDemo-frameworks.sh\"\n";
			showEnvVarsInLog = 0;
		};
		0793EA18E731A0138928EFEB /* [Expo] Configure project */ = {
			isa = PBXShellScriptBuildPhase;
			alwaysOutOfDate = 1;
			buildActionMask = **********;
			files = (
			);
			inputFileListPaths = (
			);
			inputPaths = (
			);
			name = "[Expo] Configure project";
			outputFileListPaths = (
			);
			outputPaths = (
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "# This script configures Expo modules and generates the modules provider file.\nbash -l -c \"./Pods/Target\\ Support\\ Files/Pods-taroDemo/expo-configure-project.sh\"\n";
		};
		398A020A6EED227A98F9950B /* [Expo] Configure project */ = {
			isa = PBXShellScriptBuildPhase;
			alwaysOutOfDate = 1;
			buildActionMask = **********;
			files = (
			);
			inputFileListPaths = (
			);
			inputPaths = (
			);
			name = "[Expo] Configure project";
			outputFileListPaths = (
			);
			outputPaths = (
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "# This script configures Expo modules and generates the modules provider file.\nbash -l -c \"./Pods/Target\\ Support\\ Files/Pods-taroDemo-rnTests/expo-configure-project.sh\"\n";
		};
		A55EABD7B0C7F3A422A6CC61 /* [CP] Check Pods Manifest.lock */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = **********;
			files = (
			);
			inputFileListPaths = (
			);
			inputPaths = (
				"${PODS_PODFILE_DIR_PATH}/Podfile.lock",
				"${PODS_ROOT}/Manifest.lock",
			);
			name = "[CP] Check Pods Manifest.lock";
			outputFileListPaths = (
			);
			outputPaths = (
				"$(DERIVED_FILE_DIR)/Pods-taroDemo-rnTests-checkManifestLockResult.txt",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "diff \"${PODS_PODFILE_DIR_PATH}/Podfile.lock\" \"${PODS_ROOT}/Manifest.lock\" > /dev/null\nif [ $? != 0 ] ; then\n    # print error to STDERR\n    echo \"error: The sandbox is not in sync with the Podfile.lock. Run 'pod install' or update your CocoaPods installation.\" >&2\n    exit 1\nfi\n# This output is used by Xcode 'outputs' to avoid re-running this script phase.\necho \"SUCCESS\" > \"${SCRIPT_OUTPUT_FILE_0}\"\n";
			showEnvVarsInLog = 0;
		};
		C38B50BA6285516D6DCD4F65 /* [CP] Check Pods Manifest.lock */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = **********;
			files = (
			);
			inputFileListPaths = (
			);
			inputPaths = (
				"${PODS_PODFILE_DIR_PATH}/Podfile.lock",
				"${PODS_ROOT}/Manifest.lock",
			);
			name = "[CP] Check Pods Manifest.lock";
			outputFileListPaths = (
			);
			outputPaths = (
				"$(DERIVED_FILE_DIR)/Pods-taroDemo-checkManifestLockResult.txt",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "diff \"${PODS_PODFILE_DIR_PATH}/Podfile.lock\" \"${PODS_ROOT}/Manifest.lock\" > /dev/null\nif [ $? != 0 ] ; then\n    # print error to STDERR\n    echo \"error: The sandbox is not in sync with the Podfile.lock. Run 'pod install' or update your CocoaPods installation.\" >&2\n    exit 1\nfi\n# This output is used by Xcode 'outputs' to avoid re-running this script phase.\necho \"SUCCESS\" > \"${SCRIPT_OUTPUT_FILE_0}\"\n";
			showEnvVarsInLog = 0;
		};
		C59DA0FBD6956966B86A3779 /* [CP] Embed Pods Frameworks */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = **********;
			files = (
			);
			inputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-taroDemo-rnTests/Pods-taroDemo-rnTests-frameworks-${CONFIGURATION}-input-files.xcfilelist",
			);
			name = "[CP] Embed Pods Frameworks";
			outputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-taroDemo-rnTests/Pods-taroDemo-rnTests-frameworks-${CONFIGURATION}-output-files.xcfilelist",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "\"${PODS_ROOT}/Target Support Files/Pods-taroDemo-rnTests/Pods-taroDemo-rnTests-frameworks.sh\"\n";
			showEnvVarsInLog = 0;
		};
		E235C05ADACE081382539298 /* [CP] Copy Pods Resources */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = **********;
			files = (
			);
			inputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-taroDemo/Pods-taroDemo-resources-${CONFIGURATION}-input-files.xcfilelist",
			);
			name = "[CP] Copy Pods Resources";
			outputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-taroDemo/Pods-taroDemo-resources-${CONFIGURATION}-output-files.xcfilelist",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "\"${PODS_ROOT}/Target Support Files/Pods-taroDemo/Pods-taroDemo-resources.sh\"\n";
			showEnvVarsInLog = 0;
		};
		F6A41C54EA430FDDC6A6ED99 /* [CP] Copy Pods Resources */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = **********;
			files = (
			);
			inputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-taroDemo-rnTests/Pods-taroDemo-rnTests-resources-${CONFIGURATION}-input-files.xcfilelist",
			);
			name = "[CP] Copy Pods Resources";
			outputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-taroDemo-rnTests/Pods-taroDemo-rnTests-resources-${CONFIGURATION}-output-files.xcfilelist",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "\"${PODS_ROOT}/Target Support Files/Pods-taroDemo-rnTests/Pods-taroDemo-rnTests-resources.sh\"\n";
			showEnvVarsInLog = 0;
		};
/* End PBXShellScriptBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		00E356EA1AD99517003FC87E /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = **********;
			files = (
				BB818C29E87010F1B78FC091 /* ExpoModulesProvider.swift in Sources */,
				59DB395FFE37E41AD7C3662A /* ExpoModulesProvider.swift in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		13B07F871A680F5B00A75B9A /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = **********;
			files = (
				74C8E3DB2B9FEE77002C8E2D /* NativeLocationManager.m in Sources */,
				74C4D4AD2BD7B84500BF9B74 /* ScreenShieldView.m in Sources */,
				74C8E3DE2B9FEEA4002C8E2D /* LocationManager.m in Sources */,
				74D26A032B872F2C00271CB7 /* main.m in Sources */,
				74D26A052B872F2C00271CB7 /* AppDelegate.mm in Sources */,
				D4FF510C42AB98E4D4F9784D /* ExpoModulesProvider.swift in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin PBXTargetDependency section */
		00E356F51AD99517003FC87E /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = 13B07F861A680F5B00A75B9A /* taroDemo */;
			targetProxy = 00E356F41AD99517003FC87E /* PBXContainerItemProxy */;
		};
/* End PBXTargetDependency section */

/* Begin XCBuildConfiguration section */
		00E356F61AD99517003FC87E /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 65238539801331C00D2C6061 /* Pods-taroDemo-rnTests.debug.xcconfig */;
			buildSettings = {
				BUNDLE_LOADER = "$(TEST_HOST)";
				CODE_SIGN_STYLE = Manual;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				INFOPLIST_FILE = rnTests/Info.plist;
				IPHONEOS_DEPLOYMENT_TARGET = 13.4;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@loader_path/Frameworks",
				);
				OTHER_LDFLAGS = (
					"-ObjC",
					"-lc++",
					"$(inherited)",
				);
				OTHER_SWIFT_FLAGS = "$(inherited) -D EXPO_CONFIGURATION_DEBUG";
				PRODUCT_BUNDLE_IDENTIFIER = "org.reactjs.native.example.$(PRODUCT_NAME:rfc1034identifier)";
				PRODUCT_NAME = "$(TARGET_NAME)";
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/rn.app/rn";
			};
			name = Debug;
		};
		00E356F71AD99517003FC87E /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = CBF4D5834AF18C49A5386AB9 /* Pods-taroDemo-rnTests.release.xcconfig */;
			buildSettings = {
				BUNDLE_LOADER = "$(TEST_HOST)";
				CODE_SIGN_STYLE = Manual;
				COPY_PHASE_STRIP = NO;
				INFOPLIST_FILE = rnTests/Info.plist;
				IPHONEOS_DEPLOYMENT_TARGET = 13.4;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@loader_path/Frameworks",
				);
				OTHER_LDFLAGS = (
					"-ObjC",
					"-lc++",
					"$(inherited)",
				);
				OTHER_SWIFT_FLAGS = "$(inherited) -D EXPO_CONFIGURATION_RELEASE";
				PRODUCT_BUNDLE_IDENTIFIER = "org.reactjs.native.example.$(PRODUCT_NAME:rfc1034identifier)";
				PRODUCT_NAME = "$(TARGET_NAME)";
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/rn.app/rn";
			};
			name = Release;
		};
		13B07F941A680F5B00A75B9A /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = B0D45856C5157B7783F2B4E0 /* Pods-taroDemo.debug.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				CLANG_ENABLE_MODULES = YES;
				CODE_SIGN_IDENTITY = "Apple Development";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 18;
				DEFINES_MODULE = NO;
				DEVELOPMENT_TEAM = QXRC9J274M;
				ENABLE_BITCODE = NO;
				INFOPLIST_FILE = taroDemo/Info.plist;
				IPHONEOS_DEPLOYMENT_TARGET = 13.4;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				LIBRARY_SEARCH_PATHS = (
					"$(SDKROOT)/usr/lib/swift",
					"$(inherited)",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/DoubleConversion\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/EXAV\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/EXBarCodeScanner\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/EXConstants\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/EXFont\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/EXImageLoader\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/EXLocation\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/Expo\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/ExpoBrightness\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/ExpoCamera\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/ExpoFileSystem\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/ExpoImagePicker\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/ExpoKeepAwake\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/ExpoModulesCore\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/ExpoSensors\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/LookinServer\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/RCT-Folly\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/RCTTypeSafety\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/RNCAsyncStorage\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/RNCClipboard\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/RNCMaskedView\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/RNCPicker\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/RNDeviceInfo\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/RNFS\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/RNFileViewer\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/RNGestureHandler\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/RNLinearGradient\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/RNPDF\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/RNPermissions\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/RNScreens\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/React-Codegen\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/React-Core\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/React-CoreModules\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/React-Fabric\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/React-FabricImage\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/React-ImageManager\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/React-Mapbuffer\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/React-NativeModulesApple\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/React-RCTAnimation\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/React-RCTAppDelegate\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/React-RCTBlob\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/React-RCTFabric\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/React-RCTImage\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/React-RCTLinking\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/React-RCTNetwork\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/React-RCTSettings\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/React-RCTText\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/React-RCTVibration\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/React-RuntimeApple\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/React-RuntimeCore\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/React-RuntimeHermes\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/React-cxxreact\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/React-debug\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/React-graphics\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/React-hermes\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/React-jserrorhandler\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/React-jsi\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/React-jsiexecutor\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/React-jsinspector\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/React-jsitracing\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/React-logger\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/React-nativeconfig\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/React-perflogger\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/React-rendererdebug\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/React-runtimescheduler\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/React-utils\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/ReactCommon\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/SSZipArchive\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/SocketRocket\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/Yoga\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/ZXingObjC\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/fmt\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/glog\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/libevent\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/react-native-cameraroll\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/react-native-document-picker\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/react-native-geolocation\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/react-native-image-resizer\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/react-native-menu\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/react-native-netinfo\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/react-native-orientation-locker\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/react-native-pager-view\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/react-native-retu-bugly\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/react-native-safe-area-context\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/react-native-skia\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/react-native-slider\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/react-native-splash-screen\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/react-native-update\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/react-native-webview\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/rn-fetch-blob\"",
					"\"${PODS_XCFRAMEWORKS_BUILD_DIR}/react-native-skia\"",
					"\"${TOOLCHAIN_DIR}/usr/lib/swift/${PLATFORM_NAME}\"",
					/usr/lib/swift,
					"$(PROJECT_DIR)/Vendor/iOSPGLib",
				);
				MARKETING_VERSION = 2.0.2;
				OTHER_LDFLAGS = (
					"$(inherited)",
					"-ObjC",
					"-lc++",
				);
				OTHER_SWIFT_FLAGS = "$(inherited) -D EXPO_CONFIGURATION_DEBUG";
				PRODUCT_BUNDLE_IDENTIFIER = com.hncy58.financeapp;
				PRODUCT_NAME = rn;
				PROVISIONING_PROFILE_SPECIFIER = "";
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
				SWIFT_VERSION = 5.0;
				VERSIONING_SYSTEM = "apple-generic";
			};
			name = Debug;
		};
		13B07F951A680F5B00A75B9A /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = B36CE4314231ECA603F41640 /* Pods-taroDemo.release.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				CLANG_ENABLE_MODULES = YES;
				CODE_SIGN_IDENTITY = "Apple Development";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Distribution";
				CODE_SIGN_STYLE = Manual;
				CURRENT_PROJECT_VERSION = 18;
				DEFINES_MODULE = NO;
				DEVELOPMENT_TEAM = QXRC9J274M;
				"DEVELOPMENT_TEAM[sdk=iphoneos*]" = QXRC9J274M;
				INFOPLIST_FILE = taroDemo/Info.plist;
				IPHONEOS_DEPLOYMENT_TARGET = 13.4;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				LIBRARY_SEARCH_PATHS = (
					"$(SDKROOT)/usr/lib/swift",
					"$(inherited)",
					"$(PROJECT_DIR)/Vendor/iOSPGLib",
				);
				MARKETING_VERSION = 2.0.2;
				OTHER_LDFLAGS = (
					"$(inherited)",
					"-ObjC",
					"-lc++",
				);
				OTHER_SWIFT_FLAGS = "$(inherited) -D EXPO_CONFIGURATION_RELEASE";
				PRODUCT_BUNDLE_IDENTIFIER = com.hncy58.financeapp;
				PRODUCT_NAME = rn;
				PROVISIONING_PROFILE_SPECIFIER = "";
				"PROVISIONING_PROFILE_SPECIFIER[sdk=iphoneos*]" = financeapp_AppStore;
				SWIFT_VERSION = 5.0;
				VERSIONING_SYSTEM = "apple-generic";
			};
			name = Release;
		};
		74172EE52B8DE61900853BA9 /* Adhoc */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ANALYZER_LOCALIZABILITY_NONLOCALIZED = YES;
				CLANG_CXX_LANGUAGE_STANDARD = "c++20";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Developer";
				COPY_PHASE_STRIP = YES;
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				"EXCLUDED_ARCHS[sdk=iphonesimulator*]" = i386;
				GCC_C_LANGUAGE_STANDARD = gnu99;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_PREPROCESSOR_DEFINITIONS = "ADHOC=1";
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 13.4;
				LD_RUNPATH_SEARCH_PATHS = (
					/usr/lib/swift,
					"$(inherited)",
				);
				LIBRARY_SEARCH_PATHS = (
					"\"$(SDKROOT)/usr/lib/swift\"",
					"\"$(TOOLCHAIN_DIR)/usr/lib/swift/$(PLATFORM_NAME)\"",
					"\"$(inherited)\"",
				);
				MTL_ENABLE_DEBUG_INFO = NO;
				OTHER_CFLAGS = (
					"$(inherited)",
					" ",
				);
				OTHER_CPLUSPLUSFLAGS = (
					"$(OTHER_CFLAGS)",
					"-DFOLLY_NO_CONFIG",
					"-DFOLLY_MOBILE=1",
					"-DFOLLY_USE_LIBCPP=1",
					"-DFOLLY_CFG_NO_COROUTINES=1",
					" ",
				);
				OTHER_LDFLAGS = "$(inherited)";
				REACT_NATIVE_PATH = "${PODS_ROOT}/../../node_modules/react-native";
				SDKROOT = iphoneos;
				USE_HERMES = true;
				VALIDATE_PRODUCT = YES;
			};
			name = Adhoc;
		};
		74172EE62B8DE61900853BA9 /* Adhoc */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = ADEE9BCCC9FDDC5875A775AC /* Pods-taroDemo.adhoc.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				CLANG_ENABLE_MODULES = YES;
				CODE_SIGN_IDENTITY = "Apple Development";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Distribution";
				CODE_SIGN_STYLE = Manual;
				CURRENT_PROJECT_VERSION = 18;
				DEFINES_MODULE = NO;
				DEVELOPMENT_TEAM = QXRC9J274M;
				"DEVELOPMENT_TEAM[sdk=iphoneos*]" = QXRC9J274M;
				INFOPLIST_FILE = taroDemo/Info.plist;
				IPHONEOS_DEPLOYMENT_TARGET = 13.4;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				LIBRARY_SEARCH_PATHS = (
					"$(SDKROOT)/usr/lib/swift",
					"$(inherited)",
					"$(PROJECT_DIR)/Vendor/iOSPGLib",
				);
				MARKETING_VERSION = 2.0.2;
				OTHER_LDFLAGS = (
					"$(inherited)",
					"-ObjC",
					"-lc++",
				);
				OTHER_SWIFT_FLAGS = "$(inherited) -D EXPO_CONFIGURATION_RELEASE";
				PRODUCT_BUNDLE_IDENTIFIER = com.hncy58.financeapp;
				PRODUCT_NAME = rn;
				PROVISIONING_PROFILE_SPECIFIER = "";
				"PROVISIONING_PROFILE_SPECIFIER[sdk=iphoneos*]" = financeapp_Adhoc;
				SWIFT_VERSION = 5.0;
				VERSIONING_SYSTEM = "apple-generic";
			};
			name = Adhoc;
		};
		74172EE72B8DE61900853BA9 /* Adhoc */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = BD2ABFFC92A097FA92391F03 /* Pods-taroDemo-rnTests.adhoc.xcconfig */;
			buildSettings = {
				BUNDLE_LOADER = "$(TEST_HOST)";
				CODE_SIGN_STYLE = Manual;
				COPY_PHASE_STRIP = NO;
				INFOPLIST_FILE = rnTests/Info.plist;
				IPHONEOS_DEPLOYMENT_TARGET = 13.4;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@loader_path/Frameworks",
				);
				OTHER_LDFLAGS = (
					"-ObjC",
					"-lc++",
					"$(inherited)",
				);
				OTHER_SWIFT_FLAGS = "$(inherited) -D EXPO_CONFIGURATION_RELEASE";
				PRODUCT_BUNDLE_IDENTIFIER = "org.reactjs.native.example.$(PRODUCT_NAME:rfc1034identifier)";
				PRODUCT_NAME = "$(TARGET_NAME)";
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/rn.app/rn";
			};
			name = Adhoc;
		};
		83CBBA201A601CBA00E9B192 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ANALYZER_LOCALIZABILITY_NONLOCALIZED = YES;
				CLANG_CXX_LANGUAGE_STANDARD = "c++20";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Developer";
				COPY_PHASE_STRIP = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				"EXCLUDED_ARCHS[sdk=iphonesimulator*]" = i386;
				GCC_C_LANGUAGE_STANDARD = gnu99;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_SYMBOLS_PRIVATE_EXTERN = NO;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 13.4;
				LD_RUNPATH_SEARCH_PATHS = (
					/usr/lib/swift,
					"$(inherited)",
				);
				LIBRARY_SEARCH_PATHS = (
					"\"$(SDKROOT)/usr/lib/swift\"",
					"\"$(TOOLCHAIN_DIR)/usr/lib/swift/$(PLATFORM_NAME)\"",
					"\"$(inherited)\"",
				);
				MTL_ENABLE_DEBUG_INFO = YES;
				ONLY_ACTIVE_ARCH = YES;
				OTHER_CFLAGS = (
					"$(inherited)",
					" ",
				);
				OTHER_CPLUSPLUSFLAGS = (
					"$(OTHER_CFLAGS)",
					"-DFOLLY_NO_CONFIG",
					"-DFOLLY_MOBILE=1",
					"-DFOLLY_USE_LIBCPP=1",
					"-DFOLLY_CFG_NO_COROUTINES=1",
					" ",
				);
				OTHER_LDFLAGS = "$(inherited)";
				REACT_NATIVE_PATH = "${PODS_ROOT}/../../node_modules/react-native";
				SDKROOT = iphoneos;
				USE_HERMES = true;
			};
			name = Debug;
		};
		83CBBA211A601CBA00E9B192 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ANALYZER_LOCALIZABILITY_NONLOCALIZED = YES;
				CLANG_CXX_LANGUAGE_STANDARD = "c++20";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Developer";
				COPY_PHASE_STRIP = YES;
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				"EXCLUDED_ARCHS[sdk=iphonesimulator*]" = i386;
				GCC_C_LANGUAGE_STANDARD = gnu99;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 13.4;
				LD_RUNPATH_SEARCH_PATHS = (
					/usr/lib/swift,
					"$(inherited)",
				);
				LIBRARY_SEARCH_PATHS = (
					"\"$(SDKROOT)/usr/lib/swift\"",
					"\"$(TOOLCHAIN_DIR)/usr/lib/swift/$(PLATFORM_NAME)\"",
					"\"$(inherited)\"",
				);
				MTL_ENABLE_DEBUG_INFO = NO;
				OTHER_CFLAGS = (
					"$(inherited)",
					" ",
				);
				OTHER_CPLUSPLUSFLAGS = (
					"$(OTHER_CFLAGS)",
					"-DFOLLY_NO_CONFIG",
					"-DFOLLY_MOBILE=1",
					"-DFOLLY_USE_LIBCPP=1",
					"-DFOLLY_CFG_NO_COROUTINES=1",
					" ",
				);
				OTHER_LDFLAGS = "$(inherited)";
				REACT_NATIVE_PATH = "${PODS_ROOT}/../../node_modules/react-native";
				SDKROOT = iphoneos;
				USE_HERMES = true;
				VALIDATE_PRODUCT = YES;
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		00E357021AD99517003FC87E /* Build configuration list for PBXNativeTarget "rnTests" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				00E356F61AD99517003FC87E /* Debug */,
				00E356F71AD99517003FC87E /* Release */,
				74172EE72B8DE61900853BA9 /* Adhoc */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		13B07F931A680F5B00A75B9A /* Build configuration list for PBXNativeTarget "taroDemo" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				13B07F941A680F5B00A75B9A /* Debug */,
				13B07F951A680F5B00A75B9A /* Release */,
				74172EE62B8DE61900853BA9 /* Adhoc */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		83CBB9FA1A601CBA00E9B192 /* Build configuration list for PBXProject "taroDemo" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				83CBBA201A601CBA00E9B192 /* Debug */,
				83CBBA211A601CBA00E9B192 /* Release */,
				74172EE52B8DE61900853BA9 /* Adhoc */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */
	};
	rootObject = 83CBB9F71A601CBA00E9B192 /* Project object */;
}
