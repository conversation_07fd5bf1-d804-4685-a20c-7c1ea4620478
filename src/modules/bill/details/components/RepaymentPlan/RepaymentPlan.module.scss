.repaymentPlan {
  margin-top: 20px;
}

.tabs {
  display: flex;
  gap: 65px;
  padding: 0 40px;
  font-family: Source <PERSON>;
  font-weight: 400;
  font-size: 28px;
  line-height: 40px;
  color: #999999;
}

.tab,
.tabActive {
  text-align: center;
  padding: 15px 0;
}

.tabActive {
  color: #1677ff;
  position: relative;
}

.tabActive::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 40px;
  height: 2px;
  background-color: #2f54eb;
}

.content {
  padding: 40px;
  padding-bottom: 0;
}

.planContent {
  &-list {
    margin-top: 65px;
  }
  &-header {
    background: linear-gradient(90deg, #e9edff 1%, #fafbff 99%);
    border-radius: 8px;
    padding: 20px 30px;
    font-family: Source Han Sans SC;
    font-weight: 400;
    font-size: 24px;
    color: #333333;
    &-amount {
      margin-left: 4px;
      color: #2f54eb;
    }
  }
}

.recordContent {
  display: flex;
  flex-direction: column;
  gap: 32px;
  .recordItem {
    font-family: Source Han Sans SC;
    font-weight: 400;
    font-size: 24px;
    color: #333333;
    line-height: 40px;
    border-bottom: 1px solid #dddddd;
    padding-bottom: 30px;
    &-amount {
      font-weight: bold;
      font-size: 32px;
    }
    &-bjlx {
      font-size: 24px;
      color: #999999;
    }
  }
  .recordItem:last-child {
    border-bottom: none;
    padding-bottom: 0;
  }
}

.noRecord {
  color: #999999;
  text-align: center;
}
