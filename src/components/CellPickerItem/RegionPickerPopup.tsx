import {useCallback} from 'react';
import {AreaPicker, Popup} from '@taroify/core';
import {areaList} from '@vant/area-data';

// 必须要有默认值 否则会有bug
const DEFAULT_AREA = ['110000', '110100', '110101']; // 北京市-北京市-东城区

const RegionPickerPopup: React.FC<{
  openPicker: boolean;
  setOpenPicker: (open: boolean) => void;
  onChange?: (value: any, options: any[]) => void;
  value?: any;
}> = ({openPicker, setOpenPicker, onChange, value}) => {
  const handleClose = useCallback(() => setOpenPicker(false), [setOpenPicker]);

  const handleConfirm = useCallback(
    (value: any, options: any[]) => {
      onChange?.(value, options);
      // 使用 requestAnimationFrame 替代 setTimeout 以提高性能
      requestAnimationFrame(() => {
        setOpenPicker(false);
      });
    },
    [onChange, setOpenPicker],
  );

  return (
    <Popup open={openPicker} rounded placement='bottom' onClose={handleClose}>
      <Popup.Backdrop />
      <AreaPicker areaList={areaList} defaultValue={DEFAULT_AREA} onCancel={handleClose} onConfirm={handleConfirm} value={value} />
    </Popup>
  );
};

export default RegionPickerPopup;
