import RNFetchBlob from 'rn-fetch-blob';
import {isAndriod, isIos} from '../platform';

const MIME_MapTable = [
  // {后缀名，MIME类型}
  ['.3gp', 'video/3gpp'],
  ['.apk', 'application/vnd.android.package-archive'],
  ['.asf', 'video/x-ms-asf'],
  ['.avi', 'video/x-msvideo'],
  ['.bin', 'application/octet-stream'],
  ['.bmp', 'image/bmp'],
  ['.c', 'text/plain'],
  ['.class', 'application/octet-stream'],
  ['.conf', 'text/plain'],
  ['.cpp', 'text/plain'],
  ['.doc', 'application/msword'],
  ['.docx', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'],
  ['.xls', 'application/vnd.ms-excel'],
  ['.xlsx', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'],
  ['.exe', 'application/octet-stream'],
  ['.gif', 'image/gif'],
  ['.gtar', 'application/x-gtar'],
  ['.gz', 'application/x-gzip'],
  ['.h', 'text/plain'],
  ['.htm', 'text/html'],
  ['.html', 'text/html'],
  ['.jar', 'application/java-archive'],
  ['.java', 'text/plain'],
  ['.jpeg', 'image/jpeg'],
  ['.jpg', 'image/jpeg'],
  ['.js', 'application/x-javascript'],
  ['.log', 'text/plain'],
  ['.m3u', 'audio/x-mpegurl'],
  ['.m4a', 'audio/mp4a-latm'],
  ['.m4b', 'audio/mp4a-latm'],
  ['.m4p', 'audio/mp4a-latm'],
  ['.m4u', 'video/vnd.mpegurl'],
  ['.m4v', 'video/x-m4v'],
  ['.mov', 'video/quicktime'],
  ['.mp2', 'audio/x-mpeg'],
  ['.mp3', 'audio/x-mpeg'],
  ['.mp4', 'video/mp4'],
  ['.mpc', 'application/vnd.mpohun.certificate'],
  ['.mpe', 'video/mpeg'],
  ['.mpeg', 'video/mpeg'],
  ['.mpg', 'video/mpeg'],
  ['.mpg4', 'video/mp4'],
  ['.mpga', 'audio/mpeg'],
  ['.msg', 'application/vnd.ms-outlook'],
  ['.ogg', 'audio/ogg'],
  ['.pdf', 'application/pdf'],
  ['.png', 'image/png'],
  ['.pps', 'application/vnd.ms-powerpoint'],
  ['.ppt', 'application/vnd.ms-powerpoint'],
  ['.pptx', 'application/vnd.openxmlformats-officedocument.presentationml.presentation'],
  ['.prop', 'text/plain'],
  ['.rc', 'text/plain'],
  ['.rmvb', 'audio/x-pn-realaudio'],
  ['.rtf', 'application/rtf'],
  ['.sh', 'text/plain'],
  ['.tar', 'application/x-tar'],
  ['.tgz', 'application/x-compressed'],
  ['.txt', 'text/plain'],
  ['.wav', 'audio/x-wav'],
  ['.wma', 'audio/x-ms-wma'],
  ['.wmv', 'audio/x-ms-wmv'],
  ['.wps', 'application/vnd.ms-works'],
  ['.xml', 'text/plain'],
  ['.z', 'application/x-compress'],
  ['.zip', 'application/x-zip-compressed'],
  ['', '*/*'],
];
const openFile = (fullName: string) => {
  if (isIos()) {
    RNFetchBlob.ios.previewDocument(fullName);
  } else if (isAndriod()) {
    const type = getMIMEType(fullName);
    console.log('fullName=', fullName);
    console.log('type=', type);
    RNFetchBlob.android
      .actionViewIntent(fullName, type)
      .then(res => {
        console.log('打开成功', res);
      })
      .catch((error: any) => {
        console.log('打开失败', error);
      });
  }

  // FileOpener?.open(fullName, {
  //   // 可以添加一些选项，如文件类型等
  // });
  // const options = {
  //   message: '打开文件',
  //   title: '打开文件',
  //   url: fullName,
  //   // 其他配置...
  // };
  // Share.open(options)
  //   .then(() => console.log('文件已打开'))
  //   .catch(error => console.error('打开文件失败', error));
};

/**
 * 根据文件后缀回去MIME类型
 */
export const getMIMEType = (file: string) => {
  let type = '*/*';

  //获取后缀名前的分隔符"."在fName中的位置。
  const parts = file.split('.');

  // 文件名是数组的最后一个元素
  const fileName = parts.pop() || parts.pop(); // 处理根目录下的文件名
  if (fileName === undefined) {
    return type;
  }

  /* 获取文件的后缀名*/
  const end = `.${fileName}`.toLowerCase();

  //在MIME和文件类型的匹配表中找到对应的MIME类型。
  for (let i = 0; i < MIME_MapTable.length; i++) {
    if (end === MIME_MapTable[i][0]) {
      type = MIME_MapTable[i][1];
      break;
    }
  }
  return type;
};

export default openFile;
