PODS:
  - AMapFoundation (1.8.2)
  - AMapLocation (2.10.0):
    - AMapFoundation (>= 1.8.0)
  - boost (1.83.0)
  - Bugly (2.5.93)
  - DoubleConversion (1.1.6)
  - EXAV (13.10.6):
    - ExpoModulesCore
    - ReactCommon/turbomodule/core
  - EXBarCodeScanner (12.9.3):
    - EXImageLoader
    - ExpoModulesCore
    - ZXingObjC/OneD
    - ZXingObjC/PDF417
  - EXConstants (15.4.6):
    - ExpoModulesCore
  - EXFont (11.10.3):
    - ExpoModulesCore
  - EXImageLoader (4.6.0):
    - ExpoModulesCore
    - React-Core
  - EXLocation (16.5.5):
    - ExpoModulesCore
  - Expo (50.0.21):
    - ExpoModulesCore
  - ExpoBrightness (11.8.0):
    - ExpoModulesCore
  - ExpoCamera (14.1.3):
    - ExpoModulesCore
    - ZXingObjC/OneD
    - ZXingObjC/PDF417
  - ExpoFileSystem (16.0.9):
    - ExpoModulesCore
  - ExpoImagePicker (14.7.1):
    - ExpoModulesCore
  - ExpoKeepAwake (12.8.2):
    - ExpoModulesCore
  - ExpoModulesCore (1.11.14):
    - glog
    - RCT-Folly (= 2022.05.16.00)
    - React-Core
    - React-NativeModulesApple
    - React-RCTAppDelegate
    - ReactCommon/turbomodule/core
  - ExpoSensors (12.9.1):
    - ExpoModulesCore
  - FBLazyVector (0.73.11)
  - FBReactNativeSpec (0.73.11):
    - RCT-Folly (= 2022.05.16.00)
    - RCTRequired (= 0.73.11)
    - RCTTypeSafety (= 0.73.11)
    - React-Core (= 0.73.11)
    - React-jsi (= 0.73.11)
    - ReactCommon/turbomodule/core (= 0.73.11)
  - fmt (6.2.1)
  - glog (0.3.5)
  - hermes-engine (0.73.11):
    - hermes-engine/Pre-built (= 0.73.11)
  - hermes-engine/Pre-built (0.73.11)
  - libevent (2.1.12)
  - LookinServer (1.2.6):
    - LookinServer/Core (= 1.2.6)
  - LookinServer/Core (1.2.6)
  - RCT-Folly (2022.05.16.00):
    - boost
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - RCT-Folly/Default (= 2022.05.16.00)
  - RCT-Folly/Default (2022.05.16.00):
    - boost
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
  - RCT-Folly/Fabric (2022.05.16.00):
    - boost
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
  - RCT-Folly/Futures (2022.05.16.00):
    - boost
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - libevent
  - RCTRequired (0.73.11)
  - RCTTypeSafety (0.73.11):
    - FBLazyVector (= 0.73.11)
    - RCTRequired (= 0.73.11)
    - React-Core (= 0.73.11)
  - React (0.73.11):
    - React-Core (= 0.73.11)
    - React-Core/DevSupport (= 0.73.11)
    - React-Core/RCTWebSocket (= 0.73.11)
    - React-RCTActionSheet (= 0.73.11)
    - React-RCTAnimation (= 0.73.11)
    - React-RCTBlob (= 0.73.11)
    - React-RCTImage (= 0.73.11)
    - React-RCTLinking (= 0.73.11)
    - React-RCTNetwork (= 0.73.11)
    - React-RCTSettings (= 0.73.11)
    - React-RCTText (= 0.73.11)
    - React-RCTVibration (= 0.73.11)
  - React-callinvoker (0.73.11)
  - React-Codegen (0.73.11):
    - DoubleConversion
    - FBReactNativeSpec
    - glog
    - hermes-engine
    - RCT-Folly
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-jsi
    - React-jsiexecutor
    - React-NativeModulesApple
    - React-rncore
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
  - React-Core (0.73.11):
    - glog
    - hermes-engine
    - RCT-Folly (= 2022.05.16.00)
    - React-Core/Default (= 0.73.11)
    - React-cxxreact
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-perflogger
    - React-runtimescheduler
    - React-utils
    - SocketRocket (= 0.6.1)
    - Yoga
  - React-Core/CoreModulesHeaders (0.73.11):
    - glog
    - hermes-engine
    - RCT-Folly (= 2022.05.16.00)
    - React-Core/Default
    - React-cxxreact
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-perflogger
    - React-runtimescheduler
    - React-utils
    - SocketRocket (= 0.6.1)
    - Yoga
  - React-Core/Default (0.73.11):
    - glog
    - hermes-engine
    - RCT-Folly (= 2022.05.16.00)
    - React-cxxreact
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-perflogger
    - React-runtimescheduler
    - React-utils
    - SocketRocket (= 0.6.1)
    - Yoga
  - React-Core/DevSupport (0.73.11):
    - glog
    - hermes-engine
    - RCT-Folly (= 2022.05.16.00)
    - React-Core/Default (= 0.73.11)
    - React-Core/RCTWebSocket (= 0.73.11)
    - React-cxxreact
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-jsinspector (= 0.73.11)
    - React-perflogger
    - React-runtimescheduler
    - React-utils
    - SocketRocket (= 0.6.1)
    - Yoga
  - React-Core/RCTActionSheetHeaders (0.73.11):
    - glog
    - hermes-engine
    - RCT-Folly (= 2022.05.16.00)
    - React-Core/Default
    - React-cxxreact
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-perflogger
    - React-runtimescheduler
    - React-utils
    - SocketRocket (= 0.6.1)
    - Yoga
  - React-Core/RCTAnimationHeaders (0.73.11):
    - glog
    - hermes-engine
    - RCT-Folly (= 2022.05.16.00)
    - React-Core/Default
    - React-cxxreact
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-perflogger
    - React-runtimescheduler
    - React-utils
    - SocketRocket (= 0.6.1)
    - Yoga
  - React-Core/RCTBlobHeaders (0.73.11):
    - glog
    - hermes-engine
    - RCT-Folly (= 2022.05.16.00)
    - React-Core/Default
    - React-cxxreact
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-perflogger
    - React-runtimescheduler
    - React-utils
    - SocketRocket (= 0.6.1)
    - Yoga
  - React-Core/RCTImageHeaders (0.73.11):
    - glog
    - hermes-engine
    - RCT-Folly (= 2022.05.16.00)
    - React-Core/Default
    - React-cxxreact
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-perflogger
    - React-runtimescheduler
    - React-utils
    - SocketRocket (= 0.6.1)
    - Yoga
  - React-Core/RCTLinkingHeaders (0.73.11):
    - glog
    - hermes-engine
    - RCT-Folly (= 2022.05.16.00)
    - React-Core/Default
    - React-cxxreact
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-perflogger
    - React-runtimescheduler
    - React-utils
    - SocketRocket (= 0.6.1)
    - Yoga
  - React-Core/RCTNetworkHeaders (0.73.11):
    - glog
    - hermes-engine
    - RCT-Folly (= 2022.05.16.00)
    - React-Core/Default
    - React-cxxreact
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-perflogger
    - React-runtimescheduler
    - React-utils
    - SocketRocket (= 0.6.1)
    - Yoga
  - React-Core/RCTSettingsHeaders (0.73.11):
    - glog
    - hermes-engine
    - RCT-Folly (= 2022.05.16.00)
    - React-Core/Default
    - React-cxxreact
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-perflogger
    - React-runtimescheduler
    - React-utils
    - SocketRocket (= 0.6.1)
    - Yoga
  - React-Core/RCTTextHeaders (0.73.11):
    - glog
    - hermes-engine
    - RCT-Folly (= 2022.05.16.00)
    - React-Core/Default
    - React-cxxreact
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-perflogger
    - React-runtimescheduler
    - React-utils
    - SocketRocket (= 0.6.1)
    - Yoga
  - React-Core/RCTVibrationHeaders (0.73.11):
    - glog
    - hermes-engine
    - RCT-Folly (= 2022.05.16.00)
    - React-Core/Default
    - React-cxxreact
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-perflogger
    - React-runtimescheduler
    - React-utils
    - SocketRocket (= 0.6.1)
    - Yoga
  - React-Core/RCTWebSocket (0.73.11):
    - glog
    - hermes-engine
    - RCT-Folly (= 2022.05.16.00)
    - React-Core/Default (= 0.73.11)
    - React-cxxreact
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-perflogger
    - React-runtimescheduler
    - React-utils
    - SocketRocket (= 0.6.1)
    - Yoga
  - React-CoreModules (0.73.11):
    - RCT-Folly (= 2022.05.16.00)
    - RCTTypeSafety (= 0.73.11)
    - React-Codegen
    - React-Core/CoreModulesHeaders (= 0.73.11)
    - React-jsi (= 0.73.11)
    - React-NativeModulesApple
    - React-RCTBlob
    - React-RCTImage (= 0.73.11)
    - ReactCommon
    - SocketRocket (= 0.6.1)
  - React-cxxreact (0.73.11):
    - boost (= 1.83.0)
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - hermes-engine
    - RCT-Folly (= 2022.05.16.00)
    - React-callinvoker (= 0.73.11)
    - React-debug (= 0.73.11)
    - React-jsi (= 0.73.11)
    - React-jsinspector (= 0.73.11)
    - React-logger (= 0.73.11)
    - React-perflogger (= 0.73.11)
    - React-runtimeexecutor (= 0.73.11)
  - React-debug (0.73.11)
  - React-Fabric (0.73.11):
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2022.05.16.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-Fabric/animations (= 0.73.11)
    - React-Fabric/attributedstring (= 0.73.11)
    - React-Fabric/componentregistry (= 0.73.11)
    - React-Fabric/componentregistrynative (= 0.73.11)
    - React-Fabric/components (= 0.73.11)
    - React-Fabric/core (= 0.73.11)
    - React-Fabric/imagemanager (= 0.73.11)
    - React-Fabric/leakchecker (= 0.73.11)
    - React-Fabric/mounting (= 0.73.11)
    - React-Fabric/scheduler (= 0.73.11)
    - React-Fabric/telemetry (= 0.73.11)
    - React-Fabric/templateprocessor (= 0.73.11)
    - React-Fabric/textlayoutmanager (= 0.73.11)
    - React-Fabric/uimanager (= 0.73.11)
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/animations (0.73.11):
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2022.05.16.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/attributedstring (0.73.11):
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2022.05.16.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/componentregistry (0.73.11):
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2022.05.16.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/componentregistrynative (0.73.11):
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2022.05.16.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/components (0.73.11):
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2022.05.16.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-Fabric/components/inputaccessory (= 0.73.11)
    - React-Fabric/components/legacyviewmanagerinterop (= 0.73.11)
    - React-Fabric/components/modal (= 0.73.11)
    - React-Fabric/components/rncore (= 0.73.11)
    - React-Fabric/components/root (= 0.73.11)
    - React-Fabric/components/safeareaview (= 0.73.11)
    - React-Fabric/components/scrollview (= 0.73.11)
    - React-Fabric/components/text (= 0.73.11)
    - React-Fabric/components/textinput (= 0.73.11)
    - React-Fabric/components/unimplementedview (= 0.73.11)
    - React-Fabric/components/view (= 0.73.11)
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/components/inputaccessory (0.73.11):
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2022.05.16.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/components/legacyviewmanagerinterop (0.73.11):
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2022.05.16.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/components/modal (0.73.11):
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2022.05.16.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/components/rncore (0.73.11):
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2022.05.16.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/components/root (0.73.11):
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2022.05.16.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/components/safeareaview (0.73.11):
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2022.05.16.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/components/scrollview (0.73.11):
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2022.05.16.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/components/text (0.73.11):
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2022.05.16.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/components/textinput (0.73.11):
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2022.05.16.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/components/unimplementedview (0.73.11):
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2022.05.16.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/components/view (0.73.11):
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2022.05.16.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
    - Yoga
  - React-Fabric/core (0.73.11):
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2022.05.16.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/imagemanager (0.73.11):
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2022.05.16.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/leakchecker (0.73.11):
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2022.05.16.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/mounting (0.73.11):
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2022.05.16.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/scheduler (0.73.11):
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2022.05.16.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/telemetry (0.73.11):
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2022.05.16.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/templateprocessor (0.73.11):
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2022.05.16.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/textlayoutmanager (0.73.11):
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2022.05.16.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-Fabric/uimanager
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/uimanager (0.73.11):
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2022.05.16.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-FabricImage (0.73.11):
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2022.05.16.00)
    - RCTRequired (= 0.73.11)
    - RCTTypeSafety (= 0.73.11)
    - React-Fabric
    - React-graphics
    - React-ImageManager
    - React-jsi
    - React-jsiexecutor (= 0.73.11)
    - React-logger
    - React-rendererdebug
    - React-utils
    - ReactCommon
    - Yoga
  - React-graphics (0.73.11):
    - glog
    - RCT-Folly/Fabric (= 2022.05.16.00)
    - React-Core/Default (= 0.73.11)
    - React-utils
  - React-hermes (0.73.11):
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - hermes-engine
    - RCT-Folly (= 2022.05.16.00)
    - RCT-Folly/Futures (= 2022.05.16.00)
    - React-cxxreact (= 0.73.11)
    - React-jsi
    - React-jsiexecutor (= 0.73.11)
    - React-jsinspector (= 0.73.11)
    - React-perflogger (= 0.73.11)
  - React-ImageManager (0.73.11):
    - glog
    - RCT-Folly/Fabric
    - React-Core/Default
    - React-debug
    - React-Fabric
    - React-graphics
    - React-rendererdebug
    - React-utils
  - React-jserrorhandler (0.73.11):
    - RCT-Folly/Fabric (= 2022.05.16.00)
    - React-debug
    - React-jsi
    - React-Mapbuffer
  - React-jsi (0.73.11):
    - boost (= 1.83.0)
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - hermes-engine
    - RCT-Folly (= 2022.05.16.00)
  - React-jsiexecutor (0.73.11):
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - hermes-engine
    - RCT-Folly (= 2022.05.16.00)
    - React-cxxreact (= 0.73.11)
    - React-jsi (= 0.73.11)
    - React-perflogger (= 0.73.11)
  - React-jsinspector (0.73.11)
  - React-logger (0.73.11):
    - glog
  - React-Mapbuffer (0.73.11):
    - glog
    - React-debug
  - react-native-cameraroll (7.9.0):
    - glog
    - RCT-Folly (= 2022.05.16.00)
    - React-Core
  - react-native-document-picker (9.3.1):
    - glog
    - RCT-Folly (= 2022.05.16.00)
    - React-Core
  - react-native-geolocation (3.4.0):
    - glog
    - RCT-Folly (= 2022.05.16.00)
    - React-Core
  - react-native-image-resizer (3.0.11):
    - React-Core
  - react-native-menu (1.2.2):
    - React
  - react-native-netinfo (11.1.0):
    - React-Core
  - react-native-orientation-locker (1.7.0):
    - React-Core
  - react-native-pager-view (6.2.3):
    - glog
    - RCT-Folly (= 2022.05.16.00)
    - React-Core
  - react-native-retu-bugly (0.1.5):
    - Bugly (~> 2.5.90)
    - React-Core
  - react-native-safe-area-context (4.8.2):
    - React-Core
  - react-native-skia (1.11.8):
    - glog
    - RCT-Folly (= 2022.05.16.00)
    - React
    - React-callinvoker
    - React-Core
  - react-native-slider (4.4.2):
    - React-Core
  - react-native-splash-screen (3.3.0):
    - React-Core
  - react-native-update (10.23.1):
    - glog
    - RCT-Folly (= 2022.05.16.00)
    - React
    - React-Core
    - react-native-update/HDiffPatch (= 10.23.1)
    - react-native-update/RCTPushy (= 10.23.1)
    - SSZipArchive
  - react-native-update/HDiffPatch (10.23.1):
    - glog
    - RCT-Folly (= 2022.05.16.00)
    - React
    - React-Core
    - SSZipArchive
  - react-native-update/RCTPushy (10.23.1):
    - glog
    - RCT-Folly (= 2022.05.16.00)
    - React
    - React-Core
    - SSZipArchive
  - react-native-webview (13.6.4):
    - glog
    - RCT-Folly (= 2022.05.16.00)
    - React-Core
  - React-nativeconfig (0.73.11)
  - React-NativeModulesApple (0.73.11):
    - glog
    - hermes-engine
    - React-callinvoker
    - React-Core
    - React-cxxreact
    - React-jsi
    - React-runtimeexecutor
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
  - React-perflogger (0.73.11)
  - React-RCTActionSheet (0.73.11):
    - React-Core/RCTActionSheetHeaders (= 0.73.11)
  - React-RCTAnimation (0.73.11):
    - RCT-Folly (= 2022.05.16.00)
    - RCTTypeSafety
    - React-Codegen
    - React-Core/RCTAnimationHeaders
    - React-jsi
    - React-NativeModulesApple
    - ReactCommon
  - React-RCTAppDelegate (0.73.11):
    - RCT-Folly
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-CoreModules
    - React-hermes
    - React-nativeconfig
    - React-NativeModulesApple
    - React-RCTFabric
    - React-RCTImage
    - React-RCTNetwork
    - React-runtimescheduler
    - ReactCommon
  - React-RCTBlob (0.73.11):
    - hermes-engine
    - RCT-Folly (= 2022.05.16.00)
    - React-Codegen
    - React-Core/RCTBlobHeaders
    - React-Core/RCTWebSocket
    - React-jsi
    - React-NativeModulesApple
    - React-RCTNetwork
    - ReactCommon
  - React-RCTFabric (0.73.11):
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2022.05.16.00)
    - React-Core
    - React-debug
    - React-Fabric
    - React-FabricImage
    - React-graphics
    - React-ImageManager
    - React-jsi
    - React-nativeconfig
    - React-RCTImage
    - React-RCTText
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - Yoga
  - React-RCTImage (0.73.11):
    - RCT-Folly (= 2022.05.16.00)
    - RCTTypeSafety
    - React-Codegen
    - React-Core/RCTImageHeaders
    - React-jsi
    - React-NativeModulesApple
    - React-RCTNetwork
    - ReactCommon
  - React-RCTLinking (0.73.11):
    - React-Codegen
    - React-Core/RCTLinkingHeaders (= 0.73.11)
    - React-jsi (= 0.73.11)
    - React-NativeModulesApple
    - ReactCommon
    - ReactCommon/turbomodule/core (= 0.73.11)
  - React-RCTNetwork (0.73.11):
    - RCT-Folly (= 2022.05.16.00)
    - RCTTypeSafety
    - React-Codegen
    - React-Core/RCTNetworkHeaders
    - React-jsi
    - React-NativeModulesApple
    - ReactCommon
  - React-RCTSettings (0.73.11):
    - RCT-Folly (= 2022.05.16.00)
    - RCTTypeSafety
    - React-Codegen
    - React-Core/RCTSettingsHeaders
    - React-jsi
    - React-NativeModulesApple
    - ReactCommon
  - React-RCTText (0.73.11):
    - React-Core/RCTTextHeaders (= 0.73.11)
    - Yoga
  - React-RCTVibration (0.73.11):
    - RCT-Folly (= 2022.05.16.00)
    - React-Codegen
    - React-Core/RCTVibrationHeaders
    - React-jsi
    - React-NativeModulesApple
    - ReactCommon
  - React-rendererdebug (0.73.11):
    - DoubleConversion
    - fmt (~> 6.2.1)
    - RCT-Folly (= 2022.05.16.00)
    - React-debug
  - React-rncore (0.73.11)
  - React-runtimeexecutor (0.73.11):
    - React-jsi (= 0.73.11)
  - React-runtimescheduler (0.73.11):
    - glog
    - hermes-engine
    - RCT-Folly (= 2022.05.16.00)
    - React-callinvoker
    - React-cxxreact
    - React-debug
    - React-jsi
    - React-rendererdebug
    - React-runtimeexecutor
    - React-utils
  - React-utils (0.73.11):
    - glog
    - RCT-Folly (= 2022.05.16.00)
    - React-debug
  - ReactCommon (0.73.11):
    - React-logger (= 0.73.11)
    - ReactCommon/turbomodule (= 0.73.11)
  - ReactCommon/turbomodule (0.73.11):
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - hermes-engine
    - RCT-Folly (= 2022.05.16.00)
    - React-callinvoker (= 0.73.11)
    - React-cxxreact (= 0.73.11)
    - React-jsi (= 0.73.11)
    - React-logger (= 0.73.11)
    - React-perflogger (= 0.73.11)
    - ReactCommon/turbomodule/bridging (= 0.73.11)
    - ReactCommon/turbomodule/core (= 0.73.11)
  - ReactCommon/turbomodule/bridging (0.73.11):
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - hermes-engine
    - RCT-Folly (= 2022.05.16.00)
    - React-callinvoker (= 0.73.11)
    - React-cxxreact (= 0.73.11)
    - React-jsi (= 0.73.11)
    - React-logger (= 0.73.11)
    - React-perflogger (= 0.73.11)
  - ReactCommon/turbomodule/core (0.73.11):
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - hermes-engine
    - RCT-Folly (= 2022.05.16.00)
    - React-callinvoker (= 0.73.11)
    - React-cxxreact (= 0.73.11)
    - React-jsi (= 0.73.11)
    - React-logger (= 0.73.11)
    - React-perflogger (= 0.73.11)
  - rn-fetch-blob (0.12.0):
    - React-Core
  - rn-safe-keyboard (0.0.1):
    - glog
    - RCT-Folly (= 2022.05.16.00)
    - React-Core
  - RNCAsyncStorage (1.21.0):
    - React-Core
  - RNCClipboard (1.16.1):
    - React-Core
  - RNCMaskedView (0.3.2):
    - glog
    - RCT-Folly (= 2022.05.16.00)
    - React-Core
  - RNCPicker (2.6.1):
    - React-Core
  - RNDeviceInfo (14.0.4):
    - React-Core
  - RNFileViewer (2.1.5):
    - React-Core
  - RNFS (2.20.0):
    - React-Core
  - RNGestureHandler (2.14.1):
    - glog
    - RCT-Folly (= 2022.05.16.00)
    - React-Core
  - RNLinearGradient (3.0.0-alpha.1):
    - glog
    - RCT-Folly (= 2022.05.16.00)
    - React-Core
  - RNPDF (0.14.0):
    - React
  - RNPermissions (4.1.5):
    - React-Core
  - RNScreens (3.29.0):
    - glog
    - RCT-Folly (= 2022.05.16.00)
    - React-Core
  - RNSVG (14.1.0):
    - React-Core
  - SocketRocket (0.6.1)
  - SSZipArchive (2.2.3)
  - Yoga (1.14.0)
  - ZXingObjC/Core (3.6.9)
  - ZXingObjC/OneD (3.6.9):
    - ZXingObjC/Core
  - ZXingObjC/PDF417 (3.6.9):
    - ZXingObjC/Core

DEPENDENCIES:
  - AMapLocation
  - boost (from `../node_modules/react-native/third-party-podspecs/boost.podspec`)
  - DoubleConversion (from `../node_modules/react-native/third-party-podspecs/DoubleConversion.podspec`)
  - EXAV (from `../node_modules/expo-av/ios`)
  - EXBarCodeScanner (from `../node_modules/expo-barcode-scanner/ios`)
  - EXConstants (from `../node_modules/expo-constants/ios`)
  - EXFont (from `../node_modules/expo-font/ios`)
  - EXImageLoader (from `../node_modules/expo-image-loader/ios`)
  - EXLocation (from `../node_modules/expo-location/ios`)
  - Expo (from `../node_modules/expo`)
  - ExpoBrightness (from `../node_modules/expo-brightness/ios`)
  - ExpoCamera (from `../node_modules/expo-camera/ios`)
  - ExpoFileSystem (from `../node_modules/expo-file-system/ios`)
  - ExpoImagePicker (from `../node_modules/expo-image-picker/ios`)
  - ExpoKeepAwake (from `../node_modules/expo-keep-awake/ios`)
  - ExpoModulesCore (from `../node_modules/expo-modules-core`)
  - ExpoSensors (from `../node_modules/expo-sensors/ios`)
  - FBLazyVector (from `../node_modules/react-native/Libraries/FBLazyVector`)
  - FBReactNativeSpec (from `../node_modules/react-native/React/FBReactNativeSpec`)
  - glog (from `../node_modules/react-native/third-party-podspecs/glog.podspec`)
  - hermes-engine (from `../node_modules/react-native/sdks/hermes-engine/hermes-engine.podspec`)
  - libevent (~> 2.1.12)
  - LookinServer
  - RCT-Folly (from `../node_modules/react-native/third-party-podspecs/RCT-Folly.podspec`)
  - RCT-Folly/Fabric (from `../node_modules/react-native/third-party-podspecs/RCT-Folly.podspec`)
  - RCTRequired (from `../node_modules/react-native/Libraries/RCTRequired`)
  - RCTTypeSafety (from `../node_modules/react-native/Libraries/TypeSafety`)
  - React (from `../node_modules/react-native/`)
  - React-callinvoker (from `../node_modules/react-native/ReactCommon/callinvoker`)
  - React-Codegen (from `build/generated/ios`)
  - React-Core (from `../node_modules/react-native/`)
  - React-Core/RCTWebSocket (from `../node_modules/react-native/`)
  - React-CoreModules (from `../node_modules/react-native/React/CoreModules`)
  - React-cxxreact (from `../node_modules/react-native/ReactCommon/cxxreact`)
  - React-debug (from `../node_modules/react-native/ReactCommon/react/debug`)
  - React-Fabric (from `../node_modules/react-native/ReactCommon`)
  - React-FabricImage (from `../node_modules/react-native/ReactCommon`)
  - React-graphics (from `../node_modules/react-native/ReactCommon/react/renderer/graphics`)
  - React-hermes (from `../node_modules/react-native/ReactCommon/hermes`)
  - React-ImageManager (from `../node_modules/react-native/ReactCommon/react/renderer/imagemanager/platform/ios`)
  - React-jserrorhandler (from `../node_modules/react-native/ReactCommon/jserrorhandler`)
  - React-jsi (from `../node_modules/react-native/ReactCommon/jsi`)
  - React-jsiexecutor (from `../node_modules/react-native/ReactCommon/jsiexecutor`)
  - React-jsinspector (from `../node_modules/react-native/ReactCommon/jsinspector-modern`)
  - React-logger (from `../node_modules/react-native/ReactCommon/logger`)
  - React-Mapbuffer (from `../node_modules/react-native/ReactCommon`)
  - "react-native-cameraroll (from `../node_modules/@react-native-camera-roll/camera-roll`)"
  - react-native-document-picker (from `../node_modules/react-native-document-picker`)
  - "react-native-geolocation (from `../node_modules/@react-native-community/geolocation`)"
  - "react-native-image-resizer (from `../node_modules/@bam.tech/react-native-image-resizer`)"
  - "react-native-menu (from `../node_modules/@react-native-menu/menu`)"
  - "react-native-netinfo (from `../node_modules/@react-native-community/netinfo`)"
  - react-native-orientation-locker (from `../node_modules/react-native-orientation-locker`)
  - react-native-pager-view (from `../node_modules/react-native-pager-view`)
  - "react-native-retu-bugly (from `../node_modules/@react-native-retu/bugly`)"
  - react-native-safe-area-context (from `../node_modules/react-native-safe-area-context`)
  - "react-native-skia (from `../node_modules/@shopify/react-native-skia`)"
  - "react-native-slider (from `../node_modules/@react-native-community/slider`)"
  - react-native-splash-screen (from `../node_modules/react-native-splash-screen`)
  - react-native-update (from `../node_modules/react-native-update`)
  - react-native-webview (from `../node_modules/react-native-webview`)
  - React-nativeconfig (from `../node_modules/react-native/ReactCommon`)
  - React-NativeModulesApple (from `../node_modules/react-native/ReactCommon/react/nativemodule/core/platform/ios`)
  - React-perflogger (from `../node_modules/react-native/ReactCommon/reactperflogger`)
  - React-RCTActionSheet (from `../node_modules/react-native/Libraries/ActionSheetIOS`)
  - React-RCTAnimation (from `../node_modules/react-native/Libraries/NativeAnimation`)
  - React-RCTAppDelegate (from `../node_modules/react-native/Libraries/AppDelegate`)
  - React-RCTBlob (from `../node_modules/react-native/Libraries/Blob`)
  - React-RCTFabric (from `../node_modules/react-native/React`)
  - React-RCTImage (from `../node_modules/react-native/Libraries/Image`)
  - React-RCTLinking (from `../node_modules/react-native/Libraries/LinkingIOS`)
  - React-RCTNetwork (from `../node_modules/react-native/Libraries/Network`)
  - React-RCTSettings (from `../node_modules/react-native/Libraries/Settings`)
  - React-RCTText (from `../node_modules/react-native/Libraries/Text`)
  - React-RCTVibration (from `../node_modules/react-native/Libraries/Vibration`)
  - React-rendererdebug (from `../node_modules/react-native/ReactCommon/react/renderer/debug`)
  - React-rncore (from `../node_modules/react-native/ReactCommon`)
  - React-runtimeexecutor (from `../node_modules/react-native/ReactCommon/runtimeexecutor`)
  - React-runtimescheduler (from `../node_modules/react-native/ReactCommon/react/renderer/runtimescheduler`)
  - React-utils (from `../node_modules/react-native/ReactCommon/react/utils`)
  - ReactCommon/turbomodule/core (from `../node_modules/react-native/ReactCommon`)
  - rn-fetch-blob (from `../node_modules/rn-fetch-blob`)
  - "rn-safe-keyboard (from `../node_modules/@hncy58/rn-safe-keyboard`)"
  - "RNCAsyncStorage (from `../node_modules/@react-native-async-storage/async-storage`)"
  - "RNCClipboard (from `../node_modules/@react-native-clipboard/clipboard`)"
  - "RNCMaskedView (from `../node_modules/@react-native-masked-view/masked-view`)"
  - "RNCPicker (from `../node_modules/@react-native-picker/picker`)"
  - RNDeviceInfo (from `../node_modules/react-native-device-info`)
  - RNFileViewer (from `../node_modules/react-native-file-viewer`)
  - RNFS (from `../node_modules/react-native-fs`)
  - RNGestureHandler (from `../node_modules/react-native-gesture-handler`)
  - RNLinearGradient (from `../node_modules/react-native-linear-gradient`)
  - RNPDF (from `../node_modules/react-native-view-pdf`)
  - RNPermissions (from `../node_modules/react-native-permissions`)
  - RNScreens (from `../node_modules/react-native-screens`)
  - RNSVG (from `../node_modules/react-native-svg`)
  - Yoga (from `../node_modules/react-native/ReactCommon/yoga`)

SPEC REPOS:
  trunk:
    - AMapFoundation
    - AMapLocation
    - Bugly
    - fmt
    - libevent
    - LookinServer
    - SocketRocket
    - SSZipArchive
    - ZXingObjC

EXTERNAL SOURCES:
  boost:
    :podspec: "../node_modules/react-native/third-party-podspecs/boost.podspec"
  DoubleConversion:
    :podspec: "../node_modules/react-native/third-party-podspecs/DoubleConversion.podspec"
  EXAV:
    :path: "../node_modules/expo-av/ios"
  EXBarCodeScanner:
    :path: "../node_modules/expo-barcode-scanner/ios"
  EXConstants:
    :path: "../node_modules/expo-constants/ios"
  EXFont:
    :path: "../node_modules/expo-font/ios"
  EXImageLoader:
    :path: "../node_modules/expo-image-loader/ios"
  EXLocation:
    :path: "../node_modules/expo-location/ios"
  Expo:
    :path: "../node_modules/expo"
  ExpoBrightness:
    :path: "../node_modules/expo-brightness/ios"
  ExpoCamera:
    :path: "../node_modules/expo-camera/ios"
  ExpoFileSystem:
    :path: "../node_modules/expo-file-system/ios"
  ExpoImagePicker:
    :path: "../node_modules/expo-image-picker/ios"
  ExpoKeepAwake:
    :path: "../node_modules/expo-keep-awake/ios"
  ExpoModulesCore:
    :path: "../node_modules/expo-modules-core"
  ExpoSensors:
    :path: "../node_modules/expo-sensors/ios"
  FBLazyVector:
    :path: "../node_modules/react-native/Libraries/FBLazyVector"
  FBReactNativeSpec:
    :path: "../node_modules/react-native/React/FBReactNativeSpec"
  glog:
    :podspec: "../node_modules/react-native/third-party-podspecs/glog.podspec"
  hermes-engine:
    :podspec: "../node_modules/react-native/sdks/hermes-engine/hermes-engine.podspec"
    :tag: hermes-2024-04-29-RNv0.73.8-644c8be78af1eae7c138fa4093fb87f0f4f8db85
  RCT-Folly:
    :podspec: "../node_modules/react-native/third-party-podspecs/RCT-Folly.podspec"
  RCTRequired:
    :path: "../node_modules/react-native/Libraries/RCTRequired"
  RCTTypeSafety:
    :path: "../node_modules/react-native/Libraries/TypeSafety"
  React:
    :path: "../node_modules/react-native/"
  React-callinvoker:
    :path: "../node_modules/react-native/ReactCommon/callinvoker"
  React-Codegen:
    :path: build/generated/ios
  React-Core:
    :path: "../node_modules/react-native/"
  React-CoreModules:
    :path: "../node_modules/react-native/React/CoreModules"
  React-cxxreact:
    :path: "../node_modules/react-native/ReactCommon/cxxreact"
  React-debug:
    :path: "../node_modules/react-native/ReactCommon/react/debug"
  React-Fabric:
    :path: "../node_modules/react-native/ReactCommon"
  React-FabricImage:
    :path: "../node_modules/react-native/ReactCommon"
  React-graphics:
    :path: "../node_modules/react-native/ReactCommon/react/renderer/graphics"
  React-hermes:
    :path: "../node_modules/react-native/ReactCommon/hermes"
  React-ImageManager:
    :path: "../node_modules/react-native/ReactCommon/react/renderer/imagemanager/platform/ios"
  React-jserrorhandler:
    :path: "../node_modules/react-native/ReactCommon/jserrorhandler"
  React-jsi:
    :path: "../node_modules/react-native/ReactCommon/jsi"
  React-jsiexecutor:
    :path: "../node_modules/react-native/ReactCommon/jsiexecutor"
  React-jsinspector:
    :path: "../node_modules/react-native/ReactCommon/jsinspector-modern"
  React-logger:
    :path: "../node_modules/react-native/ReactCommon/logger"
  React-Mapbuffer:
    :path: "../node_modules/react-native/ReactCommon"
  react-native-cameraroll:
    :path: "../node_modules/@react-native-camera-roll/camera-roll"
  react-native-document-picker:
    :path: "../node_modules/react-native-document-picker"
  react-native-geolocation:
    :path: "../node_modules/@react-native-community/geolocation"
  react-native-image-resizer:
    :path: "../node_modules/@bam.tech/react-native-image-resizer"
  react-native-menu:
    :path: "../node_modules/@react-native-menu/menu"
  react-native-netinfo:
    :path: "../node_modules/@react-native-community/netinfo"
  react-native-orientation-locker:
    :path: "../node_modules/react-native-orientation-locker"
  react-native-pager-view:
    :path: "../node_modules/react-native-pager-view"
  react-native-retu-bugly:
    :path: "../node_modules/@react-native-retu/bugly"
  react-native-safe-area-context:
    :path: "../node_modules/react-native-safe-area-context"
  react-native-skia:
    :path: "../node_modules/@shopify/react-native-skia"
  react-native-slider:
    :path: "../node_modules/@react-native-community/slider"
  react-native-splash-screen:
    :path: "../node_modules/react-native-splash-screen"
  react-native-update:
    :path: "../node_modules/react-native-update"
  react-native-webview:
    :path: "../node_modules/react-native-webview"
  React-nativeconfig:
    :path: "../node_modules/react-native/ReactCommon"
  React-NativeModulesApple:
    :path: "../node_modules/react-native/ReactCommon/react/nativemodule/core/platform/ios"
  React-perflogger:
    :path: "../node_modules/react-native/ReactCommon/reactperflogger"
  React-RCTActionSheet:
    :path: "../node_modules/react-native/Libraries/ActionSheetIOS"
  React-RCTAnimation:
    :path: "../node_modules/react-native/Libraries/NativeAnimation"
  React-RCTAppDelegate:
    :path: "../node_modules/react-native/Libraries/AppDelegate"
  React-RCTBlob:
    :path: "../node_modules/react-native/Libraries/Blob"
  React-RCTFabric:
    :path: "../node_modules/react-native/React"
  React-RCTImage:
    :path: "../node_modules/react-native/Libraries/Image"
  React-RCTLinking:
    :path: "../node_modules/react-native/Libraries/LinkingIOS"
  React-RCTNetwork:
    :path: "../node_modules/react-native/Libraries/Network"
  React-RCTSettings:
    :path: "../node_modules/react-native/Libraries/Settings"
  React-RCTText:
    :path: "../node_modules/react-native/Libraries/Text"
  React-RCTVibration:
    :path: "../node_modules/react-native/Libraries/Vibration"
  React-rendererdebug:
    :path: "../node_modules/react-native/ReactCommon/react/renderer/debug"
  React-rncore:
    :path: "../node_modules/react-native/ReactCommon"
  React-runtimeexecutor:
    :path: "../node_modules/react-native/ReactCommon/runtimeexecutor"
  React-runtimescheduler:
    :path: "../node_modules/react-native/ReactCommon/react/renderer/runtimescheduler"
  React-utils:
    :path: "../node_modules/react-native/ReactCommon/react/utils"
  ReactCommon:
    :path: "../node_modules/react-native/ReactCommon"
  rn-fetch-blob:
    :path: "../node_modules/rn-fetch-blob"
  rn-safe-keyboard:
    :path: "../node_modules/@hncy58/rn-safe-keyboard"
  RNCAsyncStorage:
    :path: "../node_modules/@react-native-async-storage/async-storage"
  RNCClipboard:
    :path: "../node_modules/@react-native-clipboard/clipboard"
  RNCMaskedView:
    :path: "../node_modules/@react-native-masked-view/masked-view"
  RNCPicker:
    :path: "../node_modules/@react-native-picker/picker"
  RNDeviceInfo:
    :path: "../node_modules/react-native-device-info"
  RNFileViewer:
    :path: "../node_modules/react-native-file-viewer"
  RNFS:
    :path: "../node_modules/react-native-fs"
  RNGestureHandler:
    :path: "../node_modules/react-native-gesture-handler"
  RNLinearGradient:
    :path: "../node_modules/react-native-linear-gradient"
  RNPDF:
    :path: "../node_modules/react-native-view-pdf"
  RNPermissions:
    :path: "../node_modules/react-native-permissions"
  RNScreens:
    :path: "../node_modules/react-native-screens"
  RNSVG:
    :path: "../node_modules/react-native-svg"
  Yoga:
    :path: "../node_modules/react-native/ReactCommon/yoga"

SPEC CHECKSUMS:
  AMapFoundation: 9885c48fc3a78fdfb84a0299a2293e56ea3c9fec
  AMapLocation: 5248aec2455ebb5d104b367813c946430a2ee033
  boost: d3f49c53809116a5d38da093a8aa78bf551aed09
  Bugly: b8715e6ec4004b7f7fbffab0643ba80545aee3da
  DoubleConversion: fea03f2699887d960129cc54bba7e52542b6f953
  EXAV: e4f6137431ddc4cb025895046bfefa9612025c35
  EXBarCodeScanner: d59fd943cebee3f913ebf4ffde0d05d344da8b78
  EXConstants: a5f6276e565d98f9eb4280f81241fc342d641590
  EXFont: f20669cb266ef48b004f1eb1f2b20db96cd1df9f
  EXImageLoader: 55080616b2fe9da19ef8c7f706afd9814e279b6b
  EXLocation: a631bd8dc4e7db986fb27edb7fc9ea8174ebe249
  Expo: 7b9976a9b2be116a701b233d6655b229a3c9316e
  ExpoBrightness: 6d160de5877289d11674aea61aacdb37422774cf
  ExpoCamera: f3f157db67e5a491e1104a2c0018c240242936e7
  ExpoFileSystem: 74cc0fae916f9f044248433971dcfc8c3befd057
  ExpoImagePicker: 66970181d1c838f444e5e1f81b804ab2d5ff49bd
  ExpoKeepAwake: 0f5cad99603a3268e50af9a6eb8b76d0d9ac956c
  ExpoModulesCore: 421e9c6bcb63baf153a5ec22b2b4319addfe331c
  ExpoSensors: a88c7bed8178a38e5cd3c2759baf348781027f28
  FBLazyVector: b46891061bfe0a9b07f601813114c8653a72a45c
  FBReactNativeSpec: 9a01850c21d81027fa7b20b9dcc25d9bfae083da
  fmt: ff9d55029c625d3757ed641535fd4a75fedc7ce9
  glog: c5d68082e772fa1c511173d6b30a9de2c05a69a2
  hermes-engine: d992945b77c506e5164e6a9a77510c9d57472c59
  libevent: 4049cae6c81cdb3654a443be001fb9bdceff7913
  LookinServer: de929b55f8fa2e241c18c39af3f900eaa9166389
  RCT-Folly: 7169b2b1c44399c76a47b5deaaba715eeeb476c0
  RCTRequired: 415e56f7c33799a6483e41e4dce607f3daf1e69b
  RCTTypeSafety: e984a88e713281c2d8c2309a1a6d2775af0107ae
  React: ab885684e73c5f659bad63446a977312fd3d1ecb
  React-callinvoker: 50a2d1ce3594637c700401ba306373321231eb71
  React-Codegen: 0b62f5a15aac03c4e04e7d62ebee702071d93660
  React-Core: 9d66a8a953d975aee4989abccf4602e7ba7c65fa
  React-CoreModules: e93a24aaae933d496329112cec78b681c76cdc53
  React-cxxreact: 8f6abe06e11f79f2292c7939dc6390027e53e5ba
  React-debug: cbc88cbcffdca42184a32d073ceb7d9b11122b8d
  React-Fabric: f22d9c367ae9536650d06d7c338383abf41daa73
  React-FabricImage: d5b397555e58147bfbcd24c9d2cd10e566ea29ee
  React-graphics: eb385065f994ca67550ae69df58dcbc27fdf1b07
  React-hermes: c2877efac91c02266c66cd62ccef3fa7c36d8604
  React-ImageManager: 99ffa733ce3406463da89bf74fd55a12c5aeb053
  React-jserrorhandler: 5a90e88499755b6cfe263c01c208ac3782f535ad
  React-jsi: 5da729c3787b5d58b8612fcd4308290e88af9dde
  React-jsiexecutor: 911f565c4dcb2faf750e274a18012c88355fc33c
  React-jsinspector: a98428936fb888cc15d857226a26d9ac0a668a0e
  React-logger: 6c1170f7bc315878ef4bd3b918e09130cf632798
  React-Mapbuffer: 41c166b84fc479afc78097cd51404109ec9edf68
  react-native-cameraroll: 521900f7b6ef2615c061fb30baa6d1f86e185090
  react-native-document-picker: 23b6a82e09199f4f109376e73d88892a8931eeea
  react-native-geolocation: 167798574694849024cb093222bd158e84ac8d21
  react-native-image-resizer: 6260ba497fb8d1a593c1c92ccd593f570df6f5b7
  react-native-menu: 27f634374481525b27e2123611aa4c6477324f92
  react-native-netinfo: 3aa5637c18834966e0c932de8ae1ae56fea20a97
  react-native-orientation-locker: 5819fd23ca89cbac0d736fb4314745f62716d517
  react-native-pager-view: d5f3adb58a4e6e0d200055e9a4afdcda9b9022ce
  react-native-retu-bugly: 73c66b5f1a61840d32962e2ac7b7dc4fedacea39
  react-native-safe-area-context: 0ee144a6170530ccc37a0fd9388e28d06f516a89
  react-native-skia: a7d78870856903a5b6645b5356089780293bdc9e
  react-native-slider: 33b8d190b59d4f67a541061bb91775d53d617d9d
  react-native-splash-screen: 4312f786b13a81b5169ef346d76d33bc0c6dc457
  react-native-update: 716bb60d2c3971352e21df32c2cb206510d015e0
  react-native-webview: ff06d1fe175ff827882ec88b696a7efcafa7e98d
  React-nativeconfig: 8fd29a35a3e4e8c37682d976667663d834ba6165
  React-NativeModulesApple: 68eb729eaf628ba066bca5308dd4ccacaaacba97
  React-perflogger: 3887a05940ccd34a83457fd153fdeda509b31737
  React-RCTActionSheet: 2f42b4797374b53e93b65c79eaa8a0d292e255ac
  React-RCTAnimation: 8d855a38975b065bab2528ccf158734044bcec59
  React-RCTAppDelegate: bddf79fb86d03810269d1af5d6ad06a654479a16
  React-RCTBlob: f4bad11cecd4ed488dcbbda1581ff8fe92746107
  React-RCTFabric: 05fd71882ffd9733433ad4816d899f397ffd496c
  React-RCTImage: 187d39d7f82dbddaee1c00fe5beb3fb4cd16eee2
  React-RCTLinking: 22568a7c6d2ee51ef4d5c36e9e4d9720f74e9aed
  React-RCTNetwork: 6453b643f665273e31fdfe21cfa5a32666d61685
  React-RCTSettings: 237e7aa727543b5df2e0eecbfc49fc1bf21482b5
  React-RCTText: 9a775859da5b6a1a2e4bebcd358909a77072f7c8
  React-RCTVibration: 0167279571af233088b53ca3c67606b2a355a0f2
  React-rendererdebug: 4023a996275f40116d7c88ae5be0b5882ca3eeb7
  React-rncore: 0dd62c0c8f8215747a9b49035410fde76a18876e
  React-runtimeexecutor: 2fd27b921f664e66d903ee274dcda55cc0b2cb2e
  React-runtimescheduler: a36193fd33a0187852d54d402e170510f1300952
  React-utils: eaaed6083fccd050a594baf1d72db5f949a3b9bb
  ReactCommon: 1a93f1ef5c6e33bf14b9563cdb8e7621a893aa37
  rn-fetch-blob: f065bb7ab7fb48dd002629f8bdcb0336602d3cba
  rn-safe-keyboard: b8e3f8ac7df57a78f3bf47f19308dc16a62aadcc
  RNCAsyncStorage: 618d03a5f52fbccb3d7010076bc54712844c18ef
  RNCClipboard: d7a8903d32692b0a1e71738c3825465a9acae10e
  RNCMaskedView: c8e1f01fc0f41ddf4e16246aef1143b3311588ca
  RNCPicker: b18aaf30df596e9b1738e7c1f9ee55402a229dca
  RNDeviceInfo: feea80a690d2bde1fe51461cf548039258bd03f2
  RNFileViewer: ce7ca3ac370e18554d35d6355cffd7c30437c592
  RNFS: 4ac0f0ea233904cb798630b3c077808c06931688
  RNGestureHandler: 15c6ef51acba34c49ff03003806cf5dd6098f383
  RNLinearGradient: ccaaebce083b54180da61d992e7fa56abfe000d6
  RNPDF: 8526c7210ff375c29d2461c51945e2f5480f7b60
  RNPermissions: 4871b0326d91bd8d82201f8ffde2115d99394747
  RNScreens: 17e2f657f1b09a71ec3c821368a04acbb7ebcb46
  RNSVG: ba3e7232f45e34b7b47e74472386cf4e1a676d0a
  SocketRocket: f32cd54efbe0f095c4d7594881e52619cfe80b17
  SSZipArchive: 62d4947b08730e4cda640473b0066d209ff033c9
  Yoga: a813a6d82538ccba91c54406404e3ed80b48a692
  ZXingObjC: 8898711ab495761b2dbbdec76d90164a6d7e14c5

PODFILE CHECKSUM: d547794b4c7633524297f374e036672e974f8700

COCOAPODS: 1.15.2
