import React, {Fragment, useCallback, useMemo, useState} from 'react';
import {Image, Text, View} from '@tarojs/components';
import {IPickerItem} from 'src/types/common-types';
import CellItem, {CellProps} from '~components/CellItem';
import arrowIcon from '~images/icon/<EMAIL>';
import {getLabelByValue} from '~utils/common';
import PickerPopup from './PickerPopup';
import RegionPickerPopup from './RegionPickerPopup';
import styles from './index.module.scss';

export enum PickerMode {
  Region = 'region',
  Selector = 'selector',
}

type RightPickerProps = {
  placeholder?: string;
  range?: IPickerItem[];
  value?: any;
  onChange?: (value: any, label: string, options: any[]) => void;
  mode?: PickerMode;
  pickValue?: any;
  [key: string]: any;
};

type CellPickerItemProps = Omit<CellProps, 'value'> & RightPickerProps;

const CellPickerItem: React.FC<CellPickerItemProps> = props => {
  const {
    range = [],
    mode = PickerMode.Selector,
    value,
    onChange,
    placeholder = '请选择',
    pickValue,
    ...otherProps
  } = props;

  const [openPicker, setOpenPicker] = useState(false);
  const [selectedValue, setSelectedValue] = useState('');

  const handleClick = () => {
    setOpenPicker(true);
  };

  const handleChange = useCallback(
    (values: string[], options: any[]) => {
      let label = '';

      switch (mode) {
        case PickerMode.Selector:
          const selectLabel = getLabelByValue(range, values[0]);
          label = selectLabel;
          setSelectedValue(selectLabel);
          onChange?.(values[0], label, options);
          break;

        case PickerMode.Region:
          if (Array.isArray(values)) {
            const province = options?.[0]?.label;
            const city = options?.[1]?.label;
            const county = options?.[2]?.label;
            label = [province, city, county].filter(Boolean).join('/');
            setSelectedValue(label);
            onChange?.(values, label, options);
          }
          break;

        default:
          break;
      }
    },
    [mode, range, onChange],
  );

  const displayValue = () => selectedValue || value || placeholder;
  const isPlaceholder = () => !selectedValue && !value;

  const renderRight = useCallback(
    () => (
      <View className={styles.rightPicker}>
        <Text className={isPlaceholder() ? styles.rightPickerPlaceholder : ''}>{displayValue()}</Text>
        <Image src={arrowIcon} className={styles.rightPickerArrow} />
      </View>
    ),
    [displayValue, isPlaceholder],
  );

  const renderPopup = useCallback(() => {
    switch (mode) {
      case PickerMode.Selector:
        return range.length > 0 ? (
          <PickerPopup
            range={range}
            openPicker={openPicker}
            setOpenPicker={setOpenPicker}
            onChange={handleChange}
            value={pickValue}
          />
        ) : null;

      case PickerMode.Region:
        return (
          <RegionPickerPopup
            openPicker={openPicker}
            setOpenPicker={setOpenPicker}
            onChange={handleChange}
            value={pickValue}
          />
        );

      default:
        return null;
    }
  }, [mode, range, openPicker, setOpenPicker, handleChange]);

  return (
    <Fragment>
      <CellItem {...otherProps} right={renderRight()} onClick={handleClick} />
      {renderPopup()}
    </Fragment>
  );
};

export default React.memo(CellPickerItem);
