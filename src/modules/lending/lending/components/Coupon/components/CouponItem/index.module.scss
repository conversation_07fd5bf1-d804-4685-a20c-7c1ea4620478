.coupon_item_content {
  margin-bottom: 26px;
}
.coupon_item {
  position: relative;
  display: flex;
  overflow: hidden;
  &.item_shadow {box-shadow: 0 4px 8px 0 rgb(0, 0, 0, 0.1)};
  .coupon_item_left {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: flex-end;
    width: 224px;
    height: 200px;
    background: linear-gradient(25deg, #2F54EB 0%, #85A5FF 99%);
    border-radius: 8px 0px 0px 8px;
    padding-bottom: 16px;
    .coupon_item_discount {
      color: #fff;
      font-size: 32px;
      line-height: 1;
      .coupon_item_discount_text {
        font-size: 80px;
      }
    }
    .coupon_item_type {
      display: inline-flex;
      color: #fff;
      font-size: 28px;
      padding: 10px 15px;
      margin-top: 22px;
      border-radius: 8px;
      background-color:#2F54EB;
      border: 1px solid #fff;
      line-height: 1;
    }
  }
  .coupon_item_right {
    flex: 1;
    padding-top: 40px;
    padding-left: 32px;
    background-color: #fff;
    color: #333;
    line-height: 1;
    .coupon_item_discountTitle {
      font-size: 32px;
      margin-bottom: 22px;
      font-weight: bold;
    }
    .coupon_item_date {
      font-size: 24px;
      margin-bottom: 42px;
    }
    .coupon_item_detail {
      display: flex;
      align-items: center;
      font-size: 24px;
      color: #999;
      .coupon_item_arrow {
        width: 20px;
        height: 24px;
        margin-left: 24px;
        transition: transform 0.2s ease;
        &.arror_transform {
          transform: rotate(180deg);
        }
      }
    }
  }
  .coupon_item_check {
    position: absolute;
    top: 50%;
    right: 32px;
    width: 32px;
    height: 32px;
    margin-top: -16px;
  }
  &::after,
  &::before {
    content: '';
    width: 30px;
    height: 30px;
    border-radius: 50%;
    background-color: #f6f6f6;
    position: absolute;
    left: 204px;
    z-index: 9;
  }
  &::after {
    top: -20px;
  }
  &::before {
    bottom: -20px;
  }
}
.coupon_detail {
  background-color: #fff;
  border-radius: 8px;
  max-height: 0;
  overflow: hidden;
  transition: max-height 0.2s ease-in-out;
}
.coupon_detail.expanded {
  max-height: 600px;
}
.coupon_detail_content {
  color: #999;
  font-size: 24px;
  margin-top: -20px;
  padding: 42px 28px 30px 25px;
}