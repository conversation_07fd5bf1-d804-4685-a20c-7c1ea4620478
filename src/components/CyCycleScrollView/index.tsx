import {FC, useEffect} from 'react';
import {CommonEventFunction, Image, Swiper, SwiperItem, SwiperProps, View} from '@tarojs/components';
import CyImage from '../CyImage';
import './index.scss';

type Props = {
  paths: string[];
  interval?: number;
  onChange?: (current: number) => void;
};

/**
 * 组件名称：轮播控件
 * 组件描述：轮播图
 */
const CyCycleScrollView: FC<Props> = ({paths, interval = 3000, onChange}) => {
  const change: CommonEventFunction<SwiperProps.onChangeEventDetail> = e => {
    onChange && onChange(e.detail.current);
  };
  return (
    <View className='cycle-scrollView'>
      {paths.length > 0 ? (
        <Swiper
          className='comp-cy-cycle-scroll-view'
          indicatorColor='#999'
          indicatorActiveColor='#333'
          interval={interval}
          vertical={false}
          circular
          indicatorDots
          onChange={change}
          autoplay>
          {paths.map((item, index) => (
            <SwiperItem key={index} className='item'>
              <Image className='image' src={item} mode='scaleToFill'></Image>
            </SwiperItem>
          ))}
        </Swiper>
      ) : (
        <View className='comp-cy-cycle-scroll-view'></View>
      )}
    </View>
  );
};

export default CyCycleScrollView;
