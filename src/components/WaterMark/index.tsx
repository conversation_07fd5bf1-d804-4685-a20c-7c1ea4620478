import React, {ReactNode, useMemo} from 'react';
import Taro from '@tarojs/taro';

let width = Taro.getWindowInfo().screenWidth;
let height = Taro.getWindowInfo().screenHeight;
type WaterMarkProp = {
  canvasWid: number; //水印画布的宽度
  canvasHei: number; //水印画布的高度
  txtSpace: number; //水印文字之间的行间距
  txtLines: number; //水印的列数
  txtOriginY: number; //文字距离画布上边界的距离
  txtFont: number; //水印字体大小
  txtColor: string; //水印字体颜色
  text: string; //水印文字内容
};

const _defaultProps: WaterMarkProp = {
  canvasWid: width, //水印画布的宽度
  canvasHei: height, //水印画布的高度
  txtSpace: 100, //水印文字之间的行间距
  txtLines: 3, //水印的列数
  txtOriginY: 60, //文字距离画布上边界的距离
  txtFont: 12, //水印字体大小
  txtColor: 'rgba(174,174,174,0.4)', //水印字体颜色
  text: '', //水印文字内容
};
const SvgTextBg = (props: Partial<WaterMarkProp>) => {
  const p: WaterMarkProp = {
    ..._defaultProps,
  };
  props.txtFont && (p.txtFont = props.txtFont);
  props.txtColor && (p.txtColor = props.txtColor);
  props.text && (p.text = props.text);
  let {canvasWid, canvasHei, txtSpace, txtLines, txtOriginY, txtFont, txtColor, text} = {...p};
  canvasWid = canvasWid;
  canvasHei = canvasHei;
  let space = txtSpace + txtFont * 2;
  let rowNum = Math.ceil(canvasHei / space);
  let rWidth = canvasWid / txtLines;
  let rHight = canvasHei / rowNum;

  let x = 10;
  let y = txtFont;
  y = txtFont + txtOriginY;
  if (y > rHight - txtFont) {
    y = rHight - txtFont;
  }
  const fillOpacity = '1.0';
  const res = `
      <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="${rWidth}" height="${rHight}">
        <text x="${x}" y="${y}" fill='${txtColor}'  transform = "rotate(-12)" transform-origin="50%" fill-opacity='${fillOpacity}' font-size='${txtFont}'> ${text}</text>
      </svg>`;
  const blob = new Blob([res], {
    type: 'image/svg+xml',
    lastModified: 0,
  });

  const url = URL.createObjectURL(blob);

  return (
    <div
      style={{
        position: 'absolute',
        width: `${canvasWid}px`,
        height: `${canvasHei}px`,
        backgroundImage: `url(${url})`,
        top: 0,
        left: 0,
        zIndex: 999,
        pointerEvents: 'none', //点击穿透
      }}></div>
  );
};

type propsType = {
  children?: ReactNode;
} & Partial<WaterMarkProp>;

const WaterMark = (props: propsType) => {
  const {text, txtFont, txtColor} = props;

  const memoInfo = useMemo(
    () => ({
      text,
      txtFont,
      txtColor,
    }),
    [text, txtFont, txtColor],
  );
  return (
    <div style={{position: 'fixed', width: '100%', height: ' 100%', pointerEvents: 'none'}}>
      {props.children}
      <SvgTextBg {...memoInfo} />
    </div>
  );
};

export default WaterMark;
