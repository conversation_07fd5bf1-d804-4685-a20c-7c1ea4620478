import { Image, View } from '@tarojs/components';
import Popup from '~components/Popup';
import styles from './index.module.scss';
import defaultBankIcon from '~images/bankcard/<EMAIL>'

interface SuppporBanksProps {
  visible?: boolean;
  banks: API.IBankCardInfo[];
  onClose?: () => void;
}
export default ({visible = false, banks, onClose}:   SuppporBanksProps) => {
  return (
    <Popup title='支持银行卡' visible={visible} onClose={onClose} showClose bodyStyle={{height: '60vh'}}>
      <View className={styles.bankContainer}>
        <View className={styles.tableHeader}>
          <View className={`${styles.rowFirst} ${styles.bankName}`}>银行名称</View>
          <View className={styles.rowSencond}>单笔限额(万元)</View>
          <View className={styles.rowSencond}>单日限额(万元)</View>
        </View>
        <View className={styles.tableBody}>
          {(banks || []).map((item, index) => (
            <View key={index} className={styles.tableRow}>
              <View className={`${styles.rowFirst} ${styles.rowName}`}><Image src={item.bankIconImgUrl || defaultBankIcon} className={styles.bankImg} />{item.bankName}</View>
              <View className={styles.rowSencond}>{(item.singleLimit / 10000).toFixed(0)}</View>
              <View className={styles.rowSencond}>{(item.dayLimit / 10000).toFixed(0)}</View>
            </View>
          ))}
        </View>
      </View>
    </Popup>
  )
}