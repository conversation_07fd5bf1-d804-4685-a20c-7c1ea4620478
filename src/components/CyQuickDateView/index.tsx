import {FC, useEffect, useState} from 'react';
import {BaseEventOrig, Picker, PickerDateProps, View} from '@tarojs/components';
import dayjs from 'dayjs';
import {isEmpty} from '../../utils/common';
import './index.scss';

/**
 * 组件名称：日期快速选择视图
 * 组件描述：自定义快速选择按钮
 */

export type DateRangeType = {
  startDt: string;
  endDt: string;
};

type DateItemType = 'today' | 'yesterday' | 'week' | 'month' | 'quarter' | 'year' | 'custom';

type Props = {
  onDateRangeChange?: (dateRange: DateRangeType) => void;
  defaultItem?: DateItemType | 'none'; // 按钮默认选择
  defaultRange?: DateItemType; // 默认时间区域 当defaultItem:'custom'时有效，否则
  options?: DateItemType[];
  maxDate?: string;
};

/**
 * 组件名称：时间区间选择组件
 * 组件描述：时间区间选择
 */
const CyQuickDateView: FC<Props> = props => {
  const nowDate = dayjs().format('YYYY-MM-DD');
  const yesterdayDate = dayjs(nowDate, 'YYYY-MM-DD').subtract(1, 'day').format('YYYY-MM-DD');
  const lastYearDate = dayjs(nowDate, 'YYYY-MM-DD').subtract(1, 'year').format('YYYY-MM-DD');
  const {
    onDateRangeChange,
    options = ['today', 'week', 'month'],
    defaultItem = 'custom',
    defaultRange = options[0],
    maxDate = lastYearDate,
  } = props;

  const dateItemMap = {
    today: '今日',
    yesterday: '昨日',
    week: '本周',
    month: '本月',
    quarter: '本季',
    year: '本年',
    custom: '', // 一般不用
  };
  const [selectItem, setSelectItem] = useState(defaultItem);
  const [startDt, setStartDt] = useState('');
  const [endDt, setEndDt] = useState('');

  const [custStartDt, setCustStartDt] = useState('');
  const [custEndDt, setCustEndDt] = useState('');

  useEffect(() => {
    setStartDateAndEndDate(defaultItem);
  }, []);

  useEffect(() => {
    onDateRangeChange && onDateRangeChange({startDt, endDt});
  }, [startDt, endDt]);

  useEffect(() => {
    setStartDt(custStartDt);
    setEndDt(custEndDt);
  }, [custEndDt, custStartDt]);

  const dateItemClick = (itemKey: DateItemType) => {
    setSelectItem(itemKey);
    setStartDateAndEndDate(itemKey);
  };

  function setStartDateAndEndDate(selectItemKey: DateItemType | 'none') {
    switch (selectItemKey) {
      case 'today':
        setCustStartDt(nowDate);
        setCustEndDt(nowDate);
        break;
      case 'yesterday':
        setCustStartDt(yesterdayDate);
        setCustEndDt(yesterdayDate);
        break;
      case 'week':
        const startWeekDay = dayjs().startOf('week').format('YYYY-MM-DD');
        setCustStartDt(startWeekDay);
        setCustEndDt(nowDate);
        break;
      case 'month':
        const startMonthDay = dayjs().startOf('month').format('YYYY-MM-DD');
        setCustStartDt(startMonthDay);
        setCustEndDt(nowDate);
        break;
      case 'quarter':
        const startQuarterDay = getQuarterStart();
        setCustStartDt(startQuarterDay);
        setCustEndDt(nowDate);
        break;
      case 'year':
        const startYearDay = dayjs().startOf('year').format('YYYY-MM-DD');
        setCustStartDt(startYearDay);
        setCustEndDt(yesterdayDate);
        break;
      case 'custom':
        setStartDateAndEndDate(defaultRange);
        break;
      default:
        setCustStartDt('');
        setCustEndDt('');
        break;
    }
  }

  function getQuarterStart() {
    const month = dayjs().month() + 1;
    console.log('🚀 ~ getQuarterStart ~ month:', month);
    let quarter = 1; // 获取当前季度
    if (month < 4) {
      quarter = 1;
    } else if (month < 7) {
      quarter = 2;
    } else if (month < 10) {
      quarter = 3;
    } else {
      quarter = 4;
    }
    const year = dayjs().year(); // 获取当前年份
    const startMonth = (quarter - 1) * 3 + 1; // 计算季度开始的月份

    return dayjs(new Date(year, startMonth - 1, 1)).format('YYYY-MM-DD'); // 返回季度开始的日期对象
  }

  const onDateChange = (e: BaseEventOrig<PickerDateProps.ChangeEventDetail>, flag: string) => {
    const date = e.detail.value;
    if (flag === 'start') {
      setCustStartDt(date);
    } else {
      setCustEndDt(date);
    }
    setSelectItem('custom');
  };
  return (
    <View className='comp-cy-quick-date-view'>
      {options.map(item => {
        return (
          <View
            key={item}
            className={`date-item ${selectItem === item ? 'select' : 'unselect'}`}
            onClick={() => dateItemClick(item)}>
            {dateItemMap[item]}
          </View>
        );
      })}

      <View className='date-item large' onClick={() => dateItemClick('custom')}>
        <>
          <Picker
            value={custStartDt}
            mode='date'
            start={maxDate}
            end={!isEmpty(custEndDt) ? custEndDt : nowDate}
            onChange={e => onDateChange(e, 'start')}>
            <View className={`date-label ${selectItem === 'custom' ? 'select' : 'unselect'}`}>
              {!isEmpty(custStartDt) ? custStartDt : '开始时间'}
            </View>
          </Picker>
          <View className='date-label-space'>至</View>
          <Picker
            value={custEndDt}
            mode='date'
            start={!isEmpty(custStartDt) ? custStartDt : '2010-01-01'}
            end={nowDate}
            onChange={e => onDateChange(e, 'end')}>
            <View className={`date-label ${selectItem === 'custom' ? 'select' : 'unselect'}`}>
              {!isEmpty(custEndDt) ? custEndDt : '结束时间'}
            </View>
          </Picker>
        </>
      </View>
    </View>
  );
};

export default CyQuickDateView;
