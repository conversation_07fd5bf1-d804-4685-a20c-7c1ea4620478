import { useEffect, useState } from 'react';
import { View, Input } from '@tarojs/components';
import { useReady } from '@tarojs/taro';
import Taro from '@tarojs/taro';
import { CyButton, CyModal } from 'hncy58-taro-components';
import { IPickerItem } from 'src/types/common-types';
import Card from '~components/Card';
import CellItem from '~components/CellItem';
import CellPickerItem, { PickerMode } from '~components/CellPickerItem';
import PageWrap from '~components/PageWrap';
import Steps, { Step } from '~components/Steps';
import useDict, { sortDictItems } from '~hooks/useDict';
import { updateBaseInfo, getBaseInfo } from '~services/user';
import usePersonInfoStore from '~store/personInfo';
import useUserStore from '~store/user';
import { getLabelByValue, handleMap, hideLoading, router, showLoading, showToast } from '~utils/common';
import { regExpMap } from '~utils/regex';
import { ValidationRule, validateForm } from '~utils/validate';
import styles from './index.module.scss';
import useFace from '~hooks/useFace';
import RequiredLabel from './components/RequireLabel';
const infoSteps: Step[] = [
  {
    title: '基本信息',
    status: 'process',
  },
  {
    title: '单位信息',
    status: 'process',
  },
  {
    title: '联系人信息',
    status: 'process',
  },
];

export default () => {
  const { startFace } = useFace()

  const { user } = useUserStore();
  const { setBaseInfo, resetBaseInfo, baseInfo } = usePersonInfoStore();
  const [current, setCurrent] = useState<number>(1);

  const [marryStatus, setMarryStatus] = useState<IPickerItem[]>([]);
  const [liveStatus, setLiveStatus] = useState<IPickerItem[]>([]);
  const [educationStatus, setEducationStatus] = useState<IPickerItem[]>([]);
  const [digreeStatus, setDigreeStatus] = useState<IPickerItem[]>([]);
  const [region, setRegion] = useState<string[]>([]);

  const [notFromMine, setNotFromMine] = useState<boolean>(true);

  const [formData, setFormData] = useState<API.baseInfoParams>({
    maritalStatus: '',
    highestDegree: '',
    highestEducation: '',
    postCode: '',
    homeAddress: {
      province: '',
      city: '',
      district: '',
      detailAddress: '',
    },
    houseNumber: '',
    addressStatus: '',
    detailAddress: '',
  });

  const formRules: Record<string, ValidationRule[]> = {
    maritalStatus: [
      {
        required: true,
        message: '请选择婚姻状况',
      },
    ],
    addressStatus: [
      {
        required: true,
        message: '请选择居住状况',
      },
    ],
    region: [
      {
        required: true,
        message: '请选择所在地区',
      },
    ],
    detailAddress: [
      {
        required: true,
        message: '请填写详细地址',
      },
      {
        pattern: regExpMap.text,
        message: '详细地址中包含不支持的字符，请修改',
      },
    ],
    houseNumber: [
      {
        required: true,
        message: '请填写门牌号',
      },
      {
        pattern: regExpMap.number,
        message: '请填写正确的门牌号',
      },
    ],
    postCode: [
      {
        required: true,
        message: '请填写邮政编码',
      },
      {
        pattern: regExpMap.postCode,
        message: '请输入正确的邮编',
      },
    ],
    highestEducation: [
      {
        required: true,
        message: '请选择最高学历',
      },
    ],
    highestDegree: [
      {
        required: true,
        message: '请选择最高学位',
      },
    ],
  };

  useEffect(() => {
    const params = Taro.getCurrentInstance().router?.params;
    setNotFromMine(params?.from !== 'mine');
    loadDictMap();
  }, []);

  useReady(() => {
    console.log('页面ready', baseInfo);
    if (baseInfo && baseInfo?.custUid === user.custUid) {
      CyModal.create({
        title: '温馨提示',
        content: '检测到有尚未保存的信息，是否继续填写？',
        confirmText: '确定',
        isShowCancel: true,
        onConfirm() {
          bindBaseInfo(baseInfo)
        },
        onCancel() {
          queryBaseInfo();
        },
      })
    } else {
      queryBaseInfo();
      resetBaseInfo();
    }
  });

  const loadDictMap = async () => {
    const mapList = await Promise.all([
      useDict('MARITAL_STATUS'),
      useDict('ADDRESS_STATUS'),
      useDict('EDUCATION'),
      useDict('DEGREE'),
    ]);
    if (mapList.length) {
      setMarryStatus(mapList[0].dictMap);
      setLiveStatus(mapList[1].dictMap);
      setEducationStatus(sortDictItems(mapList[2].dictMap));
      setDigreeStatus(sortDictItems(mapList[3].dictMap));
    }
  };

  const queryBaseInfo = async () => {
    try {
      showLoading();
      const res = await getBaseInfo();
      bindBaseInfo(res)
    } catch (error) {
      console.log(error);
    } finally {
      hideLoading();
    }
  }

  const bindBaseInfo = (info: API.baseInfoParams) => {
    const { city = '', province = '', district = '', detailAddress = '' } = info.homeAddress;
    setFormData({ ...info, detailAddress: info.detailAddress || detailAddress })
    setRegion(province ? [province, city, district] : []);
  }

  const updateFormData = (kv: Partial<API.baseInfoParams>) => {
    setBaseInfo({
      ...formData,
      ...kv,
      ...{ custUid: user.custUid },
    });
    setFormData({
      ...formData,
      ...kv,
    })
  };

  const transformRegion = (value: string[]) => {
    if (value.length) {
      return {
        province: value[0],
        city: value[1],
        district: value[2],
        detailAddress: formData.detailAddress,
      };
    }

    return {
      province: '',
      city: '',
      district: '',
      detailAddress: formData.detailAddress,
    };
  };

  const goNext = async () => {
    console.log('goNExt====', formData);
    const validationData = {
      ...formData,
      region,
    };
    const result = validateForm(validationData, formRules);

    if (!result.valid && result.errors) {
      const firstError = Object.values(result.errors)[0];
      showToast(firstError);
      return;
    }
    startFace({
      requestFrom: 'updateCustomerInfo',
      faceSuccess: () => {
        console.log('faceSuccess', formData)
        handleSubmit();
      },
      faceFail: () => {
        showToast('活体识别失败');
      },
    });
  };

  const handleSubmit = async () => {
    try {
      showLoading();
      const homeAddress = {
        province: region[0],
        city: region[1],
        district: region[2],
        detailAddress: formData.detailAddress,
      };
      await updateBaseInfo({ ...formData, homeAddress });
      hideLoading();
      resetBaseInfo();
      showToast({
        message: '保存成功',
        type: 'success',
      });
      await new Promise(resolve => setTimeout(resolve, 2000));
      if (notFromMine) {
        router.push({
          url: '/modules/user/workinfo/index',
        });
      } else {
        router.back();
      }
    } catch (error) {
      hideLoading();
      console.log('请求错误', error);
    }
  };
  return (
    <PageWrap className={`${styles.baseinfoPage} ${notFromMine ? '' : styles.pdt20}`}>
      {notFromMine && (
        <View className={styles.pageTop}>
          <Steps steps={infoSteps} current={current} />
        </View>
      )}
      <View className={styles.pageContent}>
        <Card title='基本信息' note='完善个人联系方式、住址等个人基础信息'>
          <CellPickerItem
            className={styles.CellItem}
            title={<RequiredLabel label='婚姻状况' required />}
            placeholder='请选择婚姻状况'
            range={handleMap(marryStatus, '90')}
            pickValue={formData.maritalStatus + ''}
            value={getLabelByValue(marryStatus, formData.maritalStatus + '')}
            onChange={maritalStatus => updateFormData({ maritalStatus })}
            clickable
          />
          <CellPickerItem
            pickValue={formData.addressStatus + ''}
            range={handleMap(liveStatus, '7')}
            value={getLabelByValue(liveStatus, formData.addressStatus + '')}
            onChange={addressStatus => updateFormData({ addressStatus })}
            className={styles.CellItem}
            title={<RequiredLabel label='居住状况' required />}
            placeholder='请选择居住状况'
            clickable
          />
          <CellPickerItem
            className={styles.CellItem}
            mode={PickerMode.Region}
            title={<RequiredLabel label='所在地区' required />}
            value={region.join('/')}
            placeholder='请选择居住所在地区'
            onChange={(value, label) => {
              console.log(value)
              const labelArr = label.split('/');
              const homeAddress = transformRegion(labelArr);
              setRegion(labelArr);
              updateFormData({ homeAddress });
              setBaseInfo({ ...formData, ...{ custUid: user.custUid }, homeAddress });
            }}
            clickable
          />
          <CellItem
            className={styles.CellItem}
            title={<RequiredLabel label='详细地址' required />}
            right={
              <Input
                placeholderClass={styles.inputPlaceholder}
                className={styles.rightInput}
                placeholder='请输入居住详细地址'
                value={formData.detailAddress}
                onInput={e => {
                  updateFormData({ detailAddress: e.detail.value })
                }}
              />
            }
            clickable
          />
          <CellItem
            className={styles.CellItem}
            title={<RequiredLabel label='门牌号' required />}
            right={
              <Input
                placeholderClass={styles.inputPlaceholder}
                className={styles.rightInput}
                type='number'
                placeholder='请输入门牌号'
                value={formData.houseNumber}
                onInput={e => updateFormData({ houseNumber: e.detail.value })}
              />
            }
            clickable
          />
          <CellItem
            className={styles.CellItem}
            title={<RequiredLabel label='邮政编码' required />}
            right={
              <Input
                placeholderClass={styles.inputPlaceholder}
                type='number'
                className={styles.rightInput}
                placeholder='请输入邮政编码'
                maxlength={6}
                value={formData.postCode}
                onInput={e => updateFormData({ postCode: e.detail.value })}
              />
            }
            clickable
          />
          <CellPickerItem
            range={handleMap(educationStatus, '99')}
            className={styles.CellItem}
            pickValue={formData.highestEducation + ''}
            value={getLabelByValue(educationStatus, formData.highestEducation + '')}
            title={<RequiredLabel label='最高学历' required />}
            placeholder='请选择学历'
            clickable
            onChange={highestEducation => updateFormData({ highestEducation })}
          />
          <CellPickerItem
            range={handleMap(digreeStatus, '0')}
            className={styles.CellItem}
            pickValue={formData.highestDegree + ''}
            value={getLabelByValue(digreeStatus, formData.highestDegree + '')}
            title={<RequiredLabel label='最高学位' required />}
            placeholder='请选择学位'
            clickable
            onChange={highestDegree => updateFormData({ highestDegree })}
          />
        </Card>
      </View>
      <View className={`${styles['bottom-fixed']} bottom-fixed`}>
        <CyButton round block type='primary' onClick={goNext} className={styles.btn}>
          {notFromMine ? '下一步' : '提交'}
        </CyButton>
      </View>
    </PageWrap>
  );
};
