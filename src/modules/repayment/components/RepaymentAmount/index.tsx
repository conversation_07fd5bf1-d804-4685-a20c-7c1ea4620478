import {useEffect, useRef, useState} from 'react';
import {Input, InputProps, View} from '@tarojs/components';
import styles from './index.module.scss';

interface RepaymentAmountProps {
  amountTitle?: string;
  amount?: number | string;
  amountNote?: string;
  isEdit?: boolean;
  onAmountChange?: (e: any) => boolean;
  onBlur?: (e: any) => boolean;
  onFocus?: (e: any) => void;
}
const RepaymentAmount = (props: RepaymentAmountProps) => {
  const {amountTitle, isEdit = false, amountNote, amount, onAmountChange, onBlur, onFocus} = props;
  const [canEdit, setCanEdit] = useState(isEdit);
  const inputRef = useRef<InputProps>(null);
  const onBlurFin = (e: any) => {
    if(onBlur) {
      const res = onBlur(e);
      // 如果输入值不符合要求，重置为初始值
      if(!res && inputRef.current?.value !== undefined) {
        inputRef.current.value = `${amount}`;
        return
      }
    }

  }
  return (
    <View className={styles.repaymentAmount}>
      <View className={styles.repaymentAmountMain}>
        {amountTitle && <View className={styles.repaymentAmountTitle}>{amountTitle}</View>}
        <View className={`${styles.repaymentAmountContainer}`}>
          <View className={styles.repaymentInput}>
            {canEdit ? (
              <Input
                type='digit'
                ref={inputRef}
                className={styles.money_input}
                // onInput={handleInputChange}
                onBlur={onBlurFin}
                onFocus={onFocus}
                defaultValue={`${amount}`}
                placeholder='请输入还款金额'
                placeholderStyle='color: #999; font-size: 18px;'
                placeholderClass={styles.money_input_placeholder}
              />
            ) : (
              <View className={styles.amount}>{amount || '0.00'}</View>
            )}
          </View>
          {isEdit && !canEdit && <View className={styles.editIcon} onClick={() => setCanEdit(true)}></View>}
        </View>
        {amountNote && <View className={styles.repaymentAmountNote}>{amountNote}</View>}
      </View>
    </View>
  );
};

export default RepaymentAmount;
