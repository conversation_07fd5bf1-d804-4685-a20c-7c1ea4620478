import {type Ticket, getMetaInfo, start} from '@hncy58/rn-private-face';
import {FaceCheckType, getAliyunFaceConfig, getPrivateFaceResult} from '../../services/aliyunFace';

export type StartFaceParams = {
  type: FaceCheckType;
  loanAmount: number;
  name: string;
  certId: string;
  frontImage: string;
  backImage: string;
};
const startFace = async (params: StartFaceParams) => {
  return new Promise(async (resolve, reject) => {
    try {
      const {type, loanAmount, name, certId, frontImage, backImage} = params;
      const areaCode = '430100';
      const metaInfo = await getMetaInfo();
      console.log('metaInfo', metaInfo);
      const faceTicket = await getAliyunFaceConfig({
        metaInfo,
        faceType: 'private',
        requestFrom: type,
        name,
        certId,
        idCardFrontPic: frontImage,
        idCardBackPic: backImage,
        areaCode,
      });

      const ticket: Ticket = {
        license: faceTicket.licence ?? '',
        securityConfig: faceTicket.securityConfig ?? '',
        certifyId: faceTicket.certifyId ?? '',
        ticketId: faceTicket.ticketId ?? '',
      };
      const res = await start(ticket);
      const result = await getPrivateFaceResult({
        certifyId: res.ticket.certifyId,
        requestFrom: type,
        serviceType: 'private',
        secureData: res.secureData,
        ticketId: res.ticket.ticketId,
        name,
        certId,
        areaCode,
      });
      if (result.code === '0') {
        resolve(true);
      } else {
        reject(result.message ?? '认证失败');
      }
    } catch (error: any) {
      reject(error?.message || '人脸识别失败');
    }
  });
};

export default startFace;
