import { useState } from 'react';
import { Image, View } from '@tarojs/components';
import PageWrap from '~components/PageWrap';
import { router, showToast } from '~utils/common';
import styles from './index.module.scss';

// 注意：请确认图标路径和组件属性是否正确。
// 我是基于项目结构和通用实践进行的推断。
import zcxxIcon from '~images/edit-message/<EMAIL>';
import jqzmIcon from '~images/edit-message/<EMAIL>';
import khjlIcon from '~images/edit-message/<EMAIL>';
import qtzlIcon from '~images/edit-message/<EMAIL>';
import lxxxIcon from '~images/edit-message/<EMAIL>';
import dwxxIcon from '~images/edit-message/<EMAIL>';
import jbxxIcon from '~images/edit-message/<EMAIL>';
import arrowIcon from '~images/edit-message/<EMAIL>'
import { Tabs } from '@taroify/core';
import useFace from '~hooks/useFace';

const DataSupplementPage = () => {
  
  const [value, setValue] = useState(0)
  const {startFace} = useFace({
    requestFrom: 'updateCustomerInfo',
  })

  // “资料补录”选项卡的数据列表
  const supplementList = [
    {
      title: '资产信息',
      note: '上传您本人名下资产信息材料',
      thumb: zcxxIcon,
      onClick: () => router.push({ url: '/modules/user/upload-material/index?type=E28' }),
    },
    {
      title: '结清证明',
      note: '全部借款结清后开具的证明',
      thumb: jqzmIcon,
      onClick: () => router.push({ url: '/modules/user/upload-material/index?type=E29' }),
      arrow: true,
    },
    {
      title: '客户经理合影',
      note: '客户经理线下核实信息后上传合影',
      thumb: khjlIcon,
      onClick: () => router.push({ url: '/modules/user/upload-material/index?type=E03' }),
      arrow: true,
    },
    {
      title: '其他资料',
      note: '补充其他资料、资产等信息',
      thumb: qtzlIcon,
      onClick: () => router.push({ url: '/modules/user/upload-material/index?type=E21' }),
      arrow: true,
    },
  ];

  const faceBeforeJump = (url: string) => {
    startFace({
      faceSuccess: () => {
        router.push({ url });
      },
      faceFail: () => {
        showToast('活体检测失败，请重新尝试');
      },
    })
  }

  const updateList = [
    {
      title: '基本信息',
      note: '补充您的最新基本信息资料',
      thumb: jbxxIcon,
      onClick: () => faceBeforeJump('/modules/user/baseinfo/index?from=mine'),
      arrow: true,
    },
    {
      title: '单位信息',
      note: '更新您最新的工作单位信息',
      thumb: dwxxIcon,
      onClick: () => faceBeforeJump('/modules/user/workinfo/index?from=mine'),
      arrow: true,
    },
    {
      title: '联系人信息',
      note: '更新最新联系人信息',
      thumb: lxxxIcon,
      onClick: () => faceBeforeJump('/modules/user/contactinfo/index?from=mine'),
      arrow: true,
    },

  ]
  

  return (
    <PageWrap className={styles.pageWrapper}>
      <Tabs value={value} onChange={setValue}>
        <Tabs.TabPane title="资料补录">
          <View className={styles.supplementList}>
            {supplementList.map((item, index) => (
              <View
                key={index}
                className={styles.listItem}
                onClick={item.onClick}
              >
                <Image src={item.thumb} className={styles.itemIcon} />
                <View className={styles.itemTextContainer}>
                  <View className={styles.itemTitle}>
                    {item.title}
                  </View>
                  <View className={styles.itemSubtitle}>
                    {item.note}
                  </View>
                </View>
                <Image src={arrowIcon} className={styles.itemArrow}></Image>
              </View>
            ))}
          </View>
        </Tabs.TabPane>
        <Tabs.TabPane title="信息更新">
          <View className={styles.supplementList}>
            {updateList.map((item, index) => (
              <View
                key={index}
                className={styles.listItem}
                onClick={item.onClick}
              >
                <Image src={item.thumb} className={styles.itemIcon} />
                <View className={styles.itemTextContainer}>
                  <View className={styles.itemTitle}>
                    {item.title}
                  </View>
                  <View className={styles.itemSubtitle}>
                    {item.note}
                  </View>
                </View>
                <Image src={arrowIcon} className={styles.itemArrow}></Image>
              </View>
            ))}
          </View>
        </Tabs.TabPane>
      </Tabs>
    </PageWrap>
  );
};

// 原组件名为 EditMessage，已重命名以反映新的UI。
// 你可能需要更新文件名/文件夹名以及相关的导入。
export default DataSupplementPage;