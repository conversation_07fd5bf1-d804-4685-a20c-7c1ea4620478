.cell {
  display: flex;
  align-items: center;
  padding: 16px;
  min-height: 100px;
  background-color: #fff;
  box-sizing: border-box;

  &.cell {
    border-bottom: 1px solid $color-border-light;
  }

  &.cell:nth-last-child(1),
  &.cell:only-child {
    border-bottom: none;
  }
}

.cell_content {
  max-width: 35%;
  white-space: nowrap;
}

.cell_title {
  font-size: 28px;
  color: $color-text-normal;
}

.cell_arrow {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  margin-left: 16px;
  text-align: right;
  flex: 1;
}
.cell_right {
  line-height: 1;
}
.cell_arrow_text {
  color: #333;
  font-size: 28px;
}
.cell_arrow_desc { 
  margin-top: 11px;
}
.cell_arrow_note {
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}

.cell_arrow_holder {
  color: #999;
  font-size: 28px;
}

.cell_arrow_container {
  &.ml {
    margin-left: 19px;
  }
}
.cell_arrow_icon {
  width: 24px;
  height: 24px;
}