import {Text, View} from '@tarojs/components';
import dayjs from 'dayjs';
import styles from '../RepaymentPlan/RepaymentPlan.module.scss';

type RecordItemProps = {
  date: string;
  amount: number;
  principal: string;
  interest: string;
};

type RecordContentProps = {
  repaymentHistoryList: API.RepaymentRecord[];
};

const RecordItem = ({date, amount, principal, interest}: RecordItemProps) => {
  return (
    <View className={styles.recordItem}>
      <View className={styles['recordItem-date']}>
        <Text>还款日期：</Text>
        <Text>{date && dayjs(date).format('YYYY-MM-DD')}</Text>
      </View>
      <View className={styles['recordItem-amount']}>{amount}元</View>
      {/* <View className={styles['recordItem-bjlx']}>
        <Text>本金{principal}元</Text>
        <Text>+</Text>
        <Text>利息{interest}元</Text>
      </View> */}
    </View>
  );
};

const RecordContent = ({repaymentHistoryList}: RecordContentProps) => {
  return (
    <View className={styles.recordContent}>
      {repaymentHistoryList.map((item, index) => (
        <RecordItem
          key={index}
          date={item.date}
          amount={item.amount}
          principal={item.principal}
          interest={item.interest}
        />
      ))}
    </View>
  );
};

export default RecordContent;
