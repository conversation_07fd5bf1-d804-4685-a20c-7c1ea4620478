import {CyButton} from 'hncy58-taro-components';
import {Image, Text, View} from '@tarojs/components';
import Button from '~components/Button';
import Card from '~components/Card';
import List from '~components/List/List';
import ListItem from '~components/List/ListItem';
import Steps, {type Step} from '~components/Steps';
import PageWrap from '~components/pageWrap';
import RightArrow from '~icons/icon-right-arrow.png';
import WarnIcon from '~icons/icon-warn.png';
import auditingImg from '~images/credit-apply/credit-auditing.png';
import styles from './index.module.scss';
import { router } from '~utils/common';
import { useEffect, useState } from 'react';
import { queryNeedWithCM } from '~services/user';

console.log('redner');
const steps: Step[] = [
  {title: '身份核验', status: 'process'},
  {title: '完善信息', status: 'process'},
  {title: '申请额度', status: 'process'},
];

const goAddInfo = () => {
  router.push({
    url: '/modules/credit_authorization/upload-info/index',
  });
};

const goUploadManagePhoto = () => {
  router.push({
    url: '/modules/user/upload-material/index?type=E03'
  })
}

export default () => {
  const [isNeedWithCM, setIsNeedWithCM] = useState(false);

  useEffect(() => {
    queryNeedWithCM().then(res => {
      setIsNeedWithCM(Boolean(res || false));
    })
  }, [])

  return (
    <PageWrap className={styles.page_container}>
      <View className={styles.top_entry} onClick={goAddInfo}>
        <Image className={styles.top_entry_icon} src={WarnIcon} />
        <Text className={styles.top_entry_text}>借款快人一步，立即补充资料，审批通过即可借款</Text>
        <Image className={styles.top_entry_arrow} src={RightArrow} />
      </View>
      <Card title='额度审批中...' note='资料已提交，请耐心等待审核结果' className={styles.card_item}>
        <View style={{textAlign: 'center'}}>
          <Image className={styles.waitImg} src={auditingImg} />
          <View className={styles.identity_info}>
            <Text className={styles.identity_info_name}>系统评估中，请您耐心等待</Text>
          </View>
          <View className={styles.verification_tip}>审批通过后会通过短信通知您，请注意查收 </View>
        </View>
      </Card>

      {/* <Card title='补充资料' note='等待时间可以补充以下资料，获得额度后可快速提款' className={styles.card_item}>
        <List>
          {[1, 2, 3].map((item, index) => (
            <ListItem
              key={index}
              title='等待时间可以补充以下资料，获得额度后可快速提款'
              note='等待时间可以补充以下资料，获得额度后可快速提款'
              extraText='等待时间可以补充以下资料，获得额度后可快速提款'
              isSuccess
              thumb={WaitImg}
            />
          ))}
        </List>
      </Card> */}
      {
        isNeedWithCM ? (
          <CyButton className={styles.bt_btn} onClick={goUploadManagePhoto} block round type='primary'>
            上传客户经理合影
          </CyButton>
        ) : null
      }
    </PageWrap>
  );
};
