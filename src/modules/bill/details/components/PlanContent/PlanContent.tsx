import {useCallback, useEffect, useRef, useState} from 'react';
import {Text, View} from '@tarojs/components';
import {ScrollView} from '@tarojs/components';
import currencyUtil from 'currency.js';
import RepaymentProcessItem, {LeftCell, RightCell} from '~components/RepaymentProcess';
import styles from '../RepaymentPlan/RepaymentPlan.module.scss';

type PlanContentProps = {
  remainPrincipal: number;
  planList: API.LoanPlan[];
};

const PlanContent = ({remainPrincipal, planList}: PlanContentProps) => {
  const remainPrincipalFormat = currencyUtil(remainPrincipal).format({
    symbol: '',
    separator: ',',
    decimal: '.',
    precision: 2,
  });

  return (
    <View className={styles.planContent}>
      <View className={styles['planContent-header']}>
        <Text className={styles['planContent-header-title']}>剩余未还本金</Text>
        <Text className={styles['planContent-header-amount']}>{remainPrincipalFormat}元</Text>
      </View>
      <View className={styles['planContent-list']}>
        {planList.map(item => (
          <RepaymentProcessItem
            key={item.currentTerm}
            left={<LeftCell currentTerm={item.currentTerm} dueDate={item.dueDate} days={item.days} />}
            right={<RightCell totalAmount={item.totalAmount} principal={item.principal} interest={item.interest} />}
            active={item.currentStatus}
          />
        ))}
      </View>
    </View>
  );
};

export default PlanContent;
