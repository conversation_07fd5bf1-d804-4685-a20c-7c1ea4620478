.modalWrapper {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 999;
  }
  
  .mask {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    opacity: 0;
    transition: transform 0.3s, opacity 0.3s;
  }

  .modalWrapper.visible .mask {
    opacity: 1;
  }
  
  .content {
    position: relative;
    width: 80%;
    max-width: 1200px;
    background-color: #fff;
    border-radius: 10px;
    overflow: hidden;
    z-index: 1;
    transform: scale(0.65);
    opacity: 0;
    transition: transform 0.3s, opacity 0.3s;
  }
  
  .modalWrapper.visible .content {
    transform: translateY(0) scale(1);
    opacity: 1;
  }
  
  .title {
    padding: 40px;
    font-size: 32px;
    font-weight: 500;
    text-align: center;
    color: $color-text-normal;
  }
  
  .body {
    padding: 20px 52px 60px 52px;
    font-size: 28px;
    color: $color-text-regular;
    line-height: 48px;  
  }
  
  .footer {
    display: flex;
    border-top: 1px solid $color-border-light;
  }
  
  .button {
    flex: 1;
    padding: 30px 0;
    text-align: center;
    font-size: 32px;
    color: $color-text-normal;
    background: #fff;
    border-right: 1px solid $color-border-light;
    color: $color-text-secondary;
    font-weight: 400;
    &:last-child {
      border-right: none;
    }
  
    &.primary {
      color: $color-primary;
    }
  }
