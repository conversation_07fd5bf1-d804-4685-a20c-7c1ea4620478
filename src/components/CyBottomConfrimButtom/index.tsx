import {FC} from 'react';
import {But<PERSON>} from '@tarojs/components';
import CyLinearGradient from '../CyLinearGradient';
import './index.scss';

interface Props {
  title: string;
  onClick?: () => void;
}

/**
 * 组件名称：底部确认按钮
 * 组件描述：底部确认按钮
 */
const CyBottomConfrimButtom: FC<Props> = ({title, onClick}) => {
  return (
    <CyLinearGradient
      className='cy-bottom-confrim-buttom-btn-wrap'
      colors={['#437DFF', '#34BAFF']}
      direction='to right'>
      <Button className='cy-bottom-confrim-buttom-btn' type='primary' onClick={onClick}>
        {title}
      </Button>
    </CyLinearGradient>
  );
};

export default CyBottomConfrimButtom;
