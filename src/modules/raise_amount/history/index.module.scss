.page-raise-amount {
  background-color: #f5f5f5;
  min-height: 100vh;
}

// 当前总额度区域
.limitSection {
  background-color: #fff;
  padding: 56px 26px;
}

.limitLabel {
  display: block;
  font-size: 28px;
  color: #333;
  margin-bottom: 38px;
}

.limitAmount {
  display: block;
  font-size: 64px;
  font-weight: 600;
  color: #333;
  line-height: 1;
}

// 历史记录区域
.historySection {
  background-color: white;
  border-radius: 12px;
  padding: 0 20px;
}

.historyTitle {
  display: block;
  font-size: 24px;
  color: #999;
  padding: 24px 0 16px 20px;
}

.recordList {
  // 记录列表容器
}

.recordItem {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 160px;
  padding: 0 20px;
  border-bottom: 1px solid #f0f0f0;
  
  &:last-child {
    border-bottom: none;
  }
}

.recordLeft {
  flex: 1;
}

.recordTitle {
  display: block;
  font-size: 28px;
  color: #333;
  font-weight: bold;
  margin-bottom: 12px;
}

.recordDate {
  display: block;
  font-size: 24px;
  color: #999;
}

.recordRight {
  text-align: right;
}

.recordStatus {
  display: block;
  font-size: 28px;
  font-weight: 500;
  
  &.processing,
  &.processing_update,
  &.pass {
    color: $color-primary;
    font-weight: bold;
  }
  
  &.refuse {
    color: #999;
  }
}

.recordAmount {
  display: block;
  font-size: 24px;
  color: #999;
}

.emptySection {
  padding-top: 280px;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.emptyText {
  margin-top: 64px;
  font-size: 32px;
  color: #333;
}

.emptyIcon {
  width: 280px;
  height: 280px;
}