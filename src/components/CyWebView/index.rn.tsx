import {FC, useEffect, useRef} from 'react';
import {Button, StyleSheet, View} from 'react-native';
import {WebView} from 'react-native-webview';
import {TEST_DATA} from '../../pages/webview/data';
import {ABNORMAL_ALARMS_COLUMNS} from '../../utils/constant';
import './index.scss';

interface Props {
  source: string;
}

/**
 * 组件名称：webview容器
 * 组件描述：webview容器
 */
const CyWebView: FC<Props> = ({source}) => {
  useEffect(() => {
    if (source.length > 0) {
      const postMsg = {base64: source};
      console.log('first1234', source.length);
      refWebView.current.postMessage(JSON.stringify(postMsg));
    }
  }, [source]);

  const refWebView = useRef<any>();
  const onMessage = (e: {nativeEvent: {data: any}}) => {
    console.log(e.nativeEvent.data);
  };
  const onPress = () => {
    const postMsg = {base64: source};
    console.log('first', source.length);
    refWebView.current.postMessage(JSON.stringify(postMsg));
    // const columns = {columns: ABNORMAL_ALARMS_COLUMNS, sourceData: TEST_DATA};
    // console.log('🚀 ~ onPress ~ TEST_DATA:', TEST_DATA.length);
    // refWebView.current.postMessage(JSON.stringify(columns));
  };
  const html = require('../../assets/html/pdf.html');

  return (
    <View style={stylesRn.container}>
      <Button title='测试' onPress={onPress}></Button>
      <WebView
        style={stylesRn.webView}
        originWhitelist={['*']}
        scalesPageToFit={false}
        ref={refWebView}
        javaScriptEnabled
        source={html}
        scrollEnabled={false}
        onMessage={onMessage}
        showsHorizontalScrollIndicator={false}
      />
    </View>
  );
};

export default CyWebView;
const stylesRn = StyleSheet.create({
  container: {
    height: '100%',
    width: '100%',
    flex: 1,
  },
  webView: {
    flex: 1,
    // overflow: 'hidden', // 隐藏滚动条
  },
});
