.comp-cy-photo-browser-view {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.close {
  margin-top: 300px;
  text-align: center;
  color: #ffffff;
  font-size: 30px;
  flex: 1;
}

.scrollview-container {
  padding: 0;
  margin: 0;
  height: 80vw;
  overflow: hidden;
}
.scrollview {
  display: flex;
  flex-direction: row;
  background-color: #ffffff;
  border-radius: 8px;
}

.image-item {
  position: relative;
  display: flex;
  flex-direction: row;
  justify-content: center;
  padding: 0;
}

.option {
  position: absolute;
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  bottom: 20px;
  width: 100%;
  box-sizing: border-box;
  padding: 0 20px;

  &-item {
    color: $color-primary;
    font-size: 24px;
  }
}

