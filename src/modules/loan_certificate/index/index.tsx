import {useState} from 'react';
import {useLoad} from '@tarojs/taro';
import Taro from '@tarojs/taro';
import {CyModal} from 'hncy58-taro-components';
import Empty from '~components/Empty';
import List from '~components/List/List';
import ListItem from '~components/List/ListItem';
import PageWrap from '~components/PageWrap';
import single from '~images/loan-certificate/dbzm.png';
import all from '~images/loan-certificate/qbzm.png';
import {canAllCreate, createAll} from '~services/loanCertificate';
import useUserStore from '~store/user';
import {router,showToast} from '~utils/common';
import goLendingPreProgress from '~utils/goLendingPreProgress';
import styles from './index.module.scss';


const LoanCertificate = () => {
  const [can, setCan] = useState(false);

  useLoad(() => {
    requestCanAllCreate();
  });

  const {
    amountCreditInfo: {activated},
  } = useUserStore();

  const goBack = () => {
    router.back()
  };

  const requestCanAllCreate = async () => {
    const can = await canAllCreate();
    setCan(can);
  };

  const goAll = async () => {
    if (!can) {
      CyModal.create({
        title: '温馨提示',
        content: '您当前有未结清的账单,请全部还清后再来开具结清证明',
        confirmText: '知道了',
      });
      return;
    }
    try {
      Taro.showLoading();
      const res = await createAll();
      router.push({
        url: `/modules/loan_certificate/details/index?type=all&securityCode=${res.securityCode}&contractCode=${res.contractCode}`,
      });
    } catch (error: any) {
      if (error.code !== 'UCS_12215') return showToast(error.message);
      CyModal.create({
        title: '温馨提示',
        content: error.message.replace('failure:', ''),
        confirmText: '知道了',
        onConfirm: () => {
          router.push({
            url: '/modules/loan_certificate/list/index?type=all',
          });
        },
      });
    } finally {
      Taro.hideLoading();
    }
  };

  return (
    <PageWrap className={styles['loan-certificate-wrapper']}>
      {activated ? (
        <List className={styles['loan-certificate-container']}>
          <ListItem
            title='全部结清证明'
            thumb={all}
            thumbClassName={styles['loan-certificate-thumb']}
            onClick={goAll}
          />
          <ListItem
            title='单笔结清证明'
            thumb={single}
            thumbClassName={styles['loan-certificate-thumb']}
            onClick={() => {
              router.push({
                url: '/modules/loan_certificate/list/index?type=single',
              });
            }}
          />
        </List>
      ) : (
        <Empty
          text='您还未申请借款,无需开具结清证明'
          subText=''
          showButton
          buttonText='返回'
          onButtonClick={goBack}
        />
      )}
    </PageWrap>
  );
};

export default LoanCertificate;
