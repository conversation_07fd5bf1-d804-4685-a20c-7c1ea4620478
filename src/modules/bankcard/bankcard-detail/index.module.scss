.bankcardDetailContainer {
  position: relative;
  padding: 40px;
  box-sizing: border-box;
  .detailItem {
    box-shadow: 0px 16px 40px 0px rgba(70,85,146,0.66);
  }
  .pageCard {
    margin-top: 64px;
    margin-bottom: 24px;
    padding: 0 40px;
    border-radius: 16px;
  }
  .cardCell {
    padding: 0;
  }
  .bottomBtn {
    position: absolute;
    bottom: 0;
    width: calc(100% - 80px);
    .deleteBtn {
      width: 100%;
      height: 100px;
      line-height: 100px;
      text-align: center;
      color: $color-primary;
      font-size: $font-size-normal;
    }
  }
}
.agreementContainer {
  padding: 45px 0 68px 0;
}
.bankcardDetailTips {
  font-size: 28px;
  line-height: 48px;
  color: #666;
  text-align: center;
}

.contractPopupFooter {
  display: flex;
  padding: 40px;
}

.contractContent {
  padding: 0 40px;
  color: $color-text-normal;
}

.cy-contractbar-confirm-btn {
  margin-left: 12px;
  flex: 1;
}