import {FC, ReactNode} from 'react';
import {SafeAreaProvider} from 'react-native-safe-area-context';
import {Pushy, PushyProvider} from 'react-native-update';
import _updateConfig from '../../../update.json';
import './index.scss';

const {appKey} = _updateConfig[Platform.OS];
// 唯一必填参数是appKey，其他选项请参阅 api 文档
const pushyClient = new Pushy({
  appKey,
  checkStrategy: 'onAppStart',
  updateStrategy: 'silentAndLater',
});

interface Props {
  children?: ReactNode;
}

/**
 * 组件名称：安全区域
 * 组件描述：安全区域
 */
const CySafeAreaProvider: FC<Props> = ({children}) => {
  return (
    <PushyProvider client={pushyClient}>
      <SafeAreaProvider>{children}</SafeAreaProvider>
    </PushyProvider>
  );
};

export default CySafeAreaProvider;
