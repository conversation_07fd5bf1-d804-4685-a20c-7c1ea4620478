import {document, location, window} from '@tarojs/runtime';
import Taro from '@tarojs/taro';
import {StartFaceParams} from '.';
import {getAliyunCertifyurl, getFaceIdResult, getFaceType} from '../../services/face';
import {showToast} from '../common';

export enum FACE_TYPE {
  private = 'private',
  public = 'public',
}
const getFaceResult = async (params: {
  ticketId: string;
  certifyId?: string;
  securityConfig?: string;
  requestFrom?: string;
}) => {
  return new Promise((resolve, reject) => {
    getFaceIdResult({
      ticketId: params.ticketId,
      certifyId: params.certifyId,
      secureData: params.securityConfig,
      requestFrom: params.requestFrom,
    })
      .then(res => {
        if (res.code) {
          resolve(true);
        } else {
          reject({msg: res.message || '人脸失败'});
        }
      })
      .catch(e => {
        reject({msg: e.message || '人脸失败'});
      });
  });
};

export default async (params: StartFaceParams) => {
  // 1. 类型查询
  try {
    const {faceType} = await getFaceType();
    const faceApiRes = await getAliyunCertifyurl({
      requestFrom: params.type,
    });

    if (faceType === FACE_TYPE.private) {
      // TODO: 私有化部分处理
      if (!document.getElementById('zoloz')) {
        const zolozIframe = document.createElement('iframe');
        zolozIframe.setAttribute('id', 'zoloz');
        zolozIframe.setAttribute('src', faceApiRes.certifyUrl);
        zolozIframe.setAttribute('allow', 'microphone;camera;midi;encrypted-media;');
        zolozIframe.setAttribute('allowusermedia', 'true');
        zolozIframe.setAttribute('webkitallowfullscreen', 'true');
        zolozIframe.setAttribute('allowFullScreen', true);
        zolozIframe.setAttribute('width', '100%');
        zolozIframe.setAttribute('height', '100%');
        zolozIframe.setAttribute('frameborder', '0');
        zolozIframe.setAttribute('style', 'position: fixed; top: 0; left: 0;height: 100vh;');
        document.body.appendChild(zolozIframe);
      }
      // 1. 私有化版本监听
      const listenCb = async (e: any) => {
        if (e.data.state === 'ready') {
          const webview = document?.getElementById('zoloz');
          const {licence, securityConfig} = faceApiRes;
          console.log('webview', webview);
          console.log('data', faceApiRes, {
            license: licence,
            securityConf: securityConfig,
            locale: 'zh-CN',
          });
          // @ts-ignore
          webview?.contentWindow?.postMessage(
            {
              license: licence,
              securityConf: securityConfig,
              locale: 'zh-CN',
            },
            '*',
          );
        }
        if (e.data.state === 'complete') {
          try {
            await getFaceResult({
              ...faceApiRes,
              requestFrom: params.type,
            });
          } catch (err) {
            // TODO: 处理错误
          }
        }
        if (e.data.state === 'interrupted') {
          // TODO: 处理错误
        }
      };
      window.addEventListener?.('message', listenCb);
    } else {
      // 2. 腾讯云版本监听async
      let timer = setInterval(async () => {
        const faceStorageStr = Taro.getStorageSync('__persisted__face__result');
        if (faceStorageStr) {
          const faceStorageObj = JSON.parse(faceStorageStr);
          if (faceStorageObj[faceApiRes.ticketId]) {
            await getFaceResult({
              ...faceApiRes,
              requestFrom: params.type,
            });
            clearInterval(timer);
          }
        }
      }, 1000);
      location.href = faceApiRes.certifyUrl;
    }
  } catch (e) {
    // TODO: 错误处理
  }
};
