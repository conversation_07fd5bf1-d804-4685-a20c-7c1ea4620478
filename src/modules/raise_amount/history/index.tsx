import {View, Text, Image} from '@tarojs/components';
import styles from './index.module.scss';
import PageWrap from '~components/PageWrap';
import { useEffect, useMemo, useState } from 'react';
import emptyIcon from '~images/raiseamount/<EMAIL>'
import { getRaiseCreditRecords } from '~services/raiseAmount';
import { formatToFixedTwo } from '~utils/number';
import useUserStore from '~store/user';
import dayjs from 'dayjs';
import currencyUtil from 'currency.js';

const RaiseAmount = () => {
  /** 1. 变量定义 */

  /** 2. usestates + storestates */
  const [historyRecords, setHistoryRecords] = useState<API.RaiseAmountRecordItem[]>([]);
  const { amountCreditInfo, getUserAmountInfo } = useUserStore();

  const currentLimit = useMemo(() => {
    return currencyUtil(amountCreditInfo?.creditAmount || 0).format({
        symbol: '',
        separator: ',',
        decimal: '.',
        precision: 2,
      });
  }, [amountCreditInfo]);

  /** 3. effects */
  useEffect(() => {
    queryData();
    getUserAmountInfo();
  }, [])

  /** 4. 方法定义 */
  const queryData = async () => {
    try {
      const res = await getRaiseCreditRecords({applyType: ''})
      setHistoryRecords(res)
    } catch (error) {
      console.log('请求错误', error)
    }
  }  
  /** 5. ui */
  return (
    <PageWrap className={styles['page-raise-amount']}>
      {
        historyRecords.length === 0 ? (
          <View className={styles.emptySection}>
            <Image
              className={styles.emptyIcon}
              src={emptyIcon}
              mode="aspectFit"
            />
            <Text className={styles.emptyText}>暂无提额记录</Text>
          </View>
        ) : (
          <>
            {/* 当前总额度 */}
            <View className={styles.limitSection}>
              <Text className={styles.limitLabel}>当前总额度(元)</Text>
              <Text className={styles.limitAmount}>{currentLimit}</Text>
            </View>
            
            <View className={styles.historyTitle}>额度变更记录</View>
            {/* 历史提额记录 */}
            <View className={styles.historySection}>
              <View className={styles.recordList}>
                {historyRecords.map((record) => (
                  <View key={record.orderNo} className={styles.recordItem}>
                    <View className={styles.recordLeft}>
                      <Text className={styles.recordTitle}>{record.applyTypeName}</Text>
                      <Text className={styles.recordDate}>{dayjs(record.applyDate).format('YYYY.MM.DD')}</Text>
                    </View>
                    
                    <View className={styles.recordRight}>
                      {
                        ['refuse', 'processing', 'processing_update'].includes(record.applyStatus) ? 
                        <Text className={`${styles.recordStatus} ${styles[record.applyStatus]}`}>{record.applyStatusName}</Text> : 
                        <Text className={`${styles.recordStatus} ${styles[record.applyStatus]}`}>{`+${formatToFixedTwo(record.raisedAmount)}元`}</Text>
                      }
                    </View>
                  </View>
                ))}
              </View>
            </View>
          </>
        )
      }
    </PageWrap>
  );
};

export default RaiseAmount;
