import { CyModal } from "hncy58-taro-components"
import { router } from "~utils/common";

interface RequestErrorProps {
  content?: React.ReactNode;
  confirmText?: string;
  onConfirm?: () => void;
}
const useRequestError = ({
  content = '请求失败，请稍后再试',
  confirmText = '确定',
  onConfirm
}: RequestErrorProps) => {
  return (
    CyModal.create({
      title: '温馨提示',
      content,
      confirmText,
      onConfirm: () => {
        if (onConfirm) {
          onConfirm();
        } else {
          router.back();
        }
      },
    })
  )
}

export default useRequestError