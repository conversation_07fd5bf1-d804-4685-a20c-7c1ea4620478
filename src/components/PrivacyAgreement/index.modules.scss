.content {
  max-width: 1200rxp;
  font-size: 28rpx;
  color: #555;
  line-height: 40rpx;
  padding: 40rpx 32rpx;
  background: #fff;
  box-sizing: border-box;
  -webkit-box-sizing: border-box;
  -webkit-user-select: none;
  -moz-user-select: none;
  user-select: none;

  view {
    text-indent: 2em;
  }

  .title {
    text-align: center;
    color: #333;
    font-weight: 800;
    margin-bottom: 30rpx;
    text-indent: 0 !important;
  }

  .section {
    color: #333;
    margin-top: 60rpx;
    font-weight: 700;
  }

  .sectionTitle {
    color: #333;
    margin-top: 20rpx;
    font-weight: 700;
  }

  .inlineBrief {
    display: inline;
    color: #333;
    margin-top: 20rpx;
    font-weight: 700;
  }

  .brief {
    margin-top: 20rpx;
    margin-bottom: 20rpx;
    .brief-two {
      margin-top: 14rpx;
    }
  }

  .briefList {
    margin-top: 10rpx;
    margin-bottom: 10rpx;
  }

  .briefListNext {
    margin-top: 10rpx;
    margin-bottom: 10rpx;
  }

  .sectionHeader {
    text-align: center;
    color: #333;
    margin: 20px 0;
    font-weight: 700;
    text-indent: 0 !important;
  }

  .w800 {
    font-weight: 800;
  }

  .c333 {
    color: #333;
  }

  .mtb10 {
    margin: 10px 0;
  }

  .underlinedText {
    text-decoration: underline;
  }

  .link {
    text-decoration: underline;
  }
}

.table {
  width: 100%;
  border-collapse: collapse;
  border-spacing: 0;
  text-indent: 0 !important;
  border-bottom: 1px solid #333;

  .thead {
    color: #333;
    font-weight: 800;
    text-indent: 0 !important;
    border-bottom: 1px solid #333;
  }

  .tr {
    display: flex;
    text-indent: 0 !important;
    justify-content: center;
    .th,
    .td {
      flex: 1;
      display: flex;
      align-items: center;
      justify-content: center;
      text-align: center;
      border-top: 1px solid #333;
      border-left: 1px solid #333;
      word-break: break-word;
      background-color: white;
      margin: 0;
      text-indent: 0 !important;
      &:nth-last-child(1) {
        border-right: 1px solid #333;
      }
      &:nth-child(3),
      &:nth-child(4) {
        flex: 5;
      }
    }
  }
  .tbody .tr:first-child .td {
    border-top: none;
  }
}
