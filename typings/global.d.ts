import {CSSProperties} from 'react';

export {};
declare global {
  const myTask: 'build' | 'dev';

  type DocumentType = 'agreement' | 'cashgift' | 'claim';

  const KEYBOARD_TYPE_NUMBER: number;
  const CFCA_OK: number;
  const OUTPUT_TYPE_ORIGINAL: number;
  const CIPHER_TYPE_RSA: number;
  const KEYBOARD_DISORDER_ALL: number;
  class CFCAKeyboard {
    constructor(KEYBOARD_TYPE_NUMBER: number) {}
    show: () => void; //显示
    hidden: () => void; //隐藏
    getVersion: () => string; //获取版本号
    clearInputValue: () => void; //清空键盘输入
    getEncryptedInputValue: () => {errorCode: string; data: string}; //获取密码加密结果
    getEncryptedClientRandom: () => {errorCode: string; data: string}; //获取客户端随机数加密结果
    checkInputValueMatch: (otherId: string) => boolean; //
  }

  class TencentCaptcha {
    constructor(appid: string, callback: (res: any) => void) {}
    show: () => void;
  }

  interface SafeKeyboardProps {
    id: string;
    cipherType?: number; //加密类型 默认sm2
    outputType?: number; //输出类型
    inputRegex?: string; //输入的正则匹配
    keyword?: []; //关键字
    isEncryptState?: boolean; //是否加密状态
    isShowKeyboard?: boolean; //是否显示键盘
    isKeyAnimation?: boolean; //是否显示键盘按键效果
    showLastCharacter?: boolean; //是否末位明文显示
    precision?: number; //设置小数点后可显示位数 默认2
    isHiddenBottom?: boolean; //设置是否隐藏底部抬起
    allowScreenRecord?: boolean;

    sipId: string;
    /**小程序有效：是否点击输入框时展示键盘, 默认为true，需要自定义oninputClick事件时请将该值设置为false */
    isClickShowboard?: boolean;
    placeholder?: string; // 占位文本
    serverRandom?: string; //服务器随机数
    maxLength?: number; //最大长度
    minLength?: number; //最小长度
    isGrid?: boolean; //是否网格显示，h5版本不支持
    keyboardType?: number; // 0: 数字键盘 1: 全键盘
    displayMode?: number; //显示模式 0 light  h5版本不支持
    orderType?: number; //设置乱序类型 0不乱序
    ref?: any;

    bindinputClick?: (res: any) => void;
    onOnError?: (error: any) => void;
    onHideKeyboard?: () => void;
    onShowKeyboard?: () => void;
    onKeyboardInit?: () => void;
    bindinputChangeCallBack?: (res: any) => void;
    binddoneClick?: () => void;
    bindhideKeyboard?: () => void;
    bindshowKeyboard?: () => void;
    bindonError?: (error: any) => void;
    bindkeyboardInit?: () => void;

    onInputChangeCallBack?: (sipId: string, length: number) => void; // 输入框内容变化回调
    onDoneClick?: () => void; //完成按钮点击回调
    onInputClick?: (res: any) => void; //输入框点击回调

    style?: CSSProperties;
  }
}

declare module 'react' {
  function forwardRef<T, P = {}>(
    render: (props: P, ref: React.Ref<T>) => React.ReactElement | null,
  ): (props: P & React.RefAttributes<T>) => React.ReactElement | null;
}

declare module '*.png';


declare module 'axios' {
  interface AxiosRequestConfig {
    args?: Record<string, any>;
  }
}

