import {ForwardRefRenderFunction, forwardRef, useImperativeHandle} from 'react';
import {PageInstance, getCurrentInstance} from '@tarojs/taro';
import {showToast} from '../../utils/common';
import './index.scss';

interface Props {
  onSuccess?: (ticket: string) => void;
  onCancel?: () => void;
  onError?: (res: any) => void;
}

export interface CyCaptchaT {
  show: () => void;
}
/**
 * 组件名称：人机校验组件
 * 组件描述：人机校验
 */
const CyCaptcha: ForwardRefRenderFunction<CyCaptchaT, Props> = ({onSuccess, onCancel, onError}, ref) => {
  useImperativeHandle(ref, () => ({
    show: () => {
      handlerCaptchaShow();
    },
  }));
  // 获取页面实例
  const {page} = getCurrentInstance();
  // 弹出验证码
  const handlerCaptchaShow = () => {
    const pageInstance = page as PageInstance;
    const captcha: any = pageInstance.selectComponent && pageInstance?.selectComponent('#captcha');
    try {
      captcha?.show();
    } catch (error) {
      // 进行业务逻辑，若出现错误需重置验证码，执行以下方法
      captcha?.refresh();
    }
  };
  const handlerVerify = (res: any) => {
    console.log('handlerVerify', res);
    if (res.detail.ret === 0) {
      const ticket = res.detail.ticket;
      console.log('验证成功', res, ticket);
      onSuccess?.(ticket);
    } else if (res.detail.ret === 2) {
      console.log('用户主动关闭验证码！');
      onCancel?.();
    } else {
      if (onError) {
        onError(res);
      } else {
        showToast('人机验证失败');
      }
    }
  };
  return <t-captcha id='captcha' appId={process.env.TARO_APP_CAPTCHA_ID} onVerify={handlerVerify} />;
};

export default forwardRef(CyCaptcha);
