import {Image, View} from '@tarojs/components';
import AddBankCard from '../AddBankCard';
import styles from './index.module.scss';
import ImgIcon from '~images/bankcard/<EMAIL>'
import { router } from '~utils/common';

export default () => {
  const handleAddBankCardClick = () => {
    router.push({
      url: '/modules/bankcard/bankcard-binding/index',
    });
  }
  return (
    <View className={styles.cardContainer}>
      <View className={styles.cardMain}>
        <View className={styles.cardImg}>
          <Image src={ImgIcon} style={{width: '100%', height: '100%'}} />
        </View>
        <View className={styles.cardTitle}>暂无绑定银行卡</View>
        <View className={styles.cardDesc}>绑定银行卡即享借还款业务</View>
      </View>
      <AddBankCard  onClick={handleAddBankCardClick} />
    </View>
  );
};
