import React from 'react';
import { View } from '@tarojs/components';
import styles from './index.module.scss';

interface CreditCardSkeletonProps {
  className?: string;
}

const CreditCardSkeleton: React.FC<CreditCardSkeletonProps> = ({ className }) => {
  return (
    <View className={`${styles.skeletonCard} ${className || ''}`}>
      {/* 顶部标签 */}
      <View className={styles.topTag}>
        <View className={styles.skeletonRect} style={{ width: '60px', height: '20px' }} />
      </View>
      
      {/* 主要内容区域 */}
      <View className={styles.mainContent}>
        {/* 标题 */}
        <View className={styles.titleSection}>
          <View className={styles.skeletonRect} style={{ width: '80px', height: '16px' }} />
        </View>
        
        {/* 金额 */}
        <View className={styles.amountSection}>
          <View className={styles.skeletonRect} style={{ width: '160px', height: '36px' }} />
        </View>
        
        {/* 按钮区域 */}
        <View className={styles.buttonSection}>
          <View className={styles.skeletonButton} style={{ width: '80px' }} />
          <View className={styles.skeletonButton} style={{ width: '80px' }} />
        </View>
        
        {/* 底部信息区域 */}
        <View className={styles.bottomInfo}>
          <View className={styles.infoItem}>
            <View className={styles.skeletonRect} style={{ width: '60px', height: '12px' }} />
            <View className={styles.skeletonRect} style={{ width: '40px', height: '16px' }} />
          </View>
          <View className={styles.infoItem}>
            <View className={styles.skeletonRect} style={{ width: '50px', height: '12px' }} />
            <View className={styles.skeletonRect} style={{ width: '45px', height: '16px' }} />
          </View>
          <View className={styles.infoItem}>
            <View className={styles.skeletonRect} style={{ width: '40px', height: '12px' }} />
            <View className={styles.skeletonRect} style={{ width: '35px', height: '16px' }} />
          </View>
        </View>
        
        {/* 底部提示文字 */}
        <View className={styles.bottomTip}>
          <View className={styles.skeletonRect} style={{ width: '200px', height: '12px' }} />
        </View>
      </View>
    </View>
  );
};

export default CreditCardSkeleton;