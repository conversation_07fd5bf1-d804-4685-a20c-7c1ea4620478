{
  "env": {
    "browser": true,
    "es6": true
  },
  "plugins": ["html"],
  "extends": ["taro/react", "plugin:prettier/recommended"],
  "rules": {
    "react/jsx-uses-react": "off",
    "react/react-in-jsx-scope": "off",
    "react/no-direct-mutation-state": "off",
    "no-multi-assign": "error",
    "no-var": "error",
    "brace-style": "error",
    "comma-spacing": ["warn", {"before": false, "after": true}],
    "eqeqeq": "error",
    "react/jsx-props-no-multi-spaces": "error",
    "object-shorthand": "warn",
    "no-new-object": "warn",
    "prefer-template": "error",
    "quotes": ["error", "single"],
    "object-curly-spacing": ["error", "never"],
    "comma-dangle": [
      "error",
      {
        "arrays": "only-multiline",
        "objects": "only-multiline",
        "imports": "only-multiline",
        "exports": "only-multiline",
        "functions": "ignore",
      },
    ],
  },
  "ignorePatterns": ["*.config.js"],
  "exclude": ["src/modules/verify_mpsdk/**/*"]
}
