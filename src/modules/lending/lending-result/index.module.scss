.page_lending_result {
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  background-color: #fff;
  padding: 160px 40px 0;
  min-height: 100vh;
  box-sizing: border-box;
  text-align: center;
}
.result_img {
  width: 280px;
  height: 280px;
}

.result_value {
  margin-top: 120px;
  margin-bottom: 80px;
}
.result_text {
  color: #333;
  font-size: 32px;
  font-weight: bold;
  line-height: 1;
}
.result_note {
  font-size: 24px;
  color: #999;
  margin-top: 30px;
  line-height: 1;
}

.lending_info {
  width: 100%;
  padding: 40px;
  box-shadow: 0px 0px 8px 0px rgba(176,176,176,0.3);
  border-radius: 16px;
  box-sizing: border-box;
  margin-bottom: 24px;
  .info_title {
    position: relative;
    color: #333;
    font-size: 32px;
    font-weight: bold;
    line-height: 1;
    margin-bottom: 65px;
    text-align: left;
    &::after {
      content: '';
      width: 4px;
      height: 100%;
      background-color: #2F54EB;
      position: absolute;
      left: -40px;
      top: 0;
    }
  }
  .info_content {
    display: flex;
    font-size: 28px;
    line-height: 1;
    margin-bottom: 64px;
    &:last-child {
      margin-bottom: 0;
    }
    .label {
      width: 150px;
      color: #999;
      text-align: left;
    }
    .value {
      flex: 1;
      color: #333;
      text-align: right;
      .value_note {
        color: #999;
        font-size: 24px;
        margin-top: 16px;
      }
    }
  }
}

.result_tip {
  position: absolute;
  bottom: 40px;
  width: 100%;
  color: #333;
  font-size: 24px;
}
.result_btn {
  position: absolute;
  bottom: 40px;
  width: calc(100% - 80px);
}