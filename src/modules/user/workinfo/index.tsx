import { useEffect, useRef, useState } from 'react';
import { Cascader } from '@taroify/core';
import { Button, Image, Picker, Text, View, Input } from '@tarojs/components';
import { useReady } from '@tarojs/taro';
import Taro from '@tarojs/taro';
import { CyButton, CyModal, CyPopup } from 'hncy58-taro-components';
import { IPickerItem } from 'src/types/common-types';
import Card from '~components/Card';
import CellItem from '~components/CellItem';
import CellPickerItem, { PickerMode } from '~components/CellPickerItem';
import CySearchInput from '~components/CySearchInput';
import PageWrap from '~components/PageWrap';
import Steps, { Step } from '~components/Steps';
import useDict, { sortDictItems } from '~hooks/useDict';
import useFace from '~hooks/useFace';
import arrowIcon from '~images/icon/<EMAIL>';
import { getOccupationList } from '~services/common';
import { searchCompanyList } from '~services/system';
import { getOccupationInfo, updateCompanyInfo } from '~services/user';
import usePersonInfoStore from '~store/personInfo';
import useUserStore from '~store/user';
import { getLabelByValue, handleMap, hideLoading, router, showLoading, showToast } from '~utils/common';
import { regExpMap } from '~utils/regex';
import { ValidationRule, validateForm } from '~utils/validate';
import styles from './index.module.scss';
import RequiredLabel from '../baseinfo/components/RequireLabel';
const infoSteps: Step[] = [
  {
    title: '基本信息',
    status: 'process',
  },
  {
    title: '单位信息',
    status: 'process',
  },
  {
    title: '联系人信息',
    status: 'process',
  },
];

export default () => {
  const { startFace } = useFace();
  const { user } = useUserStore();
  const { setCompanyInfo, resetCompanyInfo, companyInfo } = usePersonInfoStore();

  const [current, setCurrent] = useState<number>(2);
  const [industryMap, setIndustryMap] = useState<IPickerItem[]>([]);
  const [employmentStatusMap, setEmployeeStatusMap] = useState<IPickerItem[]>([]);
  const [enterpriseTypeMap, setEnterpriseTypeMap] = useState<IPickerItem[]>([]);
  const [dutyMap, setDutyMap] = useState<IPickerItem[]>([]);
  const [positionalTitleMap, setPositionalTitleMap] = useState<IPickerItem[]>([]);
  const occupationMap = useRef<API.IOccupation[]>([]);
  const [region, setRegion] = useState<string[]>([]);
  const [occupationPopupVisible, setOccupationPopupVisible] = useState<boolean>(false);

  const [occupationValue, setOccupationValue] = useState<string[]>([]);
  const [notFromMine, setNotFromMine] = useState<boolean>(true);
  const [formData, setFormData] = useState<API.companyInfoParams>({
    employeeStatus: '',
    company: '',
    enterpriseType: '',
    industry: '',
    companyAddress: {
      province: '',
      city: '',
      district: '',
      detailAddress: '',
    },
    detailAddress: '',
    occupation: '',
    middleOccupation: '',
    littleOccupation: '',
    duty: '',
    positionalTitle: '',
    occupationName: '',
  });

  const formRules: Record<string, ValidationRule[]> = {
    employeeStatus: [
      {
        required: true,
        message: '请选择就业状况',
      },
    ],
    company: [
      {
        required: true,
        message: '请输入单位名称',
      },
      {
        pattern: regExpMap.text,
        message: '单位名称不能为纯数字或纯特殊字符',
      },
    ],
    enterpriseType: [
      {
        required: true,
        message: '请选择单位性质',
      },
    ],
    industry: [
      {
        required: true,
        message: '请选择行业类别',
      },
    ],
    region: [
      {
        required: true,
        message: '请选择所在地区',
      },
    ],
    detailAddress: [
      {
        required: true,
        message: '请填写详细地址',
      },
      {
        pattern: regExpMap.text,
        message: '详细地址中包含不支持的字符，请修改',
      },
    ],
    occupation: [
      {
        required: true,
        message: '请选择职业',
      },
    ],
    duty: [
      {
        required: true,
        message: '请选择职务',
      },
    ],
    positionalTitle: [
      {
        required: true,
        message: '请选择职称',
      },
    ],
  };

  useEffect(() => {
    const params = Taro.getCurrentInstance().router?.params;
    setNotFromMine(params?.from !== 'mine');
    getDictMap();
    queryOccupationList();
  }, []);

  useReady(() => {
    console.log('页面ready', companyInfo);
    if (companyInfo && companyInfo.custUid === user.custUid) {
      CyModal.create({
        title: '温馨提示',
        content: '检测到有尚未保存的信息，是否继续填写？',
        confirmText: '确定',
        isShowCancel: true,
        onConfirm() {
          bindCompanyInfo(companyInfo);
        },
        onCancel() {
          queryCompanyInfo();
        },
      });
    } else {
      queryCompanyInfo();
      resetCompanyInfo();
    }
  });

  const getDictMap = async () => {
    const res = await Promise.all([
      useDict('INDUSTRY'),
      useDict('EMPLOYEE_STATUS'),
      useDict('ENTERPRISE_TYPE'),
      useDict('DUTY'),
      useDict('POSITIONAL_TITLE'),
    ]);
    if (res.length) {
      setIndustryMap(res[0].dictMap);
      setEmployeeStatusMap(res[1].dictMap);
      setEnterpriseTypeMap(res[2].dictMap);
      setDutyMap(sortDictItems(res[3].dictMap));
      setPositionalTitleMap(sortDictItems(res[4].dictMap));
    }
  };

  const queryCompanyInfo = async () => {
    try {
      showLoading();
      const res = await getOccupationInfo();
      console.log('queryCompanyInfo', 2222);
      bindCompanyInfo(res);
    } catch (error) {
      console.log(error);
    } finally {
      hideLoading();
    }
  };

  const bindCompanyInfo = (info: API.companyInfoParams) => {
    const { city = '', province = '', district = '', detailAddress = '' } = info.companyAddress;
    const { occupation = '', middleOccupation = '', littleOccupation = '' } = info;
    console.log('name', occupationMap.current);
    const name = `${findNameByCode(occupation, occupationMap.current)}/${findNameByCode(middleOccupation, occupationMap.current)}/${findNameByCode(littleOccupation, occupationMap.current)}`;
    setFormData({
      ...info,
      detailAddress: info.detailAddress || detailAddress,
      occupationName: name,
    });
    setRegion(province ? [province, city, district] : []);
    setOccupationValue([occupation, middleOccupation, littleOccupation]);
  };

  const queryOccupationList = async () => {
    const data = await getOccupationList();
    if (Array.isArray(data)) {
      occupationMap.current = data;
    }
  };

  const updateFormData = (kv: Partial<API.companyInfoParams>) => {
    setCompanyInfo({
      ...formData,
      ...kv,
      ...{ custUid: user.custUid },
    });
    setFormData({
      ...formData,
      ...kv,
    });
  };

  const transformRegion = (value: string[]) => {
    if (value.length) {
      return {
        province: value[0],
        city: value[1],
        district: value[2],
        detailAddress: formData.detailAddress,
      };
    }
    return {
      province: '',
      city: '',
      district: '',
      detailAddress: formData.detailAddress,
    };
  };

  const findNameByCode = (code: string, map: API.IOccupation[]): string => {
    for (const node of map) {
      if (node.code === code) {
        return node.name;
      }
      if (node.children && node.children.length > 0) {
        const result = findNameByCode(code, node.children);
        if (result) {
          return result;
        }
      }
    }
    return '';
  };

  const goNext = async () => {
    // TODO: 校验表单 + 提交数据
    console.log('goNExt====', formData);

    const validationData = {
      ...formData,
      region,
    };
    const result = validateForm(validationData, formRules);

    if (!result.valid && result.errors) {
      const firstError = Object.values(result.errors)[0];
      showToast(firstError);
      return;
    }
    startFace({
      requestFrom: 'updateCustomerInfo',
      faceSuccess: () => {
        handleSubmit();
      },
      faceFail: () => {
        showToast('活体识别失败');
      },
    });
  };

  const handleSubmit = async () => {
    try {
      showLoading();
      const companyAddress = {
        province: region[0],
        city: region[1],
        district: region[2],
        detailAddress: formData.detailAddress,
      };
      const res = await updateCompanyInfo({ ...formData, companyAddress });
      hideLoading();
      resetCompanyInfo();
      showToast({
        message: '保存成功',
        type: 'success',
      });
      await new Promise(resolve => setTimeout(resolve, 2000));
      if (notFromMine) {
        router.push({
          url: '/modules/user/contactinfo/index',
        });
      } else {
        router.back();
      }
    } catch (error) {
      hideLoading();
      console.log('请求错误', error);
    }
  };

  const handleCompanySelect = (company: any) => {
    updateFormData({ company: company.title });
  };
  return (
    <PageWrap className={`${styles.baseinfoPage} ${notFromMine ? '' : styles.pdt20}`}>
      {notFromMine && (
        <View className={styles.pageTop}>
          <Steps steps={infoSteps} current={current} />
        </View>
      )}
      <View className={styles.pageContent}>
        <Card title='单位信息' note='完善个人单位、行业等职业信息'>
          <CellPickerItem
            className={styles.CellItem}
            title={<RequiredLabel label='就业状况' required />}
            placeholder='请选择就业状况'
            range={handleMap(employmentStatusMap, '90')}
            value={getLabelByValue(employmentStatusMap, formData.employeeStatus + '')}
            pickValue={formData.employeeStatus + ''}
            onChange={employeeStatus => updateFormData({ employeeStatus })}
            clickable
          />
          <CellItem
            className={styles.CellItem}
            title={<RequiredLabel label='单位名称' required />}
            right={
              <CySearchInput
                placeholder='请输入单位名称'
                value={formData.company}
                onInput={value => updateFormData({ company: value })}
                onSelect={handleCompanySelect}
                searchFn={searchCompanyList}
                keyExtractor={item => item.creditNo || ''}
                inputClassName={styles['input']}
              />
            }
            clickable
          />
          <CellPickerItem
            range={handleMap(enterpriseTypeMap, '50')}
            value={getLabelByValue(enterpriseTypeMap, formData.enterpriseType + '')}
            pickValue={formData.enterpriseType + ''}
            onChange={enterpriseType => updateFormData({ enterpriseType })}
            className={styles.CellItem}
            title={<RequiredLabel label='单位性质' required />}
            placeholder='请选择单位性质'
            clickable
          />
          <CellPickerItem
            className={styles.CellItem}
            value={getLabelByValue(industryMap, formData.industry + '')}
            pickValue={formData.industry + ''}
            range={industryMap}
            title={<RequiredLabel label='行业类别' required />}
            placeholder='请选择您所在行业'
            onChange={industry => updateFormData({ industry })}
            clickable
          />
          <CellPickerItem
            className={styles.CellItem}
            value={region.join('/')}
            mode={PickerMode.Region}
            title={<RequiredLabel label='所在地区' required />}
            placeholder='请选择单位所在地区'
            onChange={(value, label) => {
              const labelArr = label.split('/');
              const companyAddress = transformRegion(labelArr);
              setRegion(labelArr);
              updateFormData({ companyAddress });
              setCompanyInfo({
                ...formData,
                ...{ custUid: user.custUid },
                companyAddress,
              });
            }}
            clickable
          />
          <CellItem
            className={styles.CellItem}
            title={<RequiredLabel label='详细地址' required />}
            right={
              <Input
                placeholderClass={styles.inputPlaceholder}
                className={styles.rightInput}
                placeholder='请输入单位详细地址'
                value={formData.detailAddress}
                onInput={e => updateFormData({ detailAddress: e.detail.value })}
              />
            }
            clickable
          />
          <CellItem
            className={styles.CellItem}
            title={<RequiredLabel label='职业' required />}
            right={
              <View
                onClick={() => {
                  setOccupationPopupVisible(true);
                }}
                className={styles.rightClickable}>
                {formData.occupationName ? (
                  <Text>{formData.occupationName}</Text>
                ) : (
                  <Text className={styles.rightClickablePlaceholder}>请选择工作职业</Text>
                )}
                <Image src={arrowIcon} className={styles.rightClicakableArrow} />
              </View>
            }
            clickable
          />
          <CellPickerItem
            range={handleMap(dutyMap, '4')}
            className={styles.CellItem}
            value={getLabelByValue(dutyMap, formData.duty + '')}
            pickValue={formData.duty + ''}
            title={<RequiredLabel label='职务' required />}
            placeholder='请选择工作职位'
            clickable
            onChange={duty => updateFormData({ duty })}
          />
          <CellPickerItem
            range={handleMap(positionalTitleMap, '1')}
            className={styles.CellItem}
            value={getLabelByValue(positionalTitleMap, formData.positionalTitle + '')}
            pickValue={formData.positionalTitle + ''}
            title={<RequiredLabel label='职称' required />}
            placeholder='请选择职称'
            clickable
            onChange={positionalTitle => updateFormData({ positionalTitle })}
          />
        </Card>
        <CyPopup
          title='请选择职业'
          closeOnMaskClick
          onClose={() => {
            setOccupationPopupVisible(false);
          }}
          visible={occupationPopupVisible}>
          <Cascader
            options={occupationMap.current}
            className={styles.rightInput}
            fieldNames={{
              label: 'name',
              value: 'code',
              children: 'children',
            }}
            // value={[formData.occupation, formData.middleOccupation, formData.littleOccupation]}
            value={occupationValue}
            onSelect={setOccupationValue}
            onChange={(val, options) => {
              const name = options.map(item => item.children).join('/');
              setOccupationPopupVisible(false);
              updateFormData({
                occupation: val[0],
                middleOccupation: val[1],
                littleOccupation: val[2],
                occupationName: name,
              });
            }}
          />
        </CyPopup>
      </View>
      <View className={`${styles['bottom-fixed']} bottom-fixed`}>
        <CyButton round block type='primary' onClick={goNext} className={styles.btn}>
          {notFromMine ? '下一步' : '提交'}
        </CyButton>
      </View>
    </PageWrap>
  );
};
