import type {UserConfigExport} from '@tarojs/cli';
import path from 'path';
import GitImageDiffZipPlugin from './plugins/GitImageDiffZipPlugin';

export default {
  // mini: {},
  h5: {
    /**
     * WebpackChain 插件配置
     * @docs https://github.com/neutrinojs/webpack-chain
     */
    // webpackChain (chain) {
    //   /**
    //    * 如果 h5 端编译后体积过大，可以使用 webpack-bundle-analyzer 插件对打包体积进行分析。
    //    * @docs https://github.com/webpack-contrib/webpack-bundle-analyzer
    //    */
    //   chain.plugin('analyzer')
    //     .use(require('webpack-bundle-analyzer').BundleAnalyzerPlugin, [])
    //   /**
    //    * 如果 h5 端首屏加载时间过长，可以使用 prerender-spa-plugin 插件预加载首页。
    //    * @docs https://github.com/chrisvfritz/prerender-spa-plugin
    //    */
    //   const path = require('path')
    //   const Prerender = require('prerender-spa-plugin')
    //   const staticDir = path.join(__dirname, '..', 'dist')
    //   chain
    //     .plugin('prerender')
    //     .use(new Prerender({
    //       staticDir,
    //       routes: [ '/pages/index/index' ],
    //       postProcess: (context) => ({ ...context, outputPath: path.join(staticDir, 'index.html') })
    //     }))
    // }

    // 本地图片替换为OSS地址
    imageUrlLoaderOption: {
      limit: 1024,
      name: 'images/[name].[contenthash:8][ext]',
      publicPath: 'https://hncy58test-cyd.oss-cn-beijing.aliyuncs.com/multi-platform/',
    },
    webpackChain(chain) {
      // 生成上传OSS的zip文件
      chain.plugin('GitImageDiffZipPlugin').use(GitImageDiffZipPlugin, [
        {
          outputPath: 'dist',
          zipName: 'modified-images.zip',
          pattern: /\.(png|jpe?g|gif|svg)$/i,
          excludes: [/\/static\//],
        },
      ]);
    },
  },
  mini: {
    // 本地图片替换为OSS地址
    imageUrlLoaderOption: {
      limit: 1024,
      name: 'images/[name].[contenthash:8][ext]',
      publicPath: 'https://hncy58test-cyd.oss-cn-beijing.aliyuncs.com/multi-platform/',
    },
    webpackChain(chain) {
      // 生成上传OSS的zip文件
      // chain.plugin('GitImageDiffZipPlugin').use(GitImageDiffZipPlugin, [
      //   {
      //     outputPath: 'dist',
      //     zipName: 'modified-images.zip',
      //     pattern: /\.(png|jpe?g|gif|svg)$/i,
      //     excludes: [/\/static\//],
      //   },
      // ]);
    },
  },
} satisfies UserConfigExport<'webpack5'>;
