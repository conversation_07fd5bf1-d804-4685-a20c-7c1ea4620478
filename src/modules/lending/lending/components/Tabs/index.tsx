import {ScrollView, View} from '@tarojs/components';
import styles from './index.module.scss';

// Tab 类型定义
export interface TabItemProps {
  // 选中的key值
  key: string;
  // 标签标题
  title: React.ReactNode;
  // 标签内容
  content?: React.ReactNode;
}

interface TabsProps {
  tabs: TabItemProps[];
  activeKey: string;
  onChange?: (key: string) => void;
}

const Tabs = ({tabs, onChange, activeKey = tabs[0].key}: TabsProps) => {
  const handleTabClick = (tab: TabItemProps) => {
    if (onChange) {
      onChange(tab.key);
    }
  };

  return (
    <View className={styles.tabs_container}>
      {/* Tab 标签栏 */}
      <ScrollView scrollX showScrollbar={false}>
        <View className={styles.tabs_header}>
          {tabs.map(tab => (
            <View
              key={tab.key}
              className={`${styles.tab_item} ${activeKey === tab.key ? styles.active : ''}`}
              onClick={() => handleTabClick(tab)}>
              {tab.title}
            </View>
          ))}
        </View>
      </ScrollView>

      {/* Tab 内容区域 */}
      <View className={styles.tabs_content_wrapper}>
        <View className={styles.tabs_content}>
          {tabs.map(
            tab =>
              activeKey === tab.key && (
                <View key={`${tab.key}-content`} className={styles.tab_panel}>
                  {tab.content}
                </View>
              ),
          )}
        </View>
      </View>
    </View>
  );
};

export default Tabs;
