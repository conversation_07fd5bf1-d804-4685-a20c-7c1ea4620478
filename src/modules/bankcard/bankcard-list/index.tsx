import PageWrap from '~components/PageWrap';
import List from './components/List';
import ListEmpty from './components/ListEmpty';
import { getBankCardList } from '~services/bankcard';
import { useEffect, useState } from 'react';
import { useDidShow } from '@tarojs/taro';
import styles from './index.module.scss';
import { hideLoading, showLoading } from '~utils/common';

const BankCardList = () => {
  const [bankcardList, setBankCardList] = useState<API.IBankCardInfo[]>([]);
  const [loading, setLoading] = useState<boolean>(false)

  useDidShow(() => {
    getBackcardData()
  })
  const getBackcardData = async () => {
    try {
      showLoading();
      const res = await getBankCardList();
      setBankCardList(res);
    } catch (error) {
      console.log('请求错误', error);
    } finally {
      hideLoading();
      setLoading(true)
    }
  }
  return (
    <>
    {
      loading ? 
        <PageWrap className={styles.listPageWrap}>
          {bankcardList.length === 0 ? <ListEmpty /> : <List list={bankcardList} />}
        </PageWrap>
        : null
    }
    </>
  );
};
export default BankCardList;
