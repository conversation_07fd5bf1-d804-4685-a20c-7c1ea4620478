import {ComponentType, useEffect, useRef, useState} from 'react';
import {Canvas, Image, Input, InputProps, Text, View} from '@tarojs/components';
import Taro, {useLoad} from '@tarojs/taro';
import {CyButton, CyModal, CySafeArea} from 'hncy58-taro-components';
import Card from '~components/Card';
import CyContractBar from '~components/CyContractBar';
import PageWrap from '~components/PageWrap';
import Steps, {Step} from '~components/Steps';
import useFace from '~hooks/useFace';
import {applyCredit} from '~services/credit';
import {getIsNeedCompanyUpdate, register, updateComanyInfo} from '~services/user';
import {convertToTenThousand, formatDateYYYYMMDD, removeObjEmptyString, router, showToast} from '~utils/common';
import {getLocation} from '~utils/location';
import WorkForm from './components/WorkForm';
import styles from './index.module.scss';
import useCompanyInfoStore from '~store/companyInfo';

interface IDCardInfo {
  certId: string;
  name: string;
}
interface IDCardScanInfo extends IDCardInfo {
  validity: string;
}

const CompanyInfo = () => {
  /** 1. 变量定义 */
  const steps: Step[] = [
    {title: '身份核验', status: 'process'},
    {title: '完善信息', status: 'process'},
    {title: '申请额度', status: 'process'},
  ];

  /** 2. usestates + storestates */
  const workInfo = useRef<API.UpdateCompanyInfoReqParams>();

  /** 3. effects */

  /** 4. 方法定义 */
  const startSubmitWorker = async () => {
    if (!workInfo.current) {
      showToast('请完善公司信息');
      return;
    }

    if (!workInfo.current?.workType) {
      showToast('请选择工作性质');
      return;
    }

    if (!workInfo.current?.companyName && !workInfo.current?.workName) {
      showToast('请填写单位名称或职业名称');
      return;
    }

    if (!workInfo.current?.monthInCome) {
      showToast('请选择月收入');
      return;
    }

    // 上传公司信息
    useCompanyInfoStore.getState().setCompanyInfo({
      workName: workInfo.current?.workName || '',
      workType: workInfo.current?.workType || 'free',
      monthInCome: workInfo.current?.monthInCome || '',
      companyName: workInfo.current?.companyName || '',
    });
    useCompanyInfoStore.getState().setCompanyInfoUpdate(true);
    router.back();
  };

  const onWorkerInfoChange = (data: API.UpdateCompanyInfoReqParams) => {
    console.log('onWorkerInfoChange', data);
    workInfo.current = data;
  };

  /** 5. ui */
  return (
    <PageWrap className={styles['page-identify']}>
      <View className={styles['page-top']}>
        <Steps steps={steps} current={2} />
      </View>
      <View className={styles['page-content']}>
        <Card
          className={styles.card}
          title='请完善以下信息...'
          note='仅用于额度申请，我们会严格保密并保障您的个人信息安全'>
          <WorkForm onDataChange={onWorkerInfoChange} />
        </Card>
        <View className={styles['page-footer']}>
          <CyButton type='primary' size='large' block round onClick={startSubmitWorker}>
            提交
          </CyButton>
        </View>
      </View>
    </PageWrap>
  );
};

export default CompanyInfo;
