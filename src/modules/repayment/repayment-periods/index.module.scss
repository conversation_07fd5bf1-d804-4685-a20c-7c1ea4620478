

.periodsContainer {
  
}

.topAmountShortcut {
  position: fixed;
  top: 0;
  width: 100%;
  z-index: 9;
}

.bill {
  padding: 300px 20px 240px 20px;
}

.footerBarText {
  display: flex;
  align-items: center;
  font-size: $font-size-normal;
  color: $color-text-normal;
}
.footerBarSubTextRow2,
.footerBarSubText {
  font-size: $font-size-tiny;
  color: $color-text-secondary;
}
.footerBarSubTextRow2 {
  margin-left: 40px;
}
.numContainer {
  margin-left: 20px;
}
.num {
  color: $color-primary;
}