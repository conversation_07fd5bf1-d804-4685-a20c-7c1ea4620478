# 常见问题

## npm run build:rn 输出目录不对问题

使用 yarn 命令

```
yarn build:rn
```

## 使用 android studio 打开项目太慢

使用命令行打开

```
yarn android
```

## 安装 ios 依赖库 hermes-engine 太慢

先下载 hermes-engine 库到本地，设置 HERMES_ENGINE_TARBALL_PATH 环境变量，然后重新安装

```
export HERMES_ENGINE_TARBALL_PATH=本地路径
pod install
```

## 安装 react-android-0.73.4-debug.aar 慢

手动下载后放入
下载地址：https://repo.maven.apache.org/maven2/com/facebook/react/react-android/0.73.4/
放入本地地址：~/.gradle/caches/modules-2/files-2.1/com.facebook.react/react-android/0.73.4/ 下含有 pom 文件的文件夹下

## ios 报错 node 目录找不到

先删除 ios/.xcode.env.local，在重新运行

```
pod install
```

## 运行时修改 mode 或者更新 app.config 文件，但发现未更新成功

metro 缓存导致，尝试命令后加上 --reset-cache 清除缓存

```
yarn dev:rn --mode uat --reset-cache
```
## 升级Xcode15.3后 编译报错 error - Called object type 'facebook::flipper::SocketCertificateProvider' (aka 'int') is not a function or function pointer。 该问题是因为Flipper框架被废弃了，导致xcode无法识别

在iOS工程的Podfile文件中，注释掉:flipper_configuration => flipper_config,

执行pod install

## ios成功运行reactnative 服务后，打开模拟器时出现以下字样并卡住，无法打开模拟器
### Implicit dependency on target 'React-RCTSettings' in project 'Pods' via options '-lReact-RCTSettings' in build setting 'OTHER_LDFLAGS'

方案1：移除node_modules后重新安装后重新运行
```
rm -rf node_modules
yarn 
yarn upgradePeerdeps

```
方案2：通过vscode运行reactnative服务，在通过xcode运行模拟器
在vscode中启动reactnative，之后不用vscode打开模拟器，而是使用xcode启动模拟器
```
yarn dev:rn --mode sit --reset-cache
```
修改ios工程的AppDelegate.mm的getBundleURL返回值如为http://localhost:8081/index.bundle?platform=ios（可将localhost修改为本地ip）后，使用xcode启动ios项目
```
- (NSURL *)getBundleURL
{
#if DEBUG
  return [NSURL URLWithString:@"http://localhost:8081/index.bundle?platform=ios"];
#else
  return [[NSBundle mainBundle] URLForResource:@"main" withExtension:@"jsbundle"];
#endif
}
```
 方案3  安装bundle

 ```
cd ios
	
bundle config mirror.https://rubygems.org https://gems.ruby-china.com
bundle install
bundle exec pod install
```

## 如何使用CyScrollTable？特别需要注意在config.ts文件中将disableScroll设置为true 需要跟进数据内容去构件表头，dataIndex是字段的名，flex是列的宽度，如下的列表 

```javascript
const columns = [
  {
    title: '名称',
    dataIndex: 'name',
    flex: 0.4,
  },
  {
    title: '名称',
    dataIndex: 'loanPercent',
    flex: 0.333333,
  },
  {
    title: '名称',
    dataIndex: 'price',
    flex: 0.333333,
  },
  {
    title: '名称',
    dataIndex: 'chg',
    flex: 0.333333,
  }
]

const data = [
  {
    name: '测试数据',
    loanPercent: '测试数据',
    price: '测试数据',
    chg: '测试数据'
  }
]
```