.ellic {
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}
.cus_list {
  display: flex;
  flex-direction: column;
  background-color: #fff;
  border-radius: 8px;
  overflow: hidden;
}

.cus_list_item {
  padding: 37px 0;
  display: flex;
  align-items: center;
  justify-content: space-between;
  .cus_list_item_left {
    display: flex;
    align-items: center;
    width: 60%;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    .cus_list_item_thumb {
      width: 64px;
      height: 64px;
      border-radius: 50%;
      margin-right: 16px;
      overflow: hidden;
    }
    .cus_list_item_info {
      display: flex;
      justify-content: center;
      flex-direction: column;
      width: calc(100% - 80px);
      .cus_list_item_title {
        color: #333;
        font-size: 28px;
        line-height: 1;
        width: 100%;
        @extend .ellic;
      }
      .cus_list_item_note {
        color: #999;
        font-size: 24px;
        line-height: 1;
        width: 100%;
        @extend .ellic;
      }
      .cus_mb {
        margin-bottom: 15px;
      }
    }
  }
  .cus_list_item_right {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    width: 40%;
    .cus_list_item_right_text {
      color: $color-primary;
      font-size: 24px;
      margin-right: 24px;
      width: 100%;
      text-align: right;
      @extend .ellic;
    }
    .cus_list_item_sucess {
      color: $color-text-secondary;
    }
    .cus_list_item_right_img {
      width: 24px;
      height: 24px;
    }
  }
}

.cus_list_item.has_border {
  border-bottom: 1px solid #eaeaea;
}

.cus_list_item:last-child {
  border-bottom: none;
}
