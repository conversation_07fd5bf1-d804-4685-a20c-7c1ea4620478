.marquee_tip {
  overflow: hidden;
  white-space: nowrap;
  position: relative;
  width: 100%;
  height: 80px;
  line-height: 80px;
  padding-left: 70px; /* 为图标留出空间 */
  background-color: #E9EDFF;
  color: #616C92;
  font-size: 28px;
  box-sizing: border-box;
}

.marquee {
  display: inline-block;
  animation: marquee linear infinite;
  animation-delay: 2s;
}
.tip_icon {
  position: absolute;
  left: 0;
  z-index: 9;
  width: 70px;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}
.horn_img {
  width: 30px; /* 图标大小 */
  height: 30px;
  vertical-align: top;
}

.marquee_content {
  display: inline-block;
}

.loopNoGap {
  .marquee_content {
    margin-right: 40px;
  }
}

@keyframes marquee {
  0% {
    transform: translateX(0%);
  }
  100% {
    transform: translateX(-50%);
  }
}
