const regeneratorRuntime = require("./utils/regenerator-runtime/runtime");
const util = require("./utils/util");
const defaultConfig = require("./config.js").defaultConfig;
const extend = require("./utils/extend.js").extend;

/**
 * 初始化函数
 * @param {Object} options - 初始化选项
 */
var init = function (options) {
  wx.onNetworkStatusChange(function (res) {
    if (res.networkType !== "none" && res.networkType !== "2g") {
      wx.showToast({ title: "网络异常", icon: "none" });
    }
  });

  wx.onMemoryWarning(function () {
    wx.showToast({ title: "内存告警，有闪退风险", icon: "none" });
  });

  if (!wx.verifyBaseUrl) {
    wx.verifyBaseUrl = "https://faceid.qq.com";
  }

  wx.startVerify = function (params) {
    if (console.log("startVerify start, send data", params.data), params.data && params.fail && params.success) {
      if (params.data.endPath) {
        if (!util.validate(params.data.endPath, "end_path")) {
          let err = { ErrorCode: -100, ErrorMsg: "调用SDK失败，endPath格式错误！" };
          return void wx.showModal({ title: "提示", content: err.ErrorMsg, showCancel: false });
        }
        params.fail = function (result) {
          if (result.error_code !== -999) {
            let url = "";
            url = params.data.endPath.indexOf("?") >= 0 ? params.data.endPath + "&data=" + encodeURIComponent(JSON.stringify(result)) : params.data.endPath + "?data=" + encodeURIComponent(JSON.stringify(result));
            console.log(url);
            wx.navigateTo({
              url: url,
              fail: (e) => {
                console.log(e);
                wx.showModal({ title: "提示", content: e.errMsg, showCancel: false });
              }
            });
          } else {
            wx.navigateBack();
          }
        };
        params.success = function (result) {
          let url = "";
          url = params.data.endPath.indexOf("?") >= 0 ? params.data.endPath + "&data=" + JSON.stringify(result) : params.data.endPath + "?data=" + JSON.stringify(result);
          console.log(url);
          wx.redirectTo({
            url: url,
            fail: (e) => {
              console.log(e);
              wx.showModal({ title: "提示", content: e.errMsg, showCancel: false });
            }
          });
        };
      }
      wx.verifySuccessFunc = params.success;
      wx.verifyFailureFunc = params.fail;
      if (util.validate(params.data.token, "token")) {
        console.log("data is ok", params.data);
        wx.showLoading({ title: "加载中...", mask: true });
        getCmsConfig(params.data.token, function (configRes) {
          if (console.log(configRes), wx.hideLoading(), configRes.ErrorCode === 0) {
            extend(true, defaultConfig, configRes.Data.config);
            var config = defaultConfig;
            console.log("final cmsConfig");
            console.log(config);
            wx.verifySysInfo = wx.getSystemInfoSync();
            console.log(wx.verifySysInfo);
            var minVersion = wx.verifySysInfo.platform === "ios" ? config.JustForMp.iOSVerLimit : config.JustForMp.androidVerLimit;
            if (wx.verifySysInfo.platform !== "devtools" && minVersion && util.compareVersion(minVersion, wx.verifySysInfo.version) > 0) {
              return wx.hideLoading(), void wx.showModal({ title: "提示", content: `当前微信版本低于${minVersion}，无法使用该功能，请升级到最新微信版本后重试。`, showCancel: false });
            }
            if (wx.verifySysInfo.environment && wx.verifySysInfo.environment === "wxwork") {
              return wx.showModal({ title: "提示", content: "企业微信暂不支持使用此功能，请使用微信进行操作", showCancel: false }), false;
            }
            config = reviseCmsConfig("", config);
            wx.verify_CMSConfig = config;
            wx.verify_TOKEN = params.data.token;
            wx.verify_BizData = params.data;
            if (config.Common.IsWxNative && !config.Common.Flow.includes("Ocr") && config.Common.IsHideIndexWhenNative) {
              console.log("直接调用微信原生接口");
              let isVideo = config.Common.WxVerifyTypeIsVideo;
              let checkType = config.Common.WxCheckAliveType;
              util.startNativeVerify(isVideo, checkType, wx.verifyBaseUrl, params.data.token, wx.verifyFailureFunc, (res) => {
                let result = { BizToken: params.data.token, ErrorCode: res.ErrorCode, ErrorMsg: res.ErrorMsg };
                if (res.ErrorCode === 0) {
                  wx.verifySuccessFunc(result);
                } else {
                  wx.verifyFailureFunc(result);
                }
              }, params.cancel);
            } else {
              console.log("进入验证页面");
              wx.navigateTo({ url: "/modules/verify_mpsdk/index/index?isNotice=" + false });
            }
          } else {
            wx.showModal({ title: "提示", content: configRes.ErrorMsg, showCancel: false });
          }
        });
      } else {
        wx.showModal({ title: "提示", content: "调用SDK失败,token格式错误！", showCancel: false });
      }
    } else {
      var err = { ErrorCode: -100, ErrorMsg: "调用SDK失败，wx.startVerify传入参数缺少！" };
      wx.showModal({ title: "提示", content: err.ErrorMsg, showCancel: false });
    }
  };
};

/**
 * 获取CMS配置
 * @param {string} token - BizToken
 * @param {Function} callback - 回调函数
 */
async function getCmsConfig(token, callback) {
  try {
    var requestOptions = { method: "POST", url: `${wx.verifyBaseUrl}/api/auth/getConfig?BizToken=${token}` };
    var response = await util.requestPromise(requestOptions);
    if (response.statusCode === 200 && response.data.Data && response.data.ErrorCode === 0) {
      callback({ ErrorCode: 0, Data: response.data.Data });
    } else if (response.data.ErrorCode) {
      callback({ ErrorCode: response.data.ErrorCode, ErrorMsg: `获取配置失败,${response.data.ErrorMsg}` });
    } else {
      callback({ ErrorCode: -104, ErrorMsg: "调用失败，获取配置异常！" });
    }
  } catch (err) {
    console.log("genConfig catch error", err);
    if (err.errMsg.indexOf("request:fail Unable to resolve host") >= 0 || err.errMsg.indexOf("request:fail 似乎已断开与互联网的连接") >= 0) {
      callback({ ErrorCode: 101, ErrorMsg: "网络异常，请稍后重试" });
    } else if (err.errMsg === "request:fail url not in domain list") {
      callback({ ErrorCode: -104, ErrorMsg: "接口还未添加到服务器域名，请点击右上角三个点，打开调试模式再试" });
    } else {
      callback({ ErrorCode: -104, ErrorMsg: "调用接口失败: " + err.errMsg });
    }
  }
}

/**
 * 修订CMS配置
 * @param {string} param - 参数（未使用）
 * @param {Object} config - CMS配置
 * @returns {Object} 修订后的配置
 */
function reviseCmsConfig(param, config) {
  let flow = config.Common.Flow;
  let hideSms = false;
  let hideOcr = false;
  let justOcr = false;
  let justSms = false;
  let livingType = 0;
  let hasLive = flow.indexOf("LiveFour1V1") >= 0 || flow.indexOf("LiveAction1V1") >= 0 || flow.indexOf("LiveSilence1V1") >= 0;

  if (flow.indexOf("Sms") === -1) hideSms = true;
  if (flow.indexOf("Ocr") === -1) hideOcr = true;
  if (hasLive) {
    livingType = flow.indexOf("LiveFour1V1") >= 0 ? 0 : flow.indexOf("LiveAction1V1") >= 0 ? 1 : 2;
  }
  if (!hasLive && flow.indexOf("Sms") === -1) justOcr = true;
  if (!hasLive && flow.indexOf("Ocr") === -1) justSms = true;

  let revised = {
    Common: config.Common,
    protocol: {
      title: config.Index.ProtocolTitle,
      content: config.Index.TencentProtocol,
      clientContent: config.Index.ClientProtocol
    },
    page: {
      index: {
        clientName: config.Index.ProjectName,
        businessName: config.Index.BusinessName,
        certificationCenter: config.Index.CooperationName,
        nextBtnName: config.Index.NextBtn,
        isHideTipsLogo: !config.Common.IsShowLogo,
        isHideTipsAbout: config.Common.IsHideAbout,
        protocolTitle: config.Index.ProtocolEntrance
      },
      ocr: {
        backend: config.Ocr.Backend,
        sourceType: config.Ocr.SourceType,
        isAddress: config.Ocr.IsAddress,
        isManualInput: config.Ocr.IsManualInput,
        isHideTakePhoto: config.Ocr.IsHideManualInputTakePhotoBtn,
        isCheckIDInfo: config.Ocr.IsCheckIdInfo,
        allowModifyType: config.Ocr.AllowModifyType
      },
      livingbody: {
        silentRecordTime: config.LiveFour1V1.MaxDuration
      },
      success: {
        successTitle: config.Success.SubTipsName,
        successTips: config.Success.SuccessTips,
        isAutoSkip: config.Success.AutoSkip
      },
      sms: {},
      failpage: {
        isShowExitBtn: config.Fail.IsShowQuitBtn,
        exitBtnTtile: config.Fail.ExitBtnText,
        RetryBtnText: config.Fail.RetryBtnText
      }
    },
    runEnv: "release",
    navTitle: {
      smsTitle: config.Common.NavTitle.SmsTitle,
      ocrTitle: config.Common.NavTitle.OcrTitle,
      livingbodyTitle: config.Common.NavTitle.LivingbodyTitle,
      resultTitle: config.Common.NavTitle.ResultTitle
    },
    justForJumpVer: {
      title: config.Common.Title
    },
    about: {
      title: "关于腾讯云慧眼",
      content: "腾讯云慧眼由腾讯AI Lab、腾讯优图、腾讯数据平台部提供技术支持。"
    },
    isGetUserLocation: config.Index.IsGetLocation,
    isHideSmsPage: hideSms,
    isHideOcrPage: hideOcr,
    livingbodyType: livingType,
    isJustOcr: justOcr,
    isJustSms: justSms,
    failInfo: config.Fail.CustomFailInfo
  };

  var skip = {
    isHideSmsPage: revised.isHideSmsPage,
    isHideOcrPage: revised.isHideOcrPage,
    isJustSms: revised.isJustSms,
    isJustOcr: revised.isJustOcr,
    navTitle: revised.navTitle
  };
  revised.skipConfig = skip;

  if (revised.livingbodyType !== 0 && revised.livingbodyType !== 1) {
    var defaultTime = 4;
    var time = revised.page.livingbody.silentRecordTime;
    if (time && typeof time === "number" && time > 4) defaultTime = time;
    revised.page.livingbody.silentRecordTime = defaultTime;
  }

  let allowTypes = revised.page.ocr.allowModifyType.split("");
  revised.page.ocr.isIdnameAllowEdit = allowTypes[0] === "0";
  revised.page.ocr.isIdnumberAllowEdit = allowTypes[1] === "0";
  revised.page.ocr.isIdaddressAllowEdit = allowTypes[2] === "0";
  console.log(revised.page.ocr);

  return revised;
};

module.exports = { init: init };