import request from '../http/request';

export const enum RepaymentHistoryEnum {
  SUCCESS = 'SUCCESS',
  PROCESSING = 'PROCESSING',
  FAILED = 'FAILED',
}

/** 获取借据详情 */
export const getLoanDetail = (params: API.LoanReq) => {
  return request<API.Loan>({
    url: '/api/loan/detail',
    method: 'GET',
    params,
  });
};

/** 获取还款计划 */
export const getLoanRepaymentPlan = (params: {loanNo: string}) => {
  return request<API.LoanPlanReq>({
    url: '/api/loan/repayment/plan',
    method: 'GET',
    params,
  });
};

/** 获取还款记录 */
export const getLoanRepaymentHistory = (params: {loanNo: string; stateCategory: RepaymentHistoryEnum}) => {
  return request<API.RepaymentRecordReq>({
    url: '/api/loan/repayment/history',
    method: 'GET',
    params,
  });
};


/** 获取借据列表 */
export const getLoanList = (params?: API.LoanListReq) => {
  return request<API.LoanListRes>({
    url: '/api/loan/list',
    method: 'GET',
    params,
  });
};
