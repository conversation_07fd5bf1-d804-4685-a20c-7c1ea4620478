import {PropsWithChildren} from 'react';
import {View} from '@tarojs/components';
import styles from './index.module.scss';

export interface SectionProps extends PropsWithChildren {
  title: string;
}

export default ({title, children}: SectionProps) => {
  return (
    <View className={styles.section}>
      <View className={styles.sectionTitle}>{title}</View>
      <View>{children}</View>
    </View>
  );
};
