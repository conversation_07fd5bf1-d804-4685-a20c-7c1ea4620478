import { useEffect, useRef, useState } from 'react';
import { <PERSON><PERSON><PERSON><PERSON>, CySafeArea } from 'hncy58-taro-components';
import { Button, Checkbox, CheckboxGroup, Image, Input, Picker, Text, View } from '@tarojs/components';
import Taro, { App } from '@tarojs/taro';
import Card from '~components/Card';
import CellItem from '~components/CellItem';
import CellPickerItem from '~components/CellPickerItem';
import CyCheckbox from '~components/CyCheckbox';
import PageWrap from '~components/PageWrap';
import Steps, { Step } from '~components/Steps';
import contactIcon from '~icons/icon-clock.png';
import { getLabelByValue, hideLoading, router, showLoading, showToast } from '~utils/common';
import styles from './index.module.scss';
import { checkVerifyCodeReq, sendVerifyCodeReq, updateContactInfo } from '~services/user'
import useDict from '~hooks/useDict';
import { IPickerItem, smsSceneType } from '~src/types/common-types';
import useUserStore from '~store/user';
import CyVerificationCodeInput, { CyVerificationCodeInputRef } from '~components/CyVerificationCodeInput';
import CySafeKeyboard, { CySafeKeyboardRef } from '~components/CySafeKeyboard';
import { getRandomCode, setTradePassword, updateTradePassword } from '~services/password';

const infoSteps: Step[] = [
  {
    title: '基本信息',
    status: 'process',
  },
  {
    title: '单位信息',
    status: 'process',
  },
  {
    title: '联系人信息',
    status: 'process',
  },
];

export default () => {
  const user = useUserStore((state) => state.user);
  const keyboardRef = useRef<CySafeKeyboardRef>(null);
  const keyboardRef2 = useRef<CySafeKeyboardRef>(null);
  const keyboardRef3 = useRef<CySafeKeyboardRef>(null);
  const [passwordRandomCode, setPasswordRandomCode] = useState(''); // 密码键盘随机码
  const isLengthOk = useRef<boolean>(false);
  
  const onInputChangeCallBack = (spid: string, length: number) => {
    const res = keyboardRef.current?.getEncryptedInputValue();
    if(length === 6) {
      keyboardRef.current?.doneClick();
      isLengthOk.current = true;
    } else {
      isLengthOk.current = false;
    }
  };

  const startGetRandomCode = async () => {
    try {
      const randomCode = await getRandomCode('1');
      console.log('randomCode', randomCode);
      if (randomCode) {
        setPasswordRandomCode(randomCode);
      }
    } catch (error) {
      console.log('error', error);
    }
  }

  useEffect(() => {
    startGetRandomCode()
  }, [])

  const submit = async () => {
    const tradePaswordData = keyboardRef.current?.getEncryptedInputValue();
    const check = keyboardRef.current?.checkInputValueMatch('setPasswordConfirm');
    const randomCodeData = keyboardRef.current?.getEncryptedClientRandom();
    const oldTradePasswordData = keyboardRef3.current?.getEncryptedInputValue();
    const oldRandomeCodeData = keyboardRef3.current?.getEncryptedClientRandom();

    if(!oldTradePasswordData?.data) {
      showToast('请输入旧交易密码');
      return;
    }
    if(!oldTradePasswordData?.data) {
      showToast('请输入旧交易密码');
      return;
    }
    if (!isLengthOk.current) {
      showToast('密码均为6位数');
      return
    }

    if(!oldTradePasswordData?.data) {
      showToast('请输入旧交易密码');
      return;
    }
    
    if(!check) {
      showToast('两次输入的密码不一致');
      return;
    }
    if (tradePaswordData?.data) {
      try {
        showLoading();
        await updateTradePassword({
          newTradePassword: tradePaswordData.data,
          newRandomCode: randomCodeData?.data || '',
          oldRandomCode: oldRandomeCodeData?.data || '',
          oldTradePassword: oldTradePasswordData.data,
          keyboardType: '1',
        })
        hideLoading()
        router.replace({
          url: `/modules/user/password/success/index?type=change`
        })
      } catch (error) {
        console.log('error', error);
        hideLoading()
      }
    }
  }

  const onInputClick = (e: any) => {
    console.log('e?.detail?.sipId onInputClick', e?.detail?.sipId, e?.detail?.sipId === 'setPassword')
    if (e?.detail?.sipId === 'setPassword') {
      keyboardRef.current?.show();
      keyboardRef2.current?.hidden();
      keyboardRef2.current?.doneClick();
      keyboardRef3.current?.hidden();
      keyboardRef3.current?.doneClick();
    } else if (e?.detail?.sipId ==='setPasswordConfirm') {
      keyboardRef2.current?.show();
      keyboardRef.current?.hidden();
      keyboardRef.current?.doneClick();
      keyboardRef3.current?.hidden();
      keyboardRef3.current?.doneClick();
    } else {
      keyboardRef3.current?.show();
      keyboardRef.current?.hidden();
      keyboardRef.current?.doneClick();
      keyboardRef2.current?.hidden();
      keyboardRef2.current?.doneClick();
    }
  };


  return (
    <PageWrap className={styles.changePasswordPage}>
      <View className={styles.header}>
        <View className={styles.title}>修改交易密码</View>
        <View className={styles.subtitle}>修改您当前的交易密码</View>
      </View>
      <View className={styles.tip}>为保障您的资金安全，请勿向他人透露交易密码</View>
      <View className={styles.cardContent}>
        <View className={styles.passwordRow}>
          <CySafeKeyboard
            style={{ width: '100%', height: '100rpx', lineHeight: '100rpx', background: '#fff', fontSize: '32rpx' }}
            id='oldPassword'
            sipId='oldPassword'
            ref={keyboardRef3}
            placeholder='请输入旧交易密码'
            serverRandom={passwordRandomCode}
            onInputClick={onInputClick}
            // onDoneClick={onDoneClick}
            onInputChangeCallBack={onInputChangeCallBack}
            displayMode={0}
            maxLength={6}
            keyboardType={0}
            orderType={1} />
        </View>
        <View className={styles.passwordRow}>
          <CySafeKeyboard
            style={{ width: '100%', height: '100rpx', lineHeight: '100rpx', background: '#fff', fontSize: '32rpx' }}
            id='setPassword'
            sipId='setPassword'
            ref={keyboardRef}
            placeholder='请输入新交易密码'
            serverRandom={passwordRandomCode}
            onInputClick={onInputClick}
            // onDoneClick={onDoneClick}
            onInputChangeCallBack={onInputChangeCallBack}
            displayMode={0}
            maxLength={6}
            keyboardType={0}
            orderType={1} />
        </View>
        <View className={styles.passwordRow}>
          <CySafeKeyboard
            style={{ width: '100%', height: '100rpx', lineHeight: '100rpx', background: '#F6F6F6', fontSize: '32rpx' }}
            id='setPasswordConfirm'
            sipId='setPasswordConfirm'
            ref={keyboardRef2}
            placeholder='请再一次输入密码'
            serverRandom={passwordRandomCode}
            // onDoneClick={onDoneClick}
            onInputChangeCallBack={onInputChangeCallBack}
            onInputClick={onInputClick}
            displayMode={0}
            maxLength={6}
            keyboardType={0}
            orderType={1} />
        </View>
        <View className={styles.warnText}>交易密码由6位数字组成</View>
        <CyButton className={styles.btBtn} round block type='primary' onClick={submit}>
          提交
        </CyButton>
      </View>
    </PageWrap>
  );
};
