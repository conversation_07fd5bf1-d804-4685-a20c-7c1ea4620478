import {useEffect, useRef, useState} from 'react';
import {useForm} from 'react-hook-form';

const useFormComponent = (formControl: any, fieldName: string, setValue: any) => {
  const [value, setFieldValue] = useState('');
  const inputRef = useRef(null);

  useEffect(() => {
    // 当表单状态更新时，同步自定义组件的值
    setFieldValue(formControl.getFieldState(fieldName));
  }, [formControl, fieldName]);

  const handleChange = (changedVal: any) => {
    setFieldValue(changedVal);
    setValue(fieldName, value);
  };

  return {value, handleChange, inputRef};
};

export default useFormComponent;
