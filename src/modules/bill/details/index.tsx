import {useEffect, useState} from 'react';
import {View} from '@tarojs/components';
import Taro from '@tarojs/taro';
import PageWrap from '~components/PageWrap';
import {getLoanDetail} from '~services/bill';
import BillHeader from './components/BillHeader/BillHeader';
import LoanInfo from './components/LoanInfo/LoanInfo';
import RepaymentInfo from './components/RepaymentInfo/RepaymentInfo';
import RepaymentPlan from './components/RepaymentPlan/RepaymentPlan';
import styles from './index.module.scss';

const BillDetails = () => {
  const router = Taro.useRouter();
  const {loanNo, billNo} = router.params;
  const [billData, setBillData] = useState<API.Loan>({} as API.Loan);
  useEffect(() => {
    console.log('loan:', loanNo);
    console.log('billNo:', billNo);
    requestGetLoanDetail();
  }, [loanNo, billNo]);

  const requestGetLoanDetail = () => {
    Taro.showLoading({
      title: '加载中',
      mask: true,
    });
    getLoanDetail({[loanNo ? 'loanNo' : 'billNo']: loanNo ? loanNo : billNo})
      .then((res: API.Loan) => {
        setBillData(res ?? {});
      })
      .finally(() => {
        Taro.hideLoading();
      });
  };

  return (
    <PageWrap className={styles.container}>
      <View className={styles.content}>
        <BillHeader
          principal={billData.principal}
          loanStatusName={billData.loanStatusName}
          startDate={billData.startDate}
          term={billData.term}
        />
        <LoanInfo
          name={billData.name}
          certId={billData.certId}
          loanNo={billData.loanNo}
          loanProdName={billData.loanProdName}
          rate={billData.rate}
          startDate={billData.startDate}
          endDate={billData.endDate}
          term={billData.term}
          purposeName={billData.purposeName}
          contractNo={billData.contractNo}
          accountName={billData.accountName}
          accountNo={billData.accountNo}
          repayAccountName={billData.repayAccountName}
          repayAccountNo={billData.repayAccountNo}
          dueDate={billData.dueDate}
          repaymentTypeName={billData.repaymentTypeName}
        />
        {billData.overdueDays && (
          <RepaymentInfo
            overdueDays={billData.overdueDays}
            expireInterest={billData.expireInterest}
            expirePenalty={billData.expirePenalty}
          />
        )}

        {loanNo && <RepaymentPlan remainPrincipal={billData.remainPrincipal} loanNo={loanNo} />}
      </View>
    </PageWrap>
  );
};

export default BillDetails;
