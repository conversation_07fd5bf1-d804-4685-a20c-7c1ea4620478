import {useEffect, useState} from 'react';
import {CyButton, CyModal} from 'hncy58-taro-components';
import {Image, Text, View} from '@tarojs/components';
import Taro from '@tarojs/taro';
import PrivacyModalTopImg from '~images/index/privacy-modal-top.png';
import {router} from '~utils/common';
import {checkPrivacyCache, savePrivacyCache} from '~utils/privacyCache';
import styles from './index.module.scss';

const disagreeHandler = () => {
  Taro.exitMiniProgram();
};

const goPrivacyPage = () => {
  router.push({
    url: '/modules/agreement/privacy/index',
  });
};

// 导出显示隐私弹窗的方法，供其他组件调用
export const showPrivacyModal = (options?: { onAgree?: () => void }) => {
  const closeModal = CyModal.create({
    modalRender: (
      <View className={styles.privacyModal}>
        <View className={styles.privacyModalTitleWrap}>
          <View className={styles.privacyModalTitle}>隐私政策授权</View>
        </View>
        <View className={styles.privacyModalContent}>
          <View>
            我们深知隐私对您的重要性，因此制定了《隐私政策》以明确我们如何收集、使用、存储和共享您的个人信息。在使用我们的服务前，请您仔细阅读并确认同意以下条款：
          </View>
          <View>有关个人信息收集、使用更详细的约定，请您阅读以下协议，如您同意请点击"同意"开始接受我们的服务</View>
          <View>
            点击
            <Text className={styles.linkText} onClick={goPrivacyPage}>
              《隐私政策》
            </Text>
            查看全文
          </View>
        </View>
        <View className={styles.privacyModalFooter}>
          <CyButton
            block
            round
            type='primary'
            onClick={() => {
              savePrivacyCache();
              closeModal();
              // 执行同意后的回调函数
              options?.onAgree?.();
            }}>
            同意
          </CyButton>
          <View
            className={styles.privacyDisagreeBtn}
            onClick={() => {
              closeModal();
              showDisagreeModal(options);
            }}>
            不同意
          </View>
        </View>
      </View>
    ),
  });
  return closeModal;
};

// 导出显示不同意提示弹窗的方法
export const showDisagreeModal = (options?: { onAgree?: () => void }) => {
  const closeDisagreeModal = CyModal.create({
    modalRender: (
      <View className={styles.privacyModal}>
        <View className={styles.privacyModalTitleWrap}>
          <View className={styles.privacyModalTitle}>温馨提示</View>
        </View>
        <View className={styles.privacyModalContent}>
          <Image src={PrivacyModalTopImg} className={styles.privacyModalTopImg} />
          <View>
            为了您能正常使用长银五八金融服务，保障您的合法权益，需要您同意
            <Text className={styles.linkText} onClick={goPrivacyPage}>
              《隐私政策》
            </Text>
          </View>
        </View>
        <View className={styles.privacyModalFooter}>
          <CyButton
            block
            round
            type='primary'
            onClick={() => {
              savePrivacyCache();
              closeDisagreeModal();
              // 执行同意后的回调函数
              options?.onAgree?.();
            }}>
            同意
          </CyButton>
          <View
            className={styles.privacyDisagreeBtn}
            onClick={() => {
              closeDisagreeModal();
            }}>
            关闭
          </View>
        </View>
      </View>
    ),
  });
  return closeDisagreeModal;
};

// 检查隐私协议状态并自动显示弹窗的组件
export default () => {
  const [shouldShowModal, setShouldShowModal] = useState(true);
  
  useEffect(() => {
    const init = async () => {
      const hasValidCache = await checkPrivacyCache();
      if (hasValidCache) {
        setShouldShowModal(false);
      } else if (shouldShowModal) {
        showPrivacyModal();
      }
    };
    
    init();
  }, [shouldShowModal]);
  
  return null;
};
