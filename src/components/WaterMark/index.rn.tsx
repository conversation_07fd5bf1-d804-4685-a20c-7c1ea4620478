import React from 'react';
import {Dimensions} from 'react-native';
import * as RNSvg from 'react-native-svg';

const {Svg, G, Text} = RNSvg;
let {height, width} = Dimensions.get('window');
console.log(height, width);
type WaterMarkProp = {
  canvasWid: number; //水印画布的宽度
  canvasHei: number; //水印画布的高度
  txtSpace: number; //水印文字之间的行间距
  txtLines: number; //水印的列数
  txtOriginY: number; //文字距离画布上边界的距离
  txtFont: number; //水印字体大小
  txtColor: string; //水印字体颜色
  text: string; //水印文字内容
};

const _defaultProps: WaterMarkProp = {
  canvasWid: width, //水印画布的宽度
  canvasHei: height, //水印画布的高度
  txtSpace: 100, //水印文字之间的行间距
  txtLines: 3, //水印的列数
  txtOriginY: 60, //文字距离画布上边界的距离
  txtFont: 12, //水印字体大小
  txtColor: 'rgba(174,174,174,0.4)', //水印字体颜色
  text: '', //水印文字内容
};
const WaterMark = (props: Partial<WaterMarkProp>) => {
  const p: WaterMarkProp = {
    ..._defaultProps,
    ...props,
  };
  let {canvasWid, canvasHei, txtSpace, txtLines, txtOriginY, txtFont, txtColor, text} = {...p};
  canvasWid = canvasWid + 200;
  canvasHei = canvasHei + 200; // 增大宽度，防止边角留白
  let cos = Math.cos(270 * (180 / Math.PI));
  let space = txtSpace + txtFont * 2;
  let beveling = space / cos;
  let sideLength = canvasWid + canvasHei;
  let rowNum = Math.ceil(sideLength / beveling);
  let arr: React.ReactNode & any[] = [];
  let y = txtOriginY;
  for (let i = 0; i < rowNum; i++) {
    for (let j = 0; j < txtLines; j++) {
      let x = canvasWid - (canvasWid / (txtLines - 1)) * j;
      let svgTxt = (
        <G key={`${j}${i}`} transform='rotate(-12)' fill={txtColor} width='100' height='100'>
          <Text x={[x]} y={[y]} stroke='transparent' fontSize={txtFont}>
            {text}
          </Text>
        </G>
      );

      arr.push(svgTxt as never);
    }
    y = y + txtSpace;
  }
  return (
    <Svg
      pointerEvents='none'
      height={canvasHei}
      width={canvasWid}
      style={{
        backgroundColor: 'transparent',
        position: 'absolute',
        top: -100,
        left: -100,
      }} //画布默认是透明的
    >
      <G>{arr}</G>
    </Svg>
  );
};

export default WaterMark;
