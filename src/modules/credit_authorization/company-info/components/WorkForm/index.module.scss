.work-form-box {
  padding: 0 40px;
}
.row {
  display: flex;
  align-items: center;
  justify-content: space-between;
  min-height: 120px;
  font-size: 28px;
  padding: 10px 20px;
  position: relative;

  & + .row {
    border-top: 1px solid $color-border-normal;
  }

  .value {
    display: flex;
    width: 450px;
    flex-direction: row;
    justify-content: flex-end;
  }
}
.value {
  display: flex;
  flex-wrap: wrap;
  margin-left: -12px;

  &.placeholder {
    color: $color-text-secondary;
  }
}

.single-section-value {
  margin-top: -32px;
}
  
  
.option-item {
  border: 1px solid $color-text-placeholder !important;
  // margin: 20px 0;
  margin-left: 12px;
  // line-height: 64px;
  // height: 64px;
  padding: 14px 0;
  color: $color-text-regular;
  text-align: center;
  border-radius: 8px;
  width: 200px;
  box-sizing: border-box;
  font-size: 28px;

  &.min {
    width: 160px;
  }

  &.active {
    position: relative;
    border: 1px solid $color-primary !important;
    color: $color-primary;
    background: url('../../../../../assets/images/lending/<EMAIL>') no-repeat right -1px bottom -1px;
    background-size: 40px 40px;
  }
}

.input {
  width: 100%;
  text-align: right;
}