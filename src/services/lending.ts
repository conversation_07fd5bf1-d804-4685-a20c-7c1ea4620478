import request from '../http/request';
import { IRepaymentType } from 'src/types/common-types';

/** 放款前置流程判断 */
export const lendingPreProgress = () => {
  return request<API.ILendingPreProgressRes>({
    url: '/api/lending/pre-progress',
    method: 'GET',
  });
};

/** 借款检查 */
export const lendingPreCheck = () => {
  return request<API.ILendingPreCheckRes>({
    url: '/api/lending/lending-pre-check',
    method: 'GET',
    args: {
      needIntercept: true
    },
  });
};

/** 借款试算 */
export const repaymentTrail = (params: {
  loanAmount: string | number;
  loanTerm: number;
  repaymentType: IRepaymentType;
  fixedDueDate?: string;
}) => {
  return request<API.IRepaymentTrailRes>({
    url: '/api/lending/repayment/trail/v2',
    method: 'GET',
    params: {
      ...params
    }
  });
};

/** 发起借款 */
export const lendingApply = (params: {
  loanAmount: number | string;
  loanTerm: number;
  purpose: string;
  repaymentType: IRepaymentType;
  acctId: string;
  tradePassword: string;
  tradeRandomCode: string;
  customerGoodsIds?: string;
  keyboardType?: string;
  fixedDueDate?: number;
  needSupplyDocTypes?: string;
  longitude?: string;
  latitude?: string;
}) => {
  return request<API.ILendingApplyRes>({
    url: '/api/lending/apply',
    method: 'POST',
    data: {
      ...params
    }
  });
};

// 借款结果状态查询
export const getLendingState = (params: {billNo: string}) => {
  return request<API.ILendingStatusRes>({
    url: '/api/lending/state',
    method: 'GET',
    params
  });
};


// 反诈弹框
export const getLendingPopUp = (data: {clickType: 'YES' | 'NO' | 'KNOW' | 'FIND'}) => {
  return request<boolean>({
    url: '/api/lending/anti-fraud/pop-up',
    method: 'POST',
    data
  });
};