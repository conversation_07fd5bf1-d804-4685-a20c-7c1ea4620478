.card {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30px;
  background-color: #fff;
  border-radius: 8px;
  font-family: Source <PERSON>;
  font-size: 28px;
  line-height: 28px;
  color: #333333;
  margin-bottom: 20px;

  &-left {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    flex: 1;
    gap: 18px;

    &-item {
      display: flex;
      align-items: center;
      margin-bottom: 16px;
      &:last-child {
        margin-bottom: 0;
      }
    }
  }
  &-right {
    display: flex;
    align-items: center;
    &-text {
      font-family: Source Han Sans SC;
      font-weight: 500;
      font-size: 28px;
      color: #2f54eb;
      line-height: 28px;
    }
  }

  .itemIcon {
    width: 24px;
    height: 24px;
    margin-left: 8px;
  }
}
