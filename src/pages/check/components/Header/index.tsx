import React, {useContext} from 'react';
import {CyTheme} from 'hncy58-taro-components';
import {Image, View, ViewProps} from '@tarojs/components';
import clockIcon from '~icons/icon-clock.png';
import recycleIcon from '~icons/icon-recycle.png';
import fastIcon from '~icons/icon-fast.png';
import lightningIcon from '~icons/icon-lightning.png';
import regularIcon from '~icons/icon-regular.png';
import safeIcon from '~icons/icon-safe.png';
import logo from '~images/logo/<EMAIL>';
import styles from './index.module.scss';
import useUserStore from '~store/user';


export default () => {
  const {theme, setTheme} = useContext(CyTheme.ThemeContext);
  const {user, amountCreditInfo, updateUserInfo} = useUserStore();
  const changeTheme = () => {
    console.log('changeTheme', theme);
    setTheme?.(theme === 'blue' ? 'red' : 'blue');
  };
  return (
    <View className={styles.header}>
      <Image src={logo} className={styles.title} onClick={changeTheme}></Image>
      <View className={styles.featureBar}>
        {!amountCreditInfo.creditAmount ? (
          <>
            <View className={styles.featureItem}><Image className={styles.featureIcon}  src={regularIcon} />正规持牌</View>
            <View className={styles.featureItem}><Image className={styles.featureIcon}  src={lightningIcon} />快速审批</View>
            <View className={styles.featureItem}><Image className={styles.featureIcon} src={recycleIcon} />循环使用</View>
            <View className={styles.featureItem}><Image className={styles.featureIcon}  src={safeIcon} />国企背景安全可靠</View>
          </>
        ) : (
          <>
            <View className={styles.featureItem}>
              <Image className={styles.featureIcon} src={lightningIcon}></Image>快速到账
            </View>
            <View className={styles.featureItem}>
              <Image className={styles.featureIcon} src={safeIcon}></Image>借还灵活
            </View>
            <View className={styles.featureItem}>
              <Image className={styles.featureIcon} src={recycleIcon}></Image>循环使用
            </View>
            <View className={styles.featureItem}>
              <Image className={styles.featureIcon} src={clockIcon}></Image>提前还款无手续费
            </View>
          </>
        )}
      </View>
    </View>
  );
};
