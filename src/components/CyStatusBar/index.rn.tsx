import React, {FC} from 'react';
import {ColorValue, StatusBar, StatusBarStyle} from 'react-native';

type StatusBarProps = {
  backgroundColor: ColorValue;
  barStyle: StatusBarStyle;
};

type CompCyStatusBar = FC<StatusBarProps> & {setBackgroundColor: (x: ColorValue) => void};

const statusBarRef = null;
const CyStatusBar: CompCyStatusBar = props => {
  return <StatusBar backgroundColor={props.backgroundColor} barStyle={props.barStyle} ref={statusBarRef} />;
};

CyStatusBar.setBackgroundColor = (backgroundColor: ColorValue) => {
  StatusBar.setBackgroundColor(backgroundColor);
};

export default CyStatusBar;
