/**
 * 数字添加千分位+符号位
 * @param str 数字
 * @param addSignbit 是否添加正负符号位
 * @returns 格式化过后数字
 */
export const formatMillesimal = (num: number, addSignbit: boolean = false) => {
  return `${num < 0 && addSignbit ? '-' : ''}${parseFloat(`${num}`).toLocaleString()}`;
};

export const addMoneyUnit = (num: number) => {
  if (Math.abs(num) >= 100000000) {
    return {
      num: num / 100000000,
      unit: '亿',
    };
  } else if (Math.abs(num) >= 10000) {
    return {
      num: num / 10000,
      unit: '万',
    };
  } else {
    return {
      num,
      unit: '元',
    };
  }
};

// 小数乘法运算函数，解决精度丢失问题,通过转成整数处理
export const twoNumberTimes = (num1: number, num2: number) => {
  const num1dotLen = `${num1}`.split('.')[1]?.length || 0;
  const num2dotLen = `${num2}`.split('.')[1]?.length || 0;
  return (
    (Number(`${num1}`.replace('.', '')) *
      Number(`${num2}`.replace('.', ''))) /
    Math.pow(10, num1dotLen + num2dotLen)
  );
}

/**
 * 保留两位小数并自动补零
 * @param num 输入的数字
 * @returns 格式化后的字符串
 * @example 1.2345 => 1.23  22 => 22.00
 */
export const formatToFixedTwo = (num: number | string): string => {
  const number = typeof num === 'string' ? parseFloat(num) : num;
  if (isNaN(number)) return '0.00';
  
  return number.toFixed(2);
}
