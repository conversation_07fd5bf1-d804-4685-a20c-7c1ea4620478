/** 全局mixin **/

@mixin tag($color) {
  background-color: $color;
  color: #fff;
  font-size: 24px;
  border-radius: 20px;
  padding: 8px 12px;
}

@mixin flex_justify_align_direction(
  $justify-content: center,
  $align-items: center,
  $flex-direction: row,
) {
  display: flex;
  align-items: $align-items;
  justify-content: $justify-content;
  flex-direction: $flex-direction;
}

@mixin flex_center() {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
}

@mixin flex_column_center() {
  @include flex_center();
  flex-direction: column;
}

@mixin tag($color, $bgColor) {
  color: $color;
  background-color: $bgColor;
  border-radius: 8px;
  padding: 4px 8px;
  font-size: 24px;
  border: 1px solid $color;
}