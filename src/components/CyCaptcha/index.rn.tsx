import {ForwardRefRenderFunction, forwardRef, useEffect, useImperativeHandle, useRef} from 'react';
import {PageInstance, getCurrentInstance} from '@tarojs/taro';
import {showToast} from '../../utils/common';
import './index.scss';

interface Props {
  onSuccess?: (ticket: string) => void;
  onCancel: () => void;
  onError: () => void;
}

export interface CyCaptchaT {
  show: () => void;
}
/**
 * 组件名称：人机校验组件
 * 组件描述：人机校验
 */
const CyCaptcha: ForwardRefRenderFunction<CyCaptchaT, Props> = ({onSuccess, onCancel, onError}, ref) => {
  useImperativeHandle(ref, () => ({
    show: () => {
      //展示
    },
  }));
  return <></>;
};

export default forwardRef(CyCaptcha);
