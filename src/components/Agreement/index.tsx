import {FC, useState} from 'react';
import {Checkbox, CheckboxGroup, Text, View} from '@tarojs/components';
import styles from './index.module.scss';

interface AgreementProps {
  // 协议文本
  agreementText: string;
  // 协议链接文本
  agreementLinkText: string;
  // 协议内容
  agreementContent: string;
  // 是否已同意
  onAgreeChange: (isAgreed: boolean) => void;
  // 自定义样式
  style?: React.CSSProperties;
  // 自定义类名
  className?: string;
}

const Agreement: FC<AgreementProps> = ({
  agreementText,
  agreementLinkText,
  agreementContent,
  onAgreeChange,
  style,
  className,
}) => {
  const handleCheckboxChange = (e: any) => {
    const isAgreed = e.detail.value.length;
    onAgreeChange(isAgreed);
  };

  const handleAgreementClick = () => {
    // 这里可以弹出一个模态框显示协议内容
    console.log('协议内容:', agreementContent);
  };

  return (
    <View className={`${styles.agreement} ${className}`} style={style}>
      <CheckboxGroup onChange={handleCheckboxChange}>
        <Checkbox className={styles.agreement_checkboxdd} value='agree' color='#2F54EB' />
      </CheckboxGroup>
      <View className={styles.agreement_text}>
        <Text>{agreementText}</Text>
        <Text className={styles.agreement_link} onClick={handleAgreementClick}>
          {agreementLinkText}
        </Text>
      </View>
    </View>
  );
};

export default Agreement;
