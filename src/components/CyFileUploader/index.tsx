import {ForwardRefRenderFunction, forwardRef, useImperativeHandle, useState} from 'react';
import {Image, View} from '@tarojs/components';
import Taro from '@tarojs/taro';
import clearPng from '~images/icon/clear.png';
import {cullObjectByValue, showToast} from '../../utils/common';
import {SelectPhotoResult, SizeType, selectPhoto} from '../../utils/photo';
import {open, pickSingle, readFile, types} from '../../utils/saveFile';
import {EventType, FormField} from '../CyForm';
import {showActionSheet} from '../CyGlobPopup';
import fileIcon from './images/file-icon.png';
import imageIcon from './images/image-icon.png';
import uploadIcon from './images/upload-icon.png';
import './index.scss';

const enum UploadFileType {
  file = 'file',
  image = 'image',
}
interface IFileItem extends SelectPhotoResult {
  type: UploadFileType;
}

interface ImgUploadProps extends FormField {
  value?: IFileItem[];
  multiple?: boolean;
  count?: number; // 最多可选图片的张数
  sourceType?: string[];
  sizeType?: Array<keyof SizeType>;
  onFail?: (error: Error) => void;
  name?: string;
}

/**
 * 组件名称：文件上传组件
 * 组件描述：文件上传表单组件：支持图片和文件的上传
 */
const CyFileUploader: ForwardRefRenderFunction<unknown, ImgUploadProps> = (props, ref) => {
  const {value = [], multiple = true, count = 3, sourceType, sizeType, name, onChange, onFail} = props;
  const [currentVal, setCurrentVal] = useState<IFileItem[]>(value);
  useImperativeHandle(ref, () => ({
    get value() {
      return currentVal;
    },
    set value(newValue) {
      setCurrentVal(newValue);
    },
  }));

  const addFile = () => {
    showActionSheet({
      title: '请选择',
      items: [
        {label: '文件', value: 'file'},
        {label: '照片', value: 'photo'},
      ],
      async onConfirm(val) {
        if (val === 'photo') {
          const params = cullObjectByValue({
            multiple,
            count: count - currentVal.length,
            sourceType,
            sizeType,
          });
          selectPhoto({
            ...params,
            onSuccess: res => {
              const newVal = currentVal.concat(res.map(file => ({...file, type: UploadFileType.image})));
              updateVal(newVal);
            },
            onFail,
          });
        } else if (val === 'file') {
          const file = await pickSingle({
            mode: 'open',
            type: [types.pdf],
            copyTo: 'cachesDirectory',
          });
          console.log(`file-->${JSON.stringify(file)}`);
          if (!file.name || !file.uri) {
            showToast('文件选取失败！');
            return;
          }
          try {
            const path = file.fileCopyUri;
            console.log(`path-->${path}`);
            if (path === null) {
              return;
            }
            const base64Res = await readFile(path, 'base64');
            const newVal = currentVal.concat({
              name: file.name,
              path,
              data: base64Res,
              type: UploadFileType.file,
            });
            updateVal(newVal);
          } catch (e) {
            console.log('errr', e);
          }
        }
      },
    });
  };

  const onPreview = async (idx: number) => {
    console.log('openFile===', currentVal[idx].type);
    if (currentVal[idx].type === 'image') {
      Taro.previewImage({
        current: currentVal[idx].data || currentVal[idx].file,
        urls: currentVal.filter(file => file.type === 'image').map(file => file.file || file.data),
      });
    } else if (currentVal[idx].type === 'file') {
      console.log('openFile===');
      await open(currentVal[idx].path);
    }
  };
  const updateVal = (val: IFileItem[]) => {
    const e = {
      target: {
        name,
        value: val,
      },
      detail: {
        value: val,
      },
    };
    onChange?.(e as unknown as EventType);
    setCurrentVal(val);
  };
  const removeFile = (idx: number) => {
    const newVal = currentVal.filter((file, curIdx) => curIdx !== idx);
    updateVal(newVal);
  };
  return (
    <View className='comp-cy-file-uploader' style={props.style}>
      {currentVal.length < count ? (
        <View onClick={addFile} className='file-add-btn'>
          <Image src={uploadIcon} className='file-uploader-icon' />
          上传文件
        </View>
      ) : null}
      {currentVal.map((file, idx) => (
        <View key={idx} className='file-uploader-item'>
          {file.type === 'image' ? (
            <Image src={imageIcon} className='file-uploader-item-icon' />
          ) : (
            <Image src={fileIcon} className='file-uploader-item-file-icon' />
          )}
          <Image onClick={() => removeFile(idx)} className='file-remove-icon' src={clearPng}></Image>
          <View className='file-uploader-file-name' onClick={() => onPreview(idx)}>
            {file.name}
          </View>
        </View>
      ))}
    </View>
  );
};

export default forwardRef(CyFileUploader);
