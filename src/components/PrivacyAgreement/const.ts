type SDK = {
  name: string;
  company: string;
  dataCollected: string;
  permissions: string;
  purpose: string;
  isPersonalData: string;
  url: string;
};
export const sdkList: SDK[] = [
  {
    name: '腾讯Bugly SDK',
    company: '深圳市腾讯计算机系统有限公司',
    dataCollected:
      '设备标识信息（Android ID、OAID、MAC地址、IMEI、IDFA、IDFV）、网络状态信息、进程信息、手机型号、手机品牌、Android系统版本、Android系统api等级、厂商系统版本、cpu架构类型、设备是否root、磁盘空间占用大小、sdcard空间占用大小、网络类型、应用当前正在运行的进程名和PID，使用android.permission.INTERNET、android.permission.ACCESS_NETWORK_STAT（查看网络状态，用于数据上报，实现开发者查看崩溃信息的目的）权限',
    permissions: '',
    purpose: '为了解决用户使用中的问题，提供更优质服务，排查崩溃问题，帮助App提升稳定性',
    isPersonalData: '否',
    url: 'https://bugly.qq.com/v2/',
  },
  {
    name: '极光推送SDK',
    company: '深圳市和讯华谷信息技术有限公司',
    dataCollected:
      '设备标识符（包括Android ID、GAID、OAID、UAID、IDFA、IMEI、MAC、IMSI等相关信息）、网络信息（包括SSID、BSSID、WI-FI列表信息、基站信息、网络类型、运营商名称、IP地址、WI-FI状态信息）、应用信息（应用崩溃信息、通知开关状态、软件安装列表等相关信息）、设备硬件信息（包括设备型号、设备屏幕分辨率、设备硬件制造商、设备产品名称）、操作系统信息（包括操作系统版本、系统名称、系统语言）、设备mac地址、MEID、BSSID、软件安装列表、推送信息日志',
    permissions: '',
    purpose: '用于实现消息推送（或其他推送）功能',
    isPersonalData: '否',
    url: 'https://www.jiguang.cn/license/privacy',
  },
  {
    name: '华为推送',
    company: '华为软件技术有限公司',
    dataCollected:
      '设备标识符（包括Android ID、GAID、OAID、UAID、IDFA、IMEI、MAC、IMSI等相关信息）、网络信息（包括SSID、BSSID、WI-FI列表信息、基站信息、网络类型、运营商名称、IP地址、WI-FI状态信息）、应用信息（应用崩溃信息、通知开关状态、软件安装列表等相关信息）、设备硬件信息（包括设备型号、设备屏幕分辨率、设备硬件制造商、设备产品名称）、操作系统信息（包括操作系统版本、系统名称、系统语言）、设备mac地址、MEID、BSSID、软件安装列表、推送信息日志',
    permissions: '',
    purpose: '用于实现消息推送（或其他推送）功能',
    isPersonalData: '否',
    url: 'https://developer.huawei.com/consumer/cn/doc/HMSCore-Guides/sdk-data-security-0000001050042177',
  },
  {
    name: 'OPPO推送',
    company: '广东欢太科技有限公司',
    dataCollected:
      '设备标识符（包括Android ID、GAID、OAID、UAID、IDFA、IMEI、MAC、IMSI等相关信息）、网络信息（包括SSID、BSSID、WI-FI列表信息、基站信息、网络类型、运营商名称、IP地址、WI-FI状态信息）、应用信息（应用崩溃信息、通知开关状态、软件安装列表等相关信息）、设备硬件信息（包括设备型号、设备屏幕分辨率、设备硬件制造商、设备产品名称）、操作系统信息（包括操作系统版本、系统名称、系统语言）、设备mac地址、MEID、BSSID、软件安装列表、推送信息日志',
    permissions: '',
    purpose: '用于实现消息推送（或其他推送）功能',
    isPersonalData: '否',
    url: 'https://open.oppomobile.com/new/developmentDoc/info?id=11228',
  },
  {
    name: 'vivo推送',
    company: '维沃移动通信有限公司及其关联公司',
    dataCollected:
      '设备标识符（包括Android ID、GAID、OAID、UAID、IDFA、IMEI、MAC、IMSI等相关信息）、网络信息（包括SSID、BSSID、WI-FI列表信息、基站信息、网络类型、运营商名称、IP地址、WI-FI状态信息）、应用信息（应用崩溃信息、通知开关状态、软件安装列表等相关信息）、设备硬件信息（包括设备型号、设备屏幕分辨率、设备硬件制造商、设备产品名称）、操作系统信息（包括操作系统版本、系统名称、系统语言）、设备mac地址、MEID、BSSID、软件安装列表、推送信息日志',
    permissions: '',
    purpose: '用于实现消息推送（或其他推送）功能',
    isPersonalData: '否',
    url: 'https://dev.vivo.com.cn/documentCenter/doc/652#',
  },
  {
    name: '小米推送',
    company: '北京小米移动软件有限公司及其关联公司',
    dataCollected:
      '设备标识符（包括Android ID、GAID、OAID、UAID、IDFA、IMEI、MAC、IMSI等相关信息）、网络信息（包括SSID、BSSID、WI-FI列表信息、基站信息、网络类型、运营商名称、IP地址、WI-FI状态信息）、应用信息（应用崩溃信息、通知开关状态、软件安装列表等相关信息）、设备硬件信息（包括设备型号、设备屏幕分辨率、设备硬件制造商、设备产品名称）、操作系统信息（包括操作系统版本、系统名称、系统语言）、设备mac地址、MEID、BSSID、软件安装列表、推送信息日志',
    permissions: '',
    purpose: '用于实现消息推送（或其他推送）功能',
    isPersonalData: '否',
    url: 'https://dev.mi.com/xiaomihyperos/documentation/detail?pId=1819',
  },
  {
    name: '高德定位SDK',
    company: '北京高德图强科技有限公司',
    dataCollected:
      '地理位置信息、设备标识信息（包括Android ID、GAID、OAID、UAID、IDFA、IMEI、MAC、IMSI等相关信息）、应用名、应用版本号、设备品牌及型号、操作系统、运营商信息、屏幕分辨率、IP地址、GNSS信息、网络类型、WIFI状态信息、SSID、BSSID、基站信息、传感器信息、设备信号强度信息、经纬度、应用列表、当前已连接上的wifi的信息、周围的WiFi信息、存储读写权限、申请调用A-GPS模块权限、前台服务权限、后台定位权限',
    permissions: '',
    purpose: '为了向用户提供定位服务',
    isPersonalData: '否',
    url: 'https://lbs.amap.com/pages/privacy/',
  },
  {
    name: '微信开放平台SDK',
    company: '深圳市腾讯计算机系统有限公司',
    dataCollected:
      '设备信息（硬件型号、操作系统类型、操作系统版本号）、设备标识符（包括Android ID、GAID、OAID、UAID、IDFA、IMEI、MAC、IMSI等相关信息）、应用列表',
    permissions: '',
    purpose: '为了向用户提供微信分享、登录、收藏、支付功能',
    isPersonalData: '否',
    url: 'https://support.weixin.qq.com/cgi-bin/mmsupportacctnodeweb-bin/pages/RYiYJkLOrQwu0nb8',
  },
  {
    name: '极光分享JShare SDK',
    company: '深圳市和讯华谷信息技术有限公司',
    dataCollected:
      '设备标识符（包括Android ID、GAID、OAID、UAID、IDFA、IMEI、MAC、IMSI等相关信息）、设备硬件信息（包括设备型号、设备屏幕分辨率、设备硬件制造商、设备产品名称）、操作系统信息（包括操作系统版本、系统名称、系统语言）、地理位置、网络信息（包括网络类型、运营商名称、IP地址、WIFI状态信息）',
    permissions: '',
    purpose: '为了向用户提供分享内容至微信、QQ',
    isPersonalData: '否',
    url: 'https://docs.jiguang.cn/jpush/resources',
  },
  {
    name: '腾讯云滑块验证码（腾讯人机验证）SDK',
    company: '腾讯云计算（北京）有限责任公司',
    dataCollected:
      '设备标识符（包括Android ID、GAID、OAID、UAID、IDFA、IMEI、MAC、IMSI等相关信息）、设备硬件信息（包括设备型号、设备屏幕分辨率、设备硬件制造商、设备产品名称）、网络信息（包括网络类型、运营商名称、IP地址、WIFI状态信息）、存储权限、位置权限、相机权限、网络权限',
    permissions: '',
    purpose: '用于验证操作者是否是真实用户',
    isPersonalData: '否',
    url: 'https://rule.tencent.com/rule/************',
  },
  {
    name: '合合OCR识别SDK',
    company: '上海合合信息科技股份有限公司',
    dataCollected:
      '唯一设备识别符（IDFA、Android ID、Face ID、OAID、MEID、UUID（IDFV、OID）、UAID）、手机系统的语言、国家、城市、IP地址，MAC地址，设备信息（浏览器类型，设备类型和型号，CPU，系统语言，内存，操作系统版本，Wi-Fi状态，时间戳和区域，设备运动参数和载体）、您的网络行为轨迹（包括点击次数、参与时间）、软件安装列表、访问日期和时间、服务日志信息、相机权限、存储空间权限、获取硬件序列号',
    permissions: '',
    purpose: '为了向用户提供个人身份信息、银行卡信息识别功能',
    isPersonalData: '是',
    url: 'https://www.intsig.com/public/other/privacy.shtml',
  },
  {
    name: '阿里设备风险识别SDK',
    company: '阿里巴巴网络技术有限公司',
    dataCollected:
      '设备标识符（包括Android ID、IDFV、GAID、OAID、UAID、IDFA、IMEI、MAC、IMSI等相关信息）、应用信息（应用名称、应用版本、安装时间、应用安装列表等相关信息）、网络信息（BSSID、SSID、运营商信息、网络类型、SIM卡状态、IP地址、附件WI-FI列表、网卡信息、网络状态）、设备基础信息（设备制造商、设备品牌、设备类型及型号、设备名称、设备操作系统信息、设备内存及存储大小、电池及电量信息、基带信息、开机时间、屏幕亮度及分辨率、CPU信息、系统时区、系统语言、充电状态、系统内核信息、传感器列表、光线传感器信息）',
    permissions: '',
    purpose: '用于检测异常设备，识别作弊及欺诈风险',
    isPersonalData: '否',
    url: 'https://www.aliyun.com/resources',
  },
  {
    name: 'BouncyCastl加密算法库',
    company: '开源项目',
    dataCollected:
      '设备标识符（包括Android ID、GAID、OAID、UAID、IDFA、IMEI、MAC、IMSI等相关信息）、网络信息（包括网络类型、运营商名称、IP地址、WIFI状态信息）',
    permissions: '',
    purpose: '用于前后台通信的公/私钥',
    isPersonalData: '否',
    url: 'https://github.com/bcgit/bc-java',
  },
  {
    name: '阿里百川SDK（阿里Beacon、百川电商AppLink SDK、百川电商小程序SDK、百川电商webview容器、百川电商广告SDK、百川电商基础公共组件、阿里设备标识SDK、淘宝通道服务SDK）',
    company: '阿里巴巴网络技术有限公司',
    dataCollected:
      '设备标识符（包括Android ID、GAID、OAID、UAID、IDFA、IMEI、MAC、IMSI等相关信息）、网络信息（包括网络类型、运营商名称、IP地址、WIFI状态信息）、设备硬件信息（包括设备型号、设备屏幕分辨率、设备硬件制造商、设备产品名称）',
    permissions: '',
    purpose: '用于唤起指定页面',
    isPersonalData: '否',
    url: 'https://www.aliyun.com/resources',
  },
  {
    name: '阿里移动数据分析',
    company: '阿里巴巴网络技术有限公司',
    dataCollected:
      '设备标识符（包括Android ID、GAID、OAID、UAID、IDFA、IMEI、MAC、IMSI等相关信息）、应用信息（应用崩溃信息、通知开关状态、应用安装列表等相关信息）、设备参数及系统信息（设备类型、设备型号、操作系统及硬件相关信息）、网络信息（包括网络类型、运营商名称、IP地址、WIFI状态信息）、当前运行应用进程及包名、完全的网络访问权限',
    permissions: '',
    purpose: '用于辅助开发，用于数据管理DMS数据分析的服务，实时、准确地掌握业务情况',
    isPersonalData: '否',
    url: 'https://www.aliyun.com/resources',
  },
  {
    name: 'HuaweiHiAnalytics（华为数据分析）',
    company: '华为软件技术有限公司',
    dataCollected: '手机状态和身份',
    permissions: '',
    purpose: '使用HuaweiHiAnalytics分析日志',
    isPersonalData: '否',
    url: 'https://developer.huawei.com/consumer/cn/doc/HMSCore-Guides/introduction-0000001050745149',
  },
  {
    name: '阿里金融级实人认证SDK',
    company: '阿里巴巴网络技术有限公司',
    dataCollected:
      '设备标识符（包括Android ID、GAID、OAID、UAID、IDFA、IMEI、MAC、IMSI等相关信息）、网络信息（BSSID、SSID、运营商信息、网络类型、SIM卡状态、IP地址）、应用信息（应用名称、应用版本、应用安装时间），设备基础信息（设备制造商、设备品牌、设备类型及型号、设备名称、设备操作系统信息、设备内存及存储大小、电池及电量信息、基带信息、开机时间、屏幕亮度及分辨率、CPU信息、系统时区、系统语言、充电状态、系统内核信息、传感器列表、光线传感器信息',
    permissions: '',
    purpose: '用于风控需要的场景进行身份验证',
    isPersonalData: '否',
    url: 'https://www.aliyun.com/product/id-verification?utm_content=se_1019840649#security',
  },
  {
    name: '阿里OSS',
    company: '阿里巴巴网络技术有限公司',
    dataCollected:
      '设备标识符（包括Android ID、GAID、OAID、UAID、IDFA、IMEI、MAC、IMSI等相关信息）、网络信息（BSSID、SSID、运营商信息、网络类型、SIM卡状态、IP地址）、应用信息（应用名称、应用版本、应用安装时间），设备基础信息（设备制造商、设备品牌、设备类型及型号、设备名称、设备操作系统信息、设备内存及存储大小、电池及电量信息、基带信息、开机时间、屏幕亮度及分辨率、CPU信息、系统时区、系统语言、充电状态、系统内核信息、传感器列表、光线传感器信息',
    permissions: '',
    purpose: '用于用户应用内访问到的资源可以通过OSS存储',
    isPersonalData: '否',
    url: 'https://www.aliyun.com',
  },
  {
    name: '微通新成PassGuard',
    company: '北京微通新成网络科技有限公司',
    dataCollected: '无',
    permissions: '',
    purpose:
      '微通新成提供的密码卫士PassGuard安全输入控件，密码卫士不采集您的设备及用户信息，仅在您输入密码时提供加密保护，防止木马程序/恶意代码/HOOK/录截屏软件等窃取私密数据',
    isPersonalData: '否',
    url: 'http://www.microdone.cn/',
  },
  {
    name: '微通密码键盘SDK',
    company: '北京微通新成网络科技有限公司',
    dataCollected: '设备信息、地理位置信息',
    permissions: '',
    purpose: '用于对密码信息加密，防止密码被截取，保证密码安全',
    isPersonalData: '否',
    url: 'http://www.microdone.cn/',
  },
  {
    name: '阿里设备标识SDK(TaobaoUtdid、阿里云Utdid)',
    company: '阿里巴巴网络技术有限公司',
    dataCollected:
      '设备信息（IMEI/MAC/Android ID/DEVICEID/IDFA/OpenUDID/GUID/SIM卡IMSI/IP/地理位置）、当前运行应用进程及包名、完全的网络访问权限（包括在后台状态下收集）',
    permissions: '',
    purpose: '用于为设备生成唯一deviceid',
    isPersonalData: '否',
    url: 'https://www.aliyun.com',
  },
  {
    name: '阿里巴巴SDK',
    company: '阿里巴巴网络技术有限公司',
    dataCollected:
      '设备信息（设备识别码IMSI/IMEI/MEID/OAID/AndroidID/MAC地址、MCC/MNC、BSSID/SSID、SIM卡序列号、WIFI列表）、运行中的应用程序信息、位置信息、存储信息、传感器信息（光线、方向、加速度计、陀螺仪、磁场）',
    permissions: '',
    purpose: '用于日志埋点、异常上报等功能',
    isPersonalData: '否',
    url: 'https://www.aliyun.com',
  },
  {
    name: 'Okhttp',
    company: 'Square 公司',
    dataCollected: '运行中进程信息、设备信息（硬件型号、操作系统类型、操作系统版本号）',
    permissions: '',
    purpose: '用于App网络请求',
    isPersonalData: '否',
    url: 'https://squareup.com/us/en',
  },
  {
    name: 'com.alipay（支付宝、阿里乘车码、阿里芝麻信用实名认证、芝麻认证）SDK',
    company: '支付宝（中国）网络技术有限公司',
    dataCollected: 'IMSI、设备序列号、设备MAC地址、Android_ID、传感器信息、手机终端唯一标志信息、设备信息、地理位置',
    permissions: '',
    purpose: '用于人脸认证和统计',
    isPersonalData: '否',
    url: 'https://www.alipay.com/index.html',
  },
  {
    name: '阿⾥百川云旺·即时通讯SDK',
    company: '阿里巴巴网络技术有限公司',
    dataCollected:
      '设备标识符（包括Android ID、GAID、OAID、UAID、IDFA、IMEI、MAC、IMSI等相关信息）、网络类型、切换wifi和蜂窝网络通道、设备信息（含IP地址、设备制造商、设备型号、手机操作系统、SIM卡信息SIM State、SIM卡信息ICCID）',
    permissions: '',
    purpose: '用于提供用户反馈功能',
    isPersonalData: '否',
    url: 'https://www.aliyun.com',
  },
  {
    name: '阿里云活体识别SDK',
    company: '阿里巴巴网络技术有限公司',
    dataCollected:
      '设备信息、人脸信息、日志信息；设备基础信息：设备制造商、设备品牌、设备型号、设备名称、设备操作系统信息、设备配置信息、设备环境信息；设备识别符：如AndroidID、OAID、IMEI/IMSI、SIM卡序列号等；设备网络信息：WIFI信息、网络运营商信息、网络类型、网络状态、IP地址、蓝牙地址、MAC地址等；设备应用信息：App信息（包括：应用名称、应用版本、安装时间）；设备硬件信息：如CPU信息、硬盘容量等；设备使用状态：如开机时长等；应用软件信息：如SDK版本等、个人生物识别信息、相机权限',
    permissions: '',
    purpose: '用于为用户提供活体检测，并通过地理位置校准信息准确性，提供基础反作弊能力，验证是本人操作',
    isPersonalData: '否',
    url: 'https://www.aliyun.com',
  },
  {
    name: '易观方舟SDK',
    company: '深圳易观数字科技有限公司',
    dataCollected:
      '设备标识信息(Android ID、IMEI、IMSI、IDFA、OPENUDID、GUID、SIM卡序列号、获取GAID)、地理位置信息、网络信息(IP信息、MAC地址)',
    permissions: '',
    purpose:
      '用于运营数据分析、数据统计、渠道打包。获取运行中进程信息用于判断程序是否在前台运行，判断当前进程是否在主进程',
    isPersonalData: '否',
    url: 'https://www.analysysdata.com/developer-sdk.html',
  },
  {
    name: '支付宝SDK',
    company: '支付宝（中国）网络技术有限公司',
    dataCollected:
      '设备标识信息(Android ID、OAID、AAID、SSID、BSSID、IMEI、IMSI、IDFA、OPENUDID、GUID、SIM卡序列号)、网络信息（IP 地址、网络类型、运营商信息、Wi-Fi 状态、Wi-Fi 参数、Wi-Fi 列表）、基本设备信息（系统设置、系统属性、设备型号、设备品牌、操作系统）、网络信息(IP信息、MAC地址)',
    permissions: '',
    purpose: '用于帮助用户在应用内使用支付宝支付',
    isPersonalData: '否',
    url: 'https://opendocs.alipay.com/',
  },
  {
    name: 'AndroidUtilCode',
    company: '开源框架',
    dataCollected:
      '设备型号、操作系统版本、设备标识信息(Android ID、IMEI、IMSI、IDFA、OPENUDID、GUID、SIM卡序列号、获取GAID)，完全的存储权限',
    permissions: '',
    purpose: '用于系统开发',
    isPersonalData: '否',
    url: 'https://github.com/Blankj/AndroidUtilCode',
  },
];
