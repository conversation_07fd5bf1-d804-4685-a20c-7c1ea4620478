#import "AppDelegate.h"

#import <React/RCTBundleURLProvider.h>
#import "ScreenShieldView.h"
#import "Orientation.h"
#import "RCTPushy.h"
@implementation AppDelegate

- (BOOL)application:(UIApplication *)application didFinishLaunchingWithOptions:(NSDictionary *)launchOptions
{

  self.moduleName = @"rn";
  // You can add your custom initial props in the dictionary below.
  // They will be passed down to the ViewController used by React Native.
  
  self.initialProps = @{};
  [Orientation setOrientation:UIInterfaceOrientationMaskPortrait];
  Boolean res = [super application:application didFinishLaunchingWithOptions:launchOptions];
  
  return res;
}

- (UIInterfaceOrientationMask)application:(UIApplication *)application supportedInterfaceOrientationsForWindow:(UIWindow *)window {
  NSLog(@"[Orientation getOrientation]=%ld",[Orientation getOrientation]);
  return [Orientation getOrientation];
}

- (NSURL *)sourceURLForBridge:(RCTBridge *)bridge
{
  return [self getBundleURL];
}

- (NSURL *)getBundleURL
{
#if DEBUG
  return [[RCTBundleURLProvider sharedSettings] jsBundleURLForBundleRoot:@"index"];
#else
//  return [[NSBundle mainBundle] URLForResource:@"main" withExtension:@"jsbundle"];
  return [RCTPushy bundleURL];
//  return  [CodePush bundleURL];
#endif
}

@end
