import {useEffect, useState} from 'react';
import {View} from '@tarojs/components';
import {useRouter} from '@tarojs/taro';
import Taro from '@tarojs/taro';
import dayjs from 'dayjs';
import CyInfiniteFlowTableView from '~components/CyInfiniteFlowTableView';
import PageWrap from '~components/PageWrap';
import {getAllList, getLendingList} from '~services/loanCertificate';
import {router} from '~utils/common';
import Card, {CardProps} from '../components/Card';
import styles from './index.module.scss';

export enum LoanCertificateType {
  all = '全部结清证明记录',
  single = '单笔结清证明记录',
}

const LoanCertificateList = () => {
  const [list, setList] = useState<CardProps[]>([]);
  const {type} = useRouter().params;

  useEffect(() => {
    fetchData();
    if (type) {
      Taro.setNavigationBarTitle({title: LoanCertificateType[type as keyof typeof LoanCertificateType]});
    }
  }, [type]);

  const fetchData = async () => {
    Taro.showLoading({title: '加载中', mask: true});

    try {
      if (type === 'all') {
        const {list} = await getAllList();
        setList(
          list.map(item => ({
            id: item.securityCode,
            leftItems: [
              {
                label: '开具时间：',
                value: dayjs(item.settleEvidentiaryDate).format('YYYY年MM月DD日 HH时'),
                contractCode: item.contractCode,
                securityCode: item.securityCode,
              },
              {label: '开具类型：', value: LoanCertificateType.all},
            ],
          })),
        );
      } else {
        const {list} = await getLendingList();
        setList(
          list.map(item => ({
            id: item.loanNo,
            leftItems: [
              {
                label: '借款金额',
                labelClass: styles['single-label'],
              },
              {
                value: item.loanPrincipal,
                isMoney: true,
                labelClass: styles['money-label'],
              },
              {
                label: '借款时间：',
                value: item.startDate,
                labelClass: styles['single-label'],
                valueClass: styles['single-value'],
              },
              {
                label: '借据号：',
                value: item.loanNo,
                labelClass: styles['single-label'],
                valueClass: styles['single-value'],
              },
            ],
          })),
        );
      }
    } catch (error) {
      console.log('获取失败', error);
    } finally {
      Taro.hideLoading();
    }
  };

  const toDetails = (leftItems: CardProps['leftItems']) => {
    const isAllType = type === 'all';
    const params = isAllType
      ? `&contractCode=${leftItems[0].contractCode}&securityCode=${leftItems[0].securityCode}`
      : `&loanNo=${leftItems[3].value}`;

    router.push({url: `/modules/loan_certificate/details/index?type=${type}${params}`});
  };

  return (
    <PageWrap className={styles['loan-certificate-list-wrapper']}>
      <View className={styles['loan-certificate-list-container']}>
        <CyInfiniteFlowTableView
          canLoadMore={false}
          canRefresh={false}
          list={list}
          renderItem={({leftItems}) => <Card leftItems={leftItems} onView={() => toDetails(leftItems)} />}
        />
      </View>
    </PageWrap>
  );
};

export default LoanCertificateList;
