import request from '../http/request';

/** 是否展示提额按钮 */
export const isShowRaiseCreditAmount = () => {
  return request<API.ShowRaiseAmountRes>({
    url: '/api/raise-credit/isShowRaiseAmount',
    method: 'GET',
  });
};

/** 查询是否满足提额条件 */
export const canRaiseAmount = () => {
  return request<API.CanRaiseAmountRes>({
    url: '/api/raise-credit/canRaiseAmount',
    method: 'GET',
  });
};

/** 智能认证提额 */
export const raiseCreditApply = () => {
  return request<{
    code: string | number;
  }>({
    url: '/api/raise-credit/apply',
    method: 'POST',
  });
};

/** 获取提额记录 */
export const getRaiseCreditRecords = ({applyType}: {applyType: 'guide' | 'occpucation' | 'assets' | ''}) => {
  return request<API.RaiseAmountRecordItem[]>({
    url: '/api/raise-credit/apply-records',
    method: 'GET',
    params: applyType,
  });
};