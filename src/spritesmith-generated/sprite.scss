// SCSS variables are information about icon's compiled state, stored under its original file name
//
// .icon-home {
//   width: $icon-home-width;
// }
//
// The large array-like variables contain all information about a single icon
// $icon-home: x y offset_x offset_y width height total_width total_height image_path;
//
// At the bottom of this section, we provide information about the spritesheet itself
// $spritesheet: width height image $spritesheet-sprites;
$icon-clock-name: 'icon-clock';
$icon-clock-x: 208px;
$icon-clock-y: 126px;
$icon-clock-offset-x: -208px;
$icon-clock-offset-y: -126px;
$icon-clock-width: 20px;
$icon-clock-height: 20px;
$icon-clock-total-width: 248px;
$icon-clock-total-height: 184px;
$icon-clock: (208px, 126px, -208px, -126px, 20px, 20px, 248px, 184px, "https://hncy58test-cyd.oss-cn-beijing.aliyuncs.com/multi-platform/sprite.png", 'icon-clock' );
$icon-clock-2x-name: 'icon-clock@2x';
$icon-clock-2x-x: 160px;
$icon-clock-2x-y: 48px;
$icon-clock-2x-offset-x: -160px;
$icon-clock-2x-offset-y: -48px;
$icon-clock-2x-width: 40px;
$icon-clock-2x-height: 40px;
$icon-clock-2x-total-width: 248px;
$icon-clock-2x-total-height: 184px;
$icon-clock-2x: (160px, 48px, -160px, -48px, 40px, 40px, 248px, 184px, "https://hncy58test-cyd.oss-cn-beijing.aliyuncs.com/multi-platform/sprite.png", 'icon-clock@2x' );
$icon-fast-name: 'icon-fast';
$icon-fast-x: 228px;
$icon-fast-y: 126px;
$icon-fast-offset-x: -228px;
$icon-fast-offset-y: -126px;
$icon-fast-width: 20px;
$icon-fast-height: 20px;
$icon-fast-total-width: 248px;
$icon-fast-total-height: 184px;
$icon-fast: (228px, 126px, -228px, -126px, 20px, 20px, 248px, 184px, "https://hncy58test-cyd.oss-cn-beijing.aliyuncs.com/multi-platform/sprite.png", 'icon-fast' );
$icon-fast-2x-name: 'icon-fast@2x';
$icon-fast-2x-x: 160px;
$icon-fast-2x-y: 88px;
$icon-fast-2x-offset-x: -160px;
$icon-fast-2x-offset-y: -88px;
$icon-fast-2x-width: 40px;
$icon-fast-2x-height: 40px;
$icon-fast-2x-total-width: 248px;
$icon-fast-2x-total-height: 184px;
$icon-fast-2x: (160px, 88px, -160px, -88px, 40px, 40px, 248px, 184px, "https://hncy58test-cyd.oss-cn-beijing.aliyuncs.com/multi-platform/sprite.png", 'icon-fast@2x' );
$icon-home-name: 'icon-home';
$icon-home-x: 0px;
$icon-home-y: 144px;
$icon-home-offset-x: 0px;
$icon-home-offset-y: -144px;
$icon-home-width: 40px;
$icon-home-height: 40px;
$icon-home-total-width: 248px;
$icon-home-total-height: 184px;
$icon-home: (0px, 144px, 0px, -144px, 40px, 40px, 248px, 184px, "https://hncy58test-cyd.oss-cn-beijing.aliyuncs.com/multi-platform/sprite.png", 'icon-home');
$icon-home-2x-name: 'icon-home@2x';
$icon-home-2x-x: 0px;
$icon-home-2x-y: 0px;
$icon-home-2x-offset-x: 0px;
$icon-home-2x-offset-y: 0px;
$icon-home-2x-width: 80px;
$icon-home-2x-height: 80px;
$icon-home-2x-total-width: 248px;
$icon-home-2x-total-height: 184px;
$icon-home-2x: (0px, 0px, 0px, 0px, 80px, 80px, 248px, 184px, "https://hncy58test-cyd.oss-cn-beijing.aliyuncs.com/multi-platform/sprite.png", 'icon-home@2x');
$icon-idcard-name: 'icon-idcard';
$icon-idcard-x: 208px;
$icon-idcard-y: 40px;
$icon-idcard-offset-x: -208px;
$icon-idcard-offset-y: -40px;
$icon-idcard-width: 32px;
$icon-idcard-height: 32px;
$icon-idcard-total-width: 248px;
$icon-idcard-total-height: 184px;
$icon-idcard: (208px, 40px, -208px, -40px, 32px, 32px, 248px, 184px, "https://hncy58test-cyd.oss-cn-beijing.aliyuncs.com/multi-platform/sprite.png", 'icon-idcard');
$icon-idcard-2x-name: 'icon-idcard@2x';
$icon-idcard-2x-x: 0px;
$icon-idcard-2x-y: 80px;
$icon-idcard-2x-offset-x: 0px;
$icon-idcard-2x-offset-y: -80px;
$icon-idcard-2x-width: 64px;
$icon-idcard-2x-height: 64px;
$icon-idcard-2x-total-width: 248px;
$icon-idcard-2x-total-height: 184px;
$icon-idcard-2x: (0px, 80px, 0px, -80px, 64px, 64px, 248px, 184px, "https://hncy58test-cyd.oss-cn-beijing.aliyuncs.com/multi-platform/sprite.png", 'icon-idcard@2x');
$icon-lightning-name: 'icon-lightning';
$icon-lightning-x: 208px;
$icon-lightning-y: 146px;
$icon-lightning-offset-x: -208px;
$icon-lightning-offset-y: -146px;
$icon-lightning-width: 20px;
$icon-lightning-height: 20px;
$icon-lightning-total-width: 248px;
$icon-lightning-total-height: 184px;
$icon-lightning: (208px, 146px, -208px, -146px, 20px, 20px, 248px, 184px, "https://hncy58test-cyd.oss-cn-beijing.aliyuncs.com/multi-platform/sprite.png", 'icon-lightning');
$icon-lightning-2x-name: 'icon-lightning@2x';
$icon-lightning-2x-x: 40px;
$icon-lightning-2x-y: 144px;
$icon-lightning-2x-offset-x: -40px;
$icon-lightning-2x-offset-y: -144px;
$icon-lightning-2x-width: 40px;
$icon-lightning-2x-height: 40px;
$icon-lightning-2x-total-width: 248px;
$icon-lightning-2x-total-height: 184px;
$icon-lightning-2x: (40px, 144px, -40px, -144px, 40px, 40px, 248px, 184px, "https://hncy58test-cyd.oss-cn-beijing.aliyuncs.com/multi-platform/sprite.png", 'icon-lightning@2x' );
$icon-mine-name: 'icon-mine';
$icon-mine-x: 80px;
$icon-mine-y: 144px;
$icon-mine-offset-x: -80px;
$icon-mine-offset-y: -144px;
$icon-mine-width: 40px;
$icon-mine-height: 40px;
$icon-mine-total-width: 248px;
$icon-mine-total-height: 184px;
$icon-mine: (80px, 144px, -80px, -144px, 40px, 40px, 248px, 184px, "https://hncy58test-cyd.oss-cn-beijing.aliyuncs.com/multi-platform/sprite.png", 'icon-mine');
$icon-mine-2x-name: 'icon-mine@2x';
$icon-mine-2x-x: 80px;
$icon-mine-2x-y: 0px;
$icon-mine-2x-offset-x: -80px;
$icon-mine-2x-offset-y: 0px;
$icon-mine-2x-width: 80px;
$icon-mine-2x-height: 80px;
$icon-mine-2x-total-width: 248px;
$icon-mine-2x-total-height: 184px;
$icon-mine-2x: (80px, 0px, -80px, 0px, 80px, 80px, 248px, 184px, "https://hncy58test-cyd.oss-cn-beijing.aliyuncs.com/multi-platform/sprite.png", 'icon-mine@2x' );
$icon-notice-name: 'icon-notice';
$icon-notice-x: 208px;
$icon-notice-y: 72px;
$icon-notice-offset-x: -208px;
$icon-notice-offset-y: -72px;
$icon-notice-width: 30px;
$icon-notice-height: 30px;
$icon-notice-total-width: 248px;
$icon-notice-total-height: 184px;
$icon-notice: (208px, 72px, -208px, -72px, 30px, 30px, 248px, 184px, "https://hncy58test-cyd.oss-cn-beijing.aliyuncs.com/multi-platform/sprite.png", 'icon-notice' );
$icon-notice-2x-name: 'icon-notice@2x';
$icon-notice-2x-x: 64px;
$icon-notice-2x-y: 80px;
$icon-notice-2x-offset-x: -64px;
$icon-notice-2x-offset-y: -80px;
$icon-notice-2x-width: 60px;
$icon-notice-2x-height: 60px;
$icon-notice-2x-total-width: 248px;
$icon-notice-2x-total-height: 184px;
$icon-notice-2x: (64px, 80px, -64px, -80px, 60px, 60px, 248px, 184px, "https://hncy58test-cyd.oss-cn-beijing.aliyuncs.com/multi-platform/sprite.png", 'icon-notice@2x' );
$icon-regular-name: 'icon-regular';
$icon-regular-x: 228px;
$icon-regular-y: 146px;
$icon-regular-offset-x: -228px;
$icon-regular-offset-y: -146px;
$icon-regular-width: 20px;
$icon-regular-height: 20px;
$icon-regular-total-width: 248px;
$icon-regular-total-height: 184px;
$icon-regular: (228px, 146px, -228px, -146px, 20px, 20px, 248px, 184px, "https://hncy58test-cyd.oss-cn-beijing.aliyuncs.com/multi-platform/sprite.png", 'icon-regular' );
$icon-regular-2x-name: 'icon-regular@2x';
$icon-regular-2x-x: 120px;
$icon-regular-2x-y: 144px;
$icon-regular-2x-offset-x: -120px;
$icon-regular-2x-offset-y: -144px;
$icon-regular-2x-width: 40px;
$icon-regular-2x-height: 40px;
$icon-regular-2x-total-width: 248px;
$icon-regular-2x-total-height: 184px;
$icon-regular-2x: (120px, 144px, -120px, -144px, 40px, 40px, 248px, 184px, "https://hncy58test-cyd.oss-cn-beijing.aliyuncs.com/multi-platform/sprite.png", 'icon-regular@2x' );
$icon-right-arrow-name: 'icon-right-arrow';
$icon-right-arrow-x: 124px;
$icon-right-arrow-y: 80px;
$icon-right-arrow-offset-x: -124px;
$icon-right-arrow-offset-y: -80px;
$icon-right-arrow-width: 20px;
$icon-right-arrow-height: 20px;
$icon-right-arrow-total-width: 248px;
$icon-right-arrow-total-height: 184px;
$icon-right-arrow: (124px, 80px, -124px, -80px, 20px, 20px, 248px, 184px, "https://hncy58test-cyd.oss-cn-beijing.aliyuncs.com/multi-platform/sprite.png", 'icon-right-arrow' );
$icon-right-arrow-2x-name: 'icon-right-arrow@2x';
$icon-right-arrow-2x-x: 160px;
$icon-right-arrow-2x-y: 144px;
$icon-right-arrow-2x-offset-x: -160px;
$icon-right-arrow-2x-offset-y: -144px;
$icon-right-arrow-2x-width: 40px;
$icon-right-arrow-2x-height: 40px;
$icon-right-arrow-2x-total-width: 248px;
$icon-right-arrow-2x-total-height: 184px;
$icon-right-arrow-2x: (160px, 144px, -160px, -144px, 40px, 40px, 248px, 184px, "https://hncy58test-cyd.oss-cn-beijing.aliyuncs.com/multi-platform/sprite.png", 'icon-right-arrow@2x' );
$icon-safe-name: 'icon-safe';
$icon-safe-x: 124px;
$icon-safe-y: 100px;
$icon-safe-offset-x: -124px;
$icon-safe-offset-y: -100px;
$icon-safe-width: 20px;
$icon-safe-height: 20px;
$icon-safe-total-width: 248px;
$icon-safe-total-height: 184px;
$icon-safe: (124px, 100px, -124px, -100px, 20px, 20px, 248px, 184px, "https://hncy58test-cyd.oss-cn-beijing.aliyuncs.com/multi-platform/sprite.png", 'icon-safe' );
$icon-safe-2x-name: 'icon-safe@2x';
$icon-safe-2x-x: 208px;
$icon-safe-2x-y: 0px;
$icon-safe-2x-offset-x: -208px;
$icon-safe-2x-offset-y: 0px;
$icon-safe-2x-width: 40px;
$icon-safe-2x-height: 40px;
$icon-safe-2x-total-width: 248px;
$icon-safe-2x-total-height: 184px;
$icon-safe-2x: (208px, 0px, -208px, 0px, 40px, 40px, 248px, 184px, "https://hncy58test-cyd.oss-cn-beijing.aliyuncs.com/multi-platform/sprite.png", 'icon-safe@2x' );
$icon-warn-name: 'icon-warn';
$icon-warn-x: 208px;
$icon-warn-y: 102px;
$icon-warn-offset-x: -208px;
$icon-warn-offset-y: -102px;
$icon-warn-width: 24px;
$icon-warn-height: 24px;
$icon-warn-total-width: 248px;
$icon-warn-total-height: 184px;
$icon-warn: (208px, 102px, -208px, -102px, 24px, 24px, 248px, 184px, "https://hncy58test-cyd.oss-cn-beijing.aliyuncs.com/multi-platform/sprite.png", 'icon-warn' );

// The provided mixins are intended to be used with the array-like variables
//
// .icon-home {
//   @include sprite-width($icon-home);
// }
//
// .icon-email {
//   @include sprite($icon-email);
// }
//
// Example usage in HTML:
//
// `display: block` sprite:
// <div class="icon-home"></div>
//
// To change `display` (e.g. `display: inline-block;`), we suggest using a common CSS class:
//
// // CSS
// .icon {
//   display: inline-block;
// }
//
// // HTML
// <i class="icon icon-home"></i>
@mixin sprite-width($sprite) {
  width: nth($sprite, 5);
}

@mixin sprite-height($sprite) {
  height: nth($sprite, 6);
}

@mixin sprite-position($sprite) {
  $sprite-offset-x: nth($sprite, 3);
  $sprite-offset-y: nth($sprite, 4);
  background-position: $sprite-offset-x  $sprite-offset-y;
}

@mixin sprite-image($sprite) {
  $sprite-image: nth($sprite, 9);
  background-image: url(#{$sprite-image});
}

@mixin sprite($sprite) {
  @include sprite-image($sprite);
  @include sprite-position($sprite);
  @include sprite-width($sprite);
  @include sprite-height($sprite);
}

// The `sprites` mixin generates identical output to the CSS template
//   but can be overridden inside of SCSS
//
// @include sprites($spritesheet-sprites);
@mixin sprites($sprites) {
  @each $sprite in $sprites {
    $sprite-name: nth($sprite, 10);
    .#{$sprite-name} {
      @include sprite($sprite);
    }
  }
}
