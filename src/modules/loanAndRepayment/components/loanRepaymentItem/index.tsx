import { Image, View, ViewProps } from "@tarojs/components"
import styles from "./index.module.scss"
import arrowRight from '~images/icon/<EMAIL>'
import { formatToFixedTwo } from "~utils/number";

interface LoanRepaymentProps {
  amount: string | number;
  source: string;
  date: number | string;
  status: string | React.ReactNode;
  desc?: React.ReactNode;
  onClick?: () => void;
  clickable?: boolean;
  type: 'repayment' | 'loan'
}

export default ({
  amount,
  source,
  desc,
  date,
  status,
  onClick,
  clickable = true,
  type = 'loan'
}: LoanRepaymentProps) => {
  const handleClick = () => {
    if (clickable && onClick) {
      onClick();
    }
  };
  return (
    <View className={styles.item} onClick={handleClick}>
      <View className={styles.itemContent}>
        <View className={styles.itemLeft}>
          {source && <View className={styles.itemSourece}>{`${source}（元）`}</View>}
          <View className={styles.itemTitle}>{formatToFixedTwo(amount)}</View>
          {date && <View className={styles.itemDate}>{`${type === 'loan' ? '借款日期' : '还款日期'}：${date}`}</View>}
        </View>
        <View className={styles.itemRight}>
          <View className={styles.itemRightText}>
            {status && <View className={styles.itemText}>{status}</View>}
            {desc && <View className={styles.itemDesc}>{desc}</View>}
          </View>
          {clickable && <Image src={arrowRight} className={styles.itemIcon} />}
        </View>
      </View>
    </View>
  )
}