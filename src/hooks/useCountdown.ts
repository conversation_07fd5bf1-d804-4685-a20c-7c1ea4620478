import { useCallback, useEffect, useRef, useState } from "react"

const useCountdown = (initialCount: number, minValue: number = 0) => {
    const [count, setCount] = useState(initialCount)
    const [isActive, setIsActive] = useState(false)
    const timerRef = useRef<NodeJS.Timeout | null>(null)
  
    const start = useCallback(() => {
      setIsActive(true)
    }, [])
  
    const stop = useCallback(() => {
      setIsActive(false)
      if (timerRef.current) {
        clearInterval(timerRef.current)
        timerRef.current = null
      }
    }, [])
  
    const reset = useCallback((newCount?: number) => {
      setCount(newCount ?? initialCount)
      setIsActive(false)
      if (timerRef.current) {
        clearInterval(timerRef.current)
        timerRef.current = null
      }
    }, [initialCount])
  
    useEffect(() => {
      if (isActive && count > minValue) {
        timerRef.current = setInterval(() => {
          setCount(prevCount => {
            if (prevCount <= minValue + 1) {
              setIsActive(false)
              return minValue
            }
            return prevCount - 1
          })
        }, 1000)
      } else {
        if (timerRef.current) {
          clearInterval(timerRef.current)
          timerRef.current = null
        }
      }
  
      return () => {
        if (timerRef.current) {
          clearInterval(timerRef.current)
          timerRef.current = null
        }
      }
    }, [isActive, count, minValue])
  
    return { count, start, stop, reset, isActive }
  }
  
export default useCountdown