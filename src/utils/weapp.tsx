import Taro from "@tarojs/taro";

const weappLogin = () => {
  return new Promise((resolve, reject) => {
    Taro.login({
        success: res => {
          if (res.code) {
            resolve(true)
          } else {
            console.log(`登录失败！${res.errMsg}`);
            reject(res)
          }
        },
        fail: res => {
          reject(res)
        },
      });
  });
}

export {
    weappLogin
}