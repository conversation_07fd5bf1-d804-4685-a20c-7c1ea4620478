import React, {FC, PropsWithChildren} from 'react';
import {View, ViewProps} from '@tarojs/components';
import classNames from 'classnames';
import styles from './index.module.scss';

interface AtListProps extends ViewProps {}

const CustomList: FC<PropsWithChildren & AtListProps> = ({children, className}) => {
  return <View className={classNames(styles.cus_list, className)}>{children}</View>;
};

export default CustomList;
