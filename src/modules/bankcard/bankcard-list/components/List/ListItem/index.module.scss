.itemContainer {
  position: relative;
  width: 100%;
  margin-bottom: 24px;
  // overflow: hidden;
}
.bankcardListItem {
  padding: 40px 0 55px 40px;
  box-shadow: 3px 3px 13px 0px rgba(92,92,92,0.5);
  border-radius: 16px;
  // overflow: hidden;
  background-size: 100% 100%;
  box-sizing: border-box;

  .itemBgContainer {
    position: absolute;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    overflow: hidden;
    &::before {
      content: '';
      position: absolute;
      width: 600px;
      height: 600px;
      border-radius: 50%;
      background-color: rgba(255, 255, 255, 0.03);
      top: -60px;
      left: -30px;
      z-index: 1;
    }
    &::after {
      content: '';
      position: absolute;
      width: 640px;
      height: 640px;
      border-radius: 50%;
      background-color: rgba(255, 255, 255, 0.05);
      top: -80px;
      left: -50px;
      z-index: 1;
    }
  }

  
  
  &.bg_b {
    background: linear-gradient(90deg, #5D7CFC 0%, #2F54EB 100%);
  }
  &.bg_r {
    background: linear-gradient(90deg, #FF6F65 0%, #F95252 100%);
  }
  &.bg_g {
    background: linear-gradient(90deg, #62D394 0%, #27AF64 100%);
  }

  .bankcardListItemLeft {
    display: flex;
    align-items: center;
    margin-bottom: 32px;
    .bankcardListItemImg {
      width: 80px;
      height: 80px;
      border-radius: 50%;
      background-color: #fff;
    }
    .bankcardInfo {
      display: flex;
      flex-direction: column;
      justify-content: center;
      padding-left: 24px;
      color: #fff;
      .bankcardName {
        font-size: $font-size-lg;
        margin-bottom: 14px;
        display: flex;
        justify-content: flex-start;
        align-items: center;
      }
      .bankcardDesc {
        color: #efefef;
        font-size: $font-size-tiny;
      }
      .isDefault {
        display: inline-block;
        height: 32px;
        line-height: 32px;
        border-radius: 4px;
        border: 1px solid rgba(218, 226, 255, 0.5);
        color: #fff;
        text-align: center;
        font-size: 22px;
        margin-left: 24px;
        box-sizing: border-box;
        padding: 0px 10px;
      }
    }
  }
  .bankcardNo {
    font-size: 40px;
    line-height: 1;
    padding-left: 88px;
    color: #fff;
  }
  .bankcardIcon {
    position: absolute;
    top: 40px;
    right: 24px;
    width: 48px;
    height: 48px;
  }
  .bankcardDetail {
    position: absolute;
    bottom: 24px;
    right: 24px;
    color: #efefef;
    font-size: $font-size-tiny;
  }
}
.upgradeIcon {
  position: absolute;
  top: -8px;
  right: -8px;
  width: 96px;
  height: 96px;
}