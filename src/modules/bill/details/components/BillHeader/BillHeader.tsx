import {useEffect, useState} from 'react';
import {Text, View} from '@tarojs/components';
import Taro from '@tarojs/taro';
import dayjs from 'dayjs';
import MoneyDisplay from '~components/MoneyDisplay';
import styles from './BillHeader.module.scss';

interface BillHeaderProps {
  principal: number;
  loanStatusName: string;
  startDate: string;
  term: number;
}

const BillHeader = ({principal, loanStatusName, startDate, term}: BillHeaderProps) => {
  const [currentTerm, setCurrentTerm] = useState<number>(0);
  useEffect(() => {
    Taro.eventCenter.on('updateCurrentTerm', currentTerm => {
      setCurrentTerm(currentTerm);
    });

    return () => {
      Taro.eventCenter.off('updateCurrentTerm');
    };
  }, []);

  return (
    <View className={styles.header}>
      <View className={styles.headerContent}>
        <View className={styles.dateContainer}>
          <Text className={styles.date}>{startDate && dayjs(startDate).format('YYYY年MM月DD日')}借款（元）</Text>
        </View>
        <View className={styles.amountContainer}>
          <MoneyDisplay money={principal} size='large' currency={false} />
        </View>
      </View>
      <View className={styles.statusContainer}>
        <Text className={loanStatusName === '逾期' ? styles.statusError : styles.status}>{loanStatusName}</Text>
        <Text className={styles.statusDesc}>
          已还{currentTerm ?? 0}期/共{term ?? 0}期
        </Text>
      </View>
    </View>
  );
};

export default BillHeader;
