import {Image, View, ViewProps} from '@tarojs/components';
import {router} from '~utils/common';
import styles from './index.module.scss';
import bellIcon from '~images/mine/<EMAIL>'
import noLoginAvatar from '~images/mine/<EMAIL>'
import loginAvatar from '~images/mine/<EMAIL>'
import useUserStore from '~store/user';

export interface AvatarProps extends ViewProps {
  name: string;
  mobile: string;
  onRightIconClick?: () => void;
}

const goLogin = () => {
    router.replace({
      url: '/modules/user/login/index',
    });
};

export default ({name, mobile, onRightIconClick}: AvatarProps) => {
  return (
    <View className={styles.avatarBar} onClick={mobile ? undefined : goLogin}>
      
      <View className={styles.avatar}>
        <Image className={styles.avatarImg} src={mobile ? loginAvatar : noLoginAvatar} />
      </View>
      {mobile ? (
        <View className={styles.userInfo}>
          <View className={styles.userName}>{name || '未实名'}</View>
          <View className={styles.mobile}>{mobile}</View>
        </View>
      ) : (
        <View className={styles.userInfo}>
          <View className={styles.noLogin}>注册/登录</View>
        </View>
      )}
      {/* <Image onClick={onRightIconClick} className={styles.bellIcon} src={bellIcon} /> */}
    </View>
  );
};
