$skeleton-start-color: var(--skeleton-start-color, #f0f0f0);
$skeleton-middle-color: var(--skeleton-middle-color, #e0e0e0);
$skeleton-end-color: var(--skeleton-end-color, #f0f0f0);

.cy-skeleton {
  display: block;
  width: 100%;
  height: 16px;
  background-color: $skeleton-start-color;
  position: relative;
  overflow: hidden;
  
  // 基础样式
  &-circle {
    border-radius: 50%;
  }
  
  // 渐变效果
  &-gradient {
    background: $skeleton-start-color;
    
    &.cy-skeleton-animated {
      background: linear-gradient(
        90deg,
        $skeleton-start-color 25%,
        $skeleton-middle-color 50%,
        $skeleton-end-color 75%
      );
      background-size: 200% 100%;
    }
  }
  
  // 动画效果
  &-animated {
    animation: skeleton-loading 1.5s infinite;
  }
}

// 骨架屏动画
@keyframes skeleton-loading {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}

// 响应式适配
@media (prefers-reduced-motion: reduce) {
  .cy-skeleton-animated {
    animation: none;
  }
} 