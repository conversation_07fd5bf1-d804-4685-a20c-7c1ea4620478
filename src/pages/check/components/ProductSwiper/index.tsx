// import {CyButton} from 'hncy58-taro-components';
import {CyButton} from 'hncy58-taro-components';
import {Swiper, SwiperItem, Text, View} from '@tarojs/components';
import Taro from '@tarojs/taro';
import styles from './index.module.scss';

const handleApply = () => {
  Taro.showToast({
    title: '功能开发中',
    icon: 'none',
  });
};

export default () => {
  return (
    <Swiper className={styles.productSwiper} circular nextMargin='28px'>
      <SwiperItem>
        <View className={styles.productSwiperItem}>
          <View style={{flex: 1}}>
            <View className={styles.productItemTitleRow}>
              <Text className={styles.productItemTitleActive}>旅友快贷</Text>
              {/* <Text className={styles.productItemTitle}>-文旅工作者享受专属额度</Text> */}
            </View>
            <View className={styles.productItemDesc}>文旅工作者享受专属额度</View>
          </View>
          <CyButton onClick={handleApply} type='primary' size='mini' round style={{height: '32px', lineHeight: '32px'}}>
            立即申请
          </CyButton>
        </View>
      </SwiperItem>

      <SwiperItem>
        <View className={styles.productSwiperItem}>
          <View style={{flex: 1}}>
            <View className={styles.productItemTitleRow}>
              <Text className={styles.productItemTitleActive}>工友快贷</Text>
              {/* <Text className={styles.productItemTitle}>-奋斗者专属消费额度</Text> */}
            </View>
            <View className={styles.productItemDesc}>奋斗者专属消费额度</View>
          </View>
          <CyButton onClick={handleApply} type='primary' size='mini' round style={{height: '32px', lineHeight: '32px'}}>
            立即申请
          </CyButton>
        </View>
      </SwiperItem>

      {/* <SwiperItem>
        <View className={styles.productSwiperItem}>
          <View>
            <View className={styles.productItemTitleRow}>
              <Text className={styles.productItemTitleActive}>旅友快贷</Text>
              <Text className={styles.productItemTitle}>-旅游业人员专属</Text>
            </View>
            <View className={styles.productItemDesc}>凭导游证即可申请，额度最高20万</View>
          </View>
          <CyButton onClick={handleApply} type='primary' size='mini' round style={{height: '32px', lineHeight: '32px'}}>
            立即申请
          </CyButton>
        </View>
      </SwiperItem> */}
    </Swiper>
  );
};
