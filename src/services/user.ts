import request from '../http/request';

/** 登录 */
export const getAccessToken = ({username}: {username: string}) => {
  return request<any>({
    url: '/login',
    method: 'POST',
    params: {
      target: 'wechatmp',
      username,
    },
  });
};

/** 获取用户信息 */
export const getCustomerInfo = () => {
  return request<any>({
    url: '/api/customer/info',
    method: 'GET',
  });
};

/** 发送验证码 */
export const sendVerifyCodeReq = ({mobile, smsSceneType, ticket}: {mobile?: string, smsSceneType: string; ticket?: string}) => {
  return request<any>({
    url: '/api/customer/sms',
    method: 'GET',
    params: {
      mobile,
      smsSceneType,
      ticket
    },
  });
};

/** 验证验证码 */
export const checkVerifyCodeReq = ({verificationCode, smsSceneType}: {verificationCode: string, smsSceneType: string}) => {
  return request<any>({
    url: '/api/customer/sms/verification',
    method: 'POST',
    data: {
      verificationCode,
      smsSceneType,
    },
  });
};

/** 其他手机号登录 */
export const otherMobileLogin = ({mobile, verificationCode, smsSceneType}: {mobile: string; verificationCode: string, smsSceneType: string}) => {
  return request<any>({
    url: '/api/login/by-mobile',
    method: 'POST',
    data: {
      mobile,
      verificationCode,
      smsSceneType
    },
  });
};

/** 查询客户额度信息 */
export const getUserCreditInfo = () => {
  return request<any>({
    url: '/api/credit/info',
    method: 'POST',
  });
};

/** 注销 */
export const signout = () => {
  return request<any>({
    url: '/api/login/exit',
    method: 'GET',
  });
};

/** 更新工作信息 */
export const updateComanyInfo = (data: API.UpdateCompanyInfoReqParams) => {
  return request<any>({
    url: '/api/customer/company/updateCompanyInfo',
    method: 'POST',
    data,
  });
};

/** 注册 */
export const register = (data: API.RegisterReqParams) => {
  return request<any>({
    url: '/api/register/register',
    method: 'POST',
    data,
  });
};

/** 通过jscode登录 */
export const loginByJscode = (data: API.LoginByJscodeReqParams) => {
  return request<any>({
    url: '/api/login/one-click',
    method: 'POST',
    data,
  });
};


/** 更新用户基本信息 */
export const updateBaseInfo = (data: API.baseInfoParams) => {
  return request<{}>({
    url: '/api/customer/infoUpdate/baseInfo',
    method: 'POST',
    data,
  });
}

/** 更新用户单位信息 */
export const updateCompanyInfo = (data: API.companyInfoParams) => {
  return request<{}>({
    url: '/api/customer/infoUpdate/occupation',
    method: 'POST',
    data,
  });
}


/** 更新用户联系人信息 */
export const updateContactInfo = (data: {
  contactList: API.contactInfoParams[]
}) => {
  return request<{}>({
    url: '/api/customer/infoUpdate/contactInfo',
    method: 'POST',
    data,
  });
}

/** 查询用户联系人信息 */
export const getContactInfo = () => {
  return request<{
    contactList: API.contactInfoParams[]
  }>({
    url: '/api/customer/infoUpdate/queryContactInfo',
    method: 'GET',
  });
}

/** 查询用户基本信息 */
export const getBaseInfo = () => {
  return request<API.baseInfoParams>({
    url: '/api/customer/infoUpdate/queryBaseInfo',
    method: 'GET',
  });
}

/** 查询用户单位信息 */
export const getOccupationInfo = () => {
  return request<API.companyInfoParams>({
    url: '/api/customer/infoUpdate/queryOccupation',
    method: 'GET',
  });
}

export const getIsNeedCompanyUpdate = ({prodCode}: {prodCode: string}) => {
  return request<boolean>({
    url: '/api/customer/company/isNeedCompanyUpdate',
    method: 'GET',
    params: {
      prodCode
    }
  });
}

/** 扫描带参数二维码 */
export const queryScanQrCode = (data: {scene: string}) => {
  return request<any>({
    url: '/api/wechat/scanQrCode',
    method: 'POST',
    data
  });
}


/** 额度计算中查询用户是否需要客户经理上传照片附件 */
export const queryNeedWithCM = () => {
  return request<boolean>({
    url: '/api/customer/photo/need-with-cm',
    method: 'GET',
  });
}


export const buildAuthCode = () => {
  return request<{code: string, accessChannel: string}>({
    url: '/api/customer/buildAuthCode',
    method: 'GET',
  });
}


export const saveCustomerLocationInfo = ({latitude, longitude}: {latitude: string, longitude: string}) => {
  return request<{}>({
    url: '/api/customer/saveCustomerLocationInfo',
    method: 'POST',
    data: {latitude, longitude}
  })
}