import {useState} from 'react';
import {View, ViewProps} from '@tarojs/components';
import './index.scss';

// 样式文件

interface BadgeProps extends ViewProps {
  content: React.ReactNode; // 弹出框内容
  position?: 'top' | 'bottom' | 'left' | 'right'; // 弹出框位置
  trigger?: 'click'; // 触发方式
  children: React.ReactNode;
  visible?: boolean;
  type?: API.OriginSummaryCategory;
}

const RepaymentBadge = (props: BadgeProps) => {
  const {trigger, position = 'top', content, children, visible, type = 'normal', className, ...otherProps} = props;
  const [isVisible, setIsVisible] = useState(visible);

  const handleTrigger = () => {
    if (trigger === 'click') {
      setIsVisible(!isVisible);
    }
  };

  return (
    <View className='popover-container'>
      <View className='trigger-container' onClick={handleTrigger}>
        {children}
      </View>
      {isVisible && content && (
        <View {...otherProps} className={`popover-content popover-${position} popover-content-${type} ${className}`}>
          {content}
          <View className={`popover-arrow popover-arrow-${position} popover-arrow-${type}`} />
        </View>
      )}
    </View>
  );
};

export default RepaymentBadge;
