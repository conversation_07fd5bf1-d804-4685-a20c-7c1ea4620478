import React, { forwardRef, useState } from 'react';
import { ScrollView, ScrollViewProps, View } from '@tarojs/components';
import { usePageScroll } from '@tarojs/taro';
import styles from './index.module.scss';

interface PullRefreshProps {
  onRefresh: () => void;
  refreshing: boolean;
  children: React.ReactNode;
  className?: string;
}

const PullRefresh = forwardRef<HTMLDivElement, PullRefreshProps & ScrollViewProps>((props, ref) => {
  const { onRefresh, refreshing, children, className = '', ...restProps } = props;
  const [reachTop, setReachTop] = useState(true);
  
  usePageScroll(({ scrollTop }) => setReachTop(scrollTop === 0));

  return (
    <ScrollView
      className={`${styles.pullRefresh} ${className}`}
      ref={ref}
      refresherEnabled={reachTop}
      refresherTriggered={refreshing}
      onRefresherRefresh={onRefresh}
      scrollY
      bounces={false}
      enableBackToTop={true}
      showScrollbar={false}
      refresherDefaultStyle="black"
      {...restProps}
    >
      <View className={styles.content}>
        {children}
      </View>
    </ScrollView>
  );
});

export default PullRefresh;