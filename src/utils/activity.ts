import { buildAuthCode } from "~services/user";
import { goWebviewLinkOrRoute, showToast } from "./common";
import { throttle } from "@tarojs/runtime";

// 跳转到活动页面
export const jumpToActivityPage = throttle(async (url: string) => {
    if (!url) {
        console.warn('jumpToActivityPage called with an empty URL.');
        return;
    }
    try {
        const { code } = await buildAuthCode();
        const separator = url.includes('?') ? '&' : '?';
        const finalUrl = `${url}${separator}code=${code}`;
        goWebviewLinkOrRoute(finalUrl);
    } catch (error) {
        console.error('Failed to build auth code or navigate:', error);
        // showToast('操作失败，请稍后重试');
    }
}, 3000);