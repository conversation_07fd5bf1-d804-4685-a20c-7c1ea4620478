import { Cy<PERSON><PERSON>on, CyPopup } from 'hncy58-taro-components';
import { Button, Image, InputProps, Switch, Text, View ,Input} from '@tarojs/components';
import Taro, { eventCenter, getFileSystemManager, useDidShow, useLoad } from '@tarojs/taro';
import Card from '~components/Card';
import Cell from '~components/CellItem';
import PageWrap from '~components/PageWrap';
import scanIcon from '~images/bankcard/<EMAIL>';
import arrowRight from '~images/icon/<EMAIL>'
import styles from './index.module.scss';
import { useEffect, useRef, useState } from 'react';
import { bankCardApply, getSupporBanks, bankCardConfirm, getBankNameByCardNo, getUnsignedChannelState, getUnsignedChannel, ocrBankcard } from '~services/bankcard';
import { router, showModal, showToast } from '~utils/common';
import debounce from '~utils/debounce';
import useUserStore from '~store/user';
import CyVerificationCodeButton from '~components/CyVerificationCodeButton';
import SupportBanks from './components/SupportBanks';
import CyContractBar from '~components/CyContractBar';
import { CyContractPreviewHtmlRef, ContractItemProps } from '~components/ContractPreviewHtml';
import goLendingPreProgress from '~utils/goLendingPreProgress';
import { validateByType } from '~utils/validate';
import defaultBankIcon from '~images/bankcard/<EMAIL>'
const BankCardBinding = () => {
  const { user } = useUserStore();

  const [accountNo, setAccountNo] = useState('') // 银行卡号
  const [bankMobileNo, setBankMobileNo] = useState(user.mobile) // 银行预留手机号
  const [verificationCode, setVerificationCode] = useState(''); // 验证码
  const [defaultFlag, setDefaultFlag] = useState(false) // 是否默认银行卡
  const [openingBank, setOpeningBank] = useState(''); // 开户行

  const [bankList, setBankList] = useState<API.IBankCardInfo[]>([]) // 可绑卡银行卡列表
  const [visible, setVisible] = useState(false) // 可绑银行卡列表弹窗 
  const [applyNo, setApplyNo] = useState('') // 绑定银行卡申请号
  const [seqNo, setSeqNo] = useState('')

  const [sendSuccess, setSendSuccess] = useState(false)
  const [showVerifycode, setShowVerifycode] = useState(false)
  const [contracts, setContracts] = useState<ContractItemProps[]>([]) // 补充协议
  const [isAgreed, setIsAgreed] = useState<boolean>(false); // 是否同意补充协议
  const [isAgreedCutPayment, setIsAgreedCutPayment] = useState<boolean>(false); // 是否同意委托扣款协议
  const cutPayment = [{code: 'E24'}] // 委托扣款协议
  const isFromLending = useRef(false)

  const cardInfo = useRef({
    bankcardNo: '',
    mobileNo: ''
  })

  const bankcardNoInputRef = useRef<InputProps>(null)

  useDidShow(async () => {
    if (seqNo) {
      const { result } = await getUnsignedChannelState({ seqNo })
      if (result) {
        showToast('绑卡成功')
        router.back()
      } else {
        showToast('已取消，绑卡失败')
      }
    }
  })

  useEffect(() => {
    queryChannleList()
  }, [])
  
  useLoad((query) => {
    if(query.from === 'lending') {
      isFromLending.current = true;
    }
  })
  const queryChannleList = async () => {
    const res = await getSupporBanks()
    setBankList(res)
  }

  const queryUnsignedChannel = async () => {
    try {
      Taro.showLoading();
      const res = await getUnsignedChannel({ cardNo: accountNo, defaultBank: defaultFlag, type: '1' })
      if (res.seqNo && res.seqNo.length > 0) {
        router.push({
          url: `/modules/webview/index/index?url=${encodeURIComponent(res.unsignedChannelUrl)}`,
        })
        setSeqNo(res.seqNo)
      } else {
        setShowVerifycode(true)
      }
      Taro.hideLoading();
    } catch (error) {
      console.log('请求错误', error)
    } finally {
      Taro.hideLoading();
    }
  }

  const handleGetIsBscBank = () => {
    if (!accountNo) {
      showToast('请输入银行卡号')
      return
    }
    if (!isAgreedCutPayment) {
      showToast('请先阅读并同意委托扣款协议')
      return
    }
    queryUnsignedChannel()
  }

  const handleOhterBank = async () => {
    if (!applyNo) {
      showToast('请先发送验证码')
      return
    }
    if (!verificationCode) {
      showToast('请输入验证码')
      return
    }
    if (!isAgreedCutPayment) {
      showToast('请先阅读并同意委托扣款协议')
      return
    }
    if (contracts.length && !isAgreed) {
      showToast('请先阅读并同意补充协议')
      return
    }
    if (showVerifycode && (accountNo !== cardInfo.current.bankcardNo || bankMobileNo !== cardInfo.current.mobileNo)) {
      showToast('您的信息已修改，请重新获取验证码')
      return
    }
    try {
      Taro.showLoading();
      await bankCardConfirm({ accountNo, applyNo, verificationCode, defaultFlag })
      showToast('添加成功')
      setTimeout(() => {
        if(isFromLending.current) {
          goLendingPreProgress();
        } else {
          eventCenter.trigger('refreshBankCardList')
          router.back()
        }
      }, 2000)
    } catch (error) {
      console.log('请求错误', error)
    } finally {
      Taro.hideLoading();
    }
  }

  const handleApply = async () => {
    if (!accountNo) {
      showToast('请输入银行卡号')
      return
    }
    if (!bankMobileNo) {
      showToast('请输入银行卡绑定手机号')
      return
    }
    const isPhone =  validateByType('phone')
    if (bankMobileNo !== user.mobile && !isPhone(bankMobileNo)) {
      showToast('请输入正确的手机号')
      return
    }

    // 缓存卡号和手机号，提交时判断是否被修改
    cardInfo.current.bankcardNo = accountNo
    cardInfo.current.mobileNo = bankMobileNo

    try {
      Taro.showLoading();
      const phone = bankMobileNo === user.mobile ? '' : bankMobileNo;
      const { applyNo, needSupplyDocTypes } = await bankCardApply({ accountNo, bankMobileNo: phone })
      setSendSuccess(true)
      setApplyNo(applyNo)
      showToast('短信验证码已发送')
      if (needSupplyDocTypes && needSupplyDocTypes.length > 0) {
        const docs = needSupplyDocTypes.map(code => ({ code }))
        setContracts(docs)
      }
    } catch (error) {
      console.log('请求错误', error)
    } finally {
      Taro.hideLoading();
    }
  }

  const handleSubmit = async () => {
    if (!showVerifycode) {
      handleGetIsBscBank()
    } else {
      handleOhterBank()
    }
  }

  const hanldCardNoChange = debounce(async (e) => {
    const value = e.detail.value.replace(/\s+/g, '')
    setAccountNo(value)
    const res = await getBankNameByCardNo({ cardNo: value })
    setOpeningBank(res)
  }, 1000)

  const handleOcrBankcard = () => {
    Taro.chooseImage({
      success(res) {
        const tempFilePaths = res.tempFilePaths;
        getFileSystemManager().readFile({
          filePath: tempFilePaths[0],
          encoding: 'base64',
          success: async (res) => {
            const discernRes = await ocrBankcard({
              base64Picture: res.data as string,
            })
            if (typeof discernRes === 'string') {
              if (bankcardNoInputRef.current) {
                const cardNo = discernRes.replace(/\s/g, '')
                bankcardNoInputRef.current.value = cardNo
                setAccountNo(cardNo)
                const res = await getBankNameByCardNo({ cardNo })
                setOpeningBank(res)
              }
            }
          },
        });
      },
    });
  } 
  return (
    <PageWrap className={styles.bindingPage}>
      <Card title='绑定银行卡' note='请输入您本人名下的银行卡信息' className={styles.pageCard}>
        <View className={styles.bindingContent}>
          <View className={styles.bindingTip}>
            为保障您正常使用借还款功能，需要收集您的银行卡信息，全 程保障您的隐私安全，请放心绑定
          </View>
          <Cell title='姓名' value={user.name} />
          <Cell
            title='卡号'
            right={
              <>
                <Input
                  placeholderClass={styles.inputPlaceholder}
                  type='number'
                  className={styles.bankcardLongInput}
                  ref={bankcardNoInputRef}
                  placeholder='请输入您名下银行卡号'
                  onInput={hanldCardNoChange}
                />
                <Image src={scanIcon} onClick={handleOcrBankcard} className={styles.cellIcon} />
              </>
            }
          />
          {openingBank ? <Cell className={styles.CellItem} title='开户行' value={openingBank} /> : null}
          {/* <Cell
            title='开户行'
            right={
              <>
                <CyInput
                  value={openingBank}
                  placeholderClass={styles.inputPlaceholder}
                  className={styles.rightInput}
                  placeholder='请输入开户行名称'
                  onInput={e => setOpeningBank(e.detail.value)}
                />
              </>
            }
          /> */}
        </View>
      </Card>
      {showVerifycode && <Card className={styles.pageCard} style={{padding: 0}}>
        <View className={styles.bindingMain}>
          <Cell
            title='手机号'
            right={
              <>
                <Input
                  placeholderClass={styles.inputPlaceholder}
                  type='number'
                  maxlength={11}
                  className={styles.bankcardLongInput}
                  placeholder='请输入银行卡绑定手机号'
                  value={bankMobileNo}
                  onInput={e => setBankMobileNo(e.detail.value)}
                  onFocus={e => {
                    if (bankMobileNo === user.mobile) {
                      setBankMobileNo('');
                    }
                  }}
                />
              </>
            }
          />
          <Cell
            title='验证码'
            right={
              <>
                <Input
                  placeholderClass={styles.inputPlaceholder}
                  type='number'
                  className={styles.rightInput}
                  placeholder='请输入银行卡验证码'
                  maxlength={6}
                  onInput={e => setVerificationCode(e.detail.value)}
                />
                <CyVerificationCodeButton type='text' sendSuccess={sendSuccess} onSend={handleApply} />
              </>
            }
          />
        </View>
      </Card>}
      <Card className={styles.pageCard} style={{ padding: 0 }}>
        <View className={styles.bindingMain}>
          <Cell
            title='设为默认银行卡'
            right={
              <>
                <Switch checked={defaultFlag} onChange={e => setDefaultFlag(e.detail.value)} />
              </>
            }
          />
        </View>
      </Card>
      <View className={styles.agreementContainer}>
        {cutPayment.length ? <View className={styles.agreement}>
          <CyContractBar
            justify='center'
            onChange={val => {
              setIsAgreedCutPayment(val);
            }}
            mustView
            lazy
            checked={isAgreedCutPayment}
            contracts={cutPayment}
          />
        </View> : null}
        {contracts.length ? <View className={styles.agreement}>
          <CyContractBar
            justify='center'
            onChange={val => {
              setIsAgreed(val);
            }}
            lazy
            checked={isAgreed}
            contracts={contracts}
          />
        </View> : null}
      </View>
      {bankList.length ? <Card>
        <View className={styles.bindingMain} onClick={() => setVisible(true)}>
          <View className={styles.bindingBank}>{bankList.length}家银行支持绑定</View>
          <View className={styles.bankConainer}>
            {bankList.map((item, index) => (
              index < 8 ? <View key={index} className={styles.bankIcon}>
                <Image src={item.bankIconImgUrl || defaultBankIcon} style={{ width: '100%', height: '100%' }} />
              </View> : null
            ))}
            ···
          </View>
          <Image src={arrowRight} className={styles.bindArrowRight} />
        </View>
      </Card> : null}


      <View className={`${styles['bottom-fixed']} bottom-fixed`}>
        <CyButton round block type='primary' className={styles.bindbtn} onClick={handleSubmit}>
          {showVerifycode ? '提交' : '下一步'}
        </CyButton>
      </View>
      <SupportBanks visible={visible} banks={bankList} onClose={() => setVisible(false)} />
    </PageWrap>
  );
}
  ;

export default BankCardBinding;
