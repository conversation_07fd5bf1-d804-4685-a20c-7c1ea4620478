import {CSSProperties, FC} from 'react';
import {View} from '@tarojs/components';
import Taro from '@tarojs/taro';
import './index.scss';

type Props = {
  children: React.ReactElement[] | React.ReactElement;
};
type RowProps = {
  row: number;
  children: React.ReactElement[] | React.ReactElement;
  bgColor?: string;
  darkBgColor?: string;
  key?: string | number;
};
type ItemProps = {
  width?: string | number;
  children: React.ReactElement | React.ReactElement[] | string;
};
type TCySimpleTable = FC<Props> & {Row: FC<RowProps>} & {Item: FC<ItemProps>};
/**
 * 组件名称：简单的table
 * 组件描述：展示简单的table
 */
const CySimpleTable: TCySimpleTable = ({children}) => {
  return children;
};

const Row: FC<RowProps> = ({row, children, bgColor = '#ffffff', darkBgColor = '#F8FAFF'}) => {
  return (
    <View className='table-colomn' style={{backgroundColor: row % 2 !== 0 ? darkBgColor : bgColor}}>
      {children}
    </View>
  );
};
CySimpleTable.Row = Row;

const Item: FC<ItemProps> = ({children, width}) => {
  const style: CSSProperties = {};

  if (width !== undefined) {
    if (typeof width === 'number') {
      style.flex = width;
    } else {
      if (width.endsWith('px') || width.endsWith('PX')) {
        style.width = Taro.pxTransform(parseInt(width.substring(0, width.length - 2)));
      } else {
        style.width = Taro.pxTransform(parseInt(width));
      }
    }
  } else {
    style.flex = 1;
  }
  return (
    <View className='row table-colomn-row' style={style}>
      {children}
    </View>
  );
};
CySimpleTable.Item = Item;
export default CySimpleTable;
