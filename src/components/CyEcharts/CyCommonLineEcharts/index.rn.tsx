import {FC, useEffect, useRef, useState} from 'react';
import {View} from 'react-native';
import CyLineEcharts from '../CyLineEcharts';

type Props = {
  legend_data?: string[];
  series_data: {
    name: string;
    //转折点样式
    itemStyle: {
      color: string;
    };
    data: string[] | number[];
  }[];
  y_axis_name: string[];
  x_axis_data: string[];
  x_axis_label_interval?: ((index: number, value: string) => void) | number;
  y_name_padding?: number[][];
  y_split_config?: string[];
  customerToolTip?: (data: any) => React.ReactNode; //自定义tooltip
};

/**
 * 组件名称：图表
 * 组件描述：图表
 */
const CyCommonLineEcharts: FC<Props> = props => {
  const containerRef = useRef(null);
  const [chartSize, setChartSize] = useState({width: 0, height: 0});

  const margin_top = useRef(20);
  const [options, setOptions] = useState<any>({});

  const y_name_padding = props.y_name_padding ?? [[1, 1, 1, 1]];
  useEffect(() => {
    configureOption();
  }, [props]);

  useEffect(() => {}, [options]);

  if (props.y_axis_name) {
    margin_top.current = 40;
  }

  const default_series_style = {
    type: 'line',
    smooth: false,
    symbolSize: 5,
    lineStyle: {
      opacity: 1,
      width: 1,
    },
  };

  const x_data: string[] = [];

  const default_interval = (index: number) => {
    if ((index + 1) % 5 === 0) {
      return true;
    }
    return false;
  };

  const configureOption = () => {
    let legend;

    if (props.legend_data) {
      legend = {
        left: 0,
        top: margin_top.current - 40,
        icon: 'rect',
        selectedMode: false,
        //文字样式
        textStyle: {
          color: '#333333',
          fontSize: 12,
        },
        data: [...props.legend_data],
        itemWidth: 14,
        itemHeight: 14,
      };
    }

    let series = [];
    for (let i = 0; i < props.series_data.length; i++) {
      series.push({
        ...default_series_style,
        ...props.series_data[i],
      });
    }

    const xAxis = {
      type: 'category',
      //刻度标签
      axisLabel: {
        color: '#333333',
        rotate: 0,
        interval: props.x_axis_label_interval !== undefined ? props.x_axis_label_interval : default_interval,
      },
      data: props.x_axis_data.length > 0 ? props.x_axis_data : x_data,
      axisTick: {
        alignWithLabel: true,

        length: 0,
      },
      axisLine: {
        show: props.x_axis_data.length > 0,
        lineStyle: {
          color: '#333333',
        },
      },
    };

    const yAxis = props.y_axis_name.map((value, index) => {
      let split_config = {};
      if (props.y_split_config !== undefined && props.y_split_config.length > index) {
        split_config = props.y_split_config[index];
      }
      return {
        name: value,
        nameGap: 10,
        nameTextStyle: {
          color: '#333333',
          padding: y_name_padding[index] ? y_name_padding[index] : [0, 0, 0, 0],
          align: 'left',
          fontSize: '14',
        },
        //刻度标签
        axisLabel: {
          color: '#333333',
        },
        //分割线
        splitLine: {
          show: false,
          lineStyle: {
            color: '#999999',
            width: 0.5,
          },
        },
        axisTick: {
          alignWithLabel: true,
          interval: 0,
          length: 3,
        },

        ...split_config,
        axisLine: {
          show: true,
          lineStyle: {
            color: '#333333',
          },
        },
      };
    });

    const _options = {
      tooltip: {
        trigger: 'axis',
        confine: false,
        axisPointer: {
          animation: false,
        },
        formatter: props.customerToolTip
          ? (params: any, ticket: any, callback: any) => {
              return '';
            }
          : undefined,
      },
      legend,
      //网格
      grid: {
        // show: true,
        // borderColor: "#ccc",
        // borderWidth: 2,
        containLabel: true,
        left: 0,
        right: 30,
        top: margin_top.current,
        bottom: 20,
      },
      xAxis,
      yAxis,
      series,
      backgroundColor: 'transparent',
    };
    setOptions(_options);
  };
  return (
    <View
      style={{width: '100%', height: '100%'}}
      ref={containerRef}
      onLayout={res => {
        // if (chartSize.width === 0 || chartSize.height === 0) {
        setChartSize({width: res.nativeEvent.layout.width, height: res.nativeEvent.layout.height});
        // }
      }}>
      {chartSize.width > 0 && chartSize.height > 0 && (
        <CyLineEcharts
          {...options}
          width={chartSize.width}
          height={chartSize.height}
          customerToolTip={props.customerToolTip}
        />
      )}
    </View>
  );
};

export default CyCommonLineEcharts;
