import {ForwardRefRenderFunction, forwardRef, useImperativeHandle} from 'react';
import Taro, {Page, useUnload} from '@tarojs/taro';
import {CySafeKeyboardProps, CySafeKeyboardRef} from '.';
import './index.scss';

/**
 * 组件名称：密码键盘
 * 组件描述：密码键盘
 */
const CySafeKeyboard: ForwardRefRenderFunction<CySafeKeyboardRef, CySafeKeyboardProps> = (
  {
    onInputClick,
    isClickShowboard = true,
    sipId,
    serverRandom,
    maxLength = 8,
    minLength = 6,
    isGrid = false,
    keyboardType,
    displayMode = 0,
    orderType = 0,
    placeholder,
    onInputChangeCallBack,
    onDoneClick,
    ...props
  },
  ref,
) => {
  const showLastCharacter = true;
  const isEncryptState = true;
  const precision = 2;
  useImperativeHandle(ref, () => ({
    show: () => {
      handlerKeyboardShow();
    },
    hidden: () => {
      handlerKeyboardHidden();
    },
    getVersion: () => {
      return handlerGetVersion();
    },
    clearInputValue: () => {
      return handlerClearInputValue();
    },
    getEncryptedInputValue: () => {
      return handlerGetEncryptedInputValue();
    },
    getEncryptedClientRandom: () => {
      return handlerGetEncryptedClientRandom();
    },
    checkInputValueMatch: (otherId: string) => {
      return handlerCheckInputValueMatch(otherId);
    },
    getInputValue: () => {
      return handlerGetInputValue();
    },
    doneClick: () => {
      console.log('CySafeKeyboard doneClick1111');
      return doneClick();
    },
  }));

  const handlerKeyboardShow = () => {
    const {page} = Taro.getCurrentInstance() as Page;
    page.selectComponent(`#${sipId}`)?.showKeyboard();
  };

  const handlerKeyboardHidden = () => {
    const {page} = Taro.getCurrentInstance() as Page;
    page.selectComponent(`#${sipId}`)?.hideKeyboard();
  };

  const handlerGetVersion = () => {
    const {page} = Taro.getCurrentInstance() as Page;
    return page.selectComponent(`#${sipId}`)?.getVersion();
  };

  const handlerClearInputValue = () => {
    const {page} = Taro.getCurrentInstance() as Page;
    return page.selectComponent(`#${sipId}`)?.clearInputValue();
  };

  const handlerGetEncryptedInputValue = () => {
    const {page} = Taro.getCurrentInstance() as Page;
    return page.selectComponent(`#${sipId}`)?.getEncryptedInputValue();
  };

  const handlerGetEncryptedClientRandom = () => {
    const {page} = Taro.getCurrentInstance() as Page;
    return page.selectComponent(`#${sipId}`)?.getEncryptedClientRandom();
  };

  const handlerCheckInputValueMatch = (otherId: string) => {
    const {page} = Taro.getCurrentInstance() as Page;
    return page.selectComponent(`#${sipId}`)?.checkInputValueMatch(otherId);
  };

  const handlerGetInputValue = () => {
    const {page} = Taro.getCurrentInstance() as Page;
    return page.selectComponent(`#${sipId}`)?.getInputValue();
  };

  const doneClick = () => {
    const {page} = Taro.getCurrentInstance() as Page;
    console.log('CySafeKeyboard doneClick');
    return page.selectComponent(`#${sipId}`)?.triggerEvent("doneClick", { sipId });
  };

  /**
   * 输入框点击事件,默认触发键盘展示，若有其他事件，请设置isClickShowboard为false，再设置onInputClick
   */
  const handleInpuclick = (res: any) => {
    console.log('CySafeKeyboard onInputClick', isClickShowboard, res);
    if (isClickShowboard) {
      handlerKeyboardShow();
    }
    onInputClick?.(res);
  };

  const handleInputChange = (e: any) => {
    const {sipId: id, changeType, length} = e.detail;
    onInputChangeCallBack?.(id, length);
  };

  useUnload(() => {
    // 交易密码键盘卸载时放开屏幕录制限制
    Taro.setVisualEffectOnCapture({
      visualEffect: 'none',
      success:(res) => {
        //
      },
    })
  });

  return (
    <>
      <sip-keyboard
        focus-class='focus'
        text-class='text'
        placeholder-class='placeholder'
        serverRandom={serverRandom}
        maxLength={maxLength}
        minLength={minLength}
        isGrid={isGrid}
        keyboardType={keyboardType}
        // cipherType={cipherType}
        // outputType={outputType}
        // inputRegex={inputRegex}
        // keyword={keyword}
        isEncryptState={isEncryptState}
        displayMode={displayMode}
        orderType={orderType}
        // isShowKeyboard={isShowKeyboard}
        // isKeyAnimation={isKeyAnimation}
        showLastCharacter={showLastCharacter}
        precision={precision}
        // allowScreenRecord={allowScreenRecord}
        placeholder={placeholder}
        onInputClick={handleInpuclick}
        bindinputClick={handleInpuclick}
        onInputChangeCallBack={handleInputChange}
        bindinputChangeCallBack={handleInputChange}
        binddoneClick={onDoneClick}
        onDoneClick={onDoneClick}
        sipId={sipId}
        id={sipId}
        style={props.style}
      />
    </>
  );
};

export default forwardRef(CySafeKeyboard);
