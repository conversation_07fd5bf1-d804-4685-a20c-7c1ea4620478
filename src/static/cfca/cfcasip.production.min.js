var goog = goog || {};
goog.global = this;
goog.exportPath_ = function (name, opt_object, opt_objectToExportTo) {
	var parts = name.split('.');
	var cur = opt_objectToExportTo || goog.global;
	if (!(parts[0] in cur) && cur.execScript) cur.execScript('var ' + parts[0]);
	for (var part; parts.length && (part = parts.shift()); )
		if (!parts.length && opt_object !== undefined) cur[part] = opt_object;
		else if (cur[part]) cur = cur[part];
		else cur = cur[part] = {};
};
goog.exportSymbol = function (publicPath, object, opt_objectToExportTo) {
	goog.exportPath_(publicPath, object, opt_objectToExportTo);
};
goog.exportProperty = function (object, publicName, symbol) {
	object[publicName] = symbol;
};
var HTML5_SIP_VERSION = '*******';
var KEY_ID_SEPERATOR = '___';
var COMPLETE_KEYBOARD_CONTAINER_ID = 'CompleteKeyboard';
var NUMBER_KEYBOARD_CONTAINER_ID = 'NumberKeyboard';
var CAPS_KEY_ID = 'caps';
var DELETE_KEY_ID = 'delete';
var SHIFT_KEY_ID = 'shift';
var SPACE_KEY_ID = 'space';
var FINISH_KEY_ID = 'finish';
var SYMBOLE_DELET_KEY_ID = 'sym-delete';
var SYMBOLE_SHIFT_KEY_ID = 'sym-shift';
var SYMBOLE_SPACE_KEY_ID = 'sym-space';
var SYMBOLE_FINISH_KEY_ID = 'sym-finish';
var BLANK_DIV_ID = '_INNER_BLANK_';
var FINISH_KEY_TEXT = '\u5b8c\u6210';
var SHIFT_KEY_TEXT_LETTER = '#+=';
var SHIFT_KEY_TEXT_SYMBOL = '\u8fd4\u56de';
var ORDERED_NUMBERS = ['1', '2', '3', '4', '5', '6', '7', '8', '9', '0'];
var ORDERED_LETTERS = [
	'q',
	'w',
	'e',
	'r',
	't',
	'y',
	'u',
	'i',
	'o',
	'p',
	'a',
	's',
	'd',
	'f',
	'g',
	'h',
	'j',
	'k',
	'l',
	'z',
	'x',
	'c',
	'v',
	'b',
	'n',
	'm',
];
var ORDERED_SYMBOLS = [
	'!',
	'@',
	'#',
	'$',
	'%',
	'^',
	'&',
	'*',
	'(',
	')',
	'-',
	'_',
	'+',
	'{',
	'}',
	'[',
	']',
	'<',
	'>',
	':',
	';',
	'"',
	"'",
	',',
	'.',
	'?',
	'=',
	'/',
	'\\',
	'|',
	'~',
	'`',
];
var TOUCH_EVENTS = [
	'touchstart',
	'touchmove',
	'touchend',
	'MSPointerDown',
	'MSPointerMove',
	'MSPointerUp',
	'mousedown',
	'mousemove',
	'mouseup',
];
var KEYBOARD_TYPE_NUMBER = 0;
var KEYBOARD_TYPE_COMPLETE = 1;
var KEYBOARD_DISORDER_NONE = 0;
var KEYBOARD_DISORDER_ONLY_DIGITAL = 1;
var KEYBOARD_DISORDER_ALL = 2;
var DEFAULT_MIN_LENGTH = 6;
var DEFAULT_MAX_LENGTH = 8;
var OUTPUT_TYPE_HASH = 1;
var OUTPUT_TYPE_ORIGINAL = 2;
var CIPHER_TYPE_SM2 = 1;
var CIPHER_TYPE_RSA = 0;
var CFCA_KEYBOARD_INSERT = 1;
var CFCA_KEYBOARD_DELETE = 2;
var REG_NUMBER = '[0-9]+';
var REG_LETTERS_LOW = '[a-z]+';
var REG_LETTERS_UP = '[A-Z]+';
var REG_SYMBOLS = '[^A-Za-z0-9]+';
var NUMBERREG = 1;
var LETTERLOWREG = 2;
var LETTERUPREG = 3;
var SYMBOLSREG = 4;
var CFCA_OK = 0;
var CFCA_ERROR_INVALID_PARAMETER = 4097;
var CFCA_ERROR_INVALID_SIP_HANDLE_ID = 4098;
var CFCA_ERROR_INPUT_LENGTH_OUT_OF_RANGE = 4099;
var CFCA_ERROR_INPUT_VALUE_IS_NULL = 4100;
var CFCA_ERROR_SERVER_RANDOM_INVALID = 4101;
var CFCA_ERROR_SERVER_RANDOM_IS_NULL = 4102;
var CFCA_ERROR_INPUT_VALUE_NOT_MATCH_REGEX = 4103;
var CFCA_ERROR_RSA_ENCRYPT_FAILED = 4104;
var CFCA_ERROR_SM4_ENCRYPT_FAILED = 4105;
var CFCA_ERROR_DES3_ENCRYPT_FAILED = 4112;
(function () {
	var k,
		ca = new (function () {
			this.ob = function (a, b) {
				if ('8' != a.substring(b + 2, b + 3)) return 1;
				var c = parseInt(a.substring(b + 3, b + 4));
				return c ? (0 < c && 10 > c ? c + 1 : -2) : -1;
			};
			this.Fb = function (a, b) {
				var c = this.ob(a, b);
				return 1 > c ? '' : a.substring(b + 2, b + 2 + 2 * c);
			};
			this.Ya = function (a, b) {
				var c = this.Fb(a, b);
				return '' == c
					? -1
					: aa(
							8 > parseInt(c.substring(0, 1))
								? new m(c, 16)
								: new m(c.substring(2), 16)
					  );
			};
			this.Za = function (a, b) {
				var c = this.ob(a, b);
				return 0 > c ? c : b + 2 * (c + 1);
			};
			this.Xa = function (a, b) {
				var c = this.Za(a, b),
					d = this.Ya(a, b);
				return a.substring(c, c + 2 * d);
			};
			this.Ib = function (a, b) {
				var c = this.Za(a, b),
					d = this.Ya(a, b);
				return c + 2 * d;
			};
			this.qb = function (a, b) {
				var c = [],
					d = this.Za(a, b);
				c.push(d);
				for (var e = this.Ya(a, b), f = d, g = 0; ; ) {
					f = this.Ib(a, f);
					if (null == f || f - d >= 2 * e) break;
					if (200 <= g) break;
					c.push(f);
					g++;
				}
				return c;
			};
			this.pb = function (a, b, c) {
				if (0 == c.length) return b;
				var d = c.shift();
				b = this.qb(a, b);
				return this.pb(a, b[d], c);
			};
		})();
	ca.Yb = function (a, b, c, d) {
		b = this.pb(a, b, c);
		if (void 0 === b) throw "can't find nthList object";
		if (void 0 !== d && a.substr(b, 2) != d)
			throw "checking tag doesn't match: " + a.substr(b, 2) + '!=' + d;
		return this.Xa(a, b);
	};
	ca.Zb = function (a) {
		function b(a) {
			return 7 <= a.length ? a : Array(7 - a.length + 1).join('0') + a;
		}
		var c = [],
			d = parseInt(a.substr(0, 2), 16);
		c[0] = new String(Math.floor(d / 40));
		c[1] = new String(d % 40);
		var e = a.substr(2);
		a = [];
		for (d = 0; d < e.length / 2; d++)
			a.push(parseInt(e.substr(2 * d, 2), 16));
		for (var e = [], f = '', d = 0; d < a.length; d++)
			a[d] & 128
				? (f += b((a[d] & 127).toString(2)))
				: ((f += b((a[d] & 127).toString(2))),
				  e.push(new String(parseInt(f, 2))),
				  (f = ''));
		c = c.join('.');
		0 < e.length && (c = c + '.' + e.join('.'));
		return c;
	};
	if ('undefined' === typeof n) var n = {};
	function da(a) {
		for (var b = 0; b < a.length; b++) a[b] = 0;
	}
	function ea(a) {
		return 'undefined' === typeof n[a]
			? -1
			: fa(a) || ga(a)
			? n[a].l.length
			: n[a].s.length;
	}
	function fa(a) {
		return n[a].W === CIPHER_TYPE_SM2 && n[a].Ka === OUTPUT_TYPE_ORIGINAL;
	}
	function ga(a) {
		return n[a].W === CIPHER_TYPE_RSA && n[a].Ka === OUTPUT_TYPE_ORIGINAL;
	}
	function ha(a, b) {
		if ('undefined' === typeof n[a])
			return CFCA_ERROR_INVALID_SIP_HANDLE_ID;
		var c = ia(b),
			d,
			e = [];
		for (d = 0; 2 * d < c.length; ++d)
			e[d] = parseInt(c.substring(2 * d, 2 * d + 2), 16);
		if (16 != e.length)
			return (n[a].ma = null), CFCA_ERROR_SERVER_RANDOM_INVALID;
		n[a].ma = e;
		return CFCA_OK;
	}
	function ja(a, b) {
		if ('undefined' === typeof n[a])
			return CFCA_ERROR_INVALID_SIP_HANDLE_ID;
		if (0 > b) return CFCA_ERROR_INVALID_PARAMETER;
		n[a].maxLength = b;
		return CFCA_OK;
	}
	function ka(a, b) {
		if ('undefined' === typeof n[a])
			return CFCA_ERROR_INVALID_SIP_HANDLE_ID;
		if (0 > b) return CFCA_ERROR_INVALID_PARAMETER;
		n[a].cb = b;
		return CFCA_OK;
	}
	function la(a, b) {
		if ('undefined' === typeof n[a])
			return CFCA_ERROR_INVALID_SIP_HANDLE_ID;
		if (b != OUTPUT_TYPE_HASH && b != OUTPUT_TYPE_ORIGINAL)
			return CFCA_ERROR_INVALID_PARAMETER;
		n[a].Ka = b;
		return CFCA_OK;
	}
	function ma(a, b) {
		if ('undefined' === typeof n[a])
			return CFCA_ERROR_INVALID_SIP_HANDLE_ID;
		if (b != CIPHER_TYPE_SM2 && b != CIPHER_TYPE_RSA)
			return CFCA_ERROR_INVALID_PARAMETER;
		n[a].W = b;
		return CFCA_OK;
	}
	function na(a) {
		var b, c;
		if ('undefined' === typeof n[a])
			return (n[a].errorCode = CFCA_ERROR_INVALID_SIP_HANDLE_ID), '';
		if (null == n[a].ma)
			return (n[a].errorCode = CFCA_ERROR_SERVER_RANDOM_IS_NULL), '';
		if ('' == n[a].s && !fa(a) && !ga(a))
			return (n[a].errorCode = CFCA_ERROR_INPUT_VALUE_IS_NULL), '';
		if (ga(a) || fa(a)) {
			if (0 >= n[a].l.length)
				return (n[a].errorCode = CFCA_ERROR_INPUT_VALUE_IS_NULL), '';
			if (n[a].l.length < n[a].cb || n[a].l.length > n[a].maxLength)
				return (
					(n[a].errorCode = CFCA_ERROR_INPUT_LENGTH_OUT_OF_RANGE), ''
				);
		}
		if (n[a].W == CIPHER_TYPE_RSA) {
			b = Array(24);
			c = Array(8);
			for (var d = 0; 16 > d; d++)
				12 > d
					? ((b[d] = n[a].ma[d]), (b[d + 12] = n[a].v[d]))
					: ((c[d - 12] = n[a].ma[d]), (c[d - 12 + 4] = n[a].v[d]));
		} else
			for (b = Array(16), c = Array(16), d = 0; 16 > d; d++)
				8 > d
					? ((b[d] = n[a].ma[d]), (b[d + 8] = n[a].v[d]))
					: ((c[d - 8] = n[a].ma[d]), (c[d] = n[a].v[d]));
		if (fa(a)) {
			var e = n[a].l,
				f = c;
			c = n[a].da;
			d = 0;
			if (0 == e.length || 16 != b.length || 16 != f.length)
				b = { result: null, errorCode: -1 };
			else {
				b = oa(A(b), !1);
				for (var g = A(f, !1), f = []; d < e.length; d += 16)
					var l = A(e.slice(d, d + 16), !0, c),
						l = pa(l, b, g, 1, !0, c),
						f = f.concat(l.A);
				e.length & 15 ||
					((b = pa(
						[269488144, 269488144, 269488144, 269488144],
						b,
						g,
						1,
						!1,
						c
					)),
					(f = f.concat(b.A)));
				b = { result: ra(f), errorCode: 0 };
			}
			b = sa(b.result);
			if (null === b || '' === b)
				return (n[a].errorCode = CFCA_ERROR_SM4_ENCRYPT_FAILED), '';
			n[a].errorCode = CFCA_OK;
			return b;
		}
		if (ga(a)) {
			var d = ta,
				f = ua(n[a].l),
				h = ua(c);
			c = n[a].X;
			var l = [
					16843776, 0, 65536, 16843780, 16842756, 66564, 4, 65536,
					1024, 16843776, 16843780, 1024, 16778244, 16842756,
					16777216, 4, 1028, 16778240, 16778240, 66560, 66560,
					16842752, 16842752, 16778244, 65540, 16777220, 16777220,
					65540, 0, 1028, 66564, 16777216, 65536, 16843780, 4,
					16842752, 16843776, 16777216, 16777216, 1024, 16842756,
					65536, 66560, 16777220, 1024, 4, 16778244, 66564, 16843780,
					65540, 16842752, 16778244, 16777220, 1028, 66564, 16843776,
					1028, 16778240, 16778240, 0, 65540, 66560, 0, 16842756,
				],
				p = [
					-2146402272, -2147450880, 32768, 1081376, 1048576, 32,
					-2146435040, -2147450848, -2147483616, -2146402272,
					-2146402304, -2147483648, -2147450880, 1048576, 32,
					-2146435040, 1081344, 1048608, -2147450848, 0, -2147483648,
					32768, 1081376, -2146435072, 1048608, -2147483616, 0,
					1081344, 32800, -2146402304, -2146435072, 32800, 0, 1081376,
					-2146435040, 1048576, -2147450848, -2146435072, -2146402304,
					32768, -2146435072, -2147450880, 32, -2146402272, 1081376,
					32, 32768, -2147483648, 32800, -2146402304, 1048576,
					-2147483616, 1048608, -2147450848, -2147483616, 1048608,
					1081344, 0, -2147450880, 32800, -2147483648, -2146435040,
					-2146402272, 1081344,
				],
				x = [
					520, 134349312, 0, 134348808, 134218240, 0, 131592,
					134218240, 131080, 134217736, 134217736, 131072, 134349320,
					131080, 134348800, 520, 134217728, 8, 134349312, 512,
					131584, 134348800, 134348808, 131592, 134218248, 131584,
					131072, 134218248, 8, 134349320, 512, 134217728, 134349312,
					134217728, 131080, 520, 131072, 134349312, 134218240, 0,
					512, 131080, 134349320, 134218240, 134217736, 512, 0,
					134348808, 134218248, 131072, 134217728, 134349320, 8,
					131592, 131584, 134217736, 134348800, 134218248, 520,
					134348800, 131592, 8, 134348808, 131584,
				],
				z = [
					8396801, 8321, 8321, 128, 8396928, 8388737, 8388609, 8193,
					0, 8396800, 8396800, 8396929, 129, 0, 8388736, 8388609, 1,
					8192, 8388608, 8396801, 128, 8388608, 8193, 8320, 8388737,
					1, 8320, 8388736, 8192, 8396928, 8396929, 129, 8388736,
					8388609, 8396800, 8396929, 129, 0, 0, 8396800, 8320,
					8388736, 8388737, 1, 8396801, 8321, 8321, 128, 8396929, 129,
					1, 8192, 8388609, 8193, 8396928, 8388737, 8193, 8320,
					8388608, 8396801, 128, 8388608, 8192, 8396928,
				],
				F = [
					256, 34078976, 34078720, 1107296512, 524288, 256,
					1073741824, 34078720, 1074266368, 524288, 33554688,
					1074266368, 1107296512, 1107820544, 524544, 1073741824,
					33554432, 1074266112, 1074266112, 0, 1073742080, 1107820800,
					1107820800, 33554688, 1107820544, 1073742080, 0, 1107296256,
					34078976, 33554432, 1107296256, 524544, 524288, 1107296512,
					256, 33554432, 1073741824, 34078720, 1107296512, 1074266368,
					33554688, 1073741824, 1107820544, 34078976, 1074266368, 256,
					33554432, 1107820544, 1107820800, 524544, 1107296256,
					1107820800, 34078720, 0, 1074266112, 1107296256, 524544,
					33554688, 1073742080, 524288, 0, 1074266112, 34078976,
					1073742080,
				],
				q = [
					536870928, 541065216, 16384, 541081616, 541065216, 16,
					541081616, 4194304, 536887296, 4210704, 4194304, 536870928,
					4194320, 536887296, 536870912, 16400, 0, 4194320, 536887312,
					16384, 4210688, 536887312, 16, 541065232, 541065232, 0,
					4210704, 541081600, 16400, 4210688, 541081600, 536870912,
					536887296, 16, 541065232, 4210688, 541081616, 4194304,
					16400, 536870928, 4194304, 536887296, 536870912, 16400,
					536870928, 541081616, 4210688, 541065216, 4210704,
					541081600, 0, 541065232, 16, 16384, 541065216, 4210704,
					16384, 4194320, 536887312, 0, 541081600, 536870912, 4194320,
					536887312,
				],
				w = [
					2097152, 69206018, 67110914, 0, 2048, 67110914, 2099202,
					69208064, 69208066, 2097152, 0, 67108866, 2, 67108864,
					69206018, 2050, 67110912, 2099202, 2097154, 67110912,
					67108866, 69206016, 69208064, 2097154, 69206016, 2048, 2050,
					69208066, 2099200, 2, 67108864, 2099200, 67108864, 2099200,
					2097152, 67110914, 67110914, 69206018, 69206018, 2, 2097154,
					67108864, 67110912, 2097152, 69208064, 2050, 2099202,
					69208064, 2050, 67108866, 69208066, 69206016, 2099200, 0, 2,
					69208066, 0, 2099202, 69206016, 2048, 67108866, 67110912,
					2048, 2097154,
				],
				B = [
					268439616, 4096, 262144, 268701760, 268435456, 268439616,
					64, 268435456, 262208, 268697600, 268701760, 266240,
					268701696, 266304, 4096, 64, 268697600, 268435520,
					268439552, 4160, 266240, 262208, 268697664, 268701696, 4160,
					0, 0, 268697664, 268435520, 268439552, 266304, 262144,
					266304, 262144, 268701696, 4096, 64, 268697664, 4096,
					266304, 268439552, 64, 268435520, 268697600, 268697664,
					268435456, 262144, 268439616, 0, 268701760, 262208,
					268435520, 268697600, 268439552, 268439616, 0, 268701760,
					266240, 266240, 4160, 4160, 262208, 268435456, 268701696,
				];
			b = va(ua(b));
			var y = 0,
				r,
				v,
				u,
				ba,
				qa,
				t,
				G,
				Sa,
				Ta,
				O = f.length,
				Ba = 0,
				Ua = 32 == b.length ? 3 : 9;
			G = 3 == Ua ? [0, 32, 2] : [0, 32, 2, 62, 30, -2, 64, 96, 2];
			t = O;
			u = 8 - (O % 8);
			f += String.fromCharCode(
				u ^ c[t++ % 8],
				u ^ c[t++ % 8],
				u ^ c[t++ % 8],
				u ^ c[t++ % 8],
				u ^ c[t++ % 8],
				u ^ c[t++ % 8],
				u ^ c[t++ % 8],
				u ^ c[t++ % 8]
			);
			8 == u && (O += 8);
			tempresult = result = '';
			u =
				(h.charCodeAt(y++) << 24) |
				(h.charCodeAt(y++) << 16) |
				(h.charCodeAt(y++) << 8) |
				h.charCodeAt(y++);
			r =
				(h.charCodeAt(y++) << 24) |
				(h.charCodeAt(y++) << 16) |
				(h.charCodeAt(y++) << 8) |
				h.charCodeAt(y++);
			for (y = 0; y < O; ) {
				0 >= y &&
					((g = ua(c)),
					(e =
						(g.charCodeAt(0) << 24) |
						(g.charCodeAt(1) << 16) |
						(g.charCodeAt(2) << 8) |
						g.charCodeAt(3)),
					(g =
						(g.charCodeAt(4) << 24) |
						(g.charCodeAt(5) << 16) |
						(g.charCodeAt(6) << 8) |
						g.charCodeAt(7)));
				h =
					(f.charCodeAt(y++) << 24) |
					(f.charCodeAt(y++) << 16) |
					(f.charCodeAt(y++) << 8) |
					f.charCodeAt(y++);
				t =
					(f.charCodeAt(y++) << 24) |
					(f.charCodeAt(y++) << 16) |
					(f.charCodeAt(y++) << 8) |
					f.charCodeAt(y++);
				h ^= u ^ e;
				t ^= r ^ g;
				u = ((h >>> 4) ^ t) & 252645135;
				t ^= u;
				h ^= u << 4;
				u = ((h >>> 16) ^ t) & 65535;
				t ^= u;
				h ^= u << 16;
				u = ((t >>> 2) ^ h) & 858993459;
				h ^= u;
				t ^= u << 2;
				u = ((t >>> 8) ^ h) & 16711935;
				h ^= u;
				t ^= u << 8;
				u = ((h >>> 1) ^ t) & 1431655765;
				t ^= u;
				h ^= u << 1;
				h = (h << 1) | (h >>> 31);
				t = (t << 1) | (t >>> 31);
				for (v = 0; v < Ua; v += 3) {
					Sa = G[v + 1];
					Ta = G[v + 2];
					for (r = G[v]; r != Sa; r += Ta)
						(ba = t ^ b[r]),
							(qa = ((t >>> 4) | (t << 28)) ^ b[r + 1]),
							(u = h),
							(h = t),
							(t =
								u ^
								(p[(ba >>> 24) & 63] |
									z[(ba >>> 16) & 63] |
									q[(ba >>> 8) & 63] |
									B[ba & 63] |
									l[(qa >>> 24) & 63] |
									x[(qa >>> 16) & 63] |
									F[(qa >>> 8) & 63] |
									w[qa & 63]));
					u = h;
					h = t;
					t = u;
				}
				h = (h >>> 1) | (h << 31);
				t = (t >>> 1) | (t << 31);
				u = ((h >>> 1) ^ t) & 1431655765;
				t ^= u;
				h ^= u << 1;
				u = ((t >>> 8) ^ h) & 16711935;
				h ^= u;
				t ^= u << 8;
				u = ((t >>> 2) ^ h) & 858993459;
				h ^= u;
				t ^= u << 2;
				u = ((h >>> 16) ^ t) & 65535;
				t ^= u;
				h ^= u << 16;
				u = ((h >>> 4) ^ t) & 252645135;
				t ^= u;
				u = h ^= u << 4;
				r = t;
				tempresult += String.fromCharCode(
					h >>> 24,
					(h >>> 16) & 255,
					(h >>> 8) & 255,
					h & 255,
					t >>> 24,
					(t >>> 16) & 255,
					(t >>> 8) & 255,
					t & 255
				);
				Ba += 8;
				512 == Ba &&
					((result += tempresult), (tempresult = ''), (Ba = 0));
			}
			b = wa(d(result + tempresult));
			if (null === b || '' === b)
				return (n[a].errorCode = CFCA_ERROR_DES3_ENCRYPT_FAILED), '';
			n[a].errorCode = CFCA_OK;
			return b;
		}
		e = xa(ya(n[a].ga, n[a].s, 0, n[a].ha, 0));
		if (e.length < n[a].cb || e.length > n[a].maxLength)
			return (n[a].errorCode = CFCA_ERROR_INPUT_LENGTH_OUT_OF_RANGE), '';
		if (n[a].Ka === OUTPUT_TYPE_HASH) {
			d = '';
			for (f = -1; ++f < e.length; )
				(g = e.charCodeAt(f)),
					(l = f + 1 < e.length ? e.charCodeAt(f + 1) : 0),
					55296 <= g &&
						56319 >= g &&
						56320 <= l &&
						57343 >= l &&
						((g = 65536 + ((g & 1023) << 10) + (l & 1023)), f++),
					127 >= g
						? (d += String.fromCharCode(g))
						: 2047 >= g
						? (d += String.fromCharCode(
								192 | ((g >>> 6) & 31),
								128 | (g & 63)
						  ))
						: 65535 >= g
						? (d += String.fromCharCode(
								224 | ((g >>> 12) & 15),
								128 | ((g >>> 6) & 63),
								128 | (g & 63)
						  ))
						: 2097151 >= g &&
						  (d += String.fromCharCode(
								240 | ((g >>> 18) & 7),
								128 | ((g >>> 12) & 63),
								128 | ((g >>> 6) & 63),
								128 | (g & 63)
						  ));
			e = Array(d.length >> 2);
			for (f = 0; f < e.length; f++) e[f] = 0;
			for (f = 0; f < 8 * d.length; f += 8)
				e[f >> 5] |= (d.charCodeAt(f / 8) & 255) << (24 - (f % 32));
			d = 8 * d.length;
			e[d >> 5] |= 128 << (24 - (d % 32));
			e[(((d + 64) >> 9) << 4) + 15] = d;
			d = Array(80);
			f = 1732584193;
			g = -271733879;
			l = -1732584194;
			p = 271733878;
			x = -1009589776;
			for (z = 0; z < e.length; z += 16) {
				F = f;
				q = g;
				w = l;
				B = p;
				y = x;
				for (G = 0; 80 > G; G++)
					16 > G
						? (O = e[z + G])
						: ((O = d[G - 3] ^ d[G - 8] ^ d[G - 14] ^ d[G - 16]),
						  (O = (O << 1) | (O >>> 31))),
						(d[G] = O),
						(O = za(
							za(
								(f << 5) | (f >>> 27),
								20 > G
									? (g & l) | (~g & p)
									: 40 > G
									? g ^ l ^ p
									: 60 > G
									? (g & l) | (g & p) | (l & p)
									: g ^ l ^ p
							),
							za(
								za(x, d[G]),
								20 > G
									? 1518500249
									: 40 > G
									? 1859775393
									: 60 > G
									? -1894007588
									: -899497514
							)
						)),
						(x = p),
						(p = l),
						(l = (g << 30) | (g >>> 2)),
						(g = f),
						(f = O);
				f = za(f, F);
				g = za(g, q);
				l = za(l, w);
				p = za(p, B);
				x = za(x, y);
			}
			e = [f, g, l, p, x];
			d = '';
			for (f = 0; f < 32 * e.length; f += 8)
				d += String.fromCharCode((e[f >> 5] >>> (24 - (f % 32))) & 255);
			e = d;
			try {
				Aa;
			} catch (Zc) {
				Aa = 0;
			}
			d = Aa ? '0123456789ABCDEF' : '0123456789abcdef';
			f = '';
			for (l = 0; l < e.length; l++)
				(g = e.charCodeAt(l)),
					(f += d.charAt((g >>> 4) & 15) + d.charAt(g & 15));
			e = wa(f);
		}
		n[a].W == CIPHER_TYPE_RSA
			? (b = wa(ta(ya(ua(b), e, 1, ua(c), 1))))
			: ((b = Ca(Da(e), b, c)), (b = sa(b.result)));
		n[a].errorCode = CFCA_OK;
		return b;
	}
	function Ea(a) {
		var b;
		if ('undefined' === typeof n[a])
			return (n[a].errorCode = CFCA_ERROR_INVALID_SIP_HANDLE_ID), '';
		if (n[a].W == CIPHER_TYPE_RSA) {
			var c = n[a].v;
			b = ia(
				'MIIBCgKCAQEA58todokERdqMdllLqbTS/iLkc1WN/ZhSbVbWiUMU+r1FIYIt1T7iu8w7j8ZtYVCiERHBO6TpRDKa8s1953Tnp4/T55v5NBLwhiAVufcJebfm3KyFo+RFoqvZEAssbr8VIbiTtTxvqTr4zH9D91uZyuWBXqyKK1P3HFsgGY0FrbygB14z/x+H7s4oruTLbICQlig/QbRaA6n3zHzHd9U33McRH30J1A4Z/df5+Pt7MsPCdq1nXHtvcGV7kZ0ieZfck4GXQlREIG11yOhpTvvT6srbaAklLTQzhPJ181GgeZiAE5I/LyYyw82VaHMKWwCB7W++XoRvEUBi5iCq3SC0OQIDAQAB'
			);
			var d = ca.qb(b, 0);
			if (2 != d.length) b = null;
			else {
				var e = ca.Xa(b, d[0]),
					d = ca.Xa(b, d[1]);
				b = new Fa();
				if (null != e && null != d && 0 < e.length && 0 < d.length)
					(e = new m(e, 16)), (b.n = e), (b.e = parseInt(d, 16));
				else throw Error('Invalid RSA public key');
				e = (Ga(b.n) + 7) >> 3;
				if (e < c.length + 11) throw Error('Message too long for RSA');
				for (var d = [], f = c.length - 1; 0 <= f && 0 < e; )
					d[--e] = c[f--];
				d[--e] = 0;
				c = new Ha();
				for (f = []; 2 < e; ) {
					for (f[0] = 0; 0 == f[0]; ) c.Ja(f);
					d[--e] = f[0];
				}
				d[--e] = 2;
				d[--e] = 0;
				c = new m(d);
				b = Ia(c, b.e, b.n);
				if (null == b) b = null;
				else if (((b = c = b.toString(16)), (c = c.length), c % 128))
					for (c = 128 - (c % 128), e = 0; e < c; e++) b = '0' + b;
				b = wa(b);
			}
		} else {
			b = n[a].v;
			e = ia(Ja);
			a: {
				var c = b.length,
					d = e.slice(0, 64),
					f = e.slice(64),
					g,
					l,
					h;
				l = new m(d, 16);
				var p = new m(f, 16),
					e = new m(
						'FFFFFFFEFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF00000000FFFFFFFFFFFFFFFF',
						16
					);
				h = new m(
					'FFFFFFFEFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF00000000FFFFFFFFFFFFFFFC',
					16
				);
				g = new m(
					'28E9FA9E9D9F5E344D5A9E4BCF6509A7F39789F515AB8F92DDBCBD414D940E93',
					16
				);
				var x = new m('2', 16),
					z = new m('3', 16),
					p = Ka(p, x, e);
				p.toString(16);
				z = Ka(l, z, e);
				l = La(h.multiply(l), e);
				l = La(z.add(l), e);
				l = La(l.add(g), e);
				l.toString(16);
				if (0 == p.w(l)) b = void 0;
				else {
					for (;;)
						if (
							((e = new m(
								'FFFFFFFEFFFFFFFFFFFFFFFFFFFFFFFF7203DF6B21C6052B53BBF40939D54123',
								16
							)),
							(g = e.B(C)),
							(l = new Ha()),
							(g = D(new m(Ga(e), l), g).add(C)),
							g.toString(16),
							(e = l =
								new Ma(
									new m(
										'FFFFFFFEFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF00000000FFFFFFFFFFFFFFFF',
										16
									),
									new m(
										'FFFFFFFEFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF00000000FFFFFFFFFFFFFFFC',
										16
									),
									new m(
										'28E9FA9E9D9F5E344D5A9E4BCF6509A7F39789F515AB8F92DDBCBD414D940E93',
										16
									)
								)),
							(h = new Na(
								e,
								E(
									e,
									new m(
										'32C4AE2C1F1981195F9904466A39C9948FE30BBFF2660BE1715A4589334C74C7',
										16
									)
								),
								E(
									e,
									new m(
										'BC3736A2F4F6779C59BDCEE36B692153D0A9877CC62A474002DF32E52139F0A0',
										16
									)
								)
							).multiply(g)),
							(e = Oa(Pa(h).x.toString(16))),
							(h = Oa(Qa(h).x.toString(16))),
							!(32 > e.length || 32 > h.length))
						) {
							e = Ra(e);
							h = Ra(h);
							e = e.concat(h);
							l = new Na(
								l,
								E(l, new m(d, 16)),
								E(l, new m(f, 16))
							);
							if (Va(l)) {
								b = void 0;
								break a;
							}
							l = l.multiply(g);
							g = Oa(Pa(l).x.toString(16));
							l = Oa(Qa(l).x.toString(16));
							if (!(32 > g.length || 32 > l.length)) {
								g = Ra(g);
								l = Ra(l);
								h = g.concat(l);
								p = !0;
								for (z = 0; z < h.length; z++)
									if (0 != h[z]) {
										p = !1;
										break;
									}
								if (!p) break;
							}
						}
					f = h;
					z = h.length;
					x = c << 3;
					d = [];
					if (!(0 < x >>> 30)) {
						var F = 1;
						h = (x + 256 - 1) >>> 8;
						var q,
							w,
							B,
							y = [0, 0, 0, 0],
							p = [];
						for (q = 1; q <= h; q++) {
							w = Wa();
							var r = Xa(w.D, w.P, w.A, w.S, f, z);
							B = F;
							for (w = 3; 0 <= w; w--)
								(y[w] = B & 255), (B >>>= 8);
							r = Xa(r.D, r.P, r.A, r.S, y, 4);
							w = Ya(r.D, r.P, r.A, r.S, null);
							for (B = 0; B < w.length; B++) p.push(w[B]);
							F++;
						}
						if (x & 255) {
							f = (x & 255) >>> 3;
							for (w = 0; w < (h - 1) << 5; w++) d.push(p[w]);
							for (B = 0; B < f; B++) d.push(p[w + B]);
						} else for (w = 0; w < h << 5; w++) d.push(p[w]);
					}
					if (d.length != c) b = void 0;
					else {
						f = [];
						for (h = 0; h < c; h++) f[h] = b[h] ^ d[h];
						b = g.concat(b);
						c = b = b.concat(l);
						d = Wa();
						b = Xa(d.D, d.P, d.A, d.S, c, b.length);
						b = Ya(b.D, b.P, b.A, b.S, c);
						b = e.concat(b);
						b = b.concat(f);
						for (c = 0; c < e.length; c++);
					}
				}
			}
			b = sa(b);
		}
		if (null == b)
			return (n[a].errorCode = CFCA_ERROR_RSA_ENCRYPT_FAILED), '';
		n[a].errorCode = CFCA_OK;
		return b;
	}
	function Za(a, b) {
		if ('undefined' === typeof n[a] || 'undefined' === typeof n[b])
			return !1;
		if (fa(a) && fa(b)) {
			if (n[a].l.length !== n[b].l.length) return !1;
			for (var c = 0, d = n[a].l.length; c < d; c++)
				if (
					(n[a].l[c] ^ n[b].da[c % 16]) !==
					(n[b].l[c] ^ n[a].da[c % 16])
				)
					return !1;
			return !0;
		}
		if (ga(a) && ga(b)) {
			if (n[a].l.length !== n[b].l.length) return !1;
			c = 0;
			for (d = n[a].l.length; c < d; c++)
				if ((n[a].l[c] ^ n[b].X[c % 8]) !== (n[b].l[c] ^ n[a].X[c % 8]))
					return !1;
			return !0;
		}
		if (fa(a) || fa(b) || ga(a) || ga(a)) return !1;
		if ('' == n[a].s && '' == n[b].s) return !0;
		if ('' == n[a].s || '' == n[b].s) return !1;
		c = xa(ya(n[a].ga, n[a].s, 0, n[a].ha, 0));
		d = xa(ya(n[b].ga, n[b].s, 0, n[b].ha, 0));
		return c == d ? !0 : !1;
	}
	function $a(a) {
		return 'undefined' === typeof n[a]
			? CFCA_ERROR_INVALID_SIP_HANDLE_ID
			: n[a].errorCode;
	}
	function ab(a) {
		if ('undefined' === typeof n[a])
			return CFCA_ERROR_INVALID_SIP_HANDLE_ID;
		var b = [!1, !1, !1, !1];
		a = bb(n[a].M, n[a].v, n[a].v).result;
		if (null === a || 0 >= a.length) return b;
		b[0] = -1 < a.indexOf(LETTERLOWREG);
		b[1] = -1 < a.indexOf(LETTERUPREG);
		b[2] = -1 < a.indexOf(NUMBERREG);
		b[3] = -1 < a.indexOf(SYMBOLSREG);
		return b;
	}
	function cb(a) {
		var b = new RegExp(REG_LETTERS_UP),
			c = new RegExp(REG_NUMBER),
			d = new RegExp(REG_SYMBOLS);
		if (new RegExp(REG_LETTERS_LOW).test(a)) return LETTERLOWREG;
		if (b.test(a)) return LETTERUPREG;
		if (c.test(a)) return NUMBERREG;
		if (d.test(a)) return SYMBOLSREG;
	}
	var db =
			'data:image/png;base64,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',
		eb = 120,
		fb = 20;
	var gb = window.navigator.userAgent.toLowerCase();
	function hb(a) {
		return -1 !== gb.indexOf(a);
	}
	var ib = 0;
	function jb() {
		if (1 == ib) return !1;
		if (2 == ib) return !0;
		((!hb('windows') && hb('iphone')) || hb('ipad') || hb('ipod')) &&
			(ib = 1);
		for (
			var a = window.screen.width * window.devicePixelRatio,
				b = window.screen.height * window.devicePixelRatio,
				c,
				d = 56;
			2e3 > d;
			d++
		)
			if (
				!0 ===
				window.matchMedia('(max-resolution: ' + d + 'dpi)').matches
			) {
				c = d;
				break;
			}
		c || (c = 96 * window.devicePixelRatio);
		a = Math.sqrt(a * a + b * b) / c;
		if (hb('pad') || 10 < a) ib = 2;
		if (1 == ib) return !1;
		if (2 == ib) return !0;
	}
	function H(a, b) {
		this.width = a;
		this.height = b;
	}
	function kb() {
		var a = window.innerWidth,
			b =
				'CSS1Compat' === document.compatMode
					? document.documentElement.clientWidth
					: document.body.clientWidth;
		a && (b = Math.min(b, a));
		return b;
	}
	function lb() {
		var a = window.innerHeight,
			b =
				'CSS1Compat' === document.compatMode
					? document.documentElement.clientHeight
					: document.body.clientHeight;
		a && (b = Math.min(b, a));
		return b;
	}
	function I(a, b) {
		a.classList.add(b);
	}
	function mb(a, b, c) {
		document.addEventListener
			? a.addEventListener(b, c, !1)
			: document.attachEvent && a.attachEvent('on' + b, c);
	}
	function nb(a) {
		a.style.display = 'block';
	}
	function ob(a) {
		a.style.display = 'none';
	}
	function J(a, b) {
		var c = 'CompleteKeyboard';
		a == KEYBOARD_TYPE_NUMBER && (c = 'NumberKeyboard');
		return c + KEY_ID_SEPERATOR + b;
	}
	function pb(a) {
		if (a) {
			var b = a.indexOf(KEY_ID_SEPERATOR);
			if (0 < b) return a.slice(b + KEY_ID_SEPERATOR.length);
		}
	}
	function qb(a, b) {
		a.parentNode.style.height = b + 'px';
		a.style.height = b + 'px';
	}
	function K(a, b) {
		a.parentNode.style.width = b + 'px';
		a.style.width = b + 'px';
	}
	function rb(a, b) {
		a.parentNode.style.width = b + 'px';
		a.style.width = b - 2 + 'px';
	}
	function sb(a, b) {
		a.parentNode.style.marginLeft = b + 'px';
	}
	function tb(a, b) {
		a.parentNode.style.marginRight = b + 'px';
	}
	function K(a, b) {
		a.parentNode.style.width = b + 'px';
	}
	function ub(a) {
		if (a && 0 < a.length)
			for (var b, c = 0; c < a.length; c++) {
				b = Math.round(Math.random() * (a.length - 1));
				var d = a[b];
				a[b] = a[c];
				a[c] = d;
			}
	}
	function vb(a, b) {
		a.style.fontSize = b + 'px';
	}
	function wb(a, b, c) {
		a = a.childNodes[0];
		a.style.width = c.width + 'px';
		a.style.height = c.height + 'px';
		a.style.marginTop = Math.floor(0.5 * (b - c.height)) + 'px';
	}
	function xb(a) {
		if (!a) return null;
		for (var b = { x: 0, y: 0, wb: 0, Ia: 0 }, c = a, d = 0, e = 0; c; )
			(d += c.offsetLeft), (e += c.offsetTop), (c = c.offsetParent);
		b.x = d;
		b.y = e;
		b.wb = a.offsetWidth;
		b.Ia = a.offsetHeight;
		return b;
	}
	function yb(a, b) {
		var c = Math.floor(0.5 * a),
			d = b.height,
			e = b.width;
		c > d && (c = d);
		d = Math.floor((c * e) / d);
		d > e && (d = e);
		return new H(d, c);
	}
	function zb() {
		this.o = document.getElementById('CFCABubble');
		if (!this.o) {
			this.o = document.createElement('DIV');
			this.o.id = 'CFCABubble';
			this.o.unselectable = 'on';
			this.o.onselectstart = 'return false';
			document.body.appendChild(this.o);
			var a = this.o.style;
			a.position = 'fixed';
			a.zIndex = '9999999';
			a.backgroundSize = '100% 100%';
			a.padding = '5px';
			a = document.createElement('INPUT');
			this.o.appendChild(a);
			a.className = 'cfca-btn cfca-bubble-text';
			a.setAttribute('type', 'button');
			a.value = '';
			a.style.height = '50%';
			a.style.fontWeight = 'bold';
		}
	}
	zb.prototype.show = function (a, b, c, d, e, f) {
		c = Math.floor(1.6 * d);
		this.o.style.width = c - 10 + 'px';
		this.o.style.height = 2 * c - 10 + 'px';
		this.o.firstChild.style.fontSize = Math.round((6 * e) / 7) + 'px';
		this.o.firstChild.value = f;
		this.o.style.left = b - (c - d) / 2 + 'px';
		e = parseInt(a.split('___')[1]);
		f = document.getElementById(a).parentNode;
		var g = parseFloat(f.style.marginTop);
		this.o.style.bottom =
			(parseInt(f.style.height) + 2 * g) *
				(5 -
					(10 > e || (50 <= e && 60 > e)
						? 1
						: 20 > e || (60 <= e && 69 > e)
						? 2
						: 29 > e || (69 <= e && 77 > e)
						? 3
						: 4)) +
			g +
			'px';
		'CompleteKeyboard___0' == a ||
		'CompleteKeyboard___10' == a ||
		'CompleteKeyboard___50' == a
			? ((this.o.style.backgroundImage =
					'url(data:image/png;base64,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)'),
			  (this.o.style.left = b + 'px'))
			: 'CompleteKeyboard___9' == a ||
			  'CompleteKeyboard___19' == a ||
			  'CompleteKeyboard___59' == a
			? ((this.o.style.backgroundImage =
					'url(data:image/png;base64,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)'),
			  (this.o.style.left = b + d - c + 'px'))
			: (this.o.style.backgroundImage =
					'url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAJYAAAEsCAYAAAAhJ0pFAAAAAXNSR0IArs4c6QAAF69JREFUeAHtXQmUVNWZvu+93lhGZBUjaiKIzPGcjDrHOS4E5UQxCcfjzInAZJKoQQQNokcTl5kxWkl0WJSImqNxEpWAC1sYVEB2ZG3UGDHsSyM0IMraQDfddNd7b/57q15RXVR3V3e9+t/2ldL16i3/8t3v/fe//72vShMuvK4d8lA7/fDRmy1NDBS21U/X9b62JjoLW3S0LKtY0zSlxbZtIbflO8fL0eXoz9RZaHsy9RdaX6Z/mfozj2ezx9D1Gmq7Ly3T2q/polIT2mK9rMO81fNeOZZ5fXOfEy3e3BlNHOs/+L7OVm3tbUKzbyMDb6F/7UpKS6we3TqLHt266F27dhLdabt9uzJBbEpIkYSS200Ryznm1fmZ+pvwPbXb7/6kDE1uZPqXaT8dr6mpFUeOVolDh6vsPZVfmseqThTpmmZS+66wNf2l8qWT52SKzfa51cQaNOiXHarjhx4iGx63bLsDkSc+cMDVRd+5/ipxxbcvE8T4bHqwL6AIbN2+W6xa8zexeHm5uXff14am6WuE0B8tX/bG2uZcyplYN8ZiRQ2rdt0tNP031L31GHjDv9h3/setWt8+FzUnH8dCgoBl2WL+otXi1ddnmUeOVBlC1yb16jz4lzNnDjWzuZgTsW78/ohe8Yb4u6ZlXXXlP/Wz7h85TP/Hfpdkk4d9IUegvr5B/OG1WWLarAVC17VFxR21YR/OmVyV6XaLxLr+pruuoSTw3bKykq6/emykMYC6PLyAgIxe45+fbFlxa1tRR/u6THI1mxD1v2n4HTQyWHF+z25dX3s5BlKBTykEfjCov3hh/CO6XqRf1lBtTx8yZIaROkgbjT6kH+h/y/A7LMv889VXXa698Oyjhhzh4QUE0hHoeV430fO8rtqqNZ/1rqnfcU5lxfqFzvGsxLrhe8OvpeHlbEmq58b+Qi8rLXHOxzsQaITApb0vEjWnasWGTTuv+dalVy3eU7F+rzzhrK5QJuo0AphzQc/uxm+fHK2jfNAIR3zIgsCo4bfLmqVJA8dnncONIpYsKVh7q5a1b1f6rZcmPo7uz0EJ780iYBiGOOcfOuqryz+78OI+//x5ZcVnWxtFLLO8cgRFqyv/+9GRxoW9ejYrDAeBQDoC37v5etHrGz2opmWNkftTxJIVdao9/Jqq59Z3rrsy/RpsA4EWEaCalrhp4DUGzS3eMJim+1LEOq0d+wVNHXUfTcXPFqXgBCCQBYH+FJBoms84UV83WJHollvu7kKTw48NvOFq0a/vN7Ncgl1AoGUELrv0YtGlc6e4pms3K2LVCe02mv9r/9N/H9xiJb5l8TgjyghcdOH5Bi2OuijR7Wn2v57Xo1u8zyUXRhkT+O4CAt27nasZhn6BfuutI9vTIqlBN/S/qsgFuRARcQS6dT1Xrrf7hn4yLgbRVln/666IOCRw3w0E2rcvE1Sy6kBdoXljaWmp9e3L+7ohFzKijkBy1bkubL0f9YtybU3UIYH/biBAS4vlWntdM7S+3WmNuhsyIQMISASIV0QsoZ3btUsnIAIEXEHA6fd027Y6qkzeFbEQAgQkAtQV0jROcbt2pcADCLiGgOoKE9KcAOaabAiKKgLEKvm4Ij2LCFJFlQOF8jsxKgSvCoVvpOVSmYHGhZGGAM67iYDkkopYNJ3jplzIAgIKARWxVEULgAABlxBIFEhV8o6o5RKmEJNEgEaFcgtZFhjhEgKKUFQgdUkcxACBRgjIyjviVSNI8CEfBBKjQhRI88EQ1zaJALrCJqHBgbYjcKbynsjg2y4JVwKBDAQSdayMnfgIBNqMQDJIYVTYZgRxYTYEnMKVqmM5H7KdiH1AoDUIyFJ7cq6wNZfhXCDQPAJOuo65wuZxwtHWIiDrosSuxEI/WSXFCwi4hoCdnNJx4pdrgiEosggQl1I5FpL3yNKgII4n17wXRDaERhSBxFyhmtJBvIooBwrqdiJ5B7cKCnKkhBOXZMqOynukWp3HWZVjJVQhZPFAHgUtGBVGoZU981HlWCiPeoZ/6BTLvu9MHQs9Yega2EuHVPKOoruXTRBC3RSkVPKOacIQNq6nLsnkPfUwBfpCT9siVMrViqxEHQu0ClXLeuqM8xUzagWpp5ZAeegQOLMeK3SuwSGvEcCUjtctEDb9lFepiKVGhag5hK15vfNHVdtpBalkF5J379ohdJpVkFJr3vGdfqFrXB84pJ7SQcTyQUuExATJpdRcYUh8ghu+QSD1lI5vLIIhQUfAGRUmBoToDIPenn6zP/EkNL6S22/tEmB7Gq0gRcQKcEv60vTEUzq+NA1GBREBJ0RRV4iFyUFsQN/arJjlfCmIQzPfWgvDAoMAxSk5IExOQoNZgWk4nxsqi6Pyhcq7zxsqaOap9aNELrXQD1lW0JrP3/Ymn4TG6gZ/N1OwrJMdYXKuUGZbwTIe1vofgWTlHczyf1MFxEKikhoVJpJ4ZFkBabbAmJlcQYqIFZgW87mhiYxdfaMfau8+b6tAmef0fapAingVqLbztbGSSyrHUkNCMMvXjRU845IFUsxDB6/pfGsxBanUmndnfse3xsKwwCGA9ViBazJ/G+zM46gfG/e3qbAuaAikFUiDZjrs9TcCyTqWv42EdYFCIFlhSORYiXmdQNkPY/2LwJk6FuoN/m2lgFqmFvqpImlAHYDZ/kJAjgpVHUuu9kPh3V+NE2RrUnOFKr0Cs4Lclr6yPUElNSqkTYdmvjIRxgQZgeSoMMguwHZfIUBxKjkq9JVZMCYkCCTXYyHJCkl7eu5GagUpaqOet0XoDEgrkIbONzjkIQKp9VgoZHnYCiFVnay8h9Q7uMWPQDJdV8k7v3ZoDCsCqYV+ckLH+RBWZ+EXHwI2VdvP5Fh8eqEp5Ag4QSqRY6GMFfLm5nVPRSz5B3OFvMCHXVvy+7HITUSssLc1n3/EpbQcC8ziQz4KmtRv6TjpVhQcho+FRkDVGCi9QoG00EhHVH5iPVZEnYfbBUBAZVV4rrAAyEZcpFqNjN8rjDgLCuA+RoUFABUiUwgkcyw8TZFCBBt5IZAaFUopKDjkhSUuzoIARoVZQMGu/BGg78dCN5g/jJCQQsApN6hJaMzopHDBRv4IJB+mkILArPzhhIQEkxJcUjkWaAVSuIlA2uoGN8VCFhBI/sIqgAACbiKQjFg0KkRf6CaukEUIJOtYYBbY4BICSSolnytELcslWCEmiUByVIiIBUa4g0CjuUJ3REIKEDiDQKIrRMA6gwi28kPAybHUlE5+onA1EMhAAEuTMwDBRzcQ0PUksbAeyw04ISMdAazHSkcD23kj4AQpfD9W3lBCQDYEMCrMhgr25YWAmiukP7X19fG8BOFiIOAgUN8guaTV6pqun6irO+3sxzsQyAuBWuKSrouT9E8cPll9Ki9huBgIOAhUKy5pR3RLiO1ffX0UfaGDDN7zQuDgoWMmPZ+znUpZYvvhI8fxa/Z5wYmLJQLyga+Dh6s0TbeJWJq9wbQsfe/+g0AHCOSFwP4Dh0Q8blJ6ZWyg6nvpWilt247KvITiYiCQ4JAm7GK7XP/9c2O+oKHh11t37AEyQCAvBLbuqLQpUh1+edzDO1WB1BbWkp0V+624aeYlGBdHFwHLssSOnftoLCiWSRQUsTStaC6RSv9i94HoIgPP80Jgd+VXoiEeNwxdnycFKWIVd7QW0GPR8c3bduclHBdHF4Et2/bIn+y1Sjq0my9RUMSaFHuoStja/PJPNtEAUUYzvIBA7gjIMgNxJ07Tg0smxkYdllcqYskNCmGvnDx5yvj7pl3yI15AIGcEZE93rOpkEUWsV52LUsTq0uGBRUVG0Z6Va9YjZDno4D0nBNas22BTcvV11/ZXvOdckCJWLKZZprBe2V6xX6PqqXMc70CgWQSqjleLjVt2yx+T+1MsNjA1NZgilrxaN/TXqQ4RJwY2KwwHgYCDAOVWNJVj2qXFxh+dffK9EbFeGvvAIdo3Y1X5BrO6pjb9PGwDgbMQOF3fIFau/dykAvv8ic/c36jC3ohY8kqtpPTXphkXC5Z8dJYg7AAC6QgsW/k3UV1dp1N4iqXvl9tnEevFp+/dQR3mq6vXbbRoOU3m+fgMBBQCx0/UiMXLP6UZZ23mi+Me/DQTlrOIJU8obl/2FHWcR6dOX0Rra/CFIZmg4bMQ02cvty3TrC8p1h7PhkdWYqkil6bdu+/LQ8aiZX/Ndh32RRiBjz/dQiPBXbSMQTz+3NNjvsgGRVZiyRNfGDfmL/SFbFPnL/7I3rlrf7ZrsS+CCMhS1Iw5K6gL1D/s3H7M75uCoEliyQtKup57H1VTt73x9gLzZDVGiU2BGJX9tIhPvPHWB6ZpmVWlZeLHsvbZlO/NEuu5R+6o0Yu120+dqquf8s4CC+lWUzBGY///zVslDnx1VNcs7ScTYmO+bM5ro7mD8ti6lfMPXTNg8P4jR0/8m/yGmj6XXNDSJTgeQgQ+31gh3vtgLZURtAnPj7v/Dy252GzEci6e9D+jJ9OaiDcWLvvE3r5zn7Mb7xFB4Oixk+LtmUtNWr3wUaeyy5/Ixe2ciCUFdS4tvl/Ttc1Tpi80T5zEc4i5gBuGc+Qyqslvf2DG4/FqQxfD0ucDm/MvZ2LFYqNO6VYR5VunT0+ZJvMt1LeaAzYsx95fUC727j9kaLZ2V+a0TXM+tphjpV9cvnruYcq39lRVVf/Qsmxxae9e6YexHTIENtOq0Nnvr5S/Z/ni78aOntQa93KOWI7QSc+MfoseTfxfKufbW3fsdXbjPWQIyCmbN2csNmnFy2edSrs/0lr3Wk0sqaBTSYcHDV3bIBVLA/AKFwIyzZkybaF1uq6+zi4qGRqLDa1vrYdtIlYs9rM6Wipxe13t6bo/v7OQ8jvkW60F3s/nL1j6idhd+bVO0WrE87ERO9tia6tyrHQFa1fOPXrtgFsrjp+oHiIrsn37IN9Kxyeo29sr9omZcz6kSGH/aeLTPx/bVj/aFLEcZb975t7plNi9vHzVelsmengFGwE5bffmjCVUr9I39+ra/cF8vMmLWFLxxedd/DDNJ65/a9ZSU65/xiuYCMjq0Vszl1hy+s4QxpCHHx6a1+Rw3sR64IEfnC4uKr294XT81JRpiyjfanJeMpiIR8TqpSs+FTsq9tG6PfHzCU/fsyVft/MmljRgXGz4LqqY3lW576A+b9G6fG3C9cwI7NpzQCxc9il9oYf+5rO/vW+yG+pdIZY0ZOLT984mur+wYs3fxaatu92wDTIYEKCuT7yl8iqxs6PR5T63VLpGLGlQB73Lo5T4ffLO7GUmPRnrlo2QU0AEptESY/oO2rheZA+hepVrSbKrxJKFtCKjZCjlW9VTpi02aUVYASGB6HwRkL3Llu2VtBpKe2j8k/d+nq+89OtdJZYUPC72s930g8B3yPXy7y8sT9eFbR8hsHffQUHLzuWdP+vZ34x6xW3TXCeWNHBCbOR7uqFNXPvRJrFh8xdu2wx5eSJQV1cvpkxfHKc1dnuNDtqIPMVlvbwgxJKaep8v/pPeyqfP+dCk1adZlWOnNwjMfHeFfeKknOPVho5/fNTxQlhRMGKNGjWqoaSkeJgZN09MnY58qxCN1xaZ5R9vlr0ILQbVHpsQG/FxW2Tkck3BiCWVP/PE8L2aLX7yJS3Al+ul8fIWgQNfHRGU91pEqrnjnrrn+UJaU1BiScPHxu6ZT/Wt8ev+ukXIBfl4eYOA/CGuqVSvoqmbr4zS0ruIXDSJU7hXwYklTW8n+vyKEsVVM99badKvYBTOG0huEoHZc1fZR+ihCNvWho39rzuPNHmiSwdYiCUX4BcXlfyooT5ufL5xl0umQ0yuCMiv/1y/oYKehRFPjY/dvTrX6/I5j4VY0sBnnrhzP62CoMf9CxqB88EitNc639BY9uSINq+vai04bMSShlG/3lr7cL6LCMS0ph+Jd1GNEsVKLKIW/YcXNwISde6bmplYElJQi5tYEnLuzsIDYrHDCoWU1nI/X8xKLBWOEbD4ia4iFi/wrMTiRxQazyDAOxpnJxbS9zNNzbWVSN55m5pXGxeS0JOBgIxWIY5YyLEy2jvEHxGxQty4Kdeo1kDPIqQ+cmywauOupXAAGBwdYe8Kg9MSIbM0xOUGWaTjdS9k3AiQOx50haAWNz8S5QZe3FmJpQDl9Y+7Df2pjzDnzm+ZiQVW+ZN57lvFSizupRvuwxVQiZTbhnwSOqANE3SzVVfI21uwRiyMCr1haOiTd9kV8pbpvGlI/2mVqPMizxqxJODIs7ygnewGQ9wVKkh5bxwvWhE6CQHWiKVqKbw3DhpZIkCYc/cUrMRCK0cHAVZiybtG/odX+BFgJVb44fSnh6EvN3CPTPzZzNGwijVicSeQ0WjCHLxUyXsO57l4CiuxVJEOKZaLzedfUczEAqu8owIv9szEklkWr4PeNaR/NIc+eedebOafpo2eJawRC8m7VwQL/SQ0dYPoCfnZJQvTzN0Fa8TiXrrB34J+1sh7RzMTi9c5Pzdz2G1jJZaMxhgV8lNK3s7MPSHvspkEpFiQxU8tfo2sEUtl7ty3Dj+m/tNImIc8efcf5pGwiDqJkD/+heTdEyLL3JYZeuau0EYZywNmJZJ3XmaxEou7n/egDaEyiQArsVTyDug9QEDFLFa9zMQi37g7e1Y4ocxBgJVY4JQDO/M7BSxu7FmJxQwn1DVCIMTJeyM/8YENAZVh8fKKd0pHjgqZ/WNrPD8rUquxmGfSmLtC0MoLAsrbmbvUw0wsum24s0gvWtJvOul+5oadlVjqrmEOyX5r46jYw0osum/U/1EB119+8qYhzMTyF9TRsoa3q2AlluznMS7kp7OMVSFP3vlBhUZvEGCOWMixvGlmfq2sxOJexcgPp081ysI0fq/Qp40TeLNCnbzzDnkDzwWXHAh98o5fpnCJKa0UI2MVdxrCmmMlphUQtVrJi7xPT0SsvMW0SgArsZRl4FWrGsiVk1Xyzgs8M7F4nXOlUSCkTQiwEou7+tsmRHCRKwgwE8sVmyGklQgkcize3oKVWBIPXvda2QKhPV1Ri9U7ZmKBVqytm1KmCg6pTxwbrMRS5YZEzYHDN+hwEAj/qNDxFO+sCFDACnWBVIKJzpCVUkqZ7CR0nRd51q6QH1JolAhEYEpH3jW8dw6olUCcO7VFxIoC88KfvFO0QsDip3LYk3c8V8jPKaVR3s/MNzRzV0i3DrODHjVl5NUyEwsPf3nBOHkvcy8AYCaWhBUhi59cilqsalmJxd3PsyIJZY0QYCYWolUj9Nk+qBIpmzapiJVYqhsEt1gbWCkLfx1L3jl4cSOgMizmPIQ1YnGPTLgb0K/6VEfIfE+zEksCj4IDP/0SEYtXLzuxEnPtvE5CGz8CrMRSXSFzX88PqQ81hj959yHoMKkgCLBHLFQbCtKOvhPKSiybe+G17+D2xiCUG7zBHVoLgABrxEIdqwAtmIvIsCfvqivEqDAXKrh7DhVHubMQRCx3m9Cf0ijJ4r6fWYklUceokJ97oU/e+SGFRq8QYI1YSN69amZ+vSAWP+YeaFSdIateVmKpkQl3FskKp0+VhT15B6d8SrwCmMUasWSOhVFhAVoxJ5G8yLMSS/rPvJAxJ8ijcRIv8uzEwsiQn8aynwj1jzQpSHlvHP5W9KVGCTov8KwRS0Ur3q7el80cBaNYiRUFQP3oo7yhdZ23qXm1+RH1CNikOkLm5Q2sxELi7g2LZfbBjT03sWpqa+u9QTfCWk8R5kSsGk4IWIlFI5ONB4+cMDkdhC4hDh6WmGsbObHgJZamvXasqsao2H2Q08dI69q156A4drzGoN8af50TCNbBPy1N1l6ftnJxsaHfeN3VfY3e3+zB6WvkdElSrfl4u9kQt1YO/9GA71J3yFbMYiWWbNmpf1l5frxBTCWSfbdzpw5mj27nGO3blahGl15Lgxzvne2mjGzp/MzjSknan0z5mee3dDxNlNps6fzM45nXt6Q/1/Nr6+pV93f0WDVFKm15cbH245/+cMCBzOsL+Vn6yv6SkWvyjFUjbMu+m7LKy23L6iiNkCMX+cBFUyMY55h8z+V8dVIzf1qSl+/xZlSrQy3Jz7w+1/MJxhqCeKOuaa/fOaz/HzkjlWPz/wN+TcCXDrK7VQAAAABJRU5ErkJggg==)');
		nb(this.o);
	};
	function Ab(a, b) {
		var c = document.createElement('DIV');
		a.appendChild(c);
		var d = document.createElement('INPUT');
		c.appendChild(d);
		d.id = b;
		d.parentNode.className = 'col';
		d.className = 'cfca-btn cfca-default';
		d.setAttribute('type', 'button');
		return d;
	}
	function Bb(a, b, c) {
		var d = document.createElement('DIV');
		a.appendChild(d);
		a = document.createElement('DIV');
		d.appendChild(a);
		a.id = b;
		a.parentNode.className = 'col';
		a.className = 'cfca-btn cfca-default';
		b = document.createElement('IMG');
		a.appendChild(b);
		b.src = c;
		return a;
	}
	function L(a) {
		var b = document.createElement('DIV');
		b.className = 'cfca-row';
		a.appendChild(b);
		return b;
	}
	function M(a, b, c, d) {
		for (var e = [], f = c; f < d; f++) e[f - c] = Ab(b, J(a, f));
		return e;
	}
	function Cb() {
		this.G = document.createElement('DIV');
		this.G.style.position = 'relative';
		this.G.style.top = '0px';
		this.G.style.left = '0px';
		this.R = document.createElement('DIV');
		this.G.appendChild(this.R);
		var a = L(this.R);
		this.C = M(KEYBOARD_TYPE_COMPLETE, a, 0, 10);
		var a = L(this.R),
			a = M(KEYBOARD_TYPE_COMPLETE, a, 10, 20),
			b = L(this.R),
			b = M(KEYBOARD_TYPE_COMPLETE, b, 20, 29),
			c = L(this.R);
		this.xa = Bb(
			c,
			J(KEYBOARD_TYPE_COMPLETE, CAPS_KEY_ID),
			'data:image/png;base64,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'
		);
		I(this.xa, 'cfca-mod');
		var d = M(KEYBOARD_TYPE_COMPLETE, c, 29, 36);
		this.fa = Bb(
			c,
			J(KEYBOARD_TYPE_COMPLETE, DELETE_KEY_ID),
			'data:image/png;base64,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'
		);
		I(this.fa, 'cfca-mod');
		c = L(this.R);
		this.shiftKey = Ab(c, J(KEYBOARD_TYPE_COMPLETE, SHIFT_KEY_ID));
		I(this.shiftKey, 'cfca-mod');
		this.shiftKey.value = SHIFT_KEY_TEXT_LETTER;
		this.Da = Bb(c, J(KEYBOARD_TYPE_COMPLETE, SPACE_KEY_ID), db);
		this.u = Ab(c, J(KEYBOARD_TYPE_COMPLETE, FINISH_KEY_ID));
		I(this.u, 'cfca-finish');
		this.u.value = FINISH_KEY_TEXT;
		this.$ = a.concat(b, d);
		this.Y = [];
		Array.prototype.push.call(
			this.Y,
			this.xa,
			this.fa,
			this.shiftKey,
			this.Da,
			this.u
		);
		this.V = document.createElement('DIV');
		this.G.appendChild(this.V);
		var a = L(this.V),
			a = M(KEYBOARD_TYPE_COMPLETE, a, 50, 60),
			b = L(this.V),
			b = M(KEYBOARD_TYPE_COMPLETE, b, 60, 69),
			d = L(this.V),
			d = M(KEYBOARD_TYPE_COMPLETE, d, 69, 77),
			e = L(this.V),
			c = M(KEYBOARD_TYPE_COMPLETE, e, 77, 82);
		this.Ma = Bb(
			e,
			J(KEYBOARD_TYPE_COMPLETE, SYMBOLE_DELET_KEY_ID),
			'data:image/png;base64,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'
		);
		I(this.Ma, 'cfca-mod');
		e = L(this.V);
		this.oa = Ab(e, J(KEYBOARD_TYPE_COMPLETE, SYMBOLE_SHIFT_KEY_ID));
		I(this.oa, 'cfca-mod');
		this.oa.value = SHIFT_KEY_TEXT_SYMBOL;
		this.Ea = Bb(e, J(KEYBOARD_TYPE_COMPLETE, SYMBOLE_SPACE_KEY_ID), db);
		this.na = Ab(e, J(KEYBOARD_TYPE_COMPLETE, SYMBOLE_FINISH_KEY_ID));
		I(this.na, 'cfca-finish');
		this.na.value = FINISH_KEY_TEXT;
		this.N = a.concat(b, d, c);
		Array.prototype.push.call(this.Y, this.Ma, this.oa, this.Ea, this.na);
		this.ba = document.createElement('DIV');
		this.G.appendChild(this.ba);
		a = L(this.ba);
		this.tb = M(KEYBOARD_TYPE_COMPLETE, a, 0, 10);
		a = L(this.ba);
		a = M(KEYBOARD_TYPE_COMPLETE, a, 10, 20);
		b = L(this.ba);
		b = M(KEYBOARD_TYPE_COMPLETE, b, 20, 29);
		c = L(this.ba);
		this.Ua = Bb(
			c,
			J(KEYBOARD_TYPE_COMPLETE, CAPS_KEY_ID),
			'data:image/png;base64,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'
		);
		I(this.Ua, 'cfca-mod');
		d = M(KEYBOARD_TYPE_COMPLETE, c, 29, 36);
		this.Wa = Bb(
			c,
			J(KEYBOARD_TYPE_COMPLETE, DELETE_KEY_ID),
			'data:image/png;base64,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'
		);
		I(this.Wa, 'cfca-mod');
		c = L(this.ba);
		this.Ca = Ab(c, J(KEYBOARD_TYPE_COMPLETE, SHIFT_KEY_ID));
		I(this.Ca, 'cfca-mod');
		this.Ca.value = SHIFT_KEY_TEXT_LETTER;
		this.La = Bb(c, J(KEYBOARD_TYPE_COMPLETE, SPACE_KEY_ID), db);
		this.qa = Ab(c, J(KEYBOARD_TYPE_COMPLETE, FINISH_KEY_ID));
		I(this.qa, 'cfca-finish');
		this.qa.value = FINISH_KEY_TEXT;
		this.aa = a.concat(b, d);
		this.pa();
		this.ca(!1);
	}
	k = Cb.prototype;
	k.ca = function (a) {
		a ? (nb(this.V), ob(this.R)) : (nb(this.R), ob(this.V));
		ob(this.ba);
	};
	k.pa = function () {
		var a = hb('ipad') || jb() ? 0.00651 : 0.00667,
			b = kb();
		lb() < b && (a /= 2);
		this.g = Math.floor(b * a);
		this.bb = this.g + ((kb() - 22 * this.g) % 10) / 2;
		this.G.style.paddingLeft = this.bb + 'px';
		this.G.style.paddingRight = this.bb + 'px';
		this.h = this.Sa();
		this.lineHeight = this.Ra();
		this.ja = this.Qa(this.lineHeight);
		this.ea(this.C);
		this.ea(this.$);
		sb(this.$[10], this.h / 2 + 2 * this.g);
		tb(this.$[18], this.h / 2 + 2 * this.g);
		this.J(this.xa, new H(22, 18.3));
		rb(this.xa, (3 * this.h + 2 * this.g) / 2);
		this.J(this.fa, new H(26, 18.67));
		rb(this.fa, (3 * this.h + 2 * this.g) / 2);
		this.O(this.shiftKey);
		K(this.shiftKey, (5 * this.h + 6 * this.g) / 2);
		vb(this.shiftKey, (3 * this.ja) / 4);
		this.J(this.Da, new H(eb, fb));
		rb(this.Da, 5 * this.h + 8 * this.g);
		this.O(this.u);
		K(this.u, (5 * this.h + 6 * this.g) / 2);
		vb(this.u, (3 * this.ja) / 4);
		this.ea(this.N);
		sb(this.N[10], this.h / 2 + 2 * this.g);
		tb(this.N[18], this.h / 2 + 2 * this.g);
		sb(this.N[19], this.h + 3 * this.g);
		tb(this.N[26], this.h + 3 * this.g);
		sb(this.N[27], (5 * this.h + 10 * this.g) / 2 + this.g);
		tb(this.N[31], this.h + 3 * this.g);
		this.J(this.Ma, new H(26, 18.67));
		rb(this.Ma, (3 * this.h + 2 * this.g) / 2);
		this.O(this.oa);
		K(this.oa, (5 * this.h + 6 * this.g) / 2);
		vb(this.oa, (3 * this.ja) / 4);
		this.J(this.Ea, new H(eb, fb));
		rb(this.Ea, 5 * this.h + 8 * this.g);
		this.O(this.na);
		K(this.na, (5 * this.h + 6 * this.g) / 2);
		vb(this.na, (3 * this.ja) / 4);
		this.ea(this.tb);
		this.ea(this.aa);
		sb(this.aa[10], this.h / 2 + 2 * this.g);
		tb(this.aa[18], this.h / 2 + 2 * this.g);
		this.J(this.Ua, new H(22, 18.3));
		rb(this.Ua, (3 * this.h + 2 * this.g) / 2);
		this.J(this.Wa, new H(26, 18.67));
		rb(this.Wa, (3 * this.h + 2 * this.g) / 2);
		this.O(this.Ca);
		K(this.Ca, (5 * this.h + 6 * this.g) / 2);
		vb(this.Ca, (3 * this.ja) / 4);
		this.J(this.La, new H(eb, fb));
		rb(this.La, 5 * this.h + 8 * this.g);
		this.O(this.qa);
		K(this.qa, (5 * this.h + 6 * this.g) / 2);
		vb(this.qa, (3 * this.ja) / 4);
	};
	k.Qa = function (a) {
		return Math.floor((7 * a) / 12);
	};
	k.Ra = function () {
		var a = kb(),
			b = lb(),
			c = hb('ipad') || jb() ? 0.07 : 0.112;
		return Math.floor(Math.min(a, b) * c);
	};
	k.Sa = function () {
		return (kb() - 20 * this.g - 2 * this.bb) / 10;
	};
	k.ea = function (a) {
		if (a) for (var b = 0; b < a.length; b++) this.O(a[b]);
	};
	k.O = function (a) {
		qb(a, this.lineHeight);
		vb(a, this.ja);
		sb(a, this.g);
		tb(a, this.g);
		a.parentNode.style.marginTop = 2 * this.g + 'px';
		a.parentNode.style.marginBottom = 2 * this.g + 'px';
		K(a, this.h);
	};
	k.J = function (a, b) {
		qb(a, this.lineHeight - 2);
		sb(a, this.g);
		tb(a, this.g);
		a.parentNode.style.marginTop = 2 * this.g + 'px';
		a.parentNode.style.marginBottom = 2 * this.g + 'px';
		wb(a, this.lineHeight, yb(this.lineHeight, b));
		K(a, this.h);
	};
	k.Z = function () {
		return 5 * this.lineHeight + 20 * this.g;
	};
	k.ua = function () {
		this.Ea.firstChild.src = db;
		this.J(this.Da, new H(eb, fb));
		this.Da.firstChild.src = db;
		this.J(this.Ea, new H(eb, fb));
		this.La.firstChild.src = db;
		this.J(this.La, new H(eb, fb));
	};
	k.ta = function () {
		this.u.value = FINISH_KEY_TEXT;
		this.na.value = FINISH_KEY_TEXT;
		this.qa.value = FINISH_KEY_TEXT;
	};
	k.Ba = function () {
		this.oa.value = SHIFT_KEY_TEXT_SYMBOL;
	};
	function Db() {
		this.G = document.createElement('DIV');
		this.G.style.position = 'relative';
		this.G.style.top = '0px';
		this.G.style.left = '0px';
		var a = document.createElement('DIV');
		this.G.appendChild(a);
		var b = L(a),
			b = M(KEYBOARD_TYPE_NUMBER, b, 0, 3),
			c = L(a),
			c = M(KEYBOARD_TYPE_NUMBER, c, 3, 6),
			d = L(a),
			d = M(KEYBOARD_TYPE_NUMBER, d, 6, 9),
			a = L(a);
		this.fa = Bb(
			a,
			J(KEYBOARD_TYPE_NUMBER, DELETE_KEY_ID),
			'data:image/png;base64,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'
		);
		var e = Ab(a, J(KEYBOARD_TYPE_NUMBER, 9));
		this.u = Ab(a, J(KEYBOARD_TYPE_NUMBER, FINISH_KEY_ID));
		this.C = b.concat(c, d);
		this.C.push(e);
		this.Y = [];
		Array.prototype.push.call(this.Y, this.fa, this.u);
		this.pa();
	}
	k = Db.prototype;
	k.pa = function () {
		this.lineHeight = this.Ra();
		this.fontSize = this.Qa(this.lineHeight);
		this.h = this.Sa();
		this.ea(this.C);
		this.O(this.u);
		this.J(this.fa, new H(32, 22));
		this.C[9].style.marginLeft = '2px';
		this.u.style.marginLeft = '2px';
		this.u.style.fontSize = Math.floor((6 * this.lineHeight) / 13) + 'px';
		this.u.value = FINISH_KEY_TEXT;
		I(this.u, 'cfca-num-mod-default');
		for (var a = 0; 9 > a; a++)
			(a + 1) % 3 ||
				((this.C[a].style.borderRightWidth = '0px'),
				this.h[0] != this.h[2] && K(this.C[a], this.h[2]));
		this.u.style.borderRightWidth = '0px';
		this.h[0] != this.h[2] && K(this.u, this.h[2]);
	};
	k.Qa = function (a) {
		return Math.floor((15 * a) / 26);
	};
	k.Ra = function () {
		var a = kb(),
			b = lb(),
			c = hb('ipad') || jb() ? 0.09 : 0.14;
		return Math.floor(Math.min(a, b) * c);
	};
	k.Sa = function () {
		var a = kb(),
			b = Math.floor(a / 3),
			c = [];
		c[0] = b;
		c[1] = b;
		c[2] = b + (a % 3);
		return c;
	};
	k.ea = function (a) {
		if (a) for (var b = 0; b < a.length; b++) this.O(a[b]);
	};
	k.O = function (a) {
		qb(a, this.lineHeight);
		vb(a, this.fontSize);
		a.style.borderWidth = '2px';
		a.style.borderRadius = '0px';
		a.style.borderLeftWidth = '0px';
		a.style.borderBottomWidth = '0px';
		I(a, 'cfca-num-default');
		K(a, this.h[0]);
	};
	k.J = function (a, b) {
		qb(a, this.lineHeight - 2);
		a.style.borderWidth = '2px';
		a.style.borderRadius = '0px';
		a.style.borderLeftWidth = '0px';
		a.style.borderBottomWidth = '0px';
		I(a, 'cfca-num-mod-default');
		wb(a, this.lineHeight, yb(this.lineHeight, b));
		K(a, this.h[0] - 2);
	};
	k.Z = function () {
		return 4 * this.lineHeight;
	};
	k.ta = function () {
		this.u.value = FINISH_KEY_TEXT;
	};
	if (document.all) {
		var Eb = function (a) {
			return function (b, c) {
				var d = [].slice.call(arguments, 2);
				return a(function () {
					b.apply(this, d);
				}, c);
			};
		};
		window.setTimeout = Eb(window.setTimeout);
		window.setInterval = Eb(window.setInterval);
	}
	function Fb(a) {
		if (a == KEYBOARD_TYPE_COMPLETE)
			(this.j = new Cb()),
				(this.wa = ORDERED_NUMBERS.slice(0)),
				(this.Ga = ORDERED_LETTERS.slice(0)),
				(this.Pa = ORDERED_SYMBOLS.slice(0)),
				(this.C = this.j.C),
				(this.$ = this.j.$),
				(this.N = this.j.N),
				(this.kb = this.j.tb),
				(this.Y = this.j.Y),
				(this.aa = this.j.aa),
				(this.Na = COMPLETE_KEYBOARD_CONTAINER_ID);
		else if (a == KEYBOARD_TYPE_NUMBER)
			(this.j = new Db()),
				(this.wa = ORDERED_NUMBERS.slice(0)),
				(this.Y = this.j.Y),
				(this.C = this.j.C),
				(this.Na = NUMBER_KEYBOARD_CONTAINER_ID);
		else return;
		this.K = a;
		this.Fa = !1;
		this.Ha = {};
		this.Oa = {};
		this.c = null;
		this.i = document.getElementById(this.Na);
		this.i ||
			((this.i = document.createElement('DIV')),
			(this.i.className = 'cfca-keyboard logo-border'),
			(this.i.id = this.Na),
			this.j && this.i.appendChild(this.j.G),
			document.body.appendChild(this.i),
			this.K === KEYBOARD_TYPE_COMPLETE
				? (this.o = new zb())
				: this.K === KEYBOARD_TYPE_NUMBER &&
				  this.i.classList.remove('logo-border'));
		mb(this.i, 'touchstart', this);
		window.navigator.msPointerEnabled && mb(this.i, 'MSPointerDown', this);
		mb(this.i, 'mousedown', this);
		Gb(this);
		this.show(!1);
	}
	function Gb(a) {
		mb(window, 'resize', function () {
			a.j.pa();
		});
		mb(window, 'orientationchange', function () {
			setTimeout(function () {
				a.j.pa();
			}, 200);
		});
	}
	k = Fb.prototype;
	k.handleEvent = function (a) {
		switch (a.type) {
			case 'touchstart':
			case 'pointerdown':
			case 'mousedown':
				a.preventDefault && a.preventDefault();
				a.stopPropagation && a.stopPropagation();
				null !== this.c && Hb(this);
				var b = a.target,
					c = b.tagName.toUpperCase();
				if (
					'INPUT' == c ||
					'IMG' == c ||
					(b.childNodes[0] &&
						'IMG' == b.childNodes[0].tagName.toUpperCase())
				) {
					this.c = 'INPUT' == c || 'DIV' == c ? b : b.parentNode;
					if (!this.lb) {
						Ib(this.i, a.type, this);
						for (
							var b = this.i, c = a.type, d = 0;
							d < TOUCH_EVENTS.length;
							d++
						)
							if (TOUCH_EVENTS[d] == c) {
								mb(b, TOUCH_EVENTS[d + 1], this);
								mb(b, TOUCH_EVENTS[d + 2], this);
								break;
							}
					}
					this.lb = a.type;
					if (null !== this.c)
						switch (pb(this.c.id)) {
							case DELETE_KEY_ID:
							case SYMBOLE_DELET_KEY_ID:
								this.K == KEYBOARD_TYPE_NUMBER
									? (I(this.c, 'cfca-num-mod-click'),
									  (this.c.firstChild.src =
											'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAGAAAABCCAYAAACo/NMFAAAAAXNSR0IArs4c6QAAB7JJREFUeAHtnXtQFVUcx8/eBQRT8zGZPYC8io6mSKIoFxG4F1GYMpu6PWzkYYlNjo01TmZTDX9kTU3TOFmR2Yg4YyOOhdrki2ckoDNRokmSgZJoRaGCive5p9+5duiycB+7d3fZO+z+c+7uPfv7/c7nu+e9Fxik8JG4+Lmx2G4/hjCeprBrxd0xCN1ADLqEEVPDsExJfVlxBT8IyKPckWh+OQJ1/lOOMTIo51U9nhjEfBcaoltVU7bjHI1KRz/InZrNe1jc2bl7qMInfDHCKXaH80SSKTeZ8mbpB7nTO+8O/wx8LJfbTxDYj4Dmd5k+ZvbXba0nryhSAwzG7AKMUH4QwFEkRGAxxu7gthFnsvcBhrScfA5xWxUpWZA5YUJ06bI2QYb07Eeh3dsJXBSpaUHGn3QKVtkESDTmGRDHHYDqFhZ0YBQKmGHQCFkEWJi+YhqHcTnAH6VQWYLSDbT/4ZL3AYnG7PuARh3GOCooqSgctKRtc+qy3NEQ/yENvv8qSiZAZubaYdZubj/An+m/ey2nJAIUFBTorlqu7QL4CzWkwghI0glbmcgt4DZHmGstNyEQcA2Yb8p+HUY7azSc4ggENAoyGHNzOewsEudau4sQEC1AUtqKLA6h/fD0h2goxRMQJQCM9RMAfBWs6g0X71q7kxAQ3AcsMGVPgdHOt4MFf5EpEc2Nf1BS9abGRKMnHlskqU1/jQkaBaVm5U6w2bkqME5mu4ofBP6br+UjU1oCavqlFV3+4++AYyDwN3+wAaUsiEc9t6zo56bfArYpxIDfAsBEa9RNi6UcjA/KXi6Fr9MxiGVZZEwNXAQKf+SI2y1pwpwZiovglwBmc0FYR/efsLKJB2Uv1x0+fboCFYEPn9pVWgSffQC098zFzpZigG+iQSqZjhk9Em14JQ+RJ59/hIWGovc2rRPcJ3iCT+2vWf0kio66l57KmvqsAUe/b/0QNg5WyRqFF+MWiw01/3oBpaXOdTU9/KxCa4Iv+PDAoU3vf4Eafmziu5Ll3KsAicac9RDQW7J4FmD00uUOdPZs4CL4C//w0VoB0QWW1aMA8005zyLMFYL5/nU/MJ+i7g5UBDXCJyAGFADeW1mEObzH0/eiCEpwk1gR1AqfIOknwHxj3myEnYfhuwgJmEluQqgIaoZP4PQRwGDMmYSRsxKuj5WcnIQG/RWhp8eKNr76PKLjfH4ItMNVss3nx9DbvhsyXhiP7TdrYY1nMj+TWs/nzZmJ3n37JUSGo0IPNcAnMbtqQKr5xRGOGzePAvwZQgsymPl91QRPsakFPomPzc/PD73c3rMPJlpBuZ0oVAQ1wXcJEDZ6ehHMPR739LQEw3V/RVAbfMJWh5HuTDBA9hXjta5uZLPZvWZzOJyos/Oa1zxKf8m2n2+sjdLHjYP2f57SzqXy52uoSf2wrE6SVVRqT4rUtRiXsVC/Dox9JYVBpW34C5/GJXYBj94vdeoaBVVXV+OZ01L2W52WFHAQLbUTuewJhU/jELqAR++TI+1djj50aIt12Ch2KcMwQdEn+IJPOtySvUeQzT5wv6CWmtBnJnzh7EkL/HTmGyfHmUFt1b7Z7A98sqT8ZclBSVZR5Xjyqc0+ApCLbS0nu6OmzC2Dd/vJ77nCaUa1pP7Cp8sLvoaog90c9ROAgL7Y8lNHZEzscQYzz8Cpat77EQqfPjRqFmFAAUjg7a2n2iInxjYjhiGTtN41I1oopVOx8GmcahXBowAk8Pbzp5oi9bO6YEtyCS3IYKSBwqcxq1EErwKQwGGidiJq4qzhMFFLogVRMiWb8tsKCyRbUvZXhOqaBtTVdV32ovoUgETw+/nGiqLi0skgQqzsEfEckE15eAtvwDcfxK7t+BKh9EAlOlJWx4tEntPeeYA38zA3wDNiIvLgbx2QF7MUP3bBcLLwc7JD+v8hFj61cOKH02jjGx/1myfsLS1Dmz/eRbPJnvpVA0gUDQ0NXHRs/D5kw5lwOkH2yHgOTp85h6xWm6smBAqfmubXBKXhkzgEj26SF+ffY7dZoH7iB2hBlEyXP5WFrlztQnScL4VvsrM2J346+mRriRTmBNkQLACxnpS2cqqTcdRC2zxOkDctcz8CfvUB/Ltqq7Y3w5uCj4B6t/jfaefCCIgSgLioq9hZr0O6p+Hn9k5hLrXc7gT87oTdb6KfL15obI7Ux/0FE7WH6TUtFUYgIAGIK5ioNUROimNBBLKXoB0CCQQsAPEHIlSBCFEgwkMC/Q/57KL7AD65Ycn61bBwd5B/XTv3TkDUMNSTyYyM9Xdcd3RUwkQpwVMe7XpfApIKQEwnZ+bd5bA46mDdKGheceyLRNkzyQUg4S9IX6l3cvZ6WEMbr2xxgssbrK1dl6wPcC/6sfLtrSzWZYG6N9yva5/7EbgkiwDEzbGq4gakY80ggqOfW+3CfwRwjSxNkDtf+GsqOYjDO9yvaZ9vE9AxISZJ5gHegMIcofH+SXF2mCOYvOUbct8xTFV9ZXGBbE2QO9DjFTvfgZXvT92vDeXPsMEFf7KYcf30VxEBCOwlKfq14Lh0KIMnZSfwMcssrassbnGdKwkkNTc33NrGwd8TxYOywa9kWQf0Bc0OefIpfMUFIA6H6j9w0CF2d11lEfkBZJ/jX0NUYrX0yZ6yAAAAAElFTkSuQmCC'))
									: (I(this.c, 'cfca-mod-click'),
									  (this.c.firstChild.src =
											'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAE4AAAA4CAYAAABT0c7BAAAABGdBTUEAALGPC/xhBQAAACBjSFJNAAB6JgAAgIQAAPoAAACA6AAAdTAAAOpgAAA6mAAAF3CculE8AAABWWlUWHRYTUw6Y29tLmFkb2JlLnhtcAAAAAAAPHg6eG1wbWV0YSB4bWxuczp4PSJhZG9iZTpuczptZXRhLyIgeDp4bXB0az0iWE1QIENvcmUgNS40LjAiPgogICA8cmRmOlJERiB4bWxuczpyZGY9Imh0dHA6Ly93d3cudzMub3JnLzE5OTkvMDIvMjItcmRmLXN5bnRheC1ucyMiPgogICAgICA8cmRmOkRlc2NyaXB0aW9uIHJkZjphYm91dD0iIgogICAgICAgICAgICB4bWxuczp0aWZmPSJodHRwOi8vbnMuYWRvYmUuY29tL3RpZmYvMS4wLyI+CiAgICAgICAgIDx0aWZmOk9yaWVudGF0aW9uPjE8L3RpZmY6T3JpZW50YXRpb24+CiAgICAgIDwvcmRmOkRlc2NyaXB0aW9uPgogICA8L3JkZjpSREY+CjwveDp4bXBtZXRhPgpMwidZAAAGb0lEQVR4Ae2ce1BUVRjAv7sLDmophpMkjwRfVMbDkuShsqxS0JDTTExWw0KEDpU2Fo6Tkzk7NY3WlM34V3+Ugk1NZf84FY9BISAEn4Upw6tATGmmHDBR2Ne9fd/W3da7C9y99+wLOjPMedxzvvN9vz3vcy8A/ztFBDhFpWQWStcXlvM8vC8ze8BkQygjwMEVDrhmXgNfth07fEyqnNfApeuLN/G87XOs0Gt1SI3xVhwBNoaGaDY31VX0iHV4xagMvUFnE4QaEGCGWFGw+whqSKPRbmw5XtFMtjAHt0ZvSLQI0ASCMDfYYUn1J3gwY0Zqa+0nvRrpQzXxzPWGWKsA1VMRGnERAOaB2fIxhZm1uMzHX5xnHb3ZgtDuI8FT2mm5DVoWBmYVF4eZ/zRX42/yEAt5gS5DA5xZdVc1Go2asQH+M4SWGegGs9JPAGGN6hZn0sQewO5ZxEqpYJCD41uYqjEuLdvwuiAIe4PBWNY6KgaXnl1YyAtQiQoplsHaGF/KU2R0enZRDi8I3+K4FupLZQOpLo/Bpa03pIANGnGAvDOQDPG1Lh7NqmtzSuMEG1RNd2j0I8kGp9cbIsxWcw12z0hv/boPp9zPXHRy4nLQalUvHlz0kiUxP3/LrGGTtRahJblIYJRQVvoU7Cx/3j7V/NjeyUSqbl0qvPvOdli6JBa+bzqDqybcNDFyk4IrKPhK+/v1i19jfXpGdbqIyc3JhK1lm+zpKUkJTOARNOPuMtBqNHBvzD0wa2YYnDpzwaVupQmTgpu7IOwj/J2eVVqBnHKXBq5CwrJFEB21wJ5dLTxnaCSQ5O/74BCMjZnkqCMrz4Tg0vSFe7B175AlSUUmHo+JG5pOM4HnDtrW1/bB0NBfKjR0LTouuDSdoQShfehaxDspLOD5ChoRcAsuQ1eYh90TN+7yZ10WONXAk0Lrv3QVtpWzb2minS7g0vXFq3gQvsMMYWImX/pK4PkaGvG4DVzaoy8sAautHtPDfQlLWpcn8PwBjfR1gEvPKbsbrKYG3BVESw3xR1wOPH9Bc4DLydkx22QZrkNoK/wBabw6J4IXHj7HsU6j8t4e06Q6cllGY4ip8ZdvcDJ4TPowUOKhoSGw961XYHVqokMl2gVw3D9nFL6GRkpoIzWxdGtT4NAoAAPuWp4/oREi3JBwvwYgKxeVLBYr1NWfdElvPdnOfHHrUombBO3lvvammLikhfgsoG+oaCJ4c9cW0PzbPUVbHnxgKZO9rShPrm+fVdNW7a6+MdqzEgstk1vQl/nczZ5dvf0QvZDN3laJLXZwHR1HhJQVjxwdtdg2oJAoJYK8VcYdNNoRVNX8wGRvq1Rvxzquu/usJS5h5VHByj+Jwu5SKpBlufGg0Ybd3YSh9lTFE90d4KjQQO9Pt2LjU6ow+Az+zaY0f7mJoIk6+RPebeBIIZwshmLikxpxkfQcRv1yiyUHmr/huYAjhX7ra7+C8M7j8vJpjMq+lxCNUeN7Ak2sxx8tzy04UgjhdcfGJQ3ijiJfVNDbvhJook6+hjcuOFLoct/5czGLk7X4Ytg6UUFv+WqgiTr5Et6E4EghbHkN0fFJizCYTHFvuIzVyfC28WX7xQrJV7P3HA+eyWyBny84XuFVbYas8Sts7eLN+AIxXg96x3X19MPg4B924WqgidrR9mzXngPQduq8PWnk5i04e65DfMzEl/0KRFbBS3eYrt1oxEMJ2mEwd/MjwqF8uwHe21/BbO9Jpypv7CyFL47UQmd3H1OdZYOjWrPyiiPHRvlWvJhexFSLIBTmETiyL0NXstzGWeld34ggtJeZyrLGOOfaWhoOdnGgfQKJjzqnT7ewx+AIUGv9oRP2nQXH8dMNmGjvpMsRMaPUx2VKZ0x88jXssnnSZ9MhrhgcwUF4p6Pjk2dicNq8cU524zA14vHkQAWdHV6acBnZhk+xz9KhwLRweAjdpWiMc6aDlyZC1PzFJbhAPu6cPpXDaGuz6hYnAsrN3TZnyHS9Gce8/+7wxIdTzcdPkpiBIzYZG0oW2qzWNlwgx0w1VqI92NoaWxsOZ6nuqqJA8lvqDl7VApeLwoed06dKGFvZEH3wS/YwBUcCWxoqL+K4txH/2L3+SIL97AgafegrfiXNHBzZd6K+sgknWwMG8Rw0+B11T/rAV/w6mixiOsZJEa3WFb0KwO+Xpgd6HKFM+s8MAt2GgNXvb4Fnwe/dm9SQAAAAAElFTkSuQmCC'));
								break;
							case CAPS_KEY_ID:
								I(this.c, 'cfca-mod-click');
								this.c.firstChild.src =
									'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAEIAAAA3CAYAAAC4u5yaAAAABGdBTUEAALGPC/xhBQAAACBjSFJNAAB6JgAAgIQAAPoAAACA6AAAdTAAAOpgAAA6mAAAF3CculE8AAABWWlUWHRYTUw6Y29tLmFkb2JlLnhtcAAAAAAAPHg6eG1wbWV0YSB4bWxuczp4PSJhZG9iZTpuczptZXRhLyIgeDp4bXB0az0iWE1QIENvcmUgNS40LjAiPgogICA8cmRmOlJERiB4bWxuczpyZGY9Imh0dHA6Ly93d3cudzMub3JnLzE5OTkvMDIvMjItcmRmLXN5bnRheC1ucyMiPgogICAgICA8cmRmOkRlc2NyaXB0aW9uIHJkZjphYm91dD0iIgogICAgICAgICAgICB4bWxuczp0aWZmPSJodHRwOi8vbnMuYWRvYmUuY29tL3RpZmYvMS4wLyI+CiAgICAgICAgIDx0aWZmOk9yaWVudGF0aW9uPjE8L3RpZmY6T3JpZW50YXRpb24+CiAgICAgIDwvcmRmOkRlc2NyaXB0aW9uPgogICA8L3JkZjpSREY+CjwveDp4bXBtZXRhPgpMwidZAAADdklEQVRoBe3bS08TQRwA8PnPFoQoBg9GPRg0njyZ+EgUwwE86EH5BgjRiMGD38CkB76AF4SDCF48EGKiHugBU2ICwZt4kAtCtAdjIvEB8ijd8T9NpnQRutt57LRTNmmm+5jZ+f92utPOTgmxvCSTScoYA8vVINRmBThC6t3Cs9aO7ie2MRK2IAQC88ltQhhBDIIYfQDAbNTJSpMMImyHDQSGpt+OWsGIHWIvBMFhCyNWiDAEmxixQURFsIURS69RLgLHYITdb73WMxBXb2K8RcggiFbBUwA6OD058sB0b2IUQhVBgMSBYQxCF0JcGEYgdCPEgaEdwhSCaQytEKYRTGJog4gLwRSGFoi4EUxgKEPYQtCNoQRhG0EnhjREpSDowpCCqDQEHRhlQ1QqgipGWRCVjqCCERmiWhBkMSJBVBuCDEYoRLUilItREiKPMPV5GEeLukXB1ZhGGc/YE8IVBHHhwjB2hXANIQrGfxCuIoRhBCBcRyiF4YmdPN2Ak4P44PFu8TY337OLw89fHs4szqVEfAWI1vbuXuwdkmJHDaRXWk6f+/h1ae4TjzX/gOdq550mROivgeADIeYA+sUDpDyEv5K9gRBHA0fVwgpjZ9s6es7zUPMQjMDxWoh7txhzCdJcgGiop68JkM3dDnR7G3w70HxwtgCRnhhZooTewrkJ824Hvh0dPkt9TyntTI8NrPCtge8RfEPb9d4TLLd+ZsunR/h62ALE78Ubzs2w40zux6BeAIVxnHuUj0fcAPk5GaOBGBMefKcUFqZSw5niB8uBg2Qqe7m96zHmeyiTV1ceCvQRTjlS6vVimR+hK2CT5TgBUfxRkMVyAkI2+OJ8bkBQ9Zm7TkBgZ6F803cCoriJy753AgKD2G8Rsi1gZz4nWsT+PWLnZVVYV28RAGsK59eSFfuMrGpByhCUsAXVSqjmZ4x8US1DGYLV1Y/jPXtVtSKy+bG7WCP1iTey+UU+ZYiZ1NNlHMfowp+0G6LQGFP+K7uP10H1nIVRbJWCMosf5ltOXRgj4GcRpAHLWsfP7W9MV3AdBz5gFdf/4gzzdbyCG5huIl4W0xymOWxRPm7nf2Hi3wf4q+TC0fE14VHv3szk6KuSB0fcGXrSiOVoPYw/aErjeGrj7A/v56EEbfrl0dWGZVpHfW8r2wiJS8f+pJPJLZ0n/QcaqNpiz8GyIAAAAABJRU5ErkJggg==';
								break;
							case SHIFT_KEY_ID:
							case FINISH_KEY_ID:
							case SYMBOLE_SHIFT_KEY_ID:
							case SYMBOLE_FINISH_KEY_ID:
								this.K == KEYBOARD_TYPE_NUMBER
									? I(this.c, 'cfca-num-mod-click')
									: I(this.c, 'cfca-mod-click');
								break;
							case SPACE_KEY_ID:
							case SYMBOLE_SPACE_KEY_ID:
								break;
							default:
								Jb(this, this.c)
									? ((a = xb(this.c)),
									  this.o.show(
											this.c.id,
											a.x,
											a.y,
											a.wb,
											a.Ia,
											this.c.value
									  ))
									: this.K == KEYBOARD_TYPE_NUMBER
									? I(this.c, 'cfca-num-click')
									: I(this.c, 'cfca-click');
						}
				}
				break;
			case 'touchmove':
			case 'pointermove':
			case 'mousemove':
				a: {
					a.preventDefault && a.preventDefault();
					a.stopPropagation && a.stopPropagation();
					if (window.navigator.msPointerEnabled || !a.touches) {
						if (Kb(this, a)) break a;
					} else
						for (b = 0; b < a.touches.length; b++)
							if (Kb(this, a.touches[b])) break a;
					Hb(this);
					this.c = null;
				}
				break;
			case 'touchend':
			case 'pointerup':
			case 'mouseup':
				if (
					(a.preventDefault && a.preventDefault(),
					a.stopPropagation && a.stopPropagation(),
					Hb(this),
					null !== this.c)
				) {
					switch (pb(this.c.id)) {
						case DELETE_KEY_ID:
						case SYMBOLE_DELET_KEY_ID:
							void 0 != this.b &&
								(a = document.getElementById(this.b)) &&
								void 0 !== a.value &&
								(Lb(this),
								0 == Mb[this.b]
									? 1 <= a.value.length
										? (a.value = a.value.substring(
												0,
												a.value.length - 1
										  ))
										: (a.value = '')
									: 0 >= ea(this.b)
									? (a.value = '')
									: ((a.value = a.value.substring(
											0,
											a.value.length - 1
									  )),
									  (a = this.b),
									  'undefined' !== typeof n[a] &&
											(0 < ea(a) &&
												((b = []),
												0 < n[a].M.length &&
													(b = bb(
														n[a].M,
														n[a].v,
														n[a].v
													).result),
												b.pop(),
												(n[a].M = []),
												(b = Ca(
													b,
													n[a].v,
													n[a].v
												).result),
												null !== b &&
													(n[a].M =
														n[a].M.concat(b))),
											fa(a) || ga(a)
												? 0 < n[a].l.length &&
												  n[a].l.pop()
												: 0 < n[a].s.length &&
												  ((b = xa(
														ya(
															n[a].ga,
															n[a].s,
															0,
															n[a].ha,
															0
														)
												  )),
												  1 < b.length
														? ((b = b.substr(
																0,
																b.length - 1
														  )),
														  (n[a].s = ya(
																n[a].ga,
																b,
																1,
																n[a].ha,
																1
														  )))
														: (n[a].s = '')))));
							this.sa &&
								'function' === typeof this.sa &&
								this.sa(
									this.b,
									CFCA_KEYBOARD_DELETE,
									document.getElementById(this.b).value.length
								);
							break;
						case CAPS_KEY_ID:
							(this.Fa = !this.Fa)
								? ((a = this.j), nb(a.ba), ob(a.V), ob(a.R))
								: this.j.ca(!1);
							break;
						case SHIFT_KEY_ID:
							this.j && this.j.ca && this.j.ca(!0);
							Nb(this);
							Ob(this);
							break;
						case SYMBOLE_SHIFT_KEY_ID:
							this.j && this.j.ca && this.j.ca(!1);
							break;
						case FINISH_KEY_ID:
						case SYMBOLE_FINISH_KEY_ID:
							this.jb && this.jb(this.b);
							this.show(!1);
							break;
						case SPACE_KEY_ID:
						case SYMBOLE_SPACE_KEY_ID:
							break;
						default:
							b = this.c.value;
							if (
								void 0 != this.b &&
								(a = document.getElementById(this.b)) &&
								void 0 !== a.value
							)
								if (0 == Mb[this.b])
									a.value.length >= n[this.b].maxLength ||
										(b && !b.length) ||
										(a.value += b);
								else if (
									(0 == ea(this.b) && (a.value = ''),
									!(
										a.value.length >= n[this.b].maxLength ||
										(b && !b.length)
									) &&
										(Lb(this),
										0 < a.value.length &&
											'*' !=
												a.value.charAt(
													a.value.length - 1
												) &&
											(a.value = a.value
												.substring(
													0,
													a.value.length - 1
												)
												.concat('*')),
										Pb(this, a),
										(a.value += b),
										(a = this.b),
										'undefined' !== typeof n[a]))
								)
									if (
										((c = []),
										0 < n[a].M.length &&
											(c = bb(
												n[a].M,
												n[a].v,
												n[a].v
											).result),
										c.push(cb(b)),
										(n[a].M = []),
										(c = Ca(c, n[a].v, n[a].v).result),
										(n[a].M = n[a].M.concat(c)),
										fa(a))
									)
										for (
											b =
												Da(b) ^
												n[a].da[n[a].l.length % 16],
												n[a].l.push(b),
												b = Array(16),
												da(b),
												b = [].concat(n[a].da),
												c = new Ha(),
												n[a].da.length = 16,
												c.Ja(n[a].da),
												c = 0;
											c < n[a].l.length;
											c++
										)
											n[a].l[c] =
												n[a].l[c] ^
												b[c % 16] ^
												n[a].da[c % 16];
									else if (ga(a))
										for (
											b =
												Da(b) ^
												n[a].X[n[a].l.length % 8],
												n[a].l.push(b),
												b = Array(8),
												da(b),
												b = [].concat(n[a].X),
												c = new Ha(),
												n[a].X.length = 8,
												c.Ja(n[a].X),
												c = 0;
											c < n[a].l.length;
											c++
										)
											n[a].l[c] =
												n[a].l[c] ^
												b[c % 8] ^
												n[a].X[c % 8];
									else
										(c = ''),
											0 < n[a].s.length &&
												(c = xa(
													ya(
														n[a].ga,
														n[a].s,
														0,
														n[a].ha,
														0
													)
												)),
											(n[a].s = ya(
												n[a].ga,
												c + b,
												1,
												n[a].ha,
												1
											));
							this.sa &&
								'function' === typeof this.sa &&
								this.sa(
									this.b,
									CFCA_KEYBOARD_INSERT,
									document.getElementById(this.b).value.length
								);
					}
					this.c = null;
				}
		}
	};
	function Kb(a, b) {
		if (null !== a.c) {
			var c = a.c.getBoundingClientRect();
			return b.clientX >= c.left &&
				b.clientX <= c.right &&
				b.clientY >= c.top &&
				b.clientY <= c.bottom
				? !0
				: !1;
		}
	}
	function Hb(a) {
		if (null !== a.c)
			switch (pb(a.c.id)) {
				case DELETE_KEY_ID:
				case SYMBOLE_DELET_KEY_ID:
					a.K == KEYBOARD_TYPE_NUMBER
						? (a.c.classList.remove('cfca-num-mod-click'),
						  (a.c.firstChild.src =
								'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAGAAAABCCAYAAACo/NMFAAAAAXNSR0IArs4c6QAACbRJREFUeAHtXXtwG8UZ312dIpvSxCEMtA1lmqShw7SdYTIdsGRibMnYcWpDphkHSkaSTYNgeEwb2k4ynU6j0intZOhQwgyTmiS25DJDMEnDI3FwJDkPWwr9i0wolEd4NKQdIC6BmkSOpNt+K+diWdrV6+7ksy/3z959393ut7/f3j6+b0/CqMKHveUnV9BEYhhRen2Fi654cRihMYTRKYrwYWzBO2MHAuFsI+Ceyh32jvXVaPR0iFLkqFypxikJI3zIKpF7Dh/ofUexiigneqcdHc9Z6Ojos2YFn+FLEb0lkUy9WufqXK7gbVFO9E7nXV21Fcq4S+9yZkD+1dD9rlq8dNnuD9977b8VeQMcTo+fIuSbAeBUxETAYn4iKT/NCtN9DHA0en0ykv9SkZrNsEKwRJp0JcDR5LmdynQX9Pvcrg5jFLlmwZLW/n7/+RmGXVHmLm/xfT2ViHtkRB+BAWBOzkMYP60bAXZnlwPRZAhet+qcgpkAo2PzbTX1AwNPfsHVzyIhdMEbZEr/mF0laIBv6TIG1De5YY6fekkIPsIfWK3VrWYAn4GOLVJfNvjpa4oWav4G2J2ehZB5lFJ6LbdQjEctVKobGdrxFlc/S4W1jW5oj7mHpm9Aw6rOGihiIA/4ZyXJ8kOzgZ8L+6REMwJaWx+yjX8hvwDgf38y+8kzeNWShOA1w4M9r05KL51pQoDf7yefxc88A+DXiyHF90ZDgb1ivTk1mhDwyqETWwC+1SIICUa/jg0Fd4j0ZparJqDW5fkVjC4PiEHET0Ujfb8X682tUUWAw9nZiWQqBhfj3StuWfyQuSHOX/uyCahrdK+kNJX2Z/CKgEXGkapryVoYH2Se/pJsAgGpHCBgrn9jCqF+8Opxn4cZz+u2uZbbDvb2xsvJ30zPlPwG3OzyXAeznb0A/mV8oPBJW5Wt9eCe3jN8vTqpvdFzF/jTb1WXy9Sna51dy2qdnmnpKksioGFl59eSMt0P5l85tQoTV9DyP7MSy4qDA9s+4unVyhj4CNOgLMsvakUCAx+Dzwoa1BaHy/1ztTaW+nzRBMBCa+74OXkACljEKwTCbXEqkfYj4Z43eHq1MgV85lmFyFKVFiQo4MMsbj6zT5bRY5UmoSgCOjr8c87EP98NFb+BByQMuClEyI+PHgiM8PRqZZngK3mpJSEbfCXfSpNQkADo7/HJ0RMBqLBLMTInxfiBWLh3T45cA4Gj+b6rKKbdvJhCuSSIwFfMlSnePOHRVST6pQUJcLg8f4Jgwp0iEzAij8TCQd0iXtHBrZ9gglazLo5nQ6kkFAIfYSxjirsOh/re5JWntSwvAXan9xfQ8taLCiUYb4sNBTaJ9FrJY6G+V6DnX6WWhGLBhzoFtbK9UD5CAmpd3rWUyptFGUC//9LCBW33ifRay9WSYETwGUZcAtJTPJn2gB5mlrkHxjiGFlx5R3//GliPVe4olwSjgs+QyyGAGSvLqd2wjcjKgxa6gX8iq7Ut1v/4OZ5eb1mpJBgZfIbVlBbucHqXwBaSERh0r+YBCS3/3xaC7MOh4L94+krK7E3uFpTCe9ggzCs3PV4QtBGchZuUeX7OfRcG3Er0+QVDkunpHpX3i8CH2cHnMNdfYQTwGZDFvAmwJebPRgA/h/gMQboLaui4/3KaOLsXjP12hu7iKbT8cWyht8dCvccvCg1wUogEoYkVbPlCGy4oiM/ns46fHnseXuUfcG8GY2E8cMcO9B3i6qdZWDIJBgKfQUeOnzi3DcBvEeEIc/31sUhfv0hvBHnRJBgMfIYdoYj8Ix+I4IpYmk9vFB2VpU/BFu5qWbERZhwJ2JnxH+XaCCk5Gg5shlb+pMgYIOBBCMBsFOmNIJ+calK2L0l4QF1sWnhRhQWUoUgPws31i38Gz+4SPQ+GP+pwut0i/XTKJ8GfcCkXsqVU31Gh/NTq0wSwuO38qpq1LI4ryBCDh3A7rBOaBfppEZcKvmKkkUhIE8AMg42y4yyOC1NOwZhAreAb2sUqrVRkOtOC4LMBF6PH1Trw9K7jRQJYQSyOa5UsrXDKDSnCOuFy2GG4r7553SK9DcuXf1Hgg0sZZm8Pa+FFzWeLWt0UAlhmhwd7ThLrnFZoOWe4mYOb4nzy/P6Gdh83Lsx9RkNh0eBfcCkXmqJOd3eUQwDDKjq4/XUkgf8dVsBc7Ci9Lj527uX2dp9gZwT3KdXCUsFXCjQyCVwCmOETK1/qZhEipSJZ6U2nv4zvZJ+fZsl1uSwXfMUYo5IgJIAZzlbAMJA9rFQiO4Xpadup0Ze3Zsu1vlYLvmKPEUnISwAzHOK9T8BNwsgYfPu0jn2GqlRS65R5adm+Ha28msWSYJigPAN0JBLcCCQ8IwIXSNgEO8vWifRq5CwoTwnJ+cAtnWeZvp0iSOg2RFBeAQ4GY/q9pdVdMDMKKbLsFCO61e7ytGXLtbhm7hKIRWyYkleZ4Ct55CFhy9Ghvp8q9+mdFuyCFAO6u7sT5KvSj4CE1xRZZpresUbRzpubu27KlGt1PoUEleArNnFIqCj4zI4pIUnFsHwp+/g4cT4ehRjBtwT3nZYIrhsOB98W6FWJYZfcL8Hsj7UMI6bDmzJuikWCkLc+hygkWTIBzLy6xru/k8JJiB3TBQJz3/8KsdnD4W0fC/SmE4sIKLoLykSMfWYK3321A3uinRGLztLxfSzUmfncpfNcBMoigGUTDQdjBJE7YZ3A3RsEY8Ky8dGxXSzkmVvsJYmCQNkEsAxGhgIvwkpZ+IEeLNSaj78b3w5pWV2dYuRsTlURwIBhG3Mxwb8TgQTgu+0u7x9EerPLVRPAAAQSfgMk7BCCSekGCGs+KNSbWKEJAQw/2/LF90J3tE+EJbgSnnA0eVeL9GaVa0bAQb8/OVe6ag2smv/OBZNSQlP0r7DxdzlXb1KhZgQw/AYHH/tSslnaYMR9l4fnxeBHo/e7PL0ZZZoSwAA8MtDzqcVibYHp6Sc8QIGEmhSiAxd+V4h3y6yT1d169zd4lQK3zv80J4AVNBza8Z6FkpXwJozxCgY3xjdhFb2/qck3j6+fXVI5mfIKanRK1/m53dW5Asnpny6TeAZACzhUUzWvhe3I4Olnuoy1fAY+vPW/5X1vAeB360oAAxB+TcULe/R7ZzqYethPsOTSPZ770fvHjl2z5IYEfHfg0qMSMzZPjIdikYBflzEgG5Sj4eCj4EJ+Kltu1muYqsNPFuN7WP0rQgAriP1uEBT8N3Zu5oOBTy34tmgkcILhoPsYkAl2Q2dn1fiHMgTYaV2m3DTn0O2wlq+Az+pdUQJYgWb9AweCLM9GIz0RhkHm8X/Qt3HUcq/fXgAAAABJRU5ErkJggg=='))
						: (a.c.classList.remove('cfca-mod-click'),
						  (a.c.firstChild.src =
								'data:image/png;base64,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'));
					break;
				case CAPS_KEY_ID:
					a.c.classList.remove('cfca-mod-click');
					a.c.firstChild.src =
						'data:image/png;base64,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';
					break;
				case SHIFT_KEY_ID:
				case FINISH_KEY_ID:
				case SYMBOLE_SHIFT_KEY_ID:
				case SYMBOLE_FINISH_KEY_ID:
					a.K == KEYBOARD_TYPE_NUMBER
						? a.c.classList.remove('cfca-num-mod-click')
						: a.c.classList.remove('cfca-mod-click');
					break;
				case SPACE_KEY_ID:
				case SYMBOLE_SPACE_KEY_ID:
					break;
				default:
					Jb(a, a.c)
						? a.o &&
						  ((a = a.o), (a.o.firstChild.value = ''), ob(a.o))
						: a.K == KEYBOARD_TYPE_NUMBER
						? a.c.classList.remove('cfca-num-click')
						: a.c.classList.remove('cfca-click');
			}
	}
	function Ib(a, b, c) {
		for (
			var d = TOUCH_EVENTS.slice(0), e = 0;
			e < d.length && d[e] != b;
			e++
		);
		d.splice(e, 3);
		d.forEach(function (b) {
			document.removeEventListener
				? a.removeEventListener(b, c, !1)
				: document.detachEvent && a.detachEvent('on' + b, c);
		});
	}
	function Jb(a, b) {
		if (
			a.K == KEYBOARD_TYPE_NUMBER ||
			hb('ipad') ||
			jb() ||
			'mousedown' === a.lb ||
			'INPUT' != b.tagName ||
			lb() <= kb()
		)
			return !1;
		var c = pb(b.id);
		return c == SHIFT_KEY_ID ||
			c == FINISH_KEY_ID ||
			c == SYMBOLE_SHIFT_KEY_ID ||
			c == SYMBOLE_FINISH_KEY_ID
			? !1
			: !0;
	}
	function Ob(a) {
		var b = a.Oa[a.b];
		b || (b = KEYBOARD_DISORDER_NONE);
		KEYBOARD_DISORDER_NONE === b
			? (Qb(a, !1), Rb(a, !1), Sb(a, !1))
			: KEYBOARD_DISORDER_ONLY_DIGITAL === b
			? (Qb(a, !0), Rb(a, !1), Sb(a, !1))
			: KEYBOARD_DISORDER_ALL === b && (Qb(a, !0), Rb(a, !0), Sb(a, !0));
		if (a.C) for (b = 0; b < a.C.length; b++) a.C[b].value = a.wa[b];
		if (a.kb) for (b = 0; b < a.C.length; b++) a.kb[b].value = a.wa[b];
		if (a.$) for (b = 0; b < a.$.length; b++) a.$[b].value = a.Ga[b];
		if (a.aa)
			for (b = 0; b < a.aa.length; b++)
				a.aa[b].value = a.Ga[b].toUpperCase();
		if (a.N) for (b = 0; b < a.N.length; b++) a.N[b].value = a.Pa[b];
	}
	function Rb(a, b) {
		a.Ga && (b ? ub(a.Ga) : (a.Ga = ORDERED_LETTERS.slice(0)));
	}
	function Qb(a, b) {
		b ? ub(a.wa) : (a.wa = ORDERED_NUMBERS.slice(0));
	}
	function Sb(a, b) {
		b ? ub(a.Pa) : (a.Pa = ORDERED_SYMBOLS.slice(0));
	}
	function Nb(a) {
		a.Fa = !1;
		a.j.xa.firstChild.src =
			'data:image/png;base64,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';
	}
	function Lb(a) {
		'number' == typeof a.Ha[a.b] &&
			(window.clearTimeout(a.Ha[a.b]), delete a.Ha[a.b]);
	}
	function Pb(a, b) {
		a.Ha[a.b] = window.setTimeout(
			function (a) {
				a.value = a.value.substring(0, a.value.length - 1).concat('*');
			},
			1e3,
			b
		);
	}
	k.show = function (a) {
		var b = document.getElementById(BLANK_DIV_ID);
		if (!0 === a) {
			this.K == KEYBOARD_TYPE_COMPLETE && Nb(this);
			Ob(this);
			this.j.ca && this.j.ca(!1);
			if ((a = document.getElementById(this.b))) {
				var c = xb(a);
				if (c) {
					a = document.body;
					var d = document.documentElement,
						e = this.Z();
					a =
						e -
						(Math.max(
							a.scrollHeight,
							a.offsetHeight,
							d.clientHeight,
							d.scrollHeight,
							d.offsetHeight
						) -
							(c.y + c.Ia));
					c = e - (lb() - (c.y + c.Ia));
					if (0 <= a) {
						if (!b) {
							b = document.createElement('DIV');
							b.id = BLANK_DIV_ID;
							var e = b,
								d = this.i,
								f = d.parentNode;
							f.lastChild == d
								? f.appendChild(e)
								: f.insertBefore(e, d.nextSibling);
						}
						b.style.height = a + 10 + 'px';
					}
					(c >= window.pageYOffset ||
						-1 < navigator.userAgent.indexOf('Safari')) &&
						window.scrollTo(0, c + 10);
				}
			} else
				b && b.parentNode && b.parentNode.removeChild(b),
					(this.Fa = !1);
			nb(this.i);
			this.j.pa();
		} else b && b.parentNode && b.parentNode.removeChild(b), ob(this.i);
	};
	k.ab = function () {
		return this.i && 'block' == this.i.style.display;
	};
	k.Z = function () {
		if (this.K == KEYBOARD_TYPE_COMPLETE) return this.j.Z() + 1;
		if (this.K == KEYBOARD_TYPE_NUMBER) return this.j.Z();
	};
	k.gb = function (a, b) {
		if (this.Oa[b] != a) {
			if (a < KEYBOARD_DISORDER_NONE || a > KEYBOARD_DISORDER_ALL)
				return CFCA_ERROR_INVALID_PARAMETER;
			this.Oa[b] = a;
		}
		return CFCA_OK;
	};
	k.eb = function (a) {
		return 'function' === typeof a && 1 === a.length
			? ((this.jb = a), CFCA_OK)
			: CFCA_ERROR_INVALID_PARAMETER;
	};
	k.fb = function (a) {
		return 'function' === typeof a && 3 === a.length
			? ((this.sa = a), CFCA_OK)
			: CFCA_ERROR_INVALID_PARAMETER;
	};
	k.ua = function (a, b, c) {
		this.K != KEYBOARD_TYPE_NUMBER &&
			((db = a), (eb = b), (fb = c), this.j.ua && this.j.ua());
	};
	k.ta = function (a) {
		FINISH_KEY_TEXT = a;
		this.j.ta();
	};
	k.Ba = function (a) {
		SHIFT_KEY_TEXT_SYMBOL = a;
		this.j.Ba();
	};
	var Tb = [],
		Mb = [];
	function N(a) {
		this.i = Tb[a];
		this.i || (this.i = new Fb(a));
		Tb[a] = this.i;
	}
	goog.exportSymbol('CFCAKeyboard', N);
	N.prototype.Wb = function () {
		this.i.show(!0);
	};
	goog.exportProperty(N.prototype, 'showKeyboard', N.prototype.Wb);
	N.prototype.Kb = function () {
		this.i.show(!1);
	};
	goog.exportProperty(N.prototype, 'hideKeyboard', N.prototype.Kb);
	N.prototype.ab = function () {
		return this.i.ab();
	};
	goog.exportProperty(N.prototype, 'isShowing', N.prototype.ab);
	N.prototype.yb = function (a) {
		var b, c;
		if (void 0 === a || null === a) return CFCA_ERROR_INVALID_PARAMETER;
		'string' === typeof a
			? ((b = a), (c = document.getElementById(b) || null))
			: 'object' === typeof a && ((b = a.id), (c = a));
		if (!c) return CFCA_ERROR_INVALID_PARAMETER;
		this.b = b;
		if (void 0 == n[this.b]) {
			a = this.b;
			b = Array(16);
			c = Array(24);
			var d = Array(8),
				e = Array(16),
				f = Array(8);
			da(b);
			da(c);
			da(d);
			da(e);
			da(f);
			Ub(b);
			Ub(c);
			Ub(d);
			Ub(e);
			Ub(f);
			n[a] = {
				v: b,
				ga: ua(c),
				ha: ua(d),
				ma: null,
				maxLength: DEFAULT_MAX_LENGTH,
				cb: DEFAULT_MIN_LENGTH,
				Ka: OUTPUT_TYPE_ORIGINAL,
				W: CIPHER_TYPE_RSA,
				errorCode: CFCA_OK,
				s: '',
				da: e,
				X: f,
				l: [],
				M: [],
			};
		}
		this.i.b = this.b;
		return CFCA_OK;
	};
	goog.exportProperty(N.prototype, 'bindInputBox', N.prototype.yb);
	N.prototype.eb = function (a) {
		return this.i.eb(a);
	};
	goog.exportProperty(N.prototype, 'setDoneCallback', N.prototype.eb);
	N.prototype.Rb = function (a, b) {
		if (void 0 !== b) return this.Va(b), (Mb[b] = a), CFCA_OK;
		this.Va(this.b);
		Mb[this.b] = a;
		return CFCA_OK;
	};
	goog.exportProperty(N.prototype, 'setEncryptState', N.prototype.Rb);
	N.prototype.Bb = function (a) {
		return void 0 !== a ? Mb[a] : Mb[this.b];
	};
	goog.exportProperty(N.prototype, 'getEncryptState', N.prototype.Bb);
	N.prototype.fb = function (a) {
		return this.i.fb(a);
	};
	goog.exportProperty(N.prototype, 'setInputChangeCallback', N.prototype.fb);
	N.prototype.ua = function (a, b, c) {
		this.i.ua(a, b, c);
	};
	goog.exportProperty(N.prototype, 'setSpaceKeyImage', N.prototype.ua);
	N.prototype.Ub = function (a, b) {
		return void 0 !== b ? la(b, a) : la(this.b, a);
	};
	goog.exportProperty(N.prototype, 'setOutputType', N.prototype.Ub);
	N.prototype.Qb = function (a, b) {
		return void 0 !== b ? ma(b, a) : ma(this.b, a);
	};
	goog.exportProperty(N.prototype, 'setCipherType', N.prototype.Qb);
	N.prototype.Ab = function (a) {
		return void 0 !== a ? n[a].W : n[this.b].W;
	};
	goog.exportProperty(N.prototype, 'getCipherType', N.prototype.Ab);
	N.prototype.Sb = function (a, b) {
		return void 0 !== b ? ja(b, a) : ja(this.b, a);
	};
	goog.exportProperty(N.prototype, 'setMaxLength', N.prototype.Sb);
	N.prototype.Gb = function (a) {
		return void 0 !== a ? n[a].maxLength : n[this.b].maxLength;
	};
	goog.exportProperty(N.prototype, 'getMaxLength', N.prototype.Gb);
	N.prototype.Tb = function (a, b) {
		return void 0 !== b ? ka(b, a) : ka(this.b, a);
	};
	goog.exportProperty(N.prototype, 'setMinLength', N.prototype.Tb);
	N.prototype.Hb = function (a) {
		return void 0 !== a ? getMinLength(a) : getMinLength(this.b);
	};
	goog.exportProperty(N.prototype, 'getMinLength', N.prototype.Hb);
	N.prototype.Vb = function (a, b) {
		return void 0 !== b ? ha(b, a) : ha(this.b, a);
	};
	goog.exportProperty(N.prototype, 'setServerRandom', N.prototype.Vb);
	N.prototype.Db = function (a) {
		return 0 == Mb[a]
			? document.getElementById(a).value
			: void 0 !== a
			? na(a)
			: na(this.b);
	};
	goog.exportProperty(N.prototype, 'getEncryptedInputValue', N.prototype.Db);
	N.prototype.Cb = function (a) {
		return 0 == Mb[a] ? '' : void 0 !== a ? Ea(a) : Ea(this.b);
	};
	goog.exportProperty(
		N.prototype,
		'getEncryptedClientRandom',
		N.prototype.Cb
	);
	N.prototype.zb = function (a, b) {
		return Za(a, b);
	};
	goog.exportProperty(N.prototype, 'checkInputValueMatch', N.prototype.zb);
	N.prototype.Va = function (a) {
		a = a || this.b;
		if (void 0 == a) return CFCA_ERROR_INVALID_SIP_HANDLE_ID;
		a === this.b && Lb(this.i);
		document.getElementById(a).value = '';
		'undefined' === typeof n[a]
			? (a = CFCA_ERROR_INVALID_SIP_HANDLE_ID)
			: ((n[a].s = ''),
			  (n[a].errorCode = CFCA_OK),
			  (n[a].l = []),
			  (n[a].M = []),
			  (a = CFCA_OK));
		return a;
	};
	goog.exportProperty(N.prototype, 'clearInputValue', N.prototype.Va);
	N.prototype.Jb = function (a) {
		return 0 == Mb[a] ? [] : void 0 !== a ? ab(a) : ab(this.b);
	};
	goog.exportProperty(N.prototype, 'getWeakCipherInfo', N.prototype.Jb);
	N.prototype.gb = function (a, b) {
		return void 0 == b ? CFCA_ERROR_INVALID_SIP_HANDLE_ID : this.i.gb(a, b);
	};
	goog.exportProperty(N.prototype, 'setRandomType', N.prototype.gb);
	N.prototype.Z = function () {
		return this.i.Z();
	};
	goog.exportProperty(N.prototype, 'getKeyboardHeight', N.prototype.Z);
	N.prototype.Eb = function (a) {
		return void 0 !== a ? $a(a) : $a(this.b);
	};
	goog.exportProperty(N.prototype, 'getErrorCode', N.prototype.Eb);
	N.prototype.ta = function (a) {
		this.i.ta(a);
	};
	goog.exportProperty(N.prototype, 'setFinishBtnTitle', N.prototype.ta);
	N.prototype.Ba = function (a) {
		this.i.Ba(a);
	};
	goog.exportProperty(N.prototype, 'setBackBtnTitle', N.prototype.Ba);
	goog.exportSymbol('getCFCAKeyboardVersion', function () {
		return HTML5_SIP_VERSION;
	});
	var P = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/',
		Vb = '=';
	function wa(a) {
		var b,
			c,
			d = '';
		for (b = 0; b + 3 <= a.length; b += 3)
			(c = parseInt(a.substring(b, b + 3), 16)),
				(d += P.charAt(c >> 6) + P.charAt(c & 63));
		b + 1 == a.length
			? ((c = parseInt(a.substring(b, b + 1), 16)),
			  (d += P.charAt(c << 2)))
			: b + 2 == a.length &&
			  ((c = parseInt(a.substring(b, b + 2), 16)),
			  (d += P.charAt(c >> 2) + P.charAt((c & 3) << 4)));
		for (; 0 < (d.length & 3); ) d += Vb;
		return d;
	}
	function ia(a) {
		var b = '',
			c,
			d = 0,
			e,
			f;
		for (c = 0; c < a.length && a.charAt(c) != Vb; ++c)
			(f = P.indexOf(a.charAt(c))),
				0 > f ||
					(d
						? 1 == d
							? ((b += Wb.charAt((e << 2) | (f >> 4))),
							  (e = f & 15),
							  (d = 2))
							: 2 == d
							? ((b += Wb.charAt(e)),
							  (b += Wb.charAt(f >> 2)),
							  (e = f & 3),
							  (d = 3))
							: ((b += Wb.charAt((e << 2) | (f >> 4))),
							  (b += Wb.charAt(f & 15)),
							  (d = 0))
						: ((b += Wb.charAt(f >> 2)), (e = f & 3), (d = 1)));
		1 == d && (b += Wb.charAt(e << 2));
		return b;
	}
	function sa(a) {
		var b,
			c,
			d = '';
		for (b = 0; b + 3 <= a.length; b += 3)
			(c =
				((a[b] & 255) << 16) ^
				((a[b + 1] & 255) << 8) ^
				(a[b + 2] & 255)),
				(d +=
					P.charAt(c >>> 18) +
					P.charAt((c & 258048) >>> 12) +
					P.charAt((c & 4032) >>> 6) +
					P.charAt(c & 63));
		b == a.length - 1
			? ((c = a[b] & 255),
			  (d += P.charAt((c & 252) >>> 2) + P.charAt((c & 3) << 4)))
			: b + 1 == a.length - 1 &&
			  ((c = ((a[b] & 255) << 8) ^ (a[b + 1] & 255)),
			  (d +=
					P.charAt((c & 64512) >>> 10) +
					P.charAt((c & 1008) >>> 4) +
					P.charAt((c & 15) << 2)));
		for (; 0 < (d.length & 3); ) d += Vb;
		return d;
	}
	function ya(a, b, c, d, e) {
		var f = [
				16843776, 0, 65536, 16843780, 16842756, 66564, 4, 65536, 1024,
				16843776, 16843780, 1024, 16778244, 16842756, 16777216, 4, 1028,
				16778240, 16778240, 66560, 66560, 16842752, 16842752, 16778244,
				65540, 16777220, 16777220, 65540, 0, 1028, 66564, 16777216,
				65536, 16843780, 4, 16842752, 16843776, 16777216, 16777216,
				1024, 16842756, 65536, 66560, 16777220, 1024, 4, 16778244,
				66564, 16843780, 65540, 16842752, 16778244, 16777220, 1028,
				66564, 16843776, 1028, 16778240, 16778240, 0, 65540, 66560, 0,
				16842756,
			],
			g = [
				-2146402272, -2147450880, 32768, 1081376, 1048576, 32,
				-2146435040, -2147450848, -2147483616, -2146402272, -2146402304,
				-2147483648, -2147450880, 1048576, 32, -2146435040, 1081344,
				1048608, -2147450848, 0, -2147483648, 32768, 1081376,
				-2146435072, 1048608, -2147483616, 0, 1081344, 32800,
				-2146402304, -2146435072, 32800, 0, 1081376, -2146435040,
				1048576, -2147450848, -2146435072, -2146402304, 32768,
				-2146435072, -2147450880, 32, -2146402272, 1081376, 32, 32768,
				-2147483648, 32800, -2146402304, 1048576, -2147483616, 1048608,
				-2147450848, -2147483616, 1048608, 1081344, 0, -2147450880,
				32800, -2147483648, -2146435040, -2146402272, 1081344,
			],
			l = [
				520, 134349312, 0, 134348808, 134218240, 0, 131592, 134218240,
				131080, 134217736, 134217736, 131072, 134349320, 131080,
				134348800, 520, 134217728, 8, 134349312, 512, 131584, 134348800,
				134348808, 131592, 134218248, 131584, 131072, 134218248, 8,
				134349320, 512, 134217728, 134349312, 134217728, 131080, 520,
				131072, 134349312, 134218240, 0, 512, 131080, 134349320,
				134218240, 134217736, 512, 0, 134348808, 134218248, 131072,
				134217728, 134349320, 8, 131592, 131584, 134217736, 134348800,
				134218248, 520, 134348800, 131592, 8, 134348808, 131584,
			],
			h = [
				8396801, 8321, 8321, 128, 8396928, 8388737, 8388609, 8193, 0,
				8396800, 8396800, 8396929, 129, 0, 8388736, 8388609, 1, 8192,
				8388608, 8396801, 128, 8388608, 8193, 8320, 8388737, 1, 8320,
				8388736, 8192, 8396928, 8396929, 129, 8388736, 8388609, 8396800,
				8396929, 129, 0, 0, 8396800, 8320, 8388736, 8388737, 1, 8396801,
				8321, 8321, 128, 8396929, 129, 1, 8192, 8388609, 8193, 8396928,
				8388737, 8193, 8320, 8388608, 8396801, 128, 8388608, 8192,
				8396928,
			],
			p = [
				256, 34078976, 34078720, 1107296512, 524288, 256, 1073741824,
				34078720, 1074266368, 524288, 33554688, 1074266368, 1107296512,
				1107820544, 524544, 1073741824, 33554432, 1074266112,
				1074266112, 0, 1073742080, 1107820800, 1107820800, 33554688,
				1107820544, 1073742080, 0, 1107296256, 34078976, 33554432,
				1107296256, 524544, 524288, 1107296512, 256, 33554432,
				1073741824, 34078720, 1107296512, 1074266368, 33554688,
				1073741824, 1107820544, 34078976, 1074266368, 256, 33554432,
				1107820544, 1107820800, 524544, 1107296256, 1107820800,
				34078720, 0, 1074266112, 1107296256, 524544, 33554688,
				1073742080, 524288, 0, 1074266112, 34078976, 1073742080,
			],
			x = [
				536870928, 541065216, 16384, 541081616, 541065216, 16,
				541081616, 4194304, 536887296, 4210704, 4194304, 536870928,
				4194320, 536887296, 536870912, 16400, 0, 4194320, 536887312,
				16384, 4210688, 536887312, 16, 541065232, 541065232, 0, 4210704,
				541081600, 16400, 4210688, 541081600, 536870912, 536887296, 16,
				541065232, 4210688, 541081616, 4194304, 16400, 536870928,
				4194304, 536887296, 536870912, 16400, 536870928, 541081616,
				4210688, 541065216, 4210704, 541081600, 0, 541065232, 16, 16384,
				541065216, 4210704, 16384, 4194320, 536887312, 0, 541081600,
				536870912, 4194320, 536887312,
			],
			z = [
				2097152, 69206018, 67110914, 0, 2048, 67110914, 2099202,
				69208064, 69208066, 2097152, 0, 67108866, 2, 67108864, 69206018,
				2050, 67110912, 2099202, 2097154, 67110912, 67108866, 69206016,
				69208064, 2097154, 69206016, 2048, 2050, 69208066, 2099200, 2,
				67108864, 2099200, 67108864, 2099200, 2097152, 67110914,
				67110914, 69206018, 69206018, 2, 2097154, 67108864, 67110912,
				2097152, 69208064, 2050, 2099202, 69208064, 2050, 67108866,
				69208066, 69206016, 2099200, 0, 2, 69208066, 0, 2099202,
				69206016, 2048, 67108866, 67110912, 2048, 2097154,
			],
			F = [
				268439616, 4096, 262144, 268701760, 268435456, 268439616, 64,
				268435456, 262208, 268697600, 268701760, 266240, 268701696,
				266304, 4096, 64, 268697600, 268435520, 268439552, 4160, 266240,
				262208, 268697664, 268701696, 4160, 0, 0, 268697664, 268435520,
				268439552, 266304, 262144, 266304, 262144, 268701696, 4096, 64,
				268697664, 4096, 266304, 268439552, 64, 268435520, 268697600,
				268697664, 268435456, 262144, 268439616, 0, 268701760, 262208,
				268435520, 268697600, 268439552, 268439616, 0, 268701760,
				266240, 266240, 4160, 4160, 262208, 268435456, 268701696,
			];
		a = va(a);
		var q = 0,
			w,
			B,
			y,
			r,
			v,
			u,
			ba,
			qa,
			t,
			G,
			Sa,
			Ta,
			O = b.length,
			Ba = 0,
			Ua = 32 == a.length ? 3 : 9;
		u =
			3 == Ua
				? c
					? [0, 32, 2]
					: [30, -2, -2]
				: c
				? [0, 32, 2, 62, 30, -2, 64, 96, 2]
				: [94, 62, -2, 32, 64, 2, 30, -2, -2];
		2 == e
			? (b += '        ')
			: 1 == e
			? ((e = 8 - (O % 8)),
			  (b += String.fromCharCode(e, e, e, e, e, e, e, e)),
			  8 == e && (O += 8))
			: e || (b += '\x00\x00\x00\x00\x00\x00\x00\x00');
		tempresult = result = '';
		ba =
			(d.charCodeAt(q++) << 24) |
			(d.charCodeAt(q++) << 16) |
			(d.charCodeAt(q++) << 8) |
			d.charCodeAt(q++);
		t =
			(d.charCodeAt(q++) << 24) |
			(d.charCodeAt(q++) << 16) |
			(d.charCodeAt(q++) << 8) |
			d.charCodeAt(q++);
		for (q = 0; q < O; ) {
			r =
				(b.charCodeAt(q++) << 24) |
				(b.charCodeAt(q++) << 16) |
				(b.charCodeAt(q++) << 8) |
				b.charCodeAt(q++);
			v =
				(b.charCodeAt(q++) << 24) |
				(b.charCodeAt(q++) << 16) |
				(b.charCodeAt(q++) << 8) |
				b.charCodeAt(q++);
			c ? ((r ^= ba), (v ^= t)) : ((qa = ba), (G = t), (ba = r), (t = v));
			e = ((r >>> 4) ^ v) & 252645135;
			v ^= e;
			r ^= e << 4;
			e = ((r >>> 16) ^ v) & 65535;
			v ^= e;
			r ^= e << 16;
			e = ((v >>> 2) ^ r) & 858993459;
			r ^= e;
			v ^= e << 2;
			e = ((v >>> 8) ^ r) & 16711935;
			r ^= e;
			v ^= e << 8;
			e = ((r >>> 1) ^ v) & 1431655765;
			v ^= e;
			r ^= e << 1;
			r = (r << 1) | (r >>> 31);
			v = (v << 1) | (v >>> 31);
			for (w = 0; w < Ua; w += 3) {
				Sa = u[w + 1];
				Ta = u[w + 2];
				for (d = u[w]; d != Sa; d += Ta)
					(B = v ^ a[d]),
						(y = ((v >>> 4) | (v << 28)) ^ a[d + 1]),
						(e = r),
						(r = v),
						(v =
							e ^
							(g[(B >>> 24) & 63] |
								h[(B >>> 16) & 63] |
								x[(B >>> 8) & 63] |
								F[B & 63] |
								f[(y >>> 24) & 63] |
								l[(y >>> 16) & 63] |
								p[(y >>> 8) & 63] |
								z[y & 63]));
				e = r;
				r = v;
				v = e;
			}
			r = (r >>> 1) | (r << 31);
			v = (v >>> 1) | (v << 31);
			e = ((r >>> 1) ^ v) & 1431655765;
			v ^= e;
			r ^= e << 1;
			e = ((v >>> 8) ^ r) & 16711935;
			r ^= e;
			v ^= e << 8;
			e = ((v >>> 2) ^ r) & 858993459;
			r ^= e;
			v ^= e << 2;
			e = ((r >>> 16) ^ v) & 65535;
			v ^= e;
			r ^= e << 16;
			e = ((r >>> 4) ^ v) & 252645135;
			v ^= e;
			r ^= e << 4;
			c ? ((ba = r), (t = v)) : ((r ^= qa), (v ^= G));
			tempresult += String.fromCharCode(
				r >>> 24,
				(r >>> 16) & 255,
				(r >>> 8) & 255,
				r & 255,
				v >>> 24,
				(v >>> 16) & 255,
				(v >>> 8) & 255,
				v & 255
			);
			Ba += 8;
			512 == Ba && ((result += tempresult), (tempresult = ''), (Ba = 0));
		}
		return result + tempresult;
	}
	function va(a) {
		pc2bytes0 = [
			0, 4, 536870912, 536870916, 65536, 65540, 536936448, 536936452, 512,
			516, 536871424, 536871428, 66048, 66052, 536936960, 536936964,
		];
		pc2bytes1 = [
			0, 1, 1048576, 1048577, 67108864, 67108865, 68157440, 68157441, 256,
			257, 1048832, 1048833, 67109120, 67109121, 68157696, 68157697,
		];
		pc2bytes2 = [
			0, 8, 2048, 2056, 16777216, 16777224, 16779264, 16779272, 0, 8,
			2048, 2056, 16777216, 16777224, 16779264, 16779272,
		];
		pc2bytes3 = [
			0, 2097152, 134217728, 136314880, 8192, 2105344, 134225920,
			136323072, 131072, 2228224, 134348800, 136445952, 139264, 2236416,
			134356992, 136454144,
		];
		pc2bytes4 = [
			0, 262144, 16, 262160, 0, 262144, 16, 262160, 4096, 266240, 4112,
			266256, 4096, 266240, 4112, 266256,
		];
		pc2bytes5 = [
			0, 1024, 32, 1056, 0, 1024, 32, 1056, 33554432, 33555456, 33554464,
			33555488, 33554432, 33555456, 33554464, 33555488,
		];
		pc2bytes6 = [
			0, 268435456, 524288, 268959744, 2, 268435458, 524290, 268959746, 0,
			268435456, 524288, 268959744, 2, 268435458, 524290, 268959746,
		];
		pc2bytes7 = [
			0, 65536, 2048, 67584, 536870912, 536936448, 536872960, 536938496,
			131072, 196608, 133120, 198656, 537001984, 537067520, 537004032,
			537069568,
		];
		pc2bytes8 = [
			0, 262144, 0, 262144, 2, 262146, 2, 262146, 33554432, 33816576,
			33554432, 33816576, 33554434, 33816578, 33554434, 33816578,
		];
		pc2bytes9 = [
			0, 268435456, 8, 268435464, 0, 268435456, 8, 268435464, 1024,
			268436480, 1032, 268436488, 1024, 268436480, 1032, 268436488,
		];
		pc2bytes10 = [
			0, 32, 0, 32, 1048576, 1048608, 1048576, 1048608, 8192, 8224, 8192,
			8224, 1056768, 1056800, 1056768, 1056800,
		];
		pc2bytes11 = [
			0, 16777216, 512, 16777728, 2097152, 18874368, 2097664, 18874880,
			67108864, 83886080, 67109376, 83886592, 69206016, 85983232,
			69206528, 85983744,
		];
		pc2bytes12 = [
			0, 4096, 134217728, 134221824, 524288, 528384, 134742016, 134746112,
			16, 4112, 134217744, 134221840, 524304, 528400, 134742032,
			134746128,
		];
		pc2bytes13 = [
			0, 4, 256, 260, 0, 4, 256, 260, 1, 5, 257, 261, 1, 5, 257, 261,
		];
		for (
			var b = 8 < a.length ? 3 : 1,
				c = Array(32 * b),
				d = [0, 0, 1, 1, 1, 1, 1, 1, 0, 1, 1, 1, 1, 1, 1, 0],
				e,
				f,
				g = 0,
				l = 0,
				h,
				p = 0;
			p < b;
			p++
		) {
			left =
				(a.charCodeAt(g++) << 24) |
				(a.charCodeAt(g++) << 16) |
				(a.charCodeAt(g++) << 8) |
				a.charCodeAt(g++);
			right =
				(a.charCodeAt(g++) << 24) |
				(a.charCodeAt(g++) << 16) |
				(a.charCodeAt(g++) << 8) |
				a.charCodeAt(g++);
			h = ((left >>> 4) ^ right) & 252645135;
			right ^= h;
			left ^= h << 4;
			h = ((right >>> -16) ^ left) & 65535;
			left ^= h;
			right ^= h << -16;
			h = ((left >>> 2) ^ right) & 858993459;
			right ^= h;
			left ^= h << 2;
			h = ((right >>> -16) ^ left) & 65535;
			left ^= h;
			right ^= h << -16;
			h = ((left >>> 1) ^ right) & 1431655765;
			right ^= h;
			left ^= h << 1;
			h = ((right >>> 8) ^ left) & 16711935;
			left ^= h;
			right ^= h << 8;
			h = ((left >>> 1) ^ right) & 1431655765;
			right ^= h;
			left ^= h << 1;
			h = (left << 8) | ((right >>> 20) & 240);
			left =
				(right << 24) |
				((right << 8) & 16711680) |
				((right >>> 8) & 65280) |
				((right >>> 24) & 240);
			right = h;
			for (var x = 0; x < d.length; x++)
				d[x]
					? ((left = (left << 2) | (left >>> 26)),
					  (right = (right << 2) | (right >>> 26)))
					: ((left = (left << 1) | (left >>> 27)),
					  (right = (right << 1) | (right >>> 27))),
					(left &= -15),
					(right &= -15),
					(e =
						pc2bytes0[left >>> 28] |
						pc2bytes1[(left >>> 24) & 15] |
						pc2bytes2[(left >>> 20) & 15] |
						pc2bytes3[(left >>> 16) & 15] |
						pc2bytes4[(left >>> 12) & 15] |
						pc2bytes5[(left >>> 8) & 15] |
						pc2bytes6[(left >>> 4) & 15]),
					(f =
						pc2bytes7[right >>> 28] |
						pc2bytes8[(right >>> 24) & 15] |
						pc2bytes9[(right >>> 20) & 15] |
						pc2bytes10[(right >>> 16) & 15] |
						pc2bytes11[(right >>> 12) & 15] |
						pc2bytes12[(right >>> 8) & 15] |
						pc2bytes13[(right >>> 4) & 15]),
					(h = ((f >>> 16) ^ e) & 65535),
					(c[l++] = e ^ h),
					(c[l++] = f ^ (h << 16));
		}
		return c;
	}
	function xa(a) {
		return a.substr(0, a.length - a.charCodeAt(a.length - 1));
	}
	function ta(a) {
		for (
			var b = '', c = '0123456789abcdef'.split(''), d = 0;
			d < a.length;
			d++
		)
			b += c[a.charCodeAt(d) >> 4] + c[a.charCodeAt(d) & 15];
		return b;
	}
	function ua(a) {
		for (var b = '', c = 0; c < a.length; c++)
			b += String.fromCharCode(a[c]);
		return b;
	}
	function va(a) {
		pc2bytes0 = [
			0, 4, 536870912, 536870916, 65536, 65540, 536936448, 536936452, 512,
			516, 536871424, 536871428, 66048, 66052, 536936960, 536936964,
		];
		pc2bytes1 = [
			0, 1, 1048576, 1048577, 67108864, 67108865, 68157440, 68157441, 256,
			257, 1048832, 1048833, 67109120, 67109121, 68157696, 68157697,
		];
		pc2bytes2 = [
			0, 8, 2048, 2056, 16777216, 16777224, 16779264, 16779272, 0, 8,
			2048, 2056, 16777216, 16777224, 16779264, 16779272,
		];
		pc2bytes3 = [
			0, 2097152, 134217728, 136314880, 8192, 2105344, 134225920,
			136323072, 131072, 2228224, 134348800, 136445952, 139264, 2236416,
			134356992, 136454144,
		];
		pc2bytes4 = [
			0, 262144, 16, 262160, 0, 262144, 16, 262160, 4096, 266240, 4112,
			266256, 4096, 266240, 4112, 266256,
		];
		pc2bytes5 = [
			0, 1024, 32, 1056, 0, 1024, 32, 1056, 33554432, 33555456, 33554464,
			33555488, 33554432, 33555456, 33554464, 33555488,
		];
		pc2bytes6 = [
			0, 268435456, 524288, 268959744, 2, 268435458, 524290, 268959746, 0,
			268435456, 524288, 268959744, 2, 268435458, 524290, 268959746,
		];
		pc2bytes7 = [
			0, 65536, 2048, 67584, 536870912, 536936448, 536872960, 536938496,
			131072, 196608, 133120, 198656, 537001984, 537067520, 537004032,
			537069568,
		];
		pc2bytes8 = [
			0, 262144, 0, 262144, 2, 262146, 2, 262146, 33554432, 33816576,
			33554432, 33816576, 33554434, 33816578, 33554434, 33816578,
		];
		pc2bytes9 = [
			0, 268435456, 8, 268435464, 0, 268435456, 8, 268435464, 1024,
			268436480, 1032, 268436488, 1024, 268436480, 1032, 268436488,
		];
		pc2bytes10 = [
			0, 32, 0, 32, 1048576, 1048608, 1048576, 1048608, 8192, 8224, 8192,
			8224, 1056768, 1056800, 1056768, 1056800,
		];
		pc2bytes11 = [
			0, 16777216, 512, 16777728, 2097152, 18874368, 2097664, 18874880,
			67108864, 83886080, 67109376, 83886592, 69206016, 85983232,
			69206528, 85983744,
		];
		pc2bytes12 = [
			0, 4096, 134217728, 134221824, 524288, 528384, 134742016, 134746112,
			16, 4112, 134217744, 134221840, 524304, 528400, 134742032,
			134746128,
		];
		pc2bytes13 = [
			0, 4, 256, 260, 0, 4, 256, 260, 1, 5, 257, 261, 1, 5, 257, 261,
		];
		for (
			var b = 8 < a.length ? 3 : 1,
				c = Array(32 * b),
				d = [0, 0, 1, 1, 1, 1, 1, 1, 0, 1, 1, 1, 1, 1, 1, 0],
				e,
				f,
				g = 0,
				l = 0,
				h,
				p = 0;
			p < b;
			p++
		) {
			left =
				(a.charCodeAt(g++) << 24) |
				(a.charCodeAt(g++) << 16) |
				(a.charCodeAt(g++) << 8) |
				a.charCodeAt(g++);
			right =
				(a.charCodeAt(g++) << 24) |
				(a.charCodeAt(g++) << 16) |
				(a.charCodeAt(g++) << 8) |
				a.charCodeAt(g++);
			h = ((left >>> 4) ^ right) & 252645135;
			right ^= h;
			left ^= h << 4;
			h = ((right >>> -16) ^ left) & 65535;
			left ^= h;
			right ^= h << -16;
			h = ((left >>> 2) ^ right) & 858993459;
			right ^= h;
			left ^= h << 2;
			h = ((right >>> -16) ^ left) & 65535;
			left ^= h;
			right ^= h << -16;
			h = ((left >>> 1) ^ right) & 1431655765;
			right ^= h;
			left ^= h << 1;
			h = ((right >>> 8) ^ left) & 16711935;
			left ^= h;
			right ^= h << 8;
			h = ((left >>> 1) ^ right) & 1431655765;
			right ^= h;
			left ^= h << 1;
			h = (left << 8) | ((right >>> 20) & 240);
			left =
				(right << 24) |
				((right << 8) & 16711680) |
				((right >>> 8) & 65280) |
				((right >>> 24) & 240);
			right = h;
			for (var x = 0; x < d.length; x++)
				d[x]
					? ((left = (left << 2) | (left >>> 26)),
					  (right = (right << 2) | (right >>> 26)))
					: ((left = (left << 1) | (left >>> 27)),
					  (right = (right << 1) | (right >>> 27))),
					(left &= -15),
					(right &= -15),
					(e =
						pc2bytes0[left >>> 28] |
						pc2bytes1[(left >>> 24) & 15] |
						pc2bytes2[(left >>> 20) & 15] |
						pc2bytes3[(left >>> 16) & 15] |
						pc2bytes4[(left >>> 12) & 15] |
						pc2bytes5[(left >>> 8) & 15] |
						pc2bytes6[(left >>> 4) & 15]),
					(f =
						pc2bytes7[right >>> 28] |
						pc2bytes8[(right >>> 24) & 15] |
						pc2bytes9[(right >>> 20) & 15] |
						pc2bytes10[(right >>> 16) & 15] |
						pc2bytes11[(right >>> 12) & 15] |
						pc2bytes12[(right >>> 8) & 15] |
						pc2bytes13[(right >>> 4) & 15]),
					(h = ((f >>> 16) ^ e) & 65535),
					(c[l++] = e ^ h),
					(c[l++] = f ^ (h << 16));
		}
		return c;
	}
	function xa(a) {
		return a.substr(0, a.length - a.charCodeAt(a.length - 1));
	}
	function ta(a) {
		for (
			var b = '', c = '0123456789abcdef'.split(''), d = 0;
			d < a.length;
			d++
		)
			b += c[a.charCodeAt(d) >> 4] + c[a.charCodeAt(d) & 15];
		return b;
	}
	function ua(a) {
		for (var b = '', c = 0; c < a.length; c++)
			b += String.fromCharCode(a[c]);
		return b;
	}
	function Xb(a, b) {
		this.x = b;
		this.q = a;
	}
	k = Xb.prototype;
	k.w = function (a) {
		return a == this ? !0 : this.q.w(a.q) && this.x.w(a.x);
	};
	k.ka = function () {
		return new Xb(this.q, D(this.x.ka(), this.q));
	};
	k.add = function (a) {
		return new Xb(this.q, D(this.x.add(a.x), this.q));
	};
	k.B = function (a) {
		return new Xb(this.q, D(this.x.B(a.x), this.q));
	};
	k.multiply = function (a) {
		return new Xb(this.q, D(this.x.multiply(a.x), this.q));
	};
	k.U = function () {
		return new Xb(this.q, D(this.x.U(), this.q));
	};
	k.nb = function (a) {
		return new Xb(this.q, D(this.x.multiply(Yb(a.x, this.q)), this.q));
	};
	function Na(a, b, c, d) {
		this.curve = a;
		this.x = b;
		this.y = c;
		this.z = null == d ? C : d;
		this.va = null;
	}
	function Pa(a) {
		null == a.va && (a.va = Yb(a.z, a.curve.q));
		var b = a.x.x.multiply(a.va);
		a.curve.reduce(b);
		return E(a.curve, b);
	}
	function Qa(a) {
		null == a.va && (a.va = Yb(a.z, a.curve.q));
		var b = a.y.x.multiply(a.va);
		a.curve.reduce(b);
		return E(a.curve, b);
	}
	Na.prototype.w = function (a) {
		return a == this
			? !0
			: Va(this)
			? Va(a)
			: Va(a)
			? Va(this)
			: D(
					a.y.x.multiply(this.z).B(this.y.x.multiply(a.z)),
					this.curve.q
			  ).w(Q)
			? D(
					a.x.x.multiply(this.z).B(this.x.x.multiply(a.z)),
					this.curve.q
			  ).w(Q)
			: !1;
	};
	function Va(a) {
		return null == a.x && null == a.y ? !0 : a.z.w(Q) && !a.y.x.w(Q);
	}
	Na.prototype.ka = function () {
		return new Na(this.curve, this.x, this.y.ka(), this.z);
	};
	Na.prototype.add = function (a) {
		if (Va(this)) return a;
		if (Va(a)) return this;
		var b = D(
				a.y.x.multiply(this.z).B(this.y.x.multiply(a.z)),
				this.curve.q
			),
			c = D(
				a.x.x.multiply(this.z).B(this.x.x.multiply(a.z)),
				this.curve.q
			);
		if (Q.w(c)) return Q.w(b) ? Zb(this) : this.curve.$a;
		var d = new m('3'),
			e = this.x.x,
			f = this.y.x,
			g = c.U(),
			l = g.multiply(c),
			e = e.multiply(g),
			g = b.U().multiply(this.z),
			c = D(
				g.B(e.shiftLeft(1)).multiply(a.z).B(l).multiply(c),
				this.curve.q
			),
			b = D(
				e
					.multiply(d)
					.multiply(b)
					.B(f.multiply(l))
					.B(g.multiply(b))
					.multiply(a.z)
					.add(b.multiply(l)),
				this.curve.q
			);
		a = D(l.multiply(this.z).multiply(a.z), this.curve.q);
		return new Na(this.curve, E(this.curve, c), E(this.curve, b), a);
	};
	function Zb(a) {
		if (Va(a)) return a;
		if (0 == $b(a.y.x)) return a.curve.$a;
		var b = new m('3'),
			c = a.x.x,
			d = a.y.x,
			e = d.multiply(a.z),
			f = D(e.multiply(d), a.curve.q),
			d = a.curve.Ta.x,
			g = c.U().multiply(b);
		Q.w(d) || (g = g.add(a.z.U().multiply(d)));
		g = D(g, a.curve.q);
		d = D(
			g.U().B(c.shiftLeft(3).multiply(f)).shiftLeft(1).multiply(e),
			a.curve.q
		);
		b = D(
			g
				.multiply(b)
				.multiply(c)
				.B(f.shiftLeft(1))
				.shiftLeft(2)
				.multiply(f)
				.B(g.U().multiply(g)),
			a.curve.q
		);
		e = D(e.U().multiply(e).shiftLeft(3), a.curve.q);
		return new Na(a.curve, E(a.curve, d), E(a.curve, b), e);
	}
	Na.prototype.multiply = function (a) {
		if (Va(this)) return this;
		if (0 == $b(a)) return this.curve.$a;
		var b = a.multiply(new m('3')),
			c = this.ka(),
			d = this,
			e;
		for (e = Ga(b) - 2; 0 < e; --e) {
			var d = Zb(d),
				f = ac(b, e),
				g = ac(a, e);
			f != g && (d = d.add(f ? this : c));
		}
		return d;
	};
	function Ma(a, b, c) {
		this.q = a;
		this.Ta = E(this, b);
		this.mb = E(this, c);
		this.$a = new Na(this, null, null);
		this.Ob = new bc(this.q);
	}
	Ma.prototype.w = function (a) {
		return a == this
			? !0
			: this.q.w(a.q) && this.Ta.w(a.Ta) && this.mb.w(a.mb);
	};
	function E(a, b) {
		return new Xb(a.q, b);
	}
	Ma.prototype.reduce = function (a) {
		this.Ob.reduce(a);
	};
	var cc;
	function m(a, b, c) {
		null != a &&
			('number' == typeof a
				? dc(this, a, b, c)
				: null == b && 'string' != typeof a
				? ec(this, a, 256)
				: ec(this, a, b));
	}
	function R() {
		return new m(null);
	}
	function fc(a, b, c, d, e, f) {
		for (; 0 <= --f; ) {
			var g = b * this[a++] + c[d] + e;
			e = Math.floor(g / 67108864);
			c[d++] = g & 67108863;
		}
		return e;
	}
	function gc(a, b, c, d, e, f) {
		var g = b & 32767;
		for (b >>= 15; 0 <= --f; ) {
			var l = this[a] & 32767,
				h = this[a++] >> 15,
				p = b * l + h * g,
				l = g * l + ((p & 32767) << 15) + c[d] + (e & 1073741823);
			e = (l >>> 30) + (p >>> 15) + b * h + (e >>> 30);
			c[d++] = l & 1073741823;
		}
		return e;
	}
	function hc(a, b, c, d, e, f) {
		var g = b & 16383;
		for (b >>= 14; 0 <= --f; ) {
			var l = this[a] & 16383,
				h = this[a++] >> 14,
				p = b * l + h * g,
				l = g * l + ((p & 16383) << 14) + c[d] + e;
			e = (l >> 28) + (p >> 14) + b * h;
			c[d++] = l & 268435455;
		}
		return e;
	}
	'Microsoft Internet Explorer' == navigator.appName
		? ((m.prototype.L = gc), (cc = 30))
		: 'Netscape' != navigator.appName
		? ((m.prototype.L = fc), (cc = 26))
		: ((m.prototype.L = hc), (cc = 28));
	k = m.prototype;
	k.f = cc;
	k.H = (1 << cc) - 1;
	k.F = 1 << cc;
	k.xb = Math.pow(2, 52);
	k.hb = 52 - cc;
	k.ib = 2 * cc - 52;
	var Wb = '0123456789abcdefghijklmnopqrstuvwxyz',
		ic = [],
		jc,
		kc;
	jc = 48;
	for (kc = 0; 9 >= kc; ++kc) ic[jc++] = kc;
	jc = 97;
	for (kc = 10; 36 > kc; ++kc) ic[jc++] = kc;
	jc = 65;
	for (kc = 10; 36 > kc; ++kc) ic[jc++] = kc;
	function lc(a, b) {
		var c = ic[a.charCodeAt(b)];
		return null == c ? -1 : c;
	}
	function mc(a) {
		var b = R();
		nc(b, a);
		return b;
	}
	function oc(a) {
		var b = 1,
			c;
		if ((c = a >>> 16)) (a = c), (b += 16);
		if ((c = a >> 8)) (a = c), (b += 8);
		if ((c = a >> 4)) (a = c), (b += 4);
		if ((c = a >> 2)) (a = c), (b += 2);
		a >> 1 && (b += 1);
		return b;
	}
	function pc(a) {
		this.m = a;
	}
	k = pc.prototype;
	k.ya = function (a) {
		return 0 > a.a || 0 <= S(a, this.m) ? D(a, this.m) : a;
	};
	k.Aa = function (a) {
		return a;
	};
	k.reduce = function (a) {
		qc(a, this.m, null, a);
	};
	k.ra = function (a, b, c) {
		rc(a, b, c);
		this.reduce(c);
	};
	k.T = function (a, b) {
		sc(a, b);
		this.reduce(b);
	};
	function tc(a) {
		this.m = a;
		var b;
		if (1 > a.t) b = 0;
		else if (((b = a[0]), b & 1)) {
			var c = b & 3,
				c = (c * (2 - (b & 15) * c)) & 15,
				c = (c * (2 - (b & 255) * c)) & 255,
				c = (c * (2 - (((b & 65535) * c) & 65535))) & 65535,
				c = (c * (2 - ((b * c) % a.F))) % a.F;
			b = 0 < c ? a.F - c : -c;
		} else b = 0;
		this.rb = b;
		this.sb = this.rb & 32767;
		this.Lb = this.rb >> 15;
		this.Xb = (1 << (a.f - 15)) - 1;
		this.Mb = 2 * a.t;
	}
	k = tc.prototype;
	k.ya = function (a) {
		var b = R();
		uc(a.abs(), this.m.t, b);
		qc(b, this.m, null, b);
		0 > a.a && 0 < S(b, Q) && T(this.m, b, b);
		return b;
	};
	k.Aa = function (a) {
		var b = R();
		a.copyTo(b);
		this.reduce(b);
		return b;
	};
	k.reduce = function (a) {
		for (; a.t <= this.Mb; ) a[a.t++] = 0;
		for (var b = 0; b < this.m.t; ++b) {
			var c = a[b] & 32767,
				d =
					(c * this.sb +
						(((c * this.Lb + (a[b] >> 15) * this.sb) & this.Xb) <<
							15)) &
					a.H,
				c = b + this.m.t;
			for (a[c] += this.m.L(0, d, a, b, 0, this.m.t); a[c] >= a.F; )
				(a[c] -= a.F), a[++c]++;
		}
		U(a);
		vc(a, this.m.t, a);
		0 <= S(a, this.m) && T(a, this.m, a);
	};
	k.ra = function (a, b, c) {
		rc(a, b, c);
		this.reduce(c);
	};
	k.T = function (a, b) {
		sc(a, b);
		this.reduce(b);
	};
	k = m.prototype;
	k.copyTo = function (a) {
		for (var b = this.t - 1; 0 <= b; --b) a[b] = this[b];
		a.t = this.t;
		a.a = this.a;
	};
	function nc(a, b) {
		a.t = 1;
		a.a = 0 > b ? -1 : 0;
		0 < b ? (a[0] = b) : -1 > b ? (a[0] = b + a.F) : (a.t = 0);
	}
	function ec(a, b, c) {
		if (16 == c) c = 4;
		else if (8 == c) c = 3;
		else if (256 == c) c = 8;
		else if (2 == c) c = 1;
		else if (32 == c) c = 5;
		else if (4 == c) c = 2;
		else {
			nc(a, 0);
			null == c && (c = 10);
			for (
				var d = Math.floor((Math.LN2 * a.f) / Math.log(c)),
					e = Math.pow(c, d),
					f = !1,
					g = 0,
					l = 0,
					h = 0;
				h < b.length;
				++h
			) {
				var p = lc(b, h);
				0 > p
					? '-' == b.charAt(h) && 0 == $b(a) && (f = !0)
					: ((l = c * l + p),
					  ++g >= d && (wc(a, e), xc(a, l, 0), (l = g = 0)));
			}
			0 < g && (wc(a, Math.pow(c, g)), xc(a, l, 0));
			f && T(Q, a, a);
			return;
		}
		a.t = 0;
		a.a = 0;
		d = b.length;
		e = !1;
		for (f = 0; 0 <= --d; )
			(g = 8 == c ? b[d] & 255 : lc(b, d)),
				0 > g
					? '-' == b.charAt(d) && (e = !0)
					: ((e = !1),
					  f
							? f + c > a.f
								? ((a[a.t - 1] |=
										(g & ((1 << (a.f - f)) - 1)) << f),
								  (a[a.t++] = g >> (a.f - f)))
								: (a[a.t - 1] |= g << f)
							: (a[a.t++] = g),
					  (f += c),
					  f >= a.f && (f -= a.f));
		8 == c &&
			b[0] & 128 &&
			((a.a = -1), 0 < f && (a[a.t - 1] |= ((1 << (a.f - f)) - 1) << f));
		U(a);
		e && T(Q, a, a);
	}
	function U(a) {
		for (var b = a.a & a.H; 0 < a.t && a[a.t - 1] == b; ) --a.t;
	}
	function uc(a, b, c) {
		var d;
		for (d = a.t - 1; 0 <= d; --d) c[d + b] = a[d];
		for (d = b - 1; 0 <= d; --d) c[d] = 0;
		c.t = a.t + b;
		c.a = a.a;
	}
	function vc(a, b, c) {
		for (var d = b; d < a.t; ++d) c[d - b] = a[d];
		c.t = Math.max(a.t - b, 0);
		c.a = a.a;
	}
	function yc(a, b, c) {
		var d = b % a.f,
			e = a.f - d,
			f = (1 << e) - 1;
		b = Math.floor(b / a.f);
		var g = (a.a << d) & a.H,
			l;
		for (l = a.t - 1; 0 <= l; --l)
			(c[l + b + 1] = (a[l] >> e) | g), (g = (a[l] & f) << d);
		for (l = b - 1; 0 <= l; --l) c[l] = 0;
		c[b] = g;
		c.t = a.t + b + 1;
		c.a = a.a;
		U(c);
	}
	function zc(a, b, c) {
		c.a = a.a;
		var d = Math.floor(b / a.f);
		if (d >= a.t) c.t = 0;
		else {
			b %= a.f;
			var e = a.f - b,
				f = (1 << b) - 1;
			c[0] = a[d] >> b;
			for (var g = d + 1; g < a.t; ++g)
				(c[g - d - 1] |= (a[g] & f) << e), (c[g - d] = a[g] >> b);
			0 < b && (c[a.t - d - 1] |= (a.a & f) << e);
			c.t = a.t - d;
			U(c);
		}
	}
	function T(a, b, c) {
		for (var d = 0, e = 0, f = Math.min(b.t, a.t); d < f; )
			(e += a[d] - b[d]), (c[d++] = e & a.H), (e >>= a.f);
		if (b.t < a.t) {
			for (e -= b.a; d < a.t; )
				(e += a[d]), (c[d++] = e & a.H), (e >>= a.f);
			e += a.a;
		} else {
			for (e += a.a; d < b.t; )
				(e -= b[d]), (c[d++] = e & a.H), (e >>= a.f);
			e -= b.a;
		}
		c.a = 0 > e ? -1 : 0;
		-1 > e ? (c[d++] = a.F + e) : 0 < e && (c[d++] = e);
		c.t = d;
		U(c);
	}
	function rc(a, b, c) {
		var d = a.abs(),
			e = b.abs(),
			f = d.t;
		for (c.t = f + e.t; 0 <= --f; ) c[f] = 0;
		for (f = 0; f < e.t; ++f) c[f + d.t] = d.L(0, e[f], c, f, 0, d.t);
		c.a = 0;
		U(c);
		a.a != b.a && T(Q, c, c);
	}
	function sc(a, b) {
		for (var c = a.abs(), d = (b.t = 2 * c.t); 0 <= --d; ) b[d] = 0;
		for (d = 0; d < c.t - 1; ++d) {
			var e = c.L(d, c[d], b, 2 * d, 0, 1);
			(b[d + c.t] += c.L(
				d + 1,
				2 * c[d],
				b,
				2 * d + 1,
				e,
				c.t - d - 1
			)) >= c.F && ((b[d + c.t] -= c.F), (b[d + c.t + 1] = 1));
		}
		0 < b.t && (b[b.t - 1] += c.L(d, c[d], b, 2 * d, 0, 1));
		b.a = 0;
		U(b);
	}
	function qc(a, b, c, d) {
		var e = b.abs();
		if (!(0 >= e.t)) {
			var f = a.abs();
			if (f.t < e.t) null != c && nc(c, 0), null != d && a.copyTo(d);
			else {
				null == d && (d = R());
				var g = R(),
					l = a.a;
				b = b.a;
				var h = a.f - oc(e[e.t - 1]);
				0 < h ? (yc(e, h, g), yc(f, h, d)) : (e.copyTo(g), f.copyTo(d));
				e = g.t;
				f = g[e - 1];
				if (0 != f) {
					var p = f * (1 << a.hb) + (1 < e ? g[e - 2] >> a.ib : 0),
						x = a.xb / p,
						p = (1 << a.hb) / p,
						z = 1 << a.ib,
						F = d.t,
						q = F - e,
						w = null == c ? R() : c;
					uc(g, q, w);
					0 <= S(d, w) && ((d[d.t++] = 1), T(d, w, d));
					uc(C, e, w);
					for (T(w, g, g); g.t < e; ) g[g.t++] = 0;
					for (; 0 <= --q; ) {
						var B =
							d[--F] == f
								? a.H
								: Math.floor(d[F] * x + (d[F - 1] + z) * p);
						if ((d[F] += g.L(0, B, d, q, 0, e)) < B)
							for (uc(g, q, w), T(d, w, d); d[F] < --B; )
								T(d, w, d);
					}
					null != c && (vc(d, e, c), l != b && T(Q, c, c));
					d.t = e;
					U(d);
					0 < h && zc(d, h, d);
					0 > l && T(Q, d, d);
				}
			}
		}
	}
	function V(a) {
		return 0 == (0 < a.t ? a[0] & 1 : a.a);
	}
	k.exp = function (a, b) {
		if (4294967295 < a || 1 > a) return C;
		var c = R(),
			d = R(),
			e = b.ya(this),
			f = oc(a) - 1;
		for (e.copyTo(c); 0 <= --f; )
			if ((b.T(c, d), 0 < (a & (1 << f)))) b.ra(d, e, c);
			else
				var g = c,
					c = d,
					d = g;
		return b.Aa(c);
	};
	k.toString = function (a) {
		if (0 > this.a) return '-' + this.ka().toString(a);
		if (16 == a) a = 4;
		else if (8 == a) a = 3;
		else if (2 == a) a = 1;
		else if (32 == a) a = 5;
		else if (4 == a) a = 2;
		else {
			var b;
			b = a;
			null == b && (b = 10);
			if (0 == $b(this) || 2 > b || 36 < b) b = '0';
			else {
				a = Math.pow(b, Math.floor((Math.LN2 * this.f) / Math.log(b)));
				var c = mc(a),
					d = R(),
					e = R(),
					f = '';
				for (qc(this, c, d, e); 0 < $b(d); )
					(f = (a + aa(e)).toString(b).substr(1) + f), qc(d, c, d, e);
				b = aa(e).toString(b) + f;
			}
			return b;
		}
		var c = (1 << a) - 1,
			d = !1,
			e = '',
			f = this.t,
			g = this.f - ((f * this.f) % a);
		if (0 < f--)
			for (
				g < this.f &&
				0 < (b = this[f] >> g) &&
				((d = !0), (e = Wb.charAt(b)));
				0 <= f;

			)
				g < a
					? ((b = (this[f] & ((1 << g) - 1)) << (a - g)),
					  (b |= this[--f] >> (g += this.f - a)))
					: ((b = (this[f] >> (g -= a)) & c),
					  0 >= g && ((g += this.f), --f)),
					0 < b && (d = !0),
					d && (e += Wb.charAt(b));
		return d ? e : '0';
	};
	k.ka = function () {
		var a = R();
		T(Q, this, a);
		return a;
	};
	k.abs = function () {
		return 0 > this.a ? this.ka() : this;
	};
	function S(a, b) {
		var c = a.a - b.a;
		if (c) return c;
		var d = a.t;
		if ((c = d - b.t)) return 0 > a.a ? -c : c;
		for (; 0 <= --d; ) if ((c = a[d] - b[d])) return c;
		return 0;
	}
	function Ga(a) {
		return 0 >= a.t ? 0 : a.f * (a.t - 1) + oc(a[a.t - 1] ^ (a.a & a.H));
	}
	function D(a, b) {
		var c = R();
		qc(a.abs(), b, null, c);
		0 > a.a && 0 < S(c, Q) && T(b, c, c);
		return c;
	}
	function Ia(a, b, c) {
		c = 256 > b || V(c) ? new pc(c) : new tc(c);
		return a.exp(b, c);
	}
	var Q = mc(0),
		C = mc(1);
	function Ac() {}
	function Bc(a) {
		return a;
	}
	Ac.prototype.ya = Bc;
	Ac.prototype.Aa = Bc;
	Ac.prototype.ra = function (a, b, c) {
		rc(a, b, c);
	};
	Ac.prototype.T = function (a, b) {
		sc(a, b);
	};
	function bc(a) {
		this.la = R();
		this.ub = R();
		uc(C, 2 * a.t, this.la);
		this.Nb = this.la.nb(a);
		this.m = a;
	}
	k = bc.prototype;
	k.ya = function (a) {
		if (0 > a.a || a.t > 2 * this.m.t) return D(a, this.m);
		if (0 > S(a, this.m)) return a;
		var b = R();
		a.copyTo(b);
		this.reduce(b);
		return b;
	};
	k.Aa = function (a) {
		return a;
	};
	k.reduce = function (a) {
		vc(a, this.m.t - 1, this.la);
		a.t > this.m.t + 1 && ((a.t = this.m.t + 1), U(a));
		var b = this.Nb,
			c = this.la,
			d = this.m.t + 1,
			e = this.ub;
		--d;
		var f = (e.t = b.t + c.t - d);
		for (e.a = 0; 0 <= --f; ) e[f] = 0;
		for (f = Math.max(d - b.t, 0); f < c.t; ++f)
			e[b.t + f - d] = b.L(d - f, c[f], e, 0, 0, b.t + f - d);
		U(e);
		vc(e, 1, e);
		b = this.m;
		c = this.ub;
		d = this.m.t + 1;
		e = this.la;
		f = Math.min(b.t + c.t, d);
		e.a = 0;
		for (e.t = f; 0 < f; ) e[--f] = 0;
		var g;
		for (g = e.t - b.t; f < g; ++f) e[f + b.t] = b.L(0, c[f], e, f, 0, b.t);
		for (g = Math.min(c.t, d); f < g; ++f) b.L(0, c[f], e, f, 0, d - f);
		for (U(e); 0 > S(a, this.la); ) xc(a, 1, this.m.t + 1);
		for (T(a, this.la, a); 0 <= S(a, this.m); ) T(a, this.m, a);
	};
	k.ra = function (a, b, c) {
		rc(a, b, c);
		this.reduce(c);
	};
	k.T = function (a, b) {
		sc(a, b);
		this.reduce(b);
	};
	var W = [
			2, 3, 5, 7, 11, 13, 17, 19, 23, 29, 31, 37, 41, 43, 47, 53, 59, 61,
			67, 71, 73, 79, 83, 89, 97, 101, 103, 107, 109, 113, 127, 131, 137,
			139, 149, 151, 157, 163, 167, 173, 179, 181, 191, 193, 197, 199,
			211, 223, 227, 229, 233, 239, 241, 251, 257, 263, 269, 271, 277,
			281, 283, 293, 307, 311, 313, 317, 331, 337, 347, 349, 353, 359,
			367, 373, 379, 383, 389, 397, 401, 409, 419, 421, 431, 433, 439,
			443, 449, 457, 461, 463, 467, 479, 487, 491, 499, 503, 509, 521,
			523, 541, 547, 557, 563, 569, 571, 577, 587, 593, 599, 601, 607,
			613, 617, 619, 631, 641, 643, 647, 653, 659, 661, 673, 677, 683,
			691, 701, 709, 719, 727, 733, 739, 743, 751, 757, 761, 769, 773,
			787, 797, 809, 811, 821, 823, 827, 829, 839, 853, 857, 859, 863,
			877, 881, 883, 887, 907, 911, 919, 929, 937, 941, 947, 953, 967,
			971, 977, 983, 991, 997,
		],
		Cc = 67108864 / W[W.length - 1];
	function dc(a, b, c, d) {
		if ('number' == typeof c)
			if (2 > b) nc(a, 1);
			else {
				dc(a, b, d);
				if (!ac(a, b - 1)) {
					d = C.shiftLeft(b - 1);
					var e,
						f,
						g = Math.min(d.t, a.t);
					for (e = 0; e < g; ++e) a[e] |= d[e];
					if (d.t < a.t) {
						f = d.a & a.H;
						for (e = g; e < a.t; ++e) a[e] |= f;
						a.t = a.t;
					} else {
						f = a.a & a.H;
						for (e = g; e < d.t; ++e) a[e] = f | d[e];
						a.t = d.t;
					}
					a.a |= d.a;
					U(a);
				}
				for (V(a) && xc(a, 1, 0); !Dc(a, c); )
					xc(a, 2, 0), Ga(a) > b && T(a, C.shiftLeft(b - 1), a);
			}
		else
			(d = []),
				(e = b & 7),
				(d.length = (b >> 3) + 1),
				c.Ja(d),
				(d[0] = 0 < e ? d[0] & ((1 << e) - 1) : 0),
				ec(a, d, 256);
	}
	function Ec(a, b, c) {
		for (var d = 0, e = 0, f = Math.min(b.t, a.t); d < f; )
			(e += a[d] + b[d]), (c[d++] = e & a.H), (e >>= a.f);
		if (b.t < a.t) {
			for (e += b.a; d < a.t; )
				(e += a[d]), (c[d++] = e & a.H), (e >>= a.f);
			e += a.a;
		} else {
			for (e += a.a; d < b.t; )
				(e += b[d]), (c[d++] = e & a.H), (e >>= a.f);
			e += b.a;
		}
		c.a = 0 > e ? -1 : 0;
		0 < e ? (c[d++] = e) : -1 > e && (c[d++] = a.F + e);
		c.t = d;
		U(c);
	}
	function wc(a, b) {
		a[a.t] = a.L(0, b - 1, a, 0, 0, a.t);
		++a.t;
		U(a);
	}
	function xc(a, b, c) {
		if (0 != b) {
			for (; a.t <= c; ) a[a.t++] = 0;
			for (a[c] += b; a[c] >= a.F; )
				(a[c] -= a.F), ++c >= a.t && (a[a.t++] = 0), ++a[c];
		}
	}
	function Fc(a, b) {
		var c = a.B(C),
			d;
		a: {
			for (d = 0; d < c.t; ++d)
				if (0 != c[d]) {
					var e;
					e = c[d];
					if (0 == e) e = -1;
					else {
						var f = 0;
						e & 65535 || ((e >>= 16), (f += 16));
						e & 255 || ((e >>= 8), (f += 8));
						e & 15 || ((e >>= 4), (f += 4));
						e & 3 || ((e >>= 2), (f += 2));
						e & 1 || ++f;
						e = f;
					}
					d = d * c.f + e;
					break a;
				}
			d = 0 > c.a ? c.t * c.f : -1;
		}
		if (0 >= d) return !1;
		f = d;
		e = R();
		0 > f ? yc(c, -f, e) : zc(c, f, e);
		b = (b + 1) >> 1;
		b > W.length && (b = W.length);
		for (var f = R(), g = 0; g < b; ++g) {
			nc(f, W[Math.floor(Math.random() * W.length)]);
			var l = Ka(f, e, a);
			if (0 != S(l, C) && 0 != S(l, c)) {
				for (var h = 1; h++ < d && 0 != S(l, c); )
					if (((l = Ia(l, 2, a)), 0 == S(l, C))) return !1;
				if (0 != S(l, c)) return !1;
			}
		}
		return !0;
	}
	k = m.prototype;
	k.clone = function () {
		var a = R();
		this.copyTo(a);
		return a;
	};
	function aa(a) {
		if (0 > a.a) {
			if (1 == a.t) return a[0] - a.F;
			if (0 == a.t) return -1;
		} else {
			if (1 == a.t) return a[0];
			if (0 == a.t) return 0;
		}
		return ((a[1] & ((1 << (32 - a.f)) - 1)) << a.f) | a[0];
	}
	function $b(a) {
		return 0 > a.a ? -1 : 0 >= a.t || (1 == a.t && 0 >= a[0]) ? 0 : 1;
	}
	k.w = function (a) {
		return 0 == S(this, a);
	};
	k.min = function (a) {
		return 0 > S(this, a) ? this : a;
	};
	k.max = function (a) {
		return 0 < S(this, a) ? this : a;
	};
	k.shiftLeft = function (a) {
		var b = R();
		0 > a ? zc(this, -a, b) : yc(this, a, b);
		return b;
	};
	function ac(a, b) {
		var c = Math.floor(b / a.f);
		return c >= a.t ? 0 != a.a : !!(a[c] & (1 << b % a.f));
	}
	k.add = function (a) {
		var b = R();
		Ec(this, a, b);
		return b;
	};
	k.B = function (a) {
		var b = R();
		T(this, a, b);
		return b;
	};
	k.multiply = function (a) {
		var b = R();
		rc(this, a, b);
		return b;
	};
	k.nb = function (a) {
		var b = R();
		qc(this, a, b, null);
		return b;
	};
	function La(a, b) {
		var c = R();
		qc(a, b, null, c);
		return c;
	}
	function Ka(a, b, c) {
		var d = Ga(b),
			e,
			f = mc(1);
		if (0 >= d) return f;
		e = 18 > d ? 1 : 48 > d ? 3 : 144 > d ? 4 : 768 > d ? 5 : 6;
		c = 8 > d ? new pc(c) : V(c) ? new bc(c) : new tc(c);
		var g = [],
			l = 3,
			h = e - 1,
			p = (1 << e) - 1;
		g[1] = c.ya(a);
		if (1 < e)
			for (d = R(), c.T(g[1], d); l <= p; )
				(g[l] = R()), c.ra(d, g[l - 2], g[l]), (l += 2);
		for (var x = b.t - 1, z, F = !0, q = R(), d = oc(b[x]) - 1; 0 <= x; ) {
			d >= h
				? (z = (b[x] >> (d - h)) & p)
				: ((z = (b[x] & ((1 << (d + 1)) - 1)) << (h - d)),
				  0 < x && (z |= b[x - 1] >> (a.f + d - h)));
			for (l = e; !(z & 1); ) (z >>= 1), --l;
			0 > (d -= l) && ((d += a.f), --x);
			if (F) g[z].copyTo(f), (F = !1);
			else {
				for (; 1 < l; ) c.T(f, q), c.T(q, f), (l -= 2);
				0 < l ? c.T(f, q) : ((l = f), (f = q), (q = l));
				c.ra(q, g[z], f);
			}
			for (; 0 <= x && !(b[x] & (1 << d)); )
				c.T(f, q),
					(l = f),
					(f = q),
					(q = l),
					0 > --d && ((d = a.f - 1), --x);
		}
		return c.Aa(f);
	}
	function Yb(a, b) {
		var c = V(b);
		if ((V(a) && c) || 0 == $b(b)) return Q;
		for (
			var d = b.clone(),
				e = a.clone(),
				f = mc(1),
				g = mc(0),
				l = mc(0),
				h = mc(1);
			0 != $b(d);

		) {
			for (; V(d); )
				zc(d, 1, d),
					c
						? ((V(f) && V(g)) || (Ec(f, a, f), T(g, b, g)),
						  zc(f, 1, f))
						: V(g) || T(g, b, g),
					zc(g, 1, g);
			for (; V(e); )
				zc(e, 1, e),
					c
						? ((V(l) && V(h)) || (Ec(l, a, l), T(h, b, h)),
						  zc(l, 1, l))
						: V(h) || T(h, b, h),
					zc(h, 1, h);
			0 <= S(d, e)
				? (T(d, e, d), c && T(f, l, f), T(g, h, g))
				: (T(e, d, e), c && T(l, f, l), T(h, g, h));
		}
		if (0 != S(e, C)) return Q;
		if (0 <= S(h, b)) return h.B(b);
		for (; 0 > $b(h); ) Ec(h, b, h);
		return h;
	}
	k.pow = function (a) {
		return this.exp(a, new Ac());
	};
	function Dc(a, b) {
		var c,
			d = a.abs();
		if (1 == d.t && d[0] <= W[W.length - 1]) {
			for (c = 0; c < W.length; ++c) if (d[0] == W[c]) return !0;
			return !1;
		}
		if (V(d)) return !1;
		for (c = 1; c < W.length; ) {
			for (var e = W[c], f = c + 1; f < W.length && e < Cc; ) e *= W[f++];
			if (0 >= e) e = 0;
			else {
				var g = d.F % e,
					l = 0 > d.a ? e - 1 : 0;
				if (0 < d.t)
					if (g)
						for (var h = d.t - 1; 0 <= h; --h)
							l = (g * l + d[h]) % e;
					else l = d[0] % e;
				e = l;
			}
			for (; c < f; ) if (!(e % W[c++])) return !1;
		}
		return Fc(d, b);
	}
	k.U = function () {
		var a = R();
		sc(this, a);
		return a;
	};
	function Gc() {
		this.za = this.ia = 0;
		this.I = [];
	}
	Gc.prototype.next = function () {
		var a;
		this.ia = (this.ia + 1) & 255;
		this.za = (this.za + this.I[this.ia]) & 255;
		a = this.I[this.ia];
		this.I[this.ia] = this.I[this.za];
		this.I[this.za] = a;
		return this.I[(a + this.I[this.ia]) & 255];
	};
	var Hc = 256;
	var Ja = 'EMPTY_SM2_PUBLIC_KEY';
	var Ic, X, Y;
	function Jc() {
		var a = new Date().getTime();
		X[Y++] ^= a & 255;
		X[Y++] ^= (a >> 8) & 255;
		X[Y++] ^= (a >> 16) & 255;
		X[Y++] ^= (a >> 24) & 255;
		Y >= Hc && (Y -= Hc);
	}
	if (null == X) {
		X = [];
		Y = 0;
		var Kc;
		if (window.crypto && window.crypto.getRandomValues) {
			var Lc = new Uint8Array(32);
			window.crypto.getRandomValues(Lc);
			for (Kc = 0; 32 > Kc; ++Kc) X[Y++] = Lc[Kc];
		}
		if (
			'Netscape' == navigator.appName &&
			'5' > navigator.appVersion &&
			window.crypto
		) {
			var Mc = window.crypto.random(32);
			for (Kc = 0; Kc < Mc.length; ++Kc) X[Y++] = Mc.charCodeAt(Kc) & 255;
		}
		for (; Y < Hc; )
			(Kc = Math.floor(65536 * Math.random())),
				(X[Y++] = Kc >>> 8),
				(X[Y++] = Kc & 255);
		Y = 0;
		Jc();
	}
	function Ub(a) {
		var b;
		for (b = 0; b < a.length; ++b) {
			var c = b,
				d;
			if (null == Ic) {
				Jc();
				Ic = new Gc();
				var e,
					f,
					g = Ic,
					l = X;
				for (f = 0; 256 > f; ++f) g.I[f] = f;
				for (f = e = 0; 256 > f; ++f)
					(e = (e + g.I[f] + l[f % l.length]) & 255),
						(d = g.I[f]),
						(g.I[f] = g.I[e]),
						(g.I[e] = d);
				g.ia = 0;
				for (Y = g.za = 0; Y < X.length; ++Y) X[Y] = 0;
				Y = 0;
			}
			d = Ic.next();
			a[c] = d;
		}
	}
	function Ha() {}
	Ha.prototype.Ja = Ub;
	function Fa() {
		this.n = null;
		this.e = 0;
		this.q = this.p = this.d = null;
	}
	var Aa = 0;
	function za(a, b) {
		var c = (a & 65535) + (b & 65535);
		return (((a >> 16) + (b >> 16) + (c >> 16)) << 16) | (c & 65535);
	}
	function Ra(a) {
		var b = [];
		if (33 == a.length) for (var c = 1; 33 > c; c++) b.push(a[c]);
		else if (32 == a.length) for (c = 0; 32 > c; c++) b.push(a[c]);
		return b;
	}
	function Z(a, b) {
		b &= 31;
		return (a << b) | (a >>> (32 - b));
	}
	function Nc(a, b) {
		var c = (a & 65535) + (b & 65535);
		return (
			(((((a & 4294901760) >>> 16) +
				((b & 4294901760) >>> 16) +
				(c >>> 16)) &
				65535) <<
				16) ^
			(c & 65535)
		);
	}
	function Oc(a, b, c) {
		for (var d = [], e = 0; 16 > e; e++) {
			for (var f = 0, g = 0; 4 > g; g++) f = (f << 8) ^ c[(e << 2) + g];
			d[e] = f;
		}
		c = [0];
		e = [0];
		for (f = 0; 16 > f; f++) c[f] = d[f];
		for (f = 16; 68 > f; f++)
			(d = c[f - 16] ^ c[f - 9] ^ Z(c[f - 3], 15)),
				(c[f] = d ^ Z(d, 15) ^ Z(d, 23) ^ Z(c[f - 13], 7) ^ c[f - 6]);
		for (d = 0; 64 > d; d++) e[d] = c[d] ^ c[d + 4];
		var l,
			h,
			p,
			x,
			z,
			F,
			q,
			d = [0, 0, 0, 0, 0, 0, 0, 0],
			f = a[0],
			g = a[1];
		l = a[2];
		h = a[3];
		p = a[4];
		x = a[5];
		z = a[6];
		F = a[7];
		for (q = 0; 64 > q; q++) {
			var w = Z(
					Nc(
						Nc(Z(f, 12), p),
						Z(
							0 <= q && 15 >= q
								? 2043430169
								: 16 <= q && 63 >= q
								? 2055708042
								: 0,
							q
						)
					),
					7
				),
				B = w ^ Z(f, 12),
				y;
			0 <= q && 15 >= q
				? (y = f ^ g ^ l)
				: 16 <= q && 63 >= q
				? (y = (f & g) | (f & l) | (g & l))
				: (y = { Pb: -1 });
			B = Nc(Nc(Nc(y, h), B), e[q]);
			0 <= q && 15 >= q
				? (h = p ^ x ^ z)
				: 16 <= q && 63 >= q
				? (h = (p & x) | (~p & z))
				: (h = { Pb: -1 });
			w = Nc(Nc(Nc(h, F), w), c[q]);
			h = l;
			l = Z(g, 9);
			g = f;
			f = B;
			F = z;
			z = Z(x, 19);
			x = p;
			p = w ^ Z(w, 9) ^ Z(w, 17);
		}
		d[0] = f ^ a[0];
		d[1] = g ^ a[1];
		d[2] = l ^ a[2];
		d[3] = h ^ a[3];
		d[4] = p ^ a[4];
		d[5] = x ^ a[5];
		d[6] = z ^ a[6];
		d[7] = F ^ a[7];
		a = [0, 0, 0, 0, 0, 0, 0, 0];
		for (c = 0; 8 > c; c++) a[c] = d[c];
		return { D: a, vb: b + 64 };
	}
	function Wa() {
		var a = [0, 0, 0, 0, 0, 0, 0, 0];
		a[0] = 1937774191;
		a[1] = 1226093241;
		a[2] = 388252375;
		a[3] = -628488704;
		a[4] = -1452330820;
		a[5] = 372324522;
		a[6] = -477237683;
		a[7] = -1325724082;
		for (var b = [], c = 0; 64 > c; c++) b[c] = 0;
		return { D: a, P: 0, A: b, S: 0 };
	}
	function Xa(a, b, c, d, e, f) {
		var g, l;
		l = 0;
		if (64 >= d + f) {
			for (g = d; g < d + f; g++) c[g] = e[g - d];
			return { D: a, P: b, A: c, S: d + f };
		}
		for (g = d; 64 > g; g++) c[g] = e[g - d];
		d = 64 - d;
		a = Oc(a, b, c);
		if (0 < f - d) {
			l = (f - d) >>> 6;
			for (b = 0; b < l; b++) {
				g = [];
				for (var h = 0; 64 > h; h++) g.push(e[d + 64 * b + h]);
				a = Oc(a.D, a.vb, g);
			}
			b = (f - d) & 63;
			for (f = 0; f < b; f++) c[f] = e[d + 64 * l + f];
			l = b;
		}
		return { D: a.D, P: a.vb, A: c, S: l };
	}
	function Ya(a, b, c, d, e) {
		retUpdate = Xa(a, b, c, d, e, 0);
		b = retUpdate.A;
		c = retUpdate.S;
		d = retUpdate.P + retUpdate.S;
		a = [];
		if (56 > c) {
			for (e = 0; 64 > e; e++) a[e] = 0;
			for (e = 0; e < c; e++) a[e] = b[e];
			a[e] = 128;
			b = d << 3;
			for (c = 7; 0 <= c; c--) (a[56 + c] = b & 255), (b >>>= 8);
		} else if (64 >= c) {
			for (e = 0; 128 > e; e++) a[e] = 0;
			for (e = 0; e < c; e++) a[e] = b[e];
			a[e] = 128;
			b = d << 3;
			for (c = 7; 0 <= c; c--) (a[120 + c] = b & 255), (b >>>= 8);
		}
		b = Oc(retUpdate.D, retUpdate.P, a);
		if (64 < a.length) {
			d = [];
			for (c = 0; 64 > c; c++) d[c] = a[64 + c];
			b = Oc(b.D, b.P, d);
		}
		a = [0, 0, 0, 0, 0, 0, 0, 0];
		for (c = 0; 8 > c; c++) a[c] = b.D[c];
		d = [
			0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
			0, 0, 0, 0, 0, 0, 0, 0, 0,
		];
		for (b = 0; 8 > b; b++)
			for (e = a[b], c = 3; 0 <= c; c--)
				(d[4 * b + c] = e & 255), (e >>>= 8);
		return d;
	}
	function Pc(a) {
		var b;
		switch (a) {
			case '0':
				b = 0;
				break;
			case '1':
				b = 1;
				break;
			case '2':
				b = 2;
				break;
			case '3':
				b = 3;
				break;
			case '4':
				b = 4;
				break;
			case '5':
				b = 5;
				break;
			case '6':
				b = 6;
				break;
			case '7':
				b = 7;
				break;
			case '8':
				b = 8;
				break;
			case '9':
				b = 9;
				break;
			case 'a':
			case 'A':
				b = 10;
				break;
			case 'b':
			case 'B':
				b = 11;
				break;
			case 'c':
			case 'C':
				b = 12;
				break;
			case 'd':
			case 'D':
				b = 13;
				break;
			case 'e':
			case 'E':
				b = 14;
				break;
			case 'f':
			case 'F':
				b = 15;
		}
		return b;
	}
	function Oa(a) {
		var b = [];
		a.length & 1 && (a = '0' + a);
		for (var c = 0; c < a.length; c += 2)
			b.push((Pc(a.charAt(c)) << 4) ^ Pc(a.charAt(c + 1)));
		return b;
	}
	function Da(a) {
		for (var b = [], c = 0; c < a.length; c++) {
			var d;
			var e = a.charCodeAt(c);
			d = [];
			if (!(0 < e >>> 31))
				if (0 <= e && 127 >= e) d.push(e);
				else if (128 <= e && 2047 >= e) {
					var f = e & 255,
						g = 128 ^ ((f << 2) >>> 2);
					d.push(192 ^ ((e >>> 8) << 2) ^ (f >>> 6));
					d.push(g);
				} else if (2048 <= e && 65535 >= e) {
					var l = e >>> 8,
						f = e & 255,
						g = 128 ^ ((l & 15) << 2) ^ (f >>> 6),
						e = 128 ^ (f & 63);
					d.push(224 ^ (l >>> 4));
					d.push(g);
					d.push(e);
				} else if (65536 <= e && 1114111 >= e) {
					var h = e >>> 16,
						l = (e >>> 8) & 255,
						f = e & 255,
						g = 128 ^ ((h & 3) << 4) ^ (l >>> 4),
						e = 128 ^ ((l & 15) << 2) ^ (f >>> 6),
						f = 128 ^ (f & 63);
					d.push(240 ^ (h >>> 2));
					d.push(g);
					d.push(e);
					d.push(f);
				}
			b = b.concat(d);
		}
		return b;
	}
	var Qc = [
		214, 144, 233, 254, 204, 225, 61, 183, 22, 182, 20, 194, 40, 251, 44, 5,
		43, 103, 154, 118, 42, 190, 4, 195, 170, 68, 19, 38, 73, 134, 6, 153,
		156, 66, 80, 244, 145, 239, 152, 122, 51, 84, 11, 67, 237, 207, 172, 98,
		228, 179, 28, 169, 201, 8, 232, 149, 128, 223, 148, 250, 117, 143, 63,
		166, 71, 7, 167, 252, 243, 115, 23, 186, 131, 89, 60, 25, 230, 133, 79,
		168, 104, 107, 129, 178, 113, 100, 218, 139, 248, 235, 15, 75, 112, 86,
		157, 53, 30, 36, 14, 94, 99, 88, 209, 162, 37, 34, 124, 59, 1, 33, 120,
		135, 212, 0, 70, 87, 159, 211, 39, 82, 76, 54, 2, 231, 160, 196, 200,
		158, 234, 191, 138, 210, 64, 199, 56, 181, 163, 247, 242, 206, 249, 97,
		21, 161, 224, 174, 93, 164, 155, 52, 26, 85, 173, 147, 50, 48, 245, 140,
		177, 227, 29, 246, 226, 46, 130, 102, 202, 96, 192, 41, 35, 171, 13, 83,
		78, 111, 213, 219, 55, 69, 222, 253, 142, 47, 3, 255, 106, 114, 109,
		108, 91, 81, 141, 27, 175, 146, 187, 221, 188, 127, 17, 217, 92, 65, 31,
		16, 90, 216, 10, 193, 49, 136, 165, 205, 123, 189, 45, 116, 208, 18,
		184, 229, 180, 176, 137, 105, 151, 74, 12, 150, 119, 126, 101, 185, 241,
		9, 197, 110, 198, 132, 24, 240, 125, 236, 58, 220, 77, 32, 121, 238, 95,
		62, 215, 203, 57, 72,
	];
	function Rc(a) {
		var b = 0,
			c,
			d = [0, 0, 0, 0],
			e = [0, 0, 0, 0];
		for (c = 3; 0 <= c; c--) (d[c] = a & 255), (a >>>= 8);
		e[0] = Qc[d[0]];
		e[1] = Qc[d[1]];
		e[2] = Qc[d[2]];
		e[3] = Qc[d[3]];
		for (a = 0; 4 > a; a++) b = (b << 8) + e[a];
		return b;
	}
	function Z(a, b) {
		b &= 31;
		return (a << b) | (a >>> (32 - b));
	}
	function Sc(a) {
		return a ^ Z(a, 2) ^ Z(a, 10) ^ Z(a, 18) ^ Z(a, 24);
	}
	function Tc(a) {
		return a ^ Z(a, 13) ^ Z(a, 23);
	}
	function Uc(a) {
		return Tc(Rc(a));
	}
	function Vc(a) {
		return Sc(Rc(a));
	}
	function Wc(a, b) {
		return a[0] ^ Vc(a[1] ^ a[2] ^ a[3] ^ b);
	}
	function Xc(a, b) {
		var c = [0, 0, 0, 0],
			d,
			e = [
				0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
				0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
			];
		e[0] = a[0];
		e[1] = a[1];
		e[2] = a[2];
		e[3] = a[3];
		for (d = 0; 32 > d; d++) {
			var f = [0, 0, 0, 0];
			f[0] = e[d];
			f[1] = e[d + 1];
			f[2] = e[d + 2];
			f[3] = e[d + 3];
			e[d + 4] = Wc(f, b[d]);
		}
		c[0] = e[35];
		c[1] = e[34];
		c[2] = e[33];
		c[3] = e[32];
		return c;
	}
	function oa(a) {
		var b = [
				0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
				0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
			],
			c = [2746333894, 1453994832, 1736282519, 2993693404],
			d = [
				462357, 472066609, 943670861, 1415275113, 1886879365,
				2358483617, 2830087869, 3301692121, 3773296373, 4228057617,
				404694573, 876298825, 1347903077, 1819507329, 2291111581,
				2762715833, 3234320085, 3705924337, 4177462797, 337322537,
				808926789, 1280531041, 1752135293, 2223739545, 2695343797,
				3166948049, 3638552301, 4110090761, 269950501, 741554753,
				1213159005, 1684763257,
			],
			e = [
				0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
				0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
			];
		e[0] = a[0] ^ c[0];
		e[1] = a[1] ^ c[1];
		e[2] = a[2] ^ c[2];
		e[3] = a[3] ^ c[3];
		for (a = 0; 32 > a; a++)
			(b[a] = e[a] ^ Uc(e[a + 1] ^ e[a + 2] ^ e[a + 3] ^ d[a])),
				(e[a + 4] = b[a]);
		return b;
	}
	function Yc(a) {
		var b,
			c = [
				0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
				0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
			];
		for (b = 0; 16 > b; b++) {
			var d;
			d = a[b];
			c[b] = a[31 - b];
			c[31 - b] = d;
		}
		return c;
	}
	function pa(a, b, c, d) {
		var e, f;
		f = [0, 0, 0, 0];
		e = [0, 0, 0, 0];
		if (d) {
			for (e = 0; 4 > e; e++) f[e] = a[e] ^ c[e];
			a = Xc(f, b);
			for (e = 0; 4 > e; e++) c[e] = a[e];
		} else {
			for (f = 0; 4 > f; f++) e[f] = a[f];
			a = Xc(a, b);
			for (f = 0; 4 > f; f++) a[f] ^= c[f];
			for (f = 0; 4 > f; f++) c[f] = e[f];
		}
		return { D: c, A: a };
	}
	function A(a) {
		var b = [0, 0, 0, 0];
		if (0 == a.length) return null;
		if (16 > a.length)
			for (var c = 16 - a.length, d = 0; d < c; d++) a.push(c);
		for (d = 0; 4 > d; d++)
			b[d] =
				((a[4 * d + 0] << 24) |
					(a[4 * d + 1] << 16) |
					(a[4 * d + 2] << 8) |
					a[4 * d + 3]) >>>
				0;
		return b;
	}
	function ra(a) {
		if (0 == a.length) return null;
		for (var b = [], c = 0; c < a.length; c++)
			b.push((a[c] >>> 24) & 255),
				b.push((a[c] >>> 16) & 255),
				b.push((a[c] >>> 8) & 255),
				b.push(a[c] & 255);
		return b;
	}
	function Ca(a, b, c) {
		var d = 0;
		if (0 == a.length || 16 != b.length || 16 != c.length)
			return { result: null, errorCode: -1 };
		b = oa(A(b));
		var e = A(c);
		for (c = []; d < a.length; d += 16) {
			var f = pa(A(a.slice(d, d + 16)), b, e, 1);
			c = c.concat(f.A);
		}
		a.length & 15 ||
			((a = pa([269488144, 269488144, 269488144, 269488144], b, e, 1)),
			(c = c.concat(a.A)));
		return { result: ra(c), errorCode: 0 };
	}
	function bb(a, b, c) {
		var d = 0;
		if (0 == a.length || 16 != b.length || 16 != c.length)
			return { result: null, errorCode: -1 };
		b = Yc(oa(A(b)));
		c = A(c);
		for (var e = []; d < a.length; d += 16)
			var f = pa(A(a.slice(d, d + 16)), b, c, 0), e = e.concat(f.A);
		e = ra(e);
		return 0 == e.length ||
			0 == e[e.length - 1] ||
			16 < e[e.length - 1] ||
			e[e.length - 1] >= e.length
			? { result: null, errorCode: -2 }
			: { result: e.slice(0, e.length - e[e.length - 1]), errorCode: 0 };
	}
	Qc = [
		214, 144, 233, 254, 204, 225, 61, 183, 22, 182, 20, 194, 40, 251, 44, 5,
		43, 103, 154, 118, 42, 190, 4, 195, 170, 68, 19, 38, 73, 134, 6, 153,
		156, 66, 80, 244, 145, 239, 152, 122, 51, 84, 11, 67, 237, 207, 172, 98,
		228, 179, 28, 169, 201, 8, 232, 149, 128, 223, 148, 250, 117, 143, 63,
		166, 71, 7, 167, 252, 243, 115, 23, 186, 131, 89, 60, 25, 230, 133, 79,
		168, 104, 107, 129, 178, 113, 100, 218, 139, 248, 235, 15, 75, 112, 86,
		157, 53, 30, 36, 14, 94, 99, 88, 209, 162, 37, 34, 124, 59, 1, 33, 120,
		135, 212, 0, 70, 87, 159, 211, 39, 82, 76, 54, 2, 231, 160, 196, 200,
		158, 234, 191, 138, 210, 64, 199, 56, 181, 163, 247, 242, 206, 249, 97,
		21, 161, 224, 174, 93, 164, 155, 52, 26, 85, 173, 147, 50, 48, 245, 140,
		177, 227, 29, 246, 226, 46, 130, 102, 202, 96, 192, 41, 35, 171, 13, 83,
		78, 111, 213, 219, 55, 69, 222, 253, 142, 47, 3, 255, 106, 114, 109,
		108, 91, 81, 141, 27, 175, 146, 187, 221, 188, 127, 17, 217, 92, 65, 31,
		16, 90, 216, 10, 193, 49, 136, 165, 205, 123, 189, 45, 116, 208, 18,
		184, 229, 180, 176, 137, 105, 151, 74, 12, 150, 119, 126, 101, 185, 241,
		9, 197, 110, 198, 132, 24, 240, 125, 236, 58, 220, 77, 32, 121, 238, 95,
		62, 215, 203, 57, 72,
	];
	function Rc(a) {
		var b = 0,
			c,
			d = [0, 0, 0, 0],
			e = [0, 0, 0, 0];
		for (c = 3; 0 <= c; c--) {
			var f = a;
			d[c] = f & 255;
			a >>>= 8;
		}
		e[0] = Qc[d[0]];
		e[1] = Qc[d[1]];
		e[2] = Qc[d[2]];
		e[3] = Qc[d[3]];
		for (a = 0; 4 > a; a++) b = (b << 8) + e[a];
		return b;
	}
	function Z(a, b) {
		b &= 31;
		return (a << b) | (a >>> (32 - b));
	}
	function Sc(a) {
		return (a = a ^ Z(a, 2) ^ Z(a, 10) ^ Z(a, 18) ^ Z(a, 24));
	}
	function Tc(a) {
		return (a = a ^ Z(a, 13) ^ Z(a, 23));
	}
	function Uc(a) {
		a = Rc(a);
		return (a = Tc(a));
	}
	function Vc(a) {
		a = Rc(a);
		return (a = Sc(a));
	}
	function Wc(a, b) {
		var c;
		c = Vc(a[1] ^ a[2] ^ a[3] ^ b);
		return (c = a[0] ^ c);
	}
	function Xc(a, b, c, d) {
		var e = [0, 0, 0, 0],
			f = [
				0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
				0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
			];
		c
			? ((c = A(d, !1)),
			  (f[0] = a[0] ^ c[0]),
			  (f[1] = a[1] ^ c[1]),
			  (f[2] = a[2] ^ c[2]),
			  (f[3] = a[3] ^ c[3]))
			: ((f[0] = a[0]), (f[1] = a[1]), (f[2] = a[2]), (f[3] = a[3]));
		for (a = 0; 32 > a; a++)
			(c = [0, 0, 0, 0]),
				(c[0] = f[a]),
				(c[1] = f[a + 1]),
				(c[2] = f[a + 2]),
				(c[3] = f[a + 3]),
				(f[a + 4] = Wc(c, b[a]));
		e[0] = f[35];
		e[1] = f[34];
		e[2] = f[33];
		e[3] = f[32];
		return e;
	}
	function oa(a) {
		var b = [
				0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
				0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
			],
			c = [2746333894, 1453994832, 1736282519, 2993693404],
			d = [
				462357, 472066609, 943670861, 1415275113, 1886879365,
				2358483617, 2830087869, 3301692121, 3773296373, 4228057617,
				404694573, 876298825, 1347903077, 1819507329, 2291111581,
				2762715833, 3234320085, 3705924337, 4177462797, 337322537,
				808926789, 1280531041, 1752135293, 2223739545, 2695343797,
				3166948049, 3638552301, 4110090761, 269950501, 741554753,
				1213159005, 1684763257,
			],
			e = [
				0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
				0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
			];
		e[0] = a[0] ^ c[0];
		e[1] = a[1] ^ c[1];
		e[2] = a[2] ^ c[2];
		e[3] = a[3] ^ c[3];
		for (a = 0; 32 > a; a++)
			(c = Uc(e[a + 1] ^ e[a + 2] ^ e[a + 3] ^ d[a])),
				(b[a] = e[a] ^ c),
				(e[a + 4] = b[a]);
		return b;
	}
	function Yc(a) {
		var b,
			c = [
				0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
				0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
			];
		for (b = 0; 16 > b; b++) {
			var d;
			d = a[b];
			c[b] = a[31 - b];
			c[31 - b] = d;
		}
		return c;
	}
	function pa(a, b, c, d, e, f) {
		var g,
			l = [0, 0, 0, 0];
		g = [0, 0, 0, 0];
		if (d) {
			for (g = 0; 4 > g; g++) l[g] = a[g] ^ c[g];
			a = Xc(l, b, e, f);
			for (g = 0; 4 > g; g++) c[g] = a[g];
		} else {
			for (e = 0; 4 > e; e++) g[e] = a[e];
			a = Xc(a, b);
			for (e = 0; 4 > e; e++) a[e] ^= c[e];
			for (e = 0; 4 > e; e++) c[e] = g[e];
		}
		return { D: c, A: a };
	}
	function A(a, b, c) {
		var d = [0, 0, 0, 0];
		if (0 == a.length) return null;
		if (16 > a.length)
			for (var e = 16 - a.length, f = 0; f < e; f++)
				b ? a.push(e ^ c[a.length]) : a.push(e);
		for (f = 0; 4 > f; f++)
			d[f] =
				((a[4 * f + 0] << 24) |
					(a[4 * f + 1] << 16) |
					(a[4 * f + 2] << 8) |
					a[4 * f + 3]) >>>
				0;
		return d;
	}
	function ra(a) {
		if (0 == a.length) return null;
		for (var b = [], c = 0; c < a.length; c++)
			b.push((a[c] >>> 24) & 255),
				b.push((a[c] >>> 16) & 255),
				b.push((a[c] >>> 8) & 255),
				b.push(a[c] & 255);
		return b;
	}
	function bb(a, b, c) {
		var d = 0;
		if (0 == a.length || 16 != b.length || 16 != c.length)
			return { result: null, errorCode: -1 };
		b = Yc(oa(A(b), !1));
		c = A(c, !1);
		for (var e = []; d < a.length; d += 16)
			var f = pa(A(a.slice(d, d + 16)), b, c, 0), e = e.concat(f.A);
		e = ra(e);
		return 0 == e.length ||
			0 == e[e.length - 1] ||
			16 < e[e.length - 1] ||
			e[e.length - 1] >= e.length
			? { result: null, errorCode: -2 }
			: { result: e.slice(0, e.length - e[e.length - 1]), errorCode: 0 };
	}
})();
