import React, {FC, useEffect, useRef, useState} from 'react';
import {Keyboard, KeyboardAvoidingView} from 'react-native';
import {ViewProps} from '@tarojs/components';
import Taro from '@tarojs/taro';
import {isIos} from '../../utils/platform/index';
import './index.scss';

const onFocusDefault = (position: {x: number; y: number}) => {};
export const FocusContext = React.createContext<(e: {x: number; y: number}) => void>(onFocusDefault);
type Props = {};

/**
 * 组件名称：阻止键盘遮挡组件
 * 组件描述：阻止键盘遮挡组件
 */
const CyKeyboardAvoidingView: FC<Props & ViewProps> = props => {
  const keyboardDidShow = useRef(false);
  const [enabled, setEnabled] = useState(false);
  let windowHeight = 0;
  Taro.getSystemInfo({
    success(res) {
      windowHeight = res.windowHeight;
    },
    fail(err) {
      console.error('获取屏幕信息失败', err);
    },
  });

  useEffect(() => {
    const showSubscription = Keyboard.addListener('keyboardDidShow', e => {
      console.log('keyboardDidShow');
      keyboardDidShow.current = true;
    });
    const hideSubscription = Keyboard.addListener('keyboardDidHide', e => {
      console.log('keyboardDidHide');
      keyboardDidShow.current = false;
    });

    return () => {
      console.log('remove=');
      showSubscription.remove();
      hideSubscription.remove();
    };
  }, []);
  const onFocus = (e: {x: number; y: number}) => {
    console.log('onFocus', e, windowHeight / 2);
    //如果键盘被收起来了，那么就根据情况判断，如果键盘是拉起来的，那么就保持不变
    if (isIos()) {
      if (!keyboardDidShow.current) {
        setEnabled(e.y > windowHeight / 2);
      } else {
      }
    }
  };
  console.log('enabled=', enabled);
  return (
    <FocusContext.Provider value={onFocus}>
      <KeyboardAvoidingView
        // className='comp-cy-keyboard-avoiding-view'
        // {...props}
        behavior='position'
        enabled={enabled}
        // keyboardVerticalOffset={100}
      >
        {props.children}
      </KeyboardAvoidingView>
    </FocusContext.Provider>
  );
};

export default CyKeyboardAvoidingView;
