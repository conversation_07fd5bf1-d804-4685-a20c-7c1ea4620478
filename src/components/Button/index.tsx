import {Button, ButtonProps, ITouchEvent} from '@tarojs/components';
import styles from './index.module.scss';

type Props = {};

const CutomeButton = (props: Props & ButtonProps) => {
  const onClick = (e: ITouchEvent) => {
    if (props.disabled) return;
    return props.onClick && props.onClick(e);
  };
  return (
    <Button {...props} className={`${styles.cus_button} ${props.className}`} style={props.style} onClick={onClick}>
      {props.children}
    </Button>
  );
};

export default CutomeButton;
