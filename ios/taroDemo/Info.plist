<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>CFBundleDevelopmentRegion</key>
	<array>
		<string>en</string>
	</array>
	<key>CFBundleDisplayName</key>
	<string>长银五八跨平台应用</string>
	<key>CFBundleExecutable</key>
	<string>$(EXECUTABLE_NAME)</string>
	<key>CFBundleIdentifier</key>
	<string>$(PRODUCT_BUNDLE_IDENTIFIER)</string>
	<key>CFBundleInfoDictionaryVersion</key>
	<string>6.0</string>
	<key>CFBundleLocalizations</key>
	<array>
		<string>zh</string>
	</array>
	<key>CFBundleName</key>
	<string>$(PRODUCT_NAME)</string>
	<key>CFBundlePackageType</key>
	<string>APPL</string>
	<key>CFBundleShortVersionString</key>
	<string>$(MARKETING_VERSION)</string>
	<key>CFBundleSignature</key>
	<string>????</string>
	<key>CFBundleVersion</key>
	<string>$(CURRENT_PROJECT_VERSION)</string>
	<key>ITSAppUsesNonExemptEncryption</key>
	<false/>
	<key>LSRequiresIPhoneOS</key>
	<true/>
	<key>NSAppTransportSecurity</key>
	<dict>
		<key>NSAllowsArbitraryLoads</key>
		<true/>
	</dict>
	<key>NSCameraUsageDescription</key>
	<string>上传头像，图片，没有你允许不会被分享</string>
	<key>NSLocationAlwaysAndWhenInUseUsageDescription</key>
	<string>根据您的城市位置，进行考勤及获取周边地点</string>
	<key>NSLocationAlwaysUsageDescription</key>
	<string>根据您的城市位置，进行考勤及获取周边地点</string>
	<key>NSLocationWhenInUseUsageDescription</key>
	<string>根据您的城市位置，进行考勤及获取周边地点</string>
	<key>NSMicrophoneUsageDescription</key>
	<string>需要使用您的麦克风权限进行视频录制</string>
	<key>NSMotionUsageDescription</key>
	<string>允许访问设备的加速器</string>
	<key>NSPhotoLibraryAddUsageDescription</key>
	<string>上传头像，图片，没有你允许不会被分享</string>
	<key>NSPhotoLibraryUsageDescription</key>
	<string>上传头像，图片，没有你允许不会被分享</string>
	<key>UILaunchStoryboardName</key>
	<string>LaunchScreen</string>
	<key>UIRequiredDeviceCapabilities</key>
	<array>
		<string>armv7</string>
	</array>
	<key>UISupportedInterfaceOrientations</key>
	<array>
		<string>UIInterfaceOrientationLandscapeLeft</string>
		<string>UIInterfaceOrientationLandscapeRight</string>
		<string>UIInterfaceOrientationPortrait</string>
	</array>
	<key>UISupportedInterfaceOrientations~ipad</key>
	<array>
		<string>UIInterfaceOrientationPortrait</string>
	</array>
	<key>UIUserInterfaceStyle</key>
	<string>Light</string>
	<key>UIViewControllerBasedStatusBarAppearance</key>
	<false/>
</dict>
</plist>
