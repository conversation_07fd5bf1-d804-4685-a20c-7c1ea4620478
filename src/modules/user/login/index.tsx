import React, {use<PERSON>allback, useContext, useEffect, useMemo, useRef, useState} from 'react';
import {CyButton, CyCheckbox, CyCheckboxRef, CyModal, CyPopup, CyTheme} from 'hncy58-taro-components';
import {formatPhoneInput} from 'hncy58-utils';
import {BaseEventOrig, Button, Icon, Image, Text, View} from '@tarojs/components';
import Cy<PERSON>aptch<PERSON>, {CyCaptchaT} from '~components/CyCaptcha';
import PageWrap from '~components/PageWrap';
import FastIcon from '~icons/<EMAIL>';
import RecycleIcon from '~icons/<EMAIL>'
import regularIcon from '~icons/<EMAIL>';
import safeIcon from '~icons/<EMAIL>';
import Logo from '~images/login/<EMAIL>';
import useUserStore from '~store/user';
import {hideLoading, router, showLoading, showToast} from '~utils/common';
import Cy<PERSON><PERSON>ractBar, {ContractBarRef} from '../../../components/CyContractBar';
import {getCustomerInfo, getUserCreditInfo, loginByJscode, otherMobileLogin, sendVerifyCodeReq} from '../../../services/user';
import CountdownLinkBtn, { CountDownLinkBtnRef } from './components/CountdownLinkBtn';
import LoginInput from './components/Input';
import styles from './index.module.scss';
import {smsSceneType} from '../../../types/common-types';
import CyContractPreviewHtml, { CyContractPreviewHtmlRef } from '~components/ContractPreviewHtml';
import useMainStore from '~store/main';
import { ContractCode2Info } from '~utils/constant';
import Taro, { useLoad } from '@tarojs/taro';

interface ContractItem {
  code: string;
}

const Contracts = [{code: 'privacy'}, {code: 'E23'}]

/** 简化的手机号校验，仅校验11位数字 */
const PHONE_REGEX =
  /^1\d{10}$/;

export default () => {
  const [phone, setPhone] = useState('');
  const [verifyCode, setVerifyCode] = useState('');
  const [isSendVerifyCode, setIsSendVerifyCode] = useState(false);
  const [isUseWechatPhone, setIsUseWechatPhone] = useState<boolean | undefined>();
  const captchaRef = useRef<CyCaptchaT>(null);
  const [contractChecked, setContractChecked] = useState(false);
  const {setUser, updateUserInfo} = useUserStore();
  const {config} = useMainStore();
  const cyContractBarRef = useRef<ContractBarRef>(null);
  const countdownRef = useRef<CountDownLinkBtnRef>(null);
  const [contractVisible, setContractVisible] = useState(false);
  const [singleContract, setSingleContract] = useState<ContractItem>();
  const checkboxRef = useRef<CyCheckboxRef>(null);
  const previewRef = useRef<CyContractPreviewHtmlRef>(null);
  const [readCountdown, setReadCountdown] = useState(config.forceReadSeconds);
  const [isLoaded, setIsLoaded] = useState(false);
  const redirectUrl = useRef<string>()

  const formatedPhoneNumber = useMemo(() => phone.replace(/\D/g, ''), [phone]);
  const phoneVerifyed = useMemo(() => PHONE_REGEX.test(formatedPhoneNumber), [formatedPhoneNumber]);

  useEffect(() => {
    if (isSendVerifyCode) {
      countdownRef.current?.startCountDown();
    }
  }, [isSendVerifyCode]);

  useEffect(() => {
    let timer: NodeJS.Timeout | null = null;
    if (contractVisible && isLoaded) {
      timer = setInterval(() => {
        if (readCountdown > 0) {
          setReadCountdown(readCountdown => readCountdown - 1);
        } else {
          timer && clearInterval(timer);
        }
      }, 1000);
    }
    if(!contractVisible) {
      timer && clearInterval(timer);
      setReadCountdown(config.forceReadSeconds);
    }
    return () => {
      timer && clearInterval(timer);
    };
  }, [contractVisible, isLoaded])

  useLoad((query) => {
    if(query.redirectUrl) {
      redirectUrl.current = decodeURIComponent(query.redirectUrl);
    }
  });

  /**
   * 处理手机号输入变化
   * @param e 输入事件
   */
  const handlePhoneChange = useCallback((e: BaseEventOrig) => {
    const input = e.detail.value || '';
    const formatted = formatPhoneInput(input);
    setPhone(formatted);
  }, []);

  /**
   * 处理验证码输入变化
   * @param e 输入事件
   */
  const handleVerifyCodeChange = useCallback((e: BaseEventOrig) => {
    setVerifyCode(e.detail?.value || '');
  }, []);

  /**
   * 显示错误提示
   * @param message 错误信息
   */
  const showErrorToast = useCallback((message: string) => {
    showToast(message);
  }, []);

  /**
   * 发送验证码，先校验手机号，再触发人机校验弹窗
   */
  const sendVerifyCode = async () => {
    if (!phoneVerifyed) {
      showErrorToast('请输入正确的手机号码');
      return;
    }
    try {
      handlerCaptchaShow();
    } catch (e: any) {
      showErrorToast(e.message || '发送失败！');
    }
    handlerCaptchaShow();
  };

  /**
   * 登录成功后，获取用户信息并跳转
   */
  const handleLoginSuccess = async () => {
    try {
      await updateUserInfo()
      if(redirectUrl.current && !redirectUrl.current.startsWith('/pages')) {
        router.replace({
          url: redirectUrl.current
        })
      } else {
        router.switchTab({
          url: redirectUrl.current || '/pages/check/index',
        });
      }
    } catch (e) {
      showErrorToast('登录失败');
      console.error('获取用户信息失败:', e);
    }
  };

  /**
   * 微信一键登录回调，获取手机号后自动登录
   * @param res 微信返回的事件对象
   */
  const getPhoneCallBack = async (res: BaseEventOrig) => {
    setContractVisible(false);
    setContractChecked(true);
    checkboxRef.current?.setChecked(true);
    if (res?.detail?.code) {
      try {
        Taro.showLoading({
          title: '登录中...',
          mask: true,
        });
        await loginByJscode({code: res.detail.code});
        await handleLoginSuccess();
      } catch (e) {
        // 登录失败处理
      } finally {
        Taro.hideLoading();
      }
    }
  };

  /**
   * 手机号+验证码登录
   */
  const phoneLogin = async () => {
    if (!phoneVerifyed) {
      showErrorToast('请输入正确的手机号码');
      return;
    }
    if (!verifyCode) {
      showErrorToast('请输入验证码');
      return;
    }
    try {
      Taro.showLoading({
        title: '登录中...',
        mask: true,
      });
      await otherMobileLogin({mobile: formatedPhoneNumber, verificationCode: verifyCode, smsSceneType: smsSceneType.M1007});
      await handleLoginSuccess();
    } catch (e) {
      console.error('登录失败:', e);
    } finally {
      Taro.hideLoading();
    }
  };

  /**
   * 人机校验成功后，发送验证码
   * @param ticket 人机校验票据
   */
  const handlerCaptchaSuccess = async (ticket: string) => {
    try {
      showLoading()
      await sendVerifyCodeReq({mobile: formatedPhoneNumber, smsSceneType: smsSceneType.M1007, ticket: ticket});
      countdownRef.current?.startCountDown();
      setIsSendVerifyCode(true);
    } catch (e: any) {
      showErrorToast(e.message || '发送失败！');
    } finally{
      hideLoading()
    }
  };

  /**
   * 显示人机校验弹窗
   */
  const handlerCaptchaShow = () => {
    captchaRef?.current?.show();
  };

  /**
   * 协议勾选校验，未勾选时弹出协议弹窗，勾选后执行回调
   * @param cb 登录/发送验证码等后续操作
   */
  const signContractAndCb = (cb?: () => void) => {
    if (!contractChecked) {
      setContractVisible(true);
      return;
    }
    cb?.();
  };

  /**
   * 登录按钮点击事件，区分微信一键登录和手机号登录
   */
  const signAndNext = () => {
    // 使用其他手机号登录
    if (!isUseWechatPhone && isUseWechatPhone !== undefined) {
      if (!phoneVerifyed) {
        showErrorToast('请输入正确的手机号码');
        return;
      }
      if (isSendVerifyCode) {
        signContractAndCb(phoneLogin);
      } else {
        signContractAndCb(sendVerifyCode);
      }
    } else {
      // 使用微信登录
      if(isUseWechatPhone === undefined) {
        setIsUseWechatPhone(true);
      }
      signContractAndCb();
    }
  };

  /**
   * 切换到其他手机号登录
   */
  const otherPhoneLogin = () => {
    setIsUseWechatPhone(false);
  };

  /**
   * 协议勾选事件，未阅读时弹窗，已阅读则设置勾选状态
   * @param e 勾选事件对象
   */
  const onContractCheck = (e: any) => {
    if(e?.detail?.value && e?.touched) {
      setContractVisible(true)
      checkboxRef.current?.setChecked(false);
    } else {
      setContractChecked(e?.detail?.value);
    }
  };

  return (
    <PageWrap className={styles.login}>
      <Image className={styles.logo} src={Logo} mode='aspectFit' />
      <View className={styles.companyName}>湖南长银五八消费金融股份有限公司</View>
      <View className={styles.featureBar}>
        <View className={styles.featureItem}>
          <Image className={styles.featureIcon} src={regularIcon}></Image>正规持牌
        </View>
        <View className={styles.featureItem}>
          <Image className={styles.featureIcon} src={FastIcon}></Image>快速审批
        </View>
        <View className={styles.featureItem}>
          <Image className={styles.featureIcon} src={RecycleIcon}></Image>循环使用
        </View>
        <View className={styles.featureItem}>
          <Image className={styles.featureIcon} src={safeIcon}></Image>国企背景安全可靠
        </View>
      </View>
      <CyCaptcha onSuccess={handlerCaptchaSuccess} ref={captchaRef}></CyCaptcha>
      {isUseWechatPhone === undefined || isUseWechatPhone ? (
        <>
          <CyButton
            block
            type='primary'
            {...(contractChecked
              ? {
                  openType: 'getPhoneNumber',
                  onGetPhoneNumber: getPhoneCallBack,
                }
              : {})}
            onClick={signAndNext}
            className={styles.fastLoginButton}>
            手机号一键登录
          </CyButton>
          <CyButton block onClick={otherPhoneLogin} type='default'>
            <Text style={{color: '#999'}}>其他手机号登录</Text>
          </CyButton>
        </>
      ) : (
        <>
          <LoginInput
            type='number'
            value={phone}
            onInput={handlePhoneChange}
            placeholderStyle='font-size: 14px; font-weight: normal;line-height: 40px'
            placeholder='请输入手机号'
            name='mobile'
            inputClassName={styles.loginInput}
            inputWrapClassName={styles.custInputWrap}
            clearAble
          />
          {isSendVerifyCode ? (
            <LoginInput
              type='number'
              value={verifyCode}
              maxlength={6}
              onInput={handleVerifyCodeChange}
              inputClassName={styles.loginInput}
              inputWrapClassName={styles.custInputWrap}
              placeholderStyle='font-size: 14px; font-weight: normal;line-height: 40px'
              placeholder='请输入验证码'
              name='verifyCode'
              suffix={<CountdownLinkBtn ref={countdownRef} onClick={sendVerifyCode} />}
              className={styles.loginInput}
            />
          ) : null}
          <View className='vertical-gap'></View>
          <CyButton block type='primary' onClick={signAndNext}>
            {isSendVerifyCode ? '登录' : '获取验证码'}
          </CyButton>
        </>
      )}
      <View className={styles.contractBar}>
        <CyCheckbox ref={checkboxRef} size={18} onChange={onContractCheck} />
        <View className={styles['contract-bar-text']}>
          <Text>我已阅读并同意</Text>
          {Contracts.map(contract => (
            <Text
              key={contract.code}
              className={styles.contractBarLink}
              onClick={() => {
                setContractVisible(true);
                setSingleContract(contract);
              }}>
              {ContractCode2Info[contract.code]?.name}
            </Text>
          ))}
        </View>
      </View>
      <CyPopup title={(singleContract && ContractCode2Info[singleContract?.code]?.name) || '合同详情'} visible={contractVisible} onClose={() => {
        setContractVisible(false);
          requestAnimationFrame(() => {
            setSingleContract(undefined);
            previewRef.current?.resetScroll();
          });
      }}>
        <View className={styles.contractContent}>
          <CyContractPreviewHtml onLoadEnd={() => {setIsLoaded(true)}} ref={previewRef} singleShowType={singleContract?.code} style={{height: 300}} contracts={Contracts} />
        </View>
        {
          !singleContract ?  (
            <View className={styles.contractPopupFooter}>
              <CyButton
                block
                type='default'
                onClick={() => {
                  setContractVisible(false);
                  setContractChecked(false);
                }}>
                不同意
              </CyButton>
              <CyButton
                block
                className={styles.contractbarConfirmBtn}
                disabled={readCountdown > 0}
                type='primary'
                {...(isUseWechatPhone === true
                  ? {
                      openType: 'getPhoneNumber',
                      onGetPhoneNumber: getPhoneCallBack,
                    }
                  : {})}
                onClick={() => {
                  setContractVisible(false);
                  checkboxRef.current?.setChecked(true);
                  setContractChecked(true);
                  if(isUseWechatPhone === false && phoneVerifyed) {
                    sendVerifyCode() 
                  }
                }}>
                同意{readCountdown > 0 ? `(${readCountdown}s)` : ''}
              </CyButton>
            </View>
          ) : null
        }
      </CyPopup>
    </PageWrap>
  );
};
