import PageContainer from "~components/PageWrap"
import RepaymentAmount from "~modules/repayment/components/RepaymentAmount"
import styles from './index.module.scss'
import { View } from "@tarojs/components"
import CellItem from "~components/CellItem"
import { useLoad } from "@tarojs/taro"
import { useMemo, useState } from "react"
import { RepaymentRecordsItem } from "../repaymentRecords"
import { transDateToChinese } from "~utils/date"
import { STATUS } from "../repaymentRecords"
import { formatToFixedTwo } from "~utils/number"

const RepaymentRecordDetail = () => {
  const [infoItem, setInfoItem] = useState<RepaymentRecordsItem>()
  useLoad(({ info }) => {
    if (info) {
      setInfoItem(JSON.parse(info))
    }
  })
  return (
    <PageContainer className={styles.detailPage}>
      {
        infoItem && (
          <>
            <RepaymentAmount amount={formatToFixedTwo(infoItem?.repayAmt)} amountTitle='还款本金（元）' />
            <View className={styles.page_card}>
              <CellItem title='还款时间' value={transDateToChinese(infoItem?.repaymentDay ?? '')} />
              <CellItem title='还款方式' value={infoItem?.txnTypeName} />
              {infoItem?.cardNoLast && <CellItem title='还款账户' value={`${infoItem.bankCardName} (${infoItem?.cardNoLast})`} />}
            </View>
            <View className={styles.page_card}>
              <CellItem title='还款结果' value={<View className={styles[`${infoItem.repayStatus}`]}>{STATUS[infoItem.repayStatus]}</View>} />
              {infoItem.failReason && <CellItem title='失败原因' value={infoItem.failReason} />}
            </View>
          </>
        )
      }
    </PageContainer>
  )
}

export default RepaymentRecordDetail