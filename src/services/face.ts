import request from '../http/request';

/**
 * 获取腾讯活体识别认证token
 * @param param
 * @returns
 */
export const getTecentFaceBizToken = (data: API.getFaceBizTokenParams, loading = false) => {
  return request<{
    bizToken: string;
    requestId: string;
  }>({
    url: '/api/face/logged/token',
    method: 'POST',
    data,
    loading,
  });
};

/**
 * 未注册用户获取腾讯活体识别认证token
 * @param param
 * @returns
 */
export const getRegisterFaceIdBizToken = (data: API.getFaceBizTokenParams, loading = false) => {
  return request<{
    bizToken: string;
    requestId: string;
  }>({
    url: '/api/face/init',
    method: 'POST',
    data,
    loading,
  });
};


/**
 * 获取是否需要做人脸识别
 */
export const getFaceRequired = (params: API.getFaceRequiredParams, loading = false) => {
  return request<{flag: boolean}>({
    url: '/api/face/required',
    method: 'GET',
    params,
    loading,
  });
};

/**
 * 获取阿里云人脸认证url
 */
export const getAliyunCertifyurl = (params: API.IFaceRequestInfo, loading = false) => {
  return request<API.IFaceResponseInfo>({
    url: '/api/face/finance/certifyurl/oldCustomer',
    method: 'POST',
    data: params,
  });
};

/**
 * 获取阿里云人脸认证url
 */
export const getFaceType = (loading = false) => {
  return request<{
    faceType: API.FACE_TYPE;
  }>({
    url: '/api/face/findFaceType',
    method: 'GET',
  });
};

/**
 * 获取人脸结果
 */
export const getFaceResult = (
  {
    ticketId,
    certifyId,
    secureData,
    requestFrom,
  }: {
    ticketId: string;
    certifyId?: string;
    secureData?: string;
    requestFrom?: string;
  },
  loading = false,
) => {
  return request<{
    code: number;
  }>({
    url: '/ces/api/cloudface/finance/result',
    method: 'GET',
  });
};

export const getFaceIdResult = ({
  bizToken,
  requestFrom,
  requestId,
}: {
  bizToken: string;
  requestId: string;
  requestFrom: string;
}) => {
  return request<{code: number}>({
    url: '/api/face/logged/verify',
    method: 'POST',
    data: {
      bizToken,
      requestFrom,
      requestId,
    },
  });
};


export const getFaceIdResultWithCertid = ({
  bizToken,
  requestFrom,
  requestId,
  certId,
}: {
  bizToken: string;
  requestId: string;
  requestFrom: string;
  certId: string;
}) => {
  return request<{code: number}>({
    url: '/api/face/verify',
    method: 'POST',
    data: {
      bizToken,
      requestFrom,
      requestId,
      certId
    },
  });
};
