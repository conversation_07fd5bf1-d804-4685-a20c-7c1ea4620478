import request from "../http/request";

/** 
 * 还款总览接口
 */
export const getRepaymentSummary = () => {
    return request<API.RepaymentSummaryRes>({
      url: '/api/repayment/summary',
      method: 'GET',
    });
};

/** 
 * 查看分类应还信息明细
 */
export const getRepaymentList = (params: API.RepaymentListReq) => {
    return request<API.RepaymentListRes>({
      url: '/api/repayment/list',
      method: 'GET',
      params
    });
};

/** 
 * 提前还款试算
 */
export const aheadTrial = (params: API.AheadTrailReq) => {
    return request<API.AheadTrailRes>({
      url: '/api/repayment/trial/ahead',
      method: 'GET',
      params
    });
};

/** 
 * 主动还款试算
 */
export const activeTrial = (params: API.ActiveTrailReq) => {
    return request<API.ActiveTrailRes>({
      url: '/api/repayment/trial/active',
      method: 'GET',
      params
    });
};

/** 
 * 获取还款方式
 */
export const getPayWay = (params: API.PayWayReq) => {
    return request<API.PayWayRes>({
      url: '/api/repayment/pay-way',
      method: 'GET',
      params
    });
};

/** 
 * 银行卡还款
 */
export const bankcardPay = (data: API.BankcardPayReq) => {
    return request<API.BankcardPayRes>({
      url: '/api/repayment/apply/bankcard',
      method: 'POST',
      data
    });
};

/** 
 * 查询还款状态
 */
export const getRepaymentState = (params: API.getRepaymentStateReq) => {
  return request<API.getRepaymentStateRes>({
    url: '/api/repayment/state',
    method: 'GET',
    params
  });
};

/**
 * 查询客户全部还款计划
 */
export const getRepaymentPlanList = () => {
  return request<API.RepaymentPlanListRes>({
    url: '/api/repayment/plan/list',
    method: 'GET',
  });
};

/**
 * 查询账单日详细还款计划详情
 */
export const getRepaymentPlanDetail = ({dueDate}: {dueDate: string}) => {
  return request<API.RepaymentPlanDetailRes>({
    url: '/api/repayment/plan/detail',
    method: 'GET',
    params: {
      dueDate
    }
  });
};

/**
 * 批量查询借据还款信息
 */
export const getPrepareLoanInfo = (data: API.PrepareLoanInfoReq) => {
  return request<API.PrepareLoanInfoRes>({
    url: '/api/repayment/prepare/loan/info',
    method: 'POST',
    data
  });
};