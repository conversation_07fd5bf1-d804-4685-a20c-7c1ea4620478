import {useState} from 'react';
import {CyButton} from 'hncy58-taro-components';
import {Image, ScrollView, Text, View} from '@tarojs/components';
import Popup from '~components/Popup';
import Tabs, {TabItemProps} from '../Tabs';
import CouponList from './components/CouponList';
import styles from './index.module.scss';

interface CouponProps {
  visible: boolean;
  onClose: () => void;
}

export default (props: CouponProps) => {
  const {visible, onClose} = props;
  const [activeTab, setActiveTab] = useState('available');

  const tabData: TabItemProps[] = [
    {key: 'available', title: '可用优惠券', content: <CouponList />},
    {key: 'used', title: '已使用优惠券', content: <CouponList />},
  ];

  return (
    <Popup visible={visible} onClose={onClose} showClose title='选择优惠券'>
      <Tabs activeKey={activeTab} tabs={tabData} onChange={(key: string) => setActiveTab(key)} />
      <View className={styles.btn_container}>
        <CyButton round block type='primary'>
          确定
        </CyButton>
      </View>
    </Popup>
  );
};
