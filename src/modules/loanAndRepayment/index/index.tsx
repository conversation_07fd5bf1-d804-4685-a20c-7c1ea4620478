import { Tabs } from '@taroify/core'
import { useState } from 'react'
import PageWrap from '~components/PageWrap'
import LoanRecords from '../loanRecords'
import RepaymentRecords from '../repaymentRecords'

const LoanAndRepayment = () => {
  const [value, setValue] = useState(0)
  return (
    <PageWrap>
      <Tabs value={value} onChange={setValue}>
        <Tabs.TabPane title="借款记录">
          <LoanRecords />
        </Tabs.TabPane>
        <Tabs.TabPane title="还款记录">
          <RepaymentRecords />
        </Tabs.TabPane>
      </Tabs>
    </PageWrap>
  )
}

export default LoanAndRepayment