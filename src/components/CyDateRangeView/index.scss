.comp-cy-date-range-view{
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    padding: 0px 20px;
    align-items: center;
    height: 100px;
    background-color: #fff;
  }

  .date-item {
    font-size: 24px;
    height: 48px;
    width: 100px;
    text-align: center;
    border-radius: 8px;
    display: flex;
    flex-direction: row;
    justify-content: center;
    align-items: center;
  }
  .large {
    width: 390px;
  }
  .select {
    border: 2px solid #437DFF;
    background: #F1F5FF;
    color: #437DFF;
  }
  .unselect {
    border: 2px solid #EEEEEE;
    background: #EEEEEE;
    color: #999;
  }
  .date-label {
    height: 48px;
    text-align: center;
    border-radius: 8px;
    color: #437DFF;
    font-size: 24px;
    width: 180px;
    line-height: 40px;
    text-align: center;
  }
  .date-label-space {
      text-align: center;
      font-size: 20px;
      width: 30px;
      color: #333;
  }