# This file contains the fastlane.tools configuration
# You can find the documentation at https://docs.fastlane.tools
#
# For a list of all available actions, check out
#
#     https://docs.fastlane.tools/actions
#
# For a list of all available plugins, check out
#
#     https://docs.fastlane.tools/plugins/available-plugins
#

# Uncomment the line if you want fastlane to automatically update itself
# update_fastlane


APP_NAME = "taroDemo"
default_platform(:ios)

platform :ios do
  desc "Description of what the lane does"
  lane :custom_lane do
    # add actions here: https://docs.fastlane.tools/actions
  end

  desc "development"
	lane :build_dev do |options|
		update_info_plist
		update_code_signing_settings
		increment_version_number
		build_app(
			scheme: "taroDemo",
			workspace: "taroDemo.xcworkspace",
			export_method: "development",
			configuration: "Debug",
			clean: true,
			xcargs: "GCC_PREPROCESSOR_DEFINITIONS='$(inherited) DEBUG=1'",
			export_options: {
				method: "development",
				compileBitcode: false
			}
		)
	end

	desc "release"
	lane :build_release do |options|

		update_info_plist
		update_code_signing_settings

		#increment_version_number
		#Increment_build_number




		export_method = options[:method]
    if export_method == nil
        export_method = "ad-hoc"
    end

#profile 描述文件的结尾
    case export_method
    when "ad-hoc"
        #profile 的标题
        lastProfileComponent = "Adhoc"
      build_configuration = "Adhoc"
    when "app-store"
        lastProfileComponent = "AppStore"
      build_configuration = "Release"
    end

     #安装描述文件
      filePath = File::join(Pathname.new(File.expand_path('../..', __FILE__)) ,"/profiles/duobao2_#{lastProfileComponent}.mobileprovision")
      puts "filePath: #{filePath}"
    FastlaneCore::ProvisioningProfile.install(filePath)


        #获取版本号
    version_number = get_version_number(
      xcodeproj: "./taroDemo.xcodeproj",
      target: "taroDemo",
      #configuration: "#{build_configuration}"
      )
      #获取build号      
      build_number = get_build_number(
        xcodeproj: "./taroDemo.xcodeproj",
      )

      puts "version_number=#{version_number}, build_number=#{build_number}"
    
    today = Time.new; 
    puts "当前日期：" + today.strftime("%Y-%m-%d %H:%M:%S");
    OUTPUT_DIRECTORY = "./packages"+"/#{lastProfileComponent}"
    IPA_NAME = "#{APP_NAME}_" + "#{build_number}_"+"#{version_number}_" + "#{lastProfileComponent}_" + "#{today.strftime("%Y-%m-%d_%H.%M.%S")}"+".ipa"


 

		build_app(
			scheme: "taroDemo",
			# clean: true,
			workspace: "taroDemo.xcworkspace",
			export_method: "#{export_method}",
			configuration: "#{build_configuration}",
      			output_directory: "#{OUTPUT_DIRECTORY}",
     			include_bitcode: false,
     			xcargs: 'DEBUG_INFORMATION_FORMAT="dwarf-with-dsym"',
      			output_name: "#{IPA_NAME}",
			export_options: {
                	provisioningProfiles: {
                     		"com.hncy58.financeapp" => "duobao2_#{lastProfileComponent}"
                 	}
             }
		)
	end
	
end
