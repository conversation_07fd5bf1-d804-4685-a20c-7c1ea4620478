.page-content {
    padding: 150px 20px 0px 20px;
}

.page-top {
    position: fixed;
    top: 0;
    left: 0;
    background-color: #fff;
    padding: 24px 0;
    width: 100%;
    z-index: 9;
}

.tips {
    font-size: 28px;
    color: $color-text-secondary;
    text-align: center;
    padding: 12px 0;
    margin-top: 20px;
}

.card-tips-right {
    transform: translateY(-50px);
    display: flex;
    align-items: center;
}

.top-tips {
    display: flex;
    justify-content: space-between;
    padding-right: 40px;
}

.tips-icon {
    width: 24px;
    height: 24px;
    margin-right: 8px;
}

.mark {
    color: $color-primary;
}

.inputIdcardRow {
    display: flex;
    height: 120px;
    align-items: center;
    font-size: 28px;
    margin: 0 40px;
    
    & +.inputIdcardRow {
        border-top: 1px solid $color-border-light;
    }
}

.identity-input {
    flex: 1;
}

.inputIdcardLabel {
    padding-left: 24px;
    width: 222px;
}
.inputSection {
    background-color: #fff;
    border-radius: 16px;
    margin-bottom: 100px;
}
.idcardIcon {
    width: 32px;
    height: 32px;
    margin-right: 12px;
}

.idcardInputDesc {
    color: #a5adcc;
    font-size: 28px;
}

.row {
    display: flex;
    align-items: center;
    height: 122px;
    margin: 0 20px;
    padding: 0 28px;

    & + .row {
        border-top: 1px solid $color-border-light;
    }
}

.label {
    width: 140px;
    text-align: justify;
    text-align-last: justify;
    text-justify: inter-ideograph;
    font-size: 30px;
    font-weight: normal;
}

.value {
    font-size: 30px;
}

.canvas {
    width: 640px;
    height: 320px;
    margin: 0 auto;
}

.bottom-btn {}

.page-footer {
    // position: fixed;
    // z-index: 999;
    // bottom: 0;
    // width: 100%;
    // left: 0;
    padding: 20px 20px;
    box-sizing: border-box;
    background-color: $color-bg-normal;
}

.card-content {
    padding: 24px 20px 0 20px;
}

.identify-idcard-img {
    width: 670px;
    height: 360px;
    margin-top: 24px;
}

.photo-icon {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 120px;
    height: 120px;
}

.idcard-pic-content {
    position: relative;
}

.idcard-pic-title {
    position: relative;
    color: #2b2c05;
    font-size: 28px;
    padding-left: 16px;

    &::before {
        content: '';
        position: absolute;
        left: 0;
        top: 50%;
        transform: translateY(-50%);
        width: 8px;
        height: 8px;
        display: block;
        background-color: $color-primary;
        border-radius: 50%;
    }
}

.upload-idcard-entry-wrapper {
    border: 2px dashed $color-border-light;
    margin: 30px;
    height: 400px;
}

.upload-idcard-entry-text {
    display: flex;
    justify-content: center;
    align-items: center;
    color: $color-primary;
    font-size: 28px;
    line-height: 32px;
    margin-top: 32px;
}

.upload-idcard-entry-icon {
   width: 32px;
   height: 32px;
   margin-right: 8px;
}

.modal-content {
    // padding: 20px 40px 60px 40px;
    font-size: 28px;
    color: $color-text-regular;
    line-height: 48px;  
}

.labelValueGap {
    padding: 0 12px;
}

.iconScan {
    width: 24px;
    height: 24px;
    margin-right: 12px;
}

.scanBarLeft {
    display: flex;
    align-items: center;
    flex: 1;
}

.scanBarPlaceholderText {
    color: #999;
    margin-left: 44px;
}

.scanBar {
    display: flex;
    align-items: center;
    border: 1px solid #ddd;
    background-color: #f9f9f9;
    border-radius: 8px;
    height: 80px;
    padding: 0 24px 0 32px;
    margin: 40px 20px 0 20px;
}

.iconArrowRight {
    width: 20px;
    height: 20px;
}

.sectionTips {
    font-size: 24px;
    color: #999;
    line-height: 56px;
    margin-top: 24px;
    padding-left: 20px;
}