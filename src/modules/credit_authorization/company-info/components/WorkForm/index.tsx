import {useEffect, useMemo, useState} from 'react';
import {Picker, Text, View, Input} from '@tarojs/components';
import classNames from 'classnames';
import CyPicker from '~components/CyPicker';
import CySearchInput from '~components/CySearchInput';
import {searchCompanyList} from '~services/system';
import styles from './index.module.scss';
import CellPickerItem, {PickerMode} from '~components/CellPickerItem';
const enum WorkTypeEnum {
  '单位员工' = 'work',
  '自由职业' = 'free',
}

interface Props {
  onDataChange: (data: {workType: 'work' | 'free'; companyName: string; workName: string; monthInCome: string}) => void;
}

interface CompanyItem {
  creditNo: string;
  title: string;
}

export default ({onDataChange}: Props) => {
  const WorkTypeMap = [
    {label: '单位员工', key: WorkTypeEnum.单位员工},
    {label: '自由职业', key: WorkTypeEnum.自由职业},
  ];
  const salary = ['3000及以下', '3001-5000', '5001-8000', '8001-10000', '10001-20000', '20000以上'];
  const [formData, setFormData] = useState({
    workType: WorkTypeEnum.单位员工,
    companyName: '',
    workName: '',
    monthInCome: -1,
  });
  const isFreeCarrier = useMemo(() => {
    return formData.workType === WorkTypeEnum.自由职业;
  }, [formData.workType]);

  useEffect(() => {
    onDataChange({
      ...formData,
      monthInCome: salary[formData.monthInCome],
    });
  }, [formData, onDataChange]);

  const handleCompanySelect = (company: CompanyItem) => {
    setFormData(pre => ({...pre, companyName: company.title}));
  };

  return (
    <View className={styles['work-form-box']}>
      <View className={styles['row']}>
        <View className={styles['label']}>工作信息</View>
        <View className={styles['value']}>
          {WorkTypeMap.map(item => (
            <View
              key={item.key}
              className={classNames(
                styles['option-item'],
                styles['min'],
                formData.workType === item.key ? styles['active'] : '',
              )}
              onClick={() => {
                console.log(item.key);
                setFormData(pre => ({...pre, workType: item.key}));
              }}>
              {item.label}
            </View>
          ))}
        </View>
      </View>
      <View className={styles['row']}>
        <View className='label'>{isFreeCarrier ? '职业名称' : '单位名称'}</View>
        <View className={styles['value']}>
          {isFreeCarrier ? (
            <Input
              placeholder='请输入您从事的职业名称'
              className={styles['input']}
              onInput={e => {
                setFormData(pre => ({...pre, workName: e.detail.value}));
              }}
            />
          ) : (
            <CySearchInput<CompanyItem>
              placeholder='请输入您的工作单位名称'
              value={formData.companyName}
              onInput={value => setFormData(pre => ({...pre, companyName: value}))}
              onSelect={handleCompanySelect}
              searchFn={searchCompanyList}
              keyExtractor={(item) => item.creditNo || ''}
              inputClassName={styles['input']}
            />
          )}
        </View>
      </View>
      <View className={styles['row']} style={{paddingLeft: 0, paddingRight: 0}}>
        <CellPickerItem
              className={styles.CellItem}
              title='月收入'
              placeholder='请选择月收入'
              range={salary.map((item, idx) => ({label: item, value: idx.toString()}))}
              value={salary[formData.monthInCome]}
              onChange={monthInCome => {
                console.log('pickerChange', monthInCome);
                setFormData(pre => ({...pre, monthInCome}));
              }}
              clickable
              style={{width: '100%'}}
          />
      </View>
      {/* <View className={classNames(styles['value'], styles['single-section-value'])}>
        {salary.map((item, idx) => (
          <View
            key={idx}
            className={classNames(styles['option-item'], formData.monthInCome === idx ? styles['active'] : '')}
            onClick={() => {
              setFormData(pre => ({...pre, monthInCome: idx}));
            }}>
            {item}
          </View>
        ))}
      </View> */}
    </View>
  );
};
