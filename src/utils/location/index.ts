type GetLocationOption = {
  onSuccess: (res: {longitude: number, latitude: number}) => void;
  onFail?: (error: {message: string}) => void;
  usage: string; //是否进行逆地址
};
export const getLocation = async ({onSuccess, usage, onFail}: GetLocationOption) => {
  onFail && onFail({message: '暂未实现'});
};

//开始持续地位
export const startUpdatingLocation = () => {};

//关闭持续地位
export const stopUpdatingLocation = () => {};

//获取持续定位地址信息
export const getUpdatingLocation = async ({onSuccess, onFail}: GetLocationOption) => {
  onFail && onFail({message: '暂未实现'});
};

//获取定位是否可用
export const getLocationAuthStatusIsValid: () => boolean = () => {
  return false;
};

//当前应用是否可以使用/申请定位服务
export const getLocationServiceIsValid: () => boolean = () => {
  return false;
};

//获取定位权限状态
export const getAuthorizationStatus: () => string = () => {
  return '0';
};

/**
 * 获取定位权限
 * @param openSeting 权限不足时是否提示打开设置
 * @returns
 */
export const requestLocationPermission = async (openSeting = true) => {
  return false;
};
