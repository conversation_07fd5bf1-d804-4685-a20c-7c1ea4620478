.bindingPage {
  padding: 20px;
  padding-bottom: 180rpx;
}

.pageCard {
  margin-bottom: 20px;
  padding-bottom: 0;
}

.bindingContent {
  padding: 38px 40px 0 40px;
}
.bindingMain {
  position: relative;
  padding: 0 40px;
  .bindArrowRight {
    position: absolute;
    right: 32px;
    top: 50%;
    transform: translateY(-50%);
    width: 24px;
    height: 24px;
  }
}

.bindingTip {
  padding: 19px 30px;
  font-size: $font-size-tiny;
  color: #6171B5;
  background: #EFF2FF;
  border-radius: 8px;
  line-height: 40px;
}

.cellIcon {
  width: 24px;
  height: 24px;
  margin-left: 17px;
}

.bindingBank {
  color: $color-text-normal;
  font-size: $font-size-normal;
}
.bankConainer {
  display: flex;
  align-items: center;
  margin-top: 20px;
  font-size: $font-size-sm;
  .bankIcon {
    width: 48px;
    height: 48px;
    margin-right: 12px;
  }
}

.inputPlaceholder {
  color: #999;
  font-size: 28px;
}

.agreementContainer {
  padding: 25px 0 68px 0;
}
.agreement {
  display: flex;
  justify-content: center;
}

.bindbtn {
  // margin-bottom: 40px;
}

.bankcardLongInput {
  min-width: 420px;
}

.contractPopupFooter {
  display: flex;
  padding: 40px;
}

.contractContent {
  padding: 0 40px;
  color: $color-text-normal;
}

.cy-contractbar-confirm-btn {
  margin-left: 12px;
  flex: 1;
}

.bottom-fixed {
  padding-bottom: 40px;
}