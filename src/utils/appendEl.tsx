import {View} from '@tarojs/components';
import {render, unmountComponentAtNode} from '@tarojs/react';
import {TaroElement, TaroRootElement, document} from '@tarojs/runtime';
import Taro from '@tarojs/taro';
import Modal, {ModalProps} from '~components/Modal';

export const createModal = (props: ModalProps) => {
  const {...rest} = props;
  const view = document.createElement('view');
  const currentPages = Taro.getCurrentPages();
  const currentPage = currentPages[currentPages.length - 1];
  const path = currentPage.$taroPath;
  const pageElement = document.getElementById<TaroRootElement>(path);

  // 用于控制 Modal 的 visible 状态
  let visible = true;

  // 重新渲染 Modal
  const rerender = () => {
    render(<Modal {...rest} visible={visible} onClose={onClose} />, view, console.log);
  };

  // 关闭时先隐藏动画，动画结束后再销毁
  const onClose = () => {
    visible = false;
    rerender();
    setTimeout(() => destroyModal(view), 300); // 300ms为动画时长
  };

  rerender();
  const pageContainer = pageElement?.childNodes[0];
  pageContainer?.insertBefore(view, pageContainer?.childNodes[0]);
  return onClose;
};

export const destroyModal = (node: TaroElement) => {
  const currentPages = Taro.getCurrentPages();
  const currentPage = currentPages[currentPages.length - 1];
  const path = currentPage.$taroPath;
  const pageElement = document.getElementById<TaroRootElement>(path);
  const pageContainer = pageElement?.childNodes[0];
  node && unmountComponentAtNode(node);
  node && pageContainer?.removeChild(node);
};
