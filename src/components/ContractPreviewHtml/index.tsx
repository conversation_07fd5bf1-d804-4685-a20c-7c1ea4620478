// components/CyContractPreviewHtml.tsx
import React, {CSSProperties, forwardRef, memo, useEffect, useImperativeHandle, useMemo, useRef, useState} from 'react';
import {Image, ScrollView, Text, View, ViewProps} from '@tarojs/components';
import {createSelectorQuery} from '@tarojs/taro';
import classNames from 'classnames';
import PrivacyAgreement from '~components/PrivacyAgreement';
import * as AgreementApi from '~services/agreement';
import './index.scss';
import Taro from '@tarojs/taro';
import CySpinner from '~components/CySpinner';
import { ContractCode2Info } from '~utils/constant';

export interface ContractItem {
  name?: string;
  code: string;
  content: string;
  contentType?: 'string' | 'image';
}

export interface ContractItemProps {
  code: string;
  params?: Record<string, any>;
  check?: () => boolean;
  noCache?: boolean;
}

interface Props extends ViewProps {
  contracts: ContractItemProps[];
  height?: string | number;
  verticalScrollable?: boolean;
  onLoadEnd?: () => void;
  setActiveContract?: (id: string) => void;
  onContractInView?: (type: string) => void;
  singleShowType?: string;
  lazy?: boolean;
}

export interface CyContractPreviewHtmlRef {
  scrollToContract: (contractType: string) => void;
  loadContracts: () => Promise<void>;
  resetScroll: () => void;
}

const CyContractPreviewHtml = forwardRef((props: Props, ref) => {
  const contractHtmlStyle = useRef<string>();
  const [contracts, setContracts] = useState<ContractItem[]>([]);
  const containerRef = useRef<HTMLDivElement>(null);
  const [activeContract, setActiveContract] = useState<string>('');
  const singleContractTypeRef = useRef<string>();
  const [loading, setLoading] = useState<boolean>(false);

  // 暴露外部方法
  useImperativeHandle(ref, () => ({
    scrollToContact,
    loadContracts,
    resetScroll
  }));

  // 未设置activeContract时，contracts变化时，设置第一个contract为activeContract
  useEffect(() => {
    if(!activeContract) {
      setActiveContract(contracts[0]?.code);
    }
  }, [contracts]);


  useEffect(() => {
    if (props.lazy) {
      return;
    }
    loadContracts();
  }, []); // eslint-disable-line react-hooks/exhaustive-deps


  // 监听props.contracts中变化，重新获取contracts
  useEffect(() => {
    if (props.singleShowType) {
      const target = props.contracts.find(contract => contract.code === props.singleShowType);
      const singleHtml = contracts.find(html => html.code === props.singleShowType);
      console.log('target && target.noCache', target, target?.noCache)
      // 不缓存 || 缓存但是不是当前展示的contract     此两种情况重新获取
      if(
        target && target.noCache || 
        (target && !singleHtml && singleContractTypeRef.current !== props.singleShowType)
      ) {
        singleContractTypeRef.current = props.singleShowType;
        getSingleContract(target);
      }
    }
  }, [props.singleShowType, props.contracts]);

  // 获取单个contract
  const getSingleContract = async ({code, params, noCache}: ContractItemProps) => {

    // 如果设置了nocache并且已经存在contracts中，直接返回不用再次调用接口重新请求合同
    if(contracts.find(it => it.code === code) && !noCache) {
      return;
    }
    
    const apiName = ContractCode2Info[code]?.api;

    if (!apiName) {
      // 特殊处理隐私政策
      if (code === 'privacy') {
        setContracts(prev => {
          if(!prev.find(it => it.code === code)) {
            return [...prev, {code, content: '', name: '《隐私政策》'}]
          }
          return prev;
        });
      }
      return;
    }

    try {
      // @ts-ignore
      if(!AgreementApi[apiName]) return;
      setLoading(true);
      const {body, content, style} = await (AgreementApi as Record<string, any>)[apiName](params);
      if (content) {
        setContracts(prev => {
          const targetIdx = prev.findIndex(it => it.code === code)
          const newContent = {
            code,
            name: ContractCode2Info[code].name,
            content: `data:image/jpeg;base64,${content}`,
            contentType: 'image',
          } as ContractItem;
          if(targetIdx === -1) {
            return [...prev, newContent]
          } else {
            prev[targetIdx] = newContent
          }
          return prev;
        });
      } else {
        contractHtmlStyle.current = style;
        // 针对不支持 align="center" 的富文本，需要手动替换为 style="text-align: center;"
        const content = body.replace(/align\=\"center\"/g, 'style="text-align: center;"')
        setContracts(prev => [...prev, {code, content, contentType: 'string', name: ContractCode2Info[code].name,}]);
      }
    } catch (error) {
      console.error('Failed to load contract:', error);
    } finally {
      setLoading(false);
    }
  };


  // 加载所有contracts
  const loadContracts = async () => {
    // setContracts([]);
    try{
      for (const contract of props.contracts) {
        await getSingleContract(contract);
      }
    }catch(error){
      console.error('Failed to load contracts:', error);
    } finally {
      props.onLoadEnd?.();
    }
  };
  
  const resetScroll = () => {
    const query = createSelectorQuery();
    query.select('#contractScrollView').node(nodeRes => {
      nodeRes.node.scrollTo({
        top: 0,
        duration: 0,
      });
    });
    query.exec();
  };

  const scrollToContact = (contractType: string) => {
    const query = createSelectorQuery();
    query.select('#contractScrollView').node(nodeRes => {
      nodeRes.node.scrollIntoView(`#${contractType}`);
    });
    query.exec();
  };

  // 监听滚动
  const handleScroll = (e: any) => {
    const scrollTop = e.detail.scrollTop;
    const query = createSelectorQuery();
    for (const contract of props.contracts) {
      query.select(`#${contract.code}`).boundingClientRect();
    }
    query.exec(res => {
      let sumHeight = 0;
      for (let i = 0; i < res.length; i++) {
        sumHeight += res[i]?.height;
        console.log('onScroll=====', sumHeight, res);
        if (scrollTop <= sumHeight) {
          res[i].id && props.onContractInView?.(res[i].id);
          res[i].id && setActiveContract(res[i].id);
          break;
        }
      }
    });
  };

  return (
    <>
      <ScrollView
        scrollY
        ref={containerRef}
        enhanced
        onScroll={handleScroll}
        style={props.style}
        id='contractScrollView'
        scrollWithAnimation
        className={classNames(
          `contract-preview-html-container ${props.verticalScrollable ? 'scrollable' : ''}`,
          props.className,
        )}>
        {
          loading ? (
            <View className='contract-item' style={{textAlign: 'center'}}>
              <CySpinner />
            </View>
          ) : contracts.map((contract, idx) => {
            if(props.singleShowType && contract.code !== props.singleShowType){
              return null
            }
            return (
              <>
                { (idx !== 0 && !props.singleShowType) ? <View className='contract-gap'>继续滑动查看其他协议</View> : null }
                {
                  contract.code === 'privacy' ? (
                    <View key={contract.code} className='contract-item' id={contract.code}>
                      <PrivacyAgreement />
                    </View>
                  ) : 
                    contract.contentType === 'string' ? (
                      <View
                        key={contract.code}
                        className={classNames('taro_html', 'contract-item')}
                        id={contract.code}
                        dangerouslySetInnerHTML={{__html: contract.content}}
                      />
                    ) : (
                      <Image 
                        onClick={() => {
                          Taro.previewImage({
                            current: contract.content,
                            urls: contracts.filter(it => it.contentType === 'image').map(img => img.content)
                          })
                        }}
                        key={contract.code}
                        id={contract.code}
                        mode='widthFix'
                        src={contract.content}
                        className='contract-item'
                        style={{width: '100%'}}
                      />
                    )
                }
              </>
            ) 
          })
        }
      </ScrollView>
      {
        contracts.length > 1 && !props.singleShowType ? (
          <View className='contract-bt-nav'>
            {contracts.map(it => {
              return <Text className={activeContract === it.code ? 'active-contract-text': 'contract-text'} onClick={() => scrollToContact(it.code)} key={it.code}>{it.name}</Text>
            })}
          </View>
        ) : null
      }
    </>
  );
});

export default memo(CyContractPreviewHtml, (prevProps, nextProps) => (prevProps.contracts.join(',') !== nextProps.contracts.join(',')));
