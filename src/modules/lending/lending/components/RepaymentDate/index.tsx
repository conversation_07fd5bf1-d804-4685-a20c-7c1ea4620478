import {useMemo, useState} from 'react';
import {View} from '@tarojs/components';
import Popup from '~components/Popup';
import styles from './index.module.scss';

interface RepaymentDateProps {
  visible: boolean;
  fixedDueCalendar: string;
  onClose?: () => void;
  oncChange?: (idx: number) => void;  
}
const RepaymentDate = ({visible, fixedDueCalendar, oncChange, onClose}: RepaymentDateProps) => {
  const [currentDate, setCurrentDate] = useState<number | null>();

  const fixedDueDate = useMemo(() => {
    if (fixedDueCalendar) {
      const fixedDueDate = fixedDueCalendar.split(',');
      return fixedDueDate;
    }
    return ['1', '2', '3', '4', '5', '6', '7', '8', '9', '10', '11', '12', '13', '14', '15', '16', '17', '18', '19', '20', '21', '22', '23'];
  }, [fixedDueCalendar]);
  const handleChoose = (idx: number) => {
    setCurrentDate(idx);
    oncChange?.(idx);
  };

  return (
    <Popup visible={visible} onClose={onClose} showClose title='还款日'>
      <View className={styles.dateContainer}>
        {currentDate ? <View className={styles.dateTitle}>每月 {currentDate} 日</View> : null}
        <View className={styles.dayContainer}>
          {fixedDueDate.map((dateItem, index) => (
            <View
              key={index}
              onClick={() => handleChoose(Number(dateItem))}
              className={`${styles.day} ${Number(dateItem) === currentDate ? styles.choosed : ''}`}>
              {dateItem}
            </View>
          ))}
        </View>
      </View>
    </Popup>
  );
};

export default RepaymentDate;
