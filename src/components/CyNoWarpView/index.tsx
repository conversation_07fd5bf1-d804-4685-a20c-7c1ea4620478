import {FC} from 'react';
import {View, ViewProps} from '@tarojs/components';
import './index.scss';

type Props = {};

/**
 * 组件名称：不换行控件
 * 组件描述：不换行控件
 */
const CyNoWarpView: FC<Props & ViewProps> = props => {
  let className = 'comp-cy-no-warp-view';
  if (props.className) {
    className = `${className} ${props.className}`;
  }
  return (
    <View {...props} className={className} style={props.style}>
      {props.children}
    </View>
  );
};

export default CyNoWarpView;
