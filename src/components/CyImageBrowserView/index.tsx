import {FC, useEffect, useState} from 'react';
import {Image, ScrollView, View} from '@tarojs/components';
import Taro from '@tarojs/taro';
import dayjs from 'dayjs';
import {isEmpty} from '../../utils/common';
import './index.scss';

type Props = {
  paths?: string[];
  getPhotoData?: Function;
  closeAction?: () => void;
};

let windowWidth = 0;
let photoWidth = 0;
Taro.getSystemInfo({
  success(res) {
    windowWidth = res.windowWidth;
    photoWidth = 0.8 * windowWidth;
  },
  fail(err) {
    console.error('获取屏幕信息失败', err);
  },
});

type PhotoData = {loading: boolean; tip: string; path: string; file: string};

/**
 * 组件名称：图片浏览器
 * 组件描述：浏览图片
 */
const CyPhotoBrowserView: FC<Props> = ({paths = [], getPhotoData, closeAction}) => {
  const temp: PhotoData[] = [];
  paths.map((val, index) => {
    temp.push({
      file: '',
      path: val,
      loading: false,
      tip: '',
    });
  });
  const [datas, setDatas] = useState<PhotoData[]>(temp);

  useEffect(() => {
    const t: PhotoData[] = [];
    datas.map((val, index) => {
      let s = val.path.split('.')[0].split('_');
      let tip = val.path;
      if (s.length > 2) {
        if (!isEmpty(s[2])) {
          const date = dayjs(s[2]);
          tip = date.format('YYYY-MM-DD HH:mm:ss');
        }
        if (s.length > 3 && !isEmpty(s[3])) {
          tip = `${tip}_${s[3]}`;
        }
      }
      console.log('path=', val);
      t.push({
        ...val,
        loading: false,
        tip,
      });
    });
    setDatas(t);
    onScroll(0);
  }, [paths]);
  //获取照片数据
  const requestPhotoData = async (index: number) => {
    if (getPhotoData === undefined) return;

    const data = datas[index];
    if (data === undefined) return;
    if (data.loading) return;
    setDatas(flag => {
      const t = [...flag];
      t[index].loading = true;
      return t;
    });
    const photoData: string = await getPhotoData(datas[index].path);
    if (photoData === '' || photoData === undefined) return;
    setDatas(flag => {
      const t = [...flag];
      t[index].file = `data:image/png;base64,${photoData}`;
      t[index].loading = false;
      return t;
    });
  };

  //滚动监听
  const onScroll = (scrollLeft: number) => {
    const index = Math.round(scrollLeft / photoWidth);
    if (getPhotoData && datas[index] !== undefined && (datas[index].file === undefined || datas[index].file === '')) {
      requestPhotoData(index);
    }
  };
  const style = {width: photoWidth, height: photoWidth, backgroundColor: '#ffffff'};

  const imageClick = (index: number, path: string) => {
    if (datas[index] !== undefined && !(datas[index].file === undefined || datas[index].file === '')) {
      const urls = datas
        .filter(item => {
          return item.file !== undefined && item.file !== '';
        })
        .map(item => {
          return item.file;
        });
      Taro.previewImage({
        current: path,
        urls,
      });
    }
  };
  return (
    <View className='comp-cy-photo-browser-view'>
      {/* <View
          className='close'
          onClick={() => {
            setshowPhotosBrowser(false);
          }}>
          关闭
        </View> */}
      <View className='scrollview-container'>
        {datas && (
          <ScrollView
            scrollX
            className='scrollview'
            pagingEnabled
            enhanced
            scrollWithAnimation
            scrollAnimationDuration='1000'
            onScroll={e => {
              onScroll(e.detail.scrollLeft);
            }}
            style={style}>
            {datas.map((val, index) => (
              <View
                className='image-item'
                key={index}
                style={style}
                onClick={() => {
                  imageClick(index, val.file);
                }}>
                {val.file !== '' ? (
                  <Image
                    className='flex-center image'
                    src={val.file}
                    key={index}
                    mode='aspectFit'
                    style={style}></Image>
                ) : (
                  <View className='image' style={style}></View>
                )}
                <View className='option'>
                  <View className='option-item'>{val.tip}</View>
                  <View className='option-item'>{`${index + 1}/${datas.length}`}</View>
                </View>
              </View>
            ))}
          </ScrollView>
        )}
      </View>
      {/* <View className='place'></View> */}
    </View>
  );
};

export default CyPhotoBrowserView;
