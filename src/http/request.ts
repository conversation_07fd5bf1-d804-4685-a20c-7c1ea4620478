import Taro, {getStorageSync, setStorageSync} from '@tarojs/taro';
import axios, {AxiosInstance, AxiosRequestConfig, AxiosResponse} from 'axios';
import {isEmpty} from '../utils/common';
import {reqSucInterceptor, resErrInterceptor, resSucInterceptor} from './interceptor';
import useMainStore from '~store/main';

const TIMEOUT = 30000;

export interface RequestConfig extends AxiosRequestConfig {
  args?: Record<string, any>;
}
export interface BaseRes {
  code: string;
  status: string;
  message?: string;
}

let baseURL =
  process.env.TARO_ENV === 'h5'
    ? process.env.NODE_ENV === 'development'
      ? process.env.TARO_APP_MODE
      : process.env.TARO_APP_API
    : process.env.TARO_APP_API;

console.log('baseURL =', baseURL);
// const token = getStorageSync('baseToken') || '';
const baseParam = {
  channelCode: 'C1201',
  accessChannel: 'ucs',
  userAgent: '',
  phoneMode: 'iPhone',
  deviceId: '17308819363159658255',
  osVersion: '16.6',
  osType: 'iOS',
  distinctId: 'JSe2f4eacdf61a660408cd91a2a37fc39ae2f4',
};
// 1. 创建实例
const service: AxiosInstance = axios.create({
  baseURL,
  headers: {
    'Content-Type': 'application/json;charset=utf-8',
    // Authorization: `Bearer ${token}`,
    appBaseReqtParam: JSON.stringify(baseParam),
  },
  timeout: TIMEOUT,
});

// 2. 请求拦截
service.interceptors.request.use(reqSucInterceptor, error => {
  console.log(`request error: ${error}`);
});

service.interceptors.response.use(resSucInterceptor, resErrInterceptor);

// 3. 整合config + 统一处理返回类型

const baseRequest = async <T>(params: RequestConfig) => {
  // 统一处理返回类型
  try {
    console.log(`${params.url}, request:::`, params);
    const response: AxiosResponse<T & BaseRes> = await service.request<T & BaseRes>(params);
    console.log(`${params.url}, response:::`, response, response.data, params);
    const {data, headers} = response;
    if (headers?.token) {
      console.log('baseToken', headers.token);
      useMainStore.getState().setToken(headers.token);
    }
    if(!response.status) {
      return response as T;
    }
    return data;
  } catch (err: unknown) {
    return Promise.reject(err);
  }
};

// 4. request构造函数
function requestFactory(config: RequestConfig) {
  return async (requestConfig?: Partial<RequestConfig>) => {
    // 合并在service中定义的config和调用时从外部传入的config
    const mergedConfig: RequestConfig = {
      ...config,
      ...requestConfig,
      headers: {
        ...config.headers,
        ...requestConfig?.headers,
      },
    };
    return requestFactory(mergedConfig);
  };
}

export {requestFactory};

export default baseRequest;
