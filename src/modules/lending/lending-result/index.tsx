import { useEffect, useState } from 'react';
import { Button, Image, Text, View } from '@tarojs/components';
import resWait from '~images/credit-apply/<EMAIL>';
import resFail from '~images/lending/<EMAIL>'
import resSuccess from '~images/lending/<EMAIL>'
import resuOvertime from '~images/credit-apply/<EMAIL>'
import resPhoneVerify from '~images/lending/<EMAIL>'
import resLoanBlocking from '~images/credit-apply/<EMAIL>'
import styles from './index.module.scss';
import { getLendingState } from '~services/lending';
import { useLoad } from '@tarojs/taro';
import { CyButton } from 'hncy58-taro-components';
import PageWrap from '~components/PageWrap';
import { router } from '~utils/common';
import usePolling from '~hooks/usePolling';
import { formatToFixedTwo } from '~utils/number';
import { transDateToChinese } from '~utils/date';
import useUserStore from '~store/user';
import CustomerServiceLink from '~components/CustomerServiceLink';
import Clock from '~components/ClockAniCanvas';
const LendingResult = () => {
  /** 1. 变量定义 */
  const { user } = useUserStore();

  const [result, setResult] = useState<API.ILendingStatusCode>('PROCESSING');
  const [billNo, setBillNo] = useState<string>('');
  const [bankCardInfo, setBankCardInfo] = useState<API.IBankCardInfo | null>(null);
  const [loanAmount, setLoanAmount] = useState<string | number>('');
  const [firstDate, setFirstDate] = useState<string>('');
  const [firstAmount, setFirstAmount] = useState<string | number>('');
  const resOpt: Record<API.ILendingStatusCode, string> = {
    PROCESSING: '借款处理中，请您耐心等待',
    CREDIT_REAPPRAISE_PROCESSING: '借款处理中，请您耐心等待',
    SUCCESS: '借款申请成功',
    FAILED: '借款失败',
    OVERTIME: '放款正在处理中，详细结果可在借还记录查询',
    PHONE_VERIFY: '等待电核中...',
    LOAN_BLOCKING: '存在处理中的放款，请稍后~',
  };
  const resNote: Record<API.ILendingStatusCode, string> = {
    PROCESSING: '',
    CREDIT_REAPPRAISE_PROCESSING: '',
    SUCCESS: '借款已发放至银行卡，请留意到账短信',
    FAILED: '放款失败，请核实银行卡状态',
    OVERTIME: '',
    PHONE_VERIFY: user.mobile.slice(-4) ? `手机尾号${user.mobile.slice(-4)}的亲，记得接电话哦~` : '亲，记得接电话哦~',
    LOAN_BLOCKING: '',
  };
  const imgUrlMap = {
    PROCESSING: resWait,
    CREDIT_REAPPRAISE_PROCESSING: resWait,
    SUCCESS: resSuccess,
    FAILED: resFail,
    OVERTIME: resuOvertime,
    PHONE_VERIFY: resPhoneVerify,
    LOAN_BLOCKING: resLoanBlocking,
  }

  /** 3. effects */
  useLoad(async ({ billNo, bankCardInfo, status }) => {
    // if (billNo) {
      // let count = 0;
      // let maxCount = 60 / 5; // 60s / 5s间隔

      // const timer = setInterval(async () => {
      //   count++;
      //   if (count >  maxCount) {
      //     timer && clearInterval(timer);
      //     return
      //   }
      //   const {state, loanAmount} = await getLendingState({ billNo });
      //   setResult(state);
      //   setLoanAmount(loanAmount);
      //   if (state !== 'PROCESSING') {
      //     timer && clearInterval(timer);
      //   }
      // }, 5000)
    // }
    if (status) {
      setResult(status);
      return
    }
    if (billNo) setBillNo(billNo);
    if (bankCardInfo) setBankCardInfo(JSON.parse(bankCardInfo));
  });

  usePolling({
    fetchFn: async () => {
      if (!billNo) return null
      return await getLendingState({ billNo })
    },
    onSuccess: (data) => {
      if (!data) return
      const { state, loanAmount, firstPaymentAmount, firstPaymentDate } = data
      setResult(state)
      setLoanAmount(loanAmount)
      setFirstAmount(firstPaymentAmount)
      setFirstDate(firstPaymentDate)
    },
    interval: 4000,
    maxDuration: 12000,
    onMaxAttemptsReached: () => {
      setResult('OVERTIME')
    },
    stopCondition: (data) => {
      if (!data?.state) {
        return false
      } else {
        return !['PROCESSING', 'CREDIT_REAPPRAISE_PROCESSING'].includes(data?.state)
      }
    },
  });

  /** 4. 方法定义 */
  const goHome = () => {
    router.reLaunch({url: '/pages/check/index'});
  };

  const getBankAccount = () => {
    if (bankCardInfo) {
      const { bankName, desensitizeAccountNo } = bankCardInfo;
      if (!desensitizeAccountNo) return bankName || '未选择';
      return `${bankName}(${desensitizeAccountNo?.slice(-4)})`;
    }
    return ''
  }
  
  /** 5. ui */
  return (
    <PageWrap className={styles.page_lending_result}>
      {
        ['PROCESSING', 'CREDIT_REAPPRAISE_PROCESSING'].includes(result) ? (
          <Clock size={160} className={styles.waitClock} />
        ) : <Image className={styles.result_img} src={imgUrlMap[result]} />
      }
      <View className={styles.result_value}>
        <View className={styles.result_text}>{resOpt[result]}</View>
        {resNote[result] && <View className={styles.result_note}>{resNote[result]}</View>}
      </View>

      {!['PROCESSING', 'CREDIT_REAPPRAISE_PROCESSING', 'OVERTIME', 'PHONE_VERIFY', 'LOAN_BLOCKING'].includes(result) && (
        <View className={styles.lending_info}>
          <View className={styles.info_content}>
            <View className={styles.label}>借款金额</View>
            <View className={styles.value}>{`${formatToFixedTwo(loanAmount) || ''}元`}</View>
          </View>
          <View className={styles.info_content}>
            <View className={styles.label}>收款账户</View>
            <View className={styles.value}>{getBankAccount()}</View>
          </View>
        </View>
      )}

      {result === 'SUCCESS' && (
        <View className={styles.lending_info}>
          <View className={styles.info_title}>首期还款</View>
          <View className={styles.info_content}>
            <View className={styles.label}>首期应还</View>
            <View className={styles.value}>{formatToFixedTwo(firstAmount)}元</View>
          </View>
          <View className={styles.info_content}>
            <View className={styles.label}>还款日</View>
            <View className={styles.value}>
              {transDateToChinese(firstDate)}
              <View className={styles.value_note}>将从您绑定的银行卡中自动扣款</View>
            </View>
          </View>
        </View>
      )}

      {['SUCCESS', 'OVERTIME', 'PHONE_VERIFY', 'LOAN_BLOCKING'].includes(result) ? (
        <CyButton block round type='primary' className={styles.result_btn} onClick={goHome}>
          我知道了
        </CyButton>
      ) : (
        <View className={styles.result_tip}>
          <CustomerServiceLink />
        </View>
      )}
    </PageWrap>
  );
};

export default LendingResult;
