.header {
  background-color: #fff;
  padding: 40px;
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  border-radius: 8px;
  // align-items: center;
}

.dateContainer {
  font-family: Source <PERSON>s SC;
  font-weight: 400;
  font-size: 28px;
  color: #333333;
  line-height: 28px;
}

.headerContent {
  display: flex;
  flex-direction: column;
  gap: 30px;
}

.amountContainer {
  font-family: Source <PERSON> SC;
  font-weight: bold;
  font-size: 64px;
  color: #333333;
  line-height: 28px;
}

.statusContainer {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.status {
  font-family: Source Han Sans SC;
  font-weight: 500;
  font-size: 32px;
  color: #2f54eb;
  line-height: 28px;
  margin-bottom: 14px;
}

.statusError {
  font-family: Source <PERSON> San<PERSON> SC;
  font-weight: 500;
  font-size: 32px;
  color: #f5222d;
  line-height: 28px;
  margin-bottom: 14px;
}

.statusDesc {
  font-family: Source <PERSON> Sans SC;
  font-weight: 400;
  font-size: 24px;
  color: #999999;
  line-height: 28px;
}
