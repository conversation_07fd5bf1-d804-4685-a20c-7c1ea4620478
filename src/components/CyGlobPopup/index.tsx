import {CSSProperties, FC, Fragment, useEffect, useState} from 'react';
import {
  BaseEventOrig,
  Image,
  Input,
  InputProps,
  Label,
  Radio,
  RadioGroup,
  ScrollView,
  Textarea,
  View,
  ViewProps,
} from '@tarojs/components';
import Taro from '@tarojs/taro';
import classNames from 'classnames';
import clearImg from '~images/icon/clear.png';
import Icon_Info from '~images/icon/icon-info.png';
import {lockToLandscape, lockToPortrait} from '../../utils/orientation';
import RootSiblingsManager from '../../utils/rootSoblingsManager/index';
import CyButton from '../CyButton';
import CyCheckbox from '../CyCheckbox';
import CyImageBrowserView from '../CyImageBrowserView';
import CyInput from '../CyInput';
import CyPdfAgreementPopup from '../CyPdfAgreementPopup';
import CySafeArea from '../CySafeArea';
import './index.scss';

type AlignType = 'top' | 'left' | 'right' | 'bottom' | 'center';

interface Props extends ViewProps {
  align?: AlignType;
  onMaskClick?: () => void;
}

interface PopupContentProps {
  onClose: () => void;
  data?: any;
}

interface showModalParams extends Props {
  content: (props: PopupContentProps) => React.ReactNode;
  maskClick?: boolean;
}

interface showSearchModalParams extends Props {
  onSearch: (text: string) => Promise<Record<string, any>[]>;
  renderItem: (item: Record<string, any>) => React.ReactNode;
  onSelect: (item: any) => void;
  type?: string;
  immediate?: boolean;
}

interface showPhotosParams {
  photos: string[];
  maskClick?: boolean;
  align?: AlignType;
}

interface showModalPickerParams {
  align?: AlignType;
  title: string;
  items: {label: string; value: string}[];
  onConfirm?: (val: unknown) => void;
  multiple?: boolean;
  selectItems?: string[];
}

interface showActionSheetParams {
  title: string;
  items: {label: string; value: string}[];
  onConfirm?: (val: unknown) => void;
  multiple?: boolean;
  readerItem?: (item: {label: string; value: string}, index: number) => React.ReactNode;
}

interface showAlertParams extends showModalParams {
  title?: string;
  showCancel?: boolean;
  onConfirm?: (onClose: () => void) => void;
  scrollViewMaxHeight?: number;
}

interface showConfirmParams {
  title: string;
  content: string;
  showCancel?: boolean;
  onConfirm?: (onClose: () => void) => void;
  onCancel?: () => void;
  cancelText?: string;
  conformText?: string;
}

interface showUpgradeParams {
  content: string;
  showCancel: boolean;
  url: string;
}

interface showInputAlertParams {
  title?: string;
  onConfirm?: (onClose: () => void, inputValue: string, extraInputValue?: string) => void;
  placeholder?: string;
  extraPlaceholder?: string; //顶部额外输入框
}

/**
 * 逾期查询发送风险告知函
 */
interface showInformLetterDialogParams {
  onConfirm?: (onClose: () => void) => void;
  onCancel?: () => void;
  maskClick?: boolean;
  align?: AlignType;
}

let defaultClick: () => void;
//设置为undefined时h5点击报错
if (process.env.TARO_ENV === 'h5') {
  defaultClick = () => {};
}

/**
 * 组件名称：全局弹出层
 * 组件描述：全局弹出框，支持命令式调用
 */
const CyGlobPopup: FC<Props> = ({children, align, onMaskClick}) => {
  return (
    <View className='comp-cy-glob-popup'>
      <View className='comp-cy-popup-mask' onClick={onMaskClick ? onMaskClick : () => {}}></View>
      <View className={classNames([align || 'center', 'content'])}>{children}</View>
    </View>
  );
};

export const showModal = ({content, align, maskClick}: showModalParams) => {
  let rootNode: any;
  const onClose = () => {
    console.log('close====');
    rootNode?.destroy();
    rootNode = null;
  };
  rootNode = new RootSiblingsManager(
    (
      <CyGlobPopup onMaskClick={maskClick ? onClose : () => {}} align={align}>
        {content({onClose})}
      </CyGlobPopup>
    ),
  );
  return onClose;
};

const SearchSelectorBox = ({onSearch, renderItem, onSelect, type, immediate}: showSearchModalParams) => {
  const [searchTxt, setSearchTxt] = useState('');
  const [resData, setResData] = useState<Record<string, any>[]>([]);

  useEffect(() => {
    if (immediate) {
      startSearch();
    }
  }, []);
  const onInput = (e: any) => {
    setSearchTxt(e.target.value);
  };
  const startSearch = async () => {
    console.log('start startSearch', searchTxt);
    const searchResList = await onSearch(searchTxt);
    setResData(searchResList || []);
  };

  return (
    <View className='search-selector-dialog'>
      <View className='row'>
        <CyInput placeholder='请输入关键字搜索' onInput={onInput}></CyInput>
        <CyButton size='mini' onClick={startSearch} type='primary'>
          查询
        </CyButton>
      </View>
      <ScrollView scrollY className='search-selector-content'>
        {resData.map((it, i) => (
          <View key={i} className='search-selector-item' onClick={() => onSelect(it)}>
            {renderItem(it)}
          </View>
        ))}
      </ScrollView>
    </View>
  );
};

export const showSearchSelectDialog = (props: showSearchModalParams) => {
  let rootNode: any;
  const onClose = () => {
    console.log('close====');
    rootNode?.destroy();
    rootNode = null;
  };
  rootNode = new RootSiblingsManager(
    (
      <CyGlobPopup align='center' onMaskClick={onClose}>
        <SearchSelectorBox {...props} />
      </CyGlobPopup>
    ),
  );
  return onClose;
};

export const showAlert = ({
  content,
  align,
  maskClick,
  title,
  showCancel,
  onConfirm,
  scrollViewMaxHeight,
}: showAlertParams) => {
  const style: string | CSSProperties = {};
  if (scrollViewMaxHeight !== undefined && scrollViewMaxHeight > 0) {
    style.maxHeight = scrollViewMaxHeight;
  }
  return showModal({
    content: ({onClose}) => (
      <View className='alert-box'>
        {title ? <View className='alert-title'>{title}</View> : null}
        <View className='alert-content' style={style}>
          <ScrollView>
            <View>{content({onClose})}</View>
          </ScrollView>
        </View>
        <View className='alert-footer'>
          <View className='footer-btn confirm-btn' onClick={onConfirm ? () => onConfirm(onClose) : onClose}>
            确认
          </View>
          {showCancel ? (
            <View className='footer-btn' onClick={onClose}>
              取消
            </View>
          ) : null}
        </View>
      </View>
    ),
    align,
    maskClick,
  });
};

export const showConfirm = ({
  content,
  title,
  showCancel,
  onConfirm,
  onCancel,
  cancelText,
  conformText = '确认',
}: showConfirmParams) => {
  return showModal({
    content: ({onClose}) => (
      <View className='confirm-box'>
        {title && title !== '' && <View className='confirm-title'>{title}</View>}
        {content && title !== '' && <View className='confirm-content'>{content}</View>}
        <View className='confirm-footer'>
          {showCancel && (
            <View
              className='confirm-footer-btn cancel-btn'
              onClick={() => {
                onCancel?.();
                onClose?.();
              }}>
              {cancelText || '取消'}
            </View>
          )}
          <View className='confirm-footer-btn confirm-btn' onClick={onConfirm ? () => onConfirm(onClose) : onClose}>
            {conformText}
          </View>
        </View>
      </View>
    ),
    align: 'center',
    maskClick: false,
  });
};

export const showInputAlert = ({
  title,
  onConfirm,
  extraPlaceholder = '',
  placeholder = '备注',
}: showInputAlertParams) => {
  return showModal({
    content: ({onClose}) => {
      // const [inputValue, setInputValue] = useState('');
      let inputValue = '';
      let extraInputValue = '';
      const textareaConfirm = () => {
        Taro.hideKeyboard();
      };
      const textareaInput = (e: {detail: {value: string}}) => {
        console.log(e);
        inputValue = e.detail.value;
      };
      const inputInput = (e: {detail: {value: string}}) => {
        console.log(e);
        extraInputValue = e.detail.value;
      };
      return (
        <View className='alert-box'>
          {title ? <View className='alert-title'>{title}</View> : null}
          {extraPlaceholder && extraPlaceholder.length > 0 && (
            <View className='alert-content'>
              <Input
                style={{height: 30}}
                placeholder={extraPlaceholder}
                maxlength={20}
                type='number'
                onConfirm={textareaConfirm}
                focus
                onInput={inputInput}
              />
            </View>
          )}
          <View className='alert-content'>
            <Textarea
              style={{height: 80}}
              placeholder={placeholder}
              maxlength={50}
              onConfirm={textareaConfirm}
              confirmType='done'
              focus
              onInput={textareaInput}
            />
          </View>
          <View className='alert-footer'>
            <View className='footer-btn' onClick={onClose}>
              取消
            </View>
            <View
              className='footer-btn confirm-btn'
              onClick={
                onConfirm
                  ? () => {
                      if (extraPlaceholder && extraPlaceholder.length > 0) {
                        onConfirm(onClose, inputValue, extraInputValue);
                      } else {
                        onConfirm(onClose, inputValue);
                      }
                    }
                  : onClose
              }>
              提交
            </View>
          </View>
        </View>
      );
    },
    align: 'center',
    maskClick: false,
  });
};

export const getPhotoData = (path: string) => {
  return new Promise<string>(async (resolve, reject) => {
    try {
      return resolve('');
      // return resolve(`data:image/png;base64,${data.file}` ?? '');
    } catch (e) {
      return resolve('');
    }
  });
};
/**
 * 输入框记录
 * @param event 输入事件
 * @param currentData 触发数据
 */
const onInputInformLetterDialog = (
  event: BaseEventOrig<InputProps.inputEventDetail>,
  currentData: API.WhetherMeetSendRiskMsgResponse,
) => {
  currentData.message = event.detail.value;
};

/**
 * 逾期查询发送风险告知函
 * @param param0
 * @returns
 */
export const showInformLetterDialog = async (
  data: API.WhetherMeetSendRiskMsgResponse,
  {onConfirm, onCancel, maskClick, align}: showInformLetterDialogParams,
) => {
  let rootNode: any;
  const onClose = () => {
    rootNode?.destroy();
    rootNode = null;
  };
  rootNode = new RootSiblingsManager(
    (
      <CyGlobPopup align={align} onMaskClick={onClose}>
        <View className='overdue-content-dialog'>
          <View className='confirm-title'>风险告知函</View>
          <View className='overdue-content-row'>
            客户经理手机号：
            <CyInput
              className='confirm-right'
              placeholder='请输入客户经理手机号'
              onInput={event => {
                onInputInformLetterDialog(event, data);
              }}
              type='text'
              value={data.message}></CyInput>
          </View>
          <View className='overdue-content-row'>
            客户姓名：
            <View className='confirm-right'>{data.name}</View>
          </View>
          <View className='overdue-content-row'>
            客户手机号：
            <View className='confirm-right'>{data.cPhone}</View>
          </View>
          <View className='overdue-content-row'>
            发送渠道：
            <View className='confirm-right'>短信</View>
          </View>
          <View className='alert-footer'>
            <View className='footer-btn confirm-btn' onClick={onConfirm ? () => onConfirm(onClose) : onClose}>
              确认
            </View>
            <View className='footer-btn' onClick={onClose}>
              取消
            </View>
          </View>
        </View>
      </CyGlobPopup>
    ),
  );
  return onClose;
};

export const initNavigationBarItem = (onClick: () => void, multiple = false) => {
  Taro.Current.rnNavigationRef?.current.setOptions({
    headerRight: () => (
      <View
        style={{
          height: 30,
          width: 80,
          display: 'flex',
          flexDirection: 'row',
          justifyContent: 'flex-end',
          alignItems: 'center',
        }}>
        {multiple && (
          <View
            style={{
              display: 'flex',
              justifyContent: 'center',
              alignItems: 'center',
              borderRadius: 4,
              borderWidth: 1,
              borderColor: '#666',
              fontSize: 12,
              paddingLeft: 8,
              paddingRight: 8,
            }}
            onClick={() => {
              const deviceOrientation = Taro.getSystemInfoSync().deviceOrientation;
              if (deviceOrientation === 'landscape') {
                lockToPortrait();
              } else {
                lockToLandscape();
              }
            }}>
            {Taro.getSystemInfoSync().deviceOrientation === 'landscape' ? '竖屏' : '横屏'}
          </View>
        )}
        <View
          style={{
            height: 30,
            width: 40,
            display: 'flex',
            justifyContent: 'center',
            alignItems: 'center',
          }}
          onClick={onClick}>
          <Image src={Icon_Info} style={{height: 16, width: 16}} />
        </View>
      </View>
    ),
  });
};

export const showModalPicker = ({align, ...props}: showModalPickerParams) => {
  return showModal({
    content: ({onClose}) => <ModalPicker {...props} onClose={onClose} />,
    align,
  });
};

const ModalPicker = ({
  items,
  title,
  onConfirm,
  onClose,
  multiple,
  selectItems,
}: Omit<showModalPickerParams, 'align'> & PopupContentProps) => {
  const [val, setVal] = useState<string>();
  const confirmHandler = () => {
    onConfirm?.(val);
    onClose();
  };
  const handleChange = (e: any) => {
    if (e.detail.value) {
      setVal(e.detail.value);
    }
  };
  return (
    <View className='picker-content'>
      <View className='picker-title'>{title}</View>
      <ScrollView className='picker-list' scrollY scrollWithAnimation>
        {multiple ? (
          <CyCheckbox options={items} onChange={handleChange} selectOptions={selectItems}></CyCheckbox>
        ) : (
          <RadioGroup onChange={handleChange}>
            {items.map(item => (
              <Label className='picker-list__label' for={item.value} key={item.value}>
                <Radio className='picker-list__radio' value={item.value}>
                  {item.label}
                </Radio>
              </Label>
            ))}
          </RadioGroup>
        )}
      </ScrollView>
      <View className='picker-footer'>
        <View className='picker-btn' onClick={onClose}>
          取消
        </View>
        <View className='picker-btn' onClick={confirmHandler || (() => {})}>
          确认
        </View>
      </View>
    </View>
  );
};

export const showActionSheet = ({...props}: showActionSheetParams) => {
  return showModal({
    content: ({onClose}) => <ActionSheet {...props} onClose={onClose} />,
    align: 'bottom',
  });
};

const ActionSheet = ({
  items,
  title,
  onConfirm,
  onClose,
  readerItem,
}: Omit<showActionSheetParams, 'align'> & PopupContentProps) => {
  const [val, setVal] = useState<string>();

  const handleChange = (value: string) => {
    console.log(value);
    if (value) {
      setVal(value);
      onConfirm?.(value);
      onClose && onClose();
    }
  };

  let height = Math.max(Math.min(50 * items.length, 500), 50);
  return (
    <View className='sheet-content'>
      <View className='flex sheet-title-container'>
        <View className='sheet-title' style={{height: 50, lineHeight: 50}}>
          {title}
        </View>
        <Image src={clearImg} className='sheet-img' onClick={onClose}></Image>
      </View>
      <View style={readerItem ? {maxHeight: 500} : {height}}>
        <ScrollView className='sheet-list' scrollY scrollWithAnimation>
          <View>
            {items.map((item, index) => {
              return readerItem ? (
                <Fragment key={index}>{readerItem(item, index)}</Fragment>
              ) : (
                <View
                  className='sheet-item'
                  style={{height: 50, lineHeight: 50}}
                  key={index}
                  onClick={() => {
                    handleChange(item.value);
                  }}>
                  {item.label}
                </View>
              );
            })}
          </View>
        </ScrollView>
      </View>
      <CySafeArea showHeader={false}></CySafeArea>
    </View>
  );
};

export const showAgreementPopup = ({
  base64,
  onReject,
  onSubmit,
}: {
  base64: string;
  onReject: () => void;
  onSubmit: () => void;
}) => {
  return showModal({
    content: ({onClose}) => (
      <CyPdfAgreementPopup
        pdfBase64={base64}
        onClose={onClose}
        onReject={onReject}
        onSubmit={onSubmit}></CyPdfAgreementPopup>
    ),
    align: 'bottom',
    maskClick: false,
  });
};

export const showFacePopup = () => {
  showModal({
    content: () => <View style={{background: '#fff'}}>hhhh</View>,
  });
};

export default CyGlobPopup;
