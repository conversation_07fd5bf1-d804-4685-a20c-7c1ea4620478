import {FC} from 'react';
import {CommonEvent, View, ViewProps} from '@tarojs/components';
import {copyToClipboard} from '../../utils/common';
import './index.scss';

type Props = {
  copyText?: string;
  copyToast?: string;
};

/**
 * 组件名称：长按复制组件
 * 组件描述：长按复制文字至剪切板
 */
const CyLongPressCopyView: FC<Props & ViewProps> = props => {
  //长按复制
  const longpressBankInfo = (event: CommonEvent) => {
    props.onLongPress && props.onLongPress(event);
    copyToClipboard(props.copyText, props.copyToast);
  };
  return (
    <View
      {...props}
      onLongPress={e => {
        longpressBankInfo(e);
      }}>
      {props.children}
    </View>
  );
};

export default CyLongPressCopyView;
