.pageContainer {
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  background-color: #fff;
  padding: 160px 40px 0;
  min-height: 100vh;
  box-sizing: border-box;
  text-align: center;
  background: url(../../../assets/images/credit-apply/credit-fail-bg.png) no-repeat;
  background-size: 100% 100%;
}

.pageImg {
  width: 280px;
  height: 280px;
}

.value {
  margin-top: 120px;
  margin-bottom: 80px;
}
.text {
  color: #333;
  font-size: 32px;
  font-weight: bold;
  line-height: 1;
}
.note {
  font-size: 24px;
  color: #999;
  margin-top: 30px;
  line-height: 48px;
}

.qrcodeContainer {
  display: flex;
  justify-content: space-between;
  width: 100%;
  margin-top: 120px;
  padding: 48px 100px;
  background: #FFFFFF;
  box-shadow: 0px 0px 8px 0px rgba(176,176,176,0.3);
  border-radius: 16px;
  box-sizing: border-box;
  .codeItem {
    width: 160px;
    font-size: $font-size-normal;
    color: $color-text-secondary;
    text-align: center;
  }
  .codeImg {
    width: 160px;
    height: 160px;
    margin-bottom: 16px;
  }
}

.result_tip {
  position: absolute;
  bottom: 40px;
  width: 100%;
  color: #333;
  font-size: 24px;
}