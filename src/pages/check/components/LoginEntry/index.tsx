import {CySafeArea} from 'hncy58-taro-components';
import {ScrollView, Text, View} from '@tarojs/components';
import Taro from '@tarojs/taro';
import {router} from '~utils/common';
import styles from './index.module.scss';

const goLogin = () => {
  router.replace({
    url: '/modules/user/login/index',
  });
};
export default () => {
  return (
    <View className={styles.loginBtBar} onClick={goLogin}>
      <View className={styles.loginEntry}>
        <Text className={styles.tips}>您未登录，登录后即可申请/查看额度哦</Text>
        <Text className={styles.loginBtn}>立即登录</Text>
      </View>
      <CySafeArea position='bottom' />
    </View>
  );
};
