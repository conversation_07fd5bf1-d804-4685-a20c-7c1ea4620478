import { useEffect, useRef } from 'react';

type PollingOptions<T> = {
  fetchFn: () => Promise<T>;
  onSuccess: (data: T) => void;
  onError?: (error: unknown) => void;
  interval?: number;
  maxDuration?: number;
  stopCondition?: (data: T) => boolean;
  onMaxAttemptsReached?: () => void;
};

/**
 * 
 * @example 
 * usePolling({
      fetchFn: () => getLendingState({ billNo }),
      onSuccess: ({ state }) => setResult(state),
      interval: 5000,
      maxDuration: 60000,
      stopCondition: ({ state }) => state !== 'PROCESSING',
    });
 */

export default function usePolling<T>({
  fetchFn,
  onSuccess,
  onError,
  interval = 5000,
  maxDuration,
  stopCondition,
  onMaxAttemptsReached
}: PollingOptions<T>) {
  const timerRef = useRef<NodeJS.Timeout>();
  const timeoutRef = useRef<NodeJS.Timeout>();
  const attemptCount = useRef(0);
  const maxAttempts = maxDuration ? Math.floor(maxDuration / interval) : Infinity;

  useEffect(() => {
    const executePolling = async () => {
      try {
        const data = await fetchFn();
        if (data === null) {
          clearPolling();
          return;
        }
        onSuccess(data);
        attemptCount.current += 1;

        if (stopCondition?.(data)) {
          clearPolling();
        } else if (attemptCount.current >= maxAttempts) {
          clearPolling();
          onMaxAttemptsReached?.()
        }
      } catch (error) {
        onError?.(error);
        clearPolling();
      }
    };

    const clearPolling = () => {
      if (timerRef.current) clearInterval(timerRef.current);
      if (timeoutRef.current) clearTimeout(timeoutRef.current);
    };

    // 立即执行首次请求
    // executePolling();
    
    // 启动轮询
    timerRef.current = setInterval(executePolling, interval);

    // 设置最大持续时间
    if (maxDuration) {
      timeoutRef.current = setTimeout(() => {
        clearPolling();
        onMaxAttemptsReached?.();
      }, maxDuration);
    }

    return clearPolling;
  }, [fetchFn, onSuccess, interval, maxDuration, stopCondition]);
}