import {useState} from 'react';
import {Image, ScrollView, View} from '@tarojs/components';
import AddBankCard from '../AddBankCard';
import ListItem from './ListItem';
import styles from './index.module.scss';
import infoIcon from '~images/bankcard/<EMAIL>'
import { router } from '~utils/common';

interface ListProps {
  list: API.IBankCardInfo[];
}

export default ({list}: ListProps) => {
  const [refreshing, setRefreshing] = useState(false);

  const handleRefresh = () => {
    setRefreshing(true);
    setTimeout(() => {
      setRefreshing(false);
    }, 2000);
  };

  const handleToDetail = (ite: API.IBankCardInfo) => {
    router.push({
      url: `/modules/bankcard/bankcard-detail/index?bankCardInfo=${JSON.stringify(ite)}`,
    });
  };
  
  const handleAddBankCardClick = () => {
    router.push({
      url: '/modules/bankcard/bankcard-binding/index',
    });
  }
  return (
    <View className={styles.listLayout}>
      <View className={styles.listTop}>
        <View className={styles.listTitle}>
          我的银行卡
          {/* <View className={styles.listTag}><Image src={infoIcon} className={styles.infoIcon}></Image>信息保障中</View> */}
        </View>
        <View className={styles.listNum}>共 {list.length} 张</View>
      </View>
      <View className={styles.listContent}>
        {list.map((ite, index) => (
          <ListItem key={ite.acctId} {...ite} onClick={() => handleToDetail(ite)} />
        ))}
        <AddBankCard onClick={handleAddBankCardClick} />
      </View>
    </View>
  );
};
