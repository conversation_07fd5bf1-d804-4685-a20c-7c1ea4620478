export const pages = [
  // 'modules/loan_certificate/index/index',
  // 'modules/product/operating-index/index',
  // 'modules/lending/lending/index',
  // 'modules/lending/lending-result/index',
  // 'modules/verify_mpsdk/index/index',
  // 'modules/user/login/index',
  // 'modules/credit_authorization/credit-fail/index',
  // 'modules/credit_authorization/credit-wait/index',
  // 'modules/credit_authorization/credit-auditing/index',
  // 'modules/credit_authorization/credit-success/index',
  // 'modules/credit_authorization/upload-info/index',
  // 'modules/credit_authorization/identify/index',
  // 'modules/credit_authorization/company-info/index',
  // 'modules/user/amount/index',
  // 'modules/user/baseinfo/index',
  // 'modules/user/workinfo/index',
  // 'modules/user/contactinfo/index',
  // 'modules/user/set-password/index',
  // 'modules/user/set-password-verifycode/index',
  // 'modules/user/password/index/index',
  // 'modules/user/password/success/index',
  // 'modules/user/edit-message/index',
  // 'modules/user/upload-material/index',
  // 'modules/bankcard/bankcard-binding/index',
  // 'modules/bankcard/bankcard-list/index',
  // 'modules/bankcard/bankcard-detail/index',
  // 'modules/repayment/entry/index',
  // 'modules/repayment/repayment-periods/index',
  // 'modules/repayment/repayment-confirm/index',
  // 'modules/repayment/repayment-all-list/index',
  // 'modules/repayment/repayment-result/index?repaymentRegisterId=2NnbVkTauJ8YkVX8Yl2Hr9',
  // 'modules/webview/test/index',
  // 'modules/user/change-password/index',
  // 'modules/user/upload-idcard/index',
  // 'modules/user/intercept-login/index',
  // 'modules/agreement/privacy/index',
  // 'modules/raise_amount/history/index',
  // 'modules/raise_amount/state/index',
  // 'modules/raise_amount/intelligent-entry/index',
  // 'modules/raise_amount/index/index',
  // 'modules/loanAndRepayment/index/index',
  // 'modules/loanAndRepayment/repaymentRecordDetail/index',
  // 'modules/manager/contact/index',
  // 首页
  'pages/check/index',
  // 我的
  'pages/mine/index',
  // #PAGE
];

let subpackages: any[] = [];

// 小程序拆包
if (process.env.TARO_ENV === 'weapp') {
  subpackages = [
    {
      root: 'modules/verify_mpsdk',
      pages: ['index/index'],
    },
    {
      root: 'modules/credit_authorization',
      pages: [
        'credit-fail/index',
        'credit-success/index',
        'credit-wait/index',
        'credit-auditing/index',
        'identify/index',
        'upload-info/index',
        'company-info/index',
      ],
    },
    {
      root: 'modules/user',
      pages: [
        'login/index',
        'amount/index',
        'baseinfo/index',
        'workinfo/index',
        'contactinfo/index',
        'set-password/index',
        'set-password-verifycode/index',
        'change-password/index',
        'setting/index',
        'intercept-login/index',
        'upload-idcard/index',
        'edit-message/index',
        'upload-material/index',
        'password/index/index',
        'password/success/index'
      ],
    },
    {
      root: 'modules/repayment',
      pages: [
        'entry/index',
        'repayment-periods/index',
        'repayment-confirm/index',
        'repayment-result/index',
        'repayment-all-list/index',
      ],
    },
    {
      root: 'modules/lending',
      pages: ['lending/index', 'lending-result/index'],
    },
    {
      root: 'modules/agreement',
      pages: ['privacy/index'],
    },
    {
      root: 'modules/webview',
      pages: ['index/index'],
    },
    {
      root: 'modules/bankcard',
      pages: ['bankcard-list/index', 'bankcard-binding/index', 'bankcard-detail/index'],
    },
    {
      root: 'modules/bill',
      pages: ['details/index'],
    },
    {
      root: 'modules/product_operating',
      pages: ['index/index'],
    },
    {
      root: 'modules/loan_certificate',
      pages: ['index/index', 'list/index', 'details/index'],
    },
    {
      root: 'modules/raise_amount',
      pages: ['index/index', 'history/index', 'state/index', 'intelligent-entry/index'],
    },
    {
      root: 'modules/loanAndRepayment',
      pages: ['index/index', 'repaymentRecordDetail/index']
    },
    {
      root: 'modules/manager',
      pages: ['contact/index']
    },
  ];
}
export default defineAppConfig({
  pages,
  // requiredPrivateInfos: ['getFuzzyLocation'],
  // permission: {
  //   'scope.userFuzzyLocation': {
  //     desc: '您的位置信息将用于定位当前位置',
  //   },
  // },
  requiredPrivateInfos: ['getLocation'],
  permission: {
    'scope.userLocation': {
      desc: '您的位置信息将用于定位当前位置',
    },
  },
  lazyCodeLoading: 'requiredComponents',
  plugins: {
    captcha: {
      version: '1.0.4',
      provider: 'wxb302e0fc8ab232b4',
    },
  },
  subpackages,
  window: {
    backgroundTextStyle: 'light',
    navigationBarBackgroundColor: '#fff',
    navigationBarTitleText: '',
    navigationBarTextStyle: 'black',
    backgroundColor: '#fff',
  },
  tabBar: {
    custom: true,
    list: [
      {
        pagePath: 'pages/check/index',
        text: '首页',
      },
      {
        pagePath: 'pages/mine/index',
        text: '个人中心',
      },
    ],
  },
  preloadRule: {
    'pages/check/index': {
      network: 'all',
      packages: ['modules/user'],
    },
  },
});
// 'getFuzzyLocation',
