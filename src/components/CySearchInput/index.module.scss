.search-container {
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  position: relative;
}

.input {
  width: 100%;
  text-align: right;
}

.dropdown-container {
  position: absolute;
  top: calc(100% + 10px);
  right: 0;
  width: 100%;
  padding: 10px 0;
  max-height: 400px;
  background-color: #fff;
  border-radius: 4px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  z-index: 100;
}

.dropdown-content {
  max-height: 364px;
  overflow-y: auto;
}

.close-btn {
  position: absolute;
  top: 10px;
  right: 10px;
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  background-color: rgba(240, 240, 240, 0.8);
  font-size: 28px;
  line-height: 28px;
  color: #666;
  cursor: pointer;
  z-index: 101;
  
  &:hover {
    background-color: #e0e0e0;
  }
}

.loading-indicator {
  padding: 20px;
  text-align: center;
  font-size: 28px;
  color: #999;
}

.item {
  padding: 20px;
  border-bottom: 1px solid #eee;
  cursor: pointer;
  font-size: 28px;
  text-align: left;
  
  &:last-child {
    border-bottom: none;
  }
  
  &:hover {
    background-color: #f5f5f5;
  }
} 