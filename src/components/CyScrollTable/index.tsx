import {CSSProperties, FC, ReactNode, useRef, useState} from 'react';
import {ScrollView, View} from '@tarojs/components';
import {desensitization} from '../../utils/common';
import CyEmpty from '../CyEmpty';
import CySimpleTable from '../CySimpleTable';
import './index.scss';

type Props = {
  dataSource: Record<string, number | string | boolean>[]; // 数据源
  columns: IColumns[]; // 表头
  totalSource?: Record<string, number | string | boolean>;
  needEncrypt?: boolean;
  onAction?: (info: Record<string, number | string | boolean>, flag: string, index: number) => void;
  onLoadMore?: () => void;
  finished?: boolean;
  loading?: boolean;
  multiHeader?: boolean;
  onColumnsClick?: (dataIndex: string) => void;
  cellHeight?: number;
};
export type IColumns = {
  title?: string | ReactNode;
  dataIndex: string;
  flex?: number;
  width?: number;
  fixed?: boolean; // 是否需要固定到左边---暂不可以
  style?: CSSProperties; // 单元格样式
  encrypt?: string; // 脱敏字段
  isAction?: boolean;
  // 显示操作文字的key
  actionLabelKey?: string;
  // 如果对应key没有值 用actionValue
  actionValue?: string;
  // 不要操作时显示的字段
  normalValue?: string;

  // 对应操作的code值
  actionCode?: string;
  showIcon?: boolean;
  canCall?: boolean;
  canDetail?: boolean;
  formatter?: <T extends string>(value: T) => string;
  alwaysEncrypt?: boolean;
  headerClick?: Function;
};

// 只读的字符串数组

// 把数组转换为字符串字面量联合类型
/**
 * 组件名称：扩展table
 * 组件描述：可固定行列的table
 */

const CyTable: FC<Props> = props => {
  const {columns, dataSource, totalSource, needEncrypt: needEncrypt = false, onAction} = props;
  const scrollColRef = useRef<any>();
  const scrollTotalRef = useRef<any>();
  console.log('encrypt', needEncrypt);
  const [showRows, setShowRows] = useState<number[]>([]);
  const [encrypt, setEncrypt] = useState(needEncrypt);
  let datas: Record<string, string | number | boolean>[] = [];
  const maxCell = 8;
  if (!dataSource) {
    return <CyEmpty></CyEmpty>;
  }

  let fixedColumns: IColumns[] = [];
  let unFixedColumns: IColumns[] = [];

  const noTotalSource: boolean = !totalSource || Object.keys(totalSource).length === 0;
  const showBottomTotalInfo = dataSource.length >= maxCell && !noTotalSource;
  if (!showBottomTotalInfo && !noTotalSource) {
    datas = [...dataSource, totalSource!];
  } else {
    datas = dataSource;
  }

  columns.map(item => {
    if (item.fixed) {
      fixedColumns.push(item);
    } else {
      unFixedColumns.push(item);
    }
  });
  const onMainScroll = (e: {detail: {scrollLeft: any}}) => {
    if (scrollColRef.current && scrollColRef.current.scrollToOffset) {
      scrollColRef.current.scrollToOffset(e.detail.scrollLeft, false);
    }
    if (scrollTotalRef.current && scrollTotalRef.current.scrollToOffset) {
      scrollTotalRef.current.scrollToOffset(e.detail.scrollLeft, false);
    }
  };
  const encryptValue = (str: number | string, key?: string) => {
    if (key === 'name' || key === 'certId' || key === 'mobile') {
      return desensitization(`${str}`, key);
    }
    return str;
  };
  const clickFixedItem = (flag: string, rowIndex: number) => {
    if (flag === 'decrypt') {
      if (!needEncrypt) return;
      setEncrypt(false);
      let atIndex = showRows.indexOf(rowIndex);
      if (atIndex !== -1) {
        const rows = showRows.filter((item, index) => {
          return index !== atIndex;
        });
        setShowRows(rows);
      } else {
        const rows = [...showRows, rowIndex];
        setShowRows(rows);
      }
      console.log('showRows', showRows);
    } else {
      onAction && onAction(dataSource[rowIndex], flag, rowIndex);
    }
  };

  // 判断某行是否需要脱敏
  const isHide = (rowIndex: number) => {
    return showRows.includes(rowIndex);
  };

  return (
    <View className='comp-cy-table'>
      <View className='comp-cy-table-col'>
        <View className='comp-cy-table-col-fixed'>
          <CySimpleTable.Row row={0}>
            {fixedColumns.map((item, index) => {
              return (
                <CySimpleTable.Item key={`${item.dataIndex}${index}`} width={`${item.width ? item.width : '180'}px`}>
                  <View className='title'>{item.title}</View>
                </CySimpleTable.Item>
              );
            })}
          </CySimpleTable.Row>
        </View>
        <View className='comp-cy-table-col-sroll'>
          <ScrollView scrollX ref={scrollColRef}>
            <CySimpleTable.Row row={0}>
              <View className='comp-cy-table-titlecontent'>
                {unFixedColumns.map((item, index) => {
                  return (
                    <CySimpleTable.Item
                      key={`${item.dataIndex}${index}`}
                      width={`${item.width ? item.width : '180'}px`}>
                      <View className='title'>{item.title}</View>
                    </CySimpleTable.Item>
                  );
                })}
              </View>
            </CySimpleTable.Row>
          </ScrollView>
        </View>
      </View>
      <ScrollView className='comp-cy-table-scrolly' scrollY>
        <View className='comp-cy-table-container'>
          <View className='comp-cy-table-left'>
            <CySimpleTable>
              <View className='fixed-left-content'>
                {datas.map((item, index) => {
                  const isTotalItem = index === datas.length - 1 && !showBottomTotalInfo && !noTotalSource;

                  return (
                    <CySimpleTable.Row row={index} key={index}>
                      {fixedColumns.map((fixedItem, fixedIndex) => {
                        const str = item[fixedItem.dataIndex] as string;
                        const itemValue = encryptValue(str, fixedItem.encrypt);
                        return (
                          <CySimpleTable.Item
                            key={`${fixedItem.dataIndex}${fixedIndex}`}
                            width={`${fixedItem.width ? fixedItem.width : '180'}px`}>
                            {fixedItem.isAction ? (
                              <View onClick={() => clickFixedItem('action', index)} className='value action-value'>
                                {fixedItem.actionValue}
                              </View>
                            ) : (
                              <View
                                onClick={() => clickFixedItem('decrypt', index)}
                                className={`value ${isTotalItem ? 'total-value' : ''}`}>
                                {encrypt || !isHide(index) ? itemValue : item[fixedItem.dataIndex]}
                              </View>
                            )}
                          </CySimpleTable.Item>
                        );
                      })}
                    </CySimpleTable.Row>
                  );
                })}
              </View>
            </CySimpleTable>
          </View>

          <View className='comp-cy-table-right'>
            <ScrollView
              className='comp-cy-table-scrollx'
              scrollX
              scrollWithAnimation
              clip={false}
              onScroll={onMainScroll}>
              <View>
                <CySimpleTable>
                  <View className='right-content'>
                    {datas.map((item, index) => {
                      const isTotalItem = index === datas.length - 1 && !showBottomTotalInfo && !noTotalSource;
                      return (
                        <CySimpleTable.Row row={index} key={index}>
                          {unFixedColumns.map((unFixedItem, unFixedIndex) => {
                            const str = item[unFixedItem.dataIndex] as string;
                            const itemValue = encryptValue(str, unFixedItem.encrypt);
                            return (
                              <CySimpleTable.Item
                                key={`${unFixedItem.dataIndex}${unFixedIndex}`}
                                width={`${unFixedItem.width ? unFixedItem.width : '180'}px`}>
                                {unFixedItem.isAction ? (
                                  <View
                                    onClick={() => clickFixedItem(unFixedItem.actionValue!, index)}
                                    className='value action-value'>
                                    {unFixedItem.actionValue}
                                  </View>
                                ) : (
                                  <View className={`value ${isTotalItem ? 'total-value' : ''}`}>
                                    {encrypt || !isHide(index) ? itemValue : item[unFixedItem.dataIndex]}
                                  </View>
                                )}
                              </CySimpleTable.Item>
                            );
                          })}
                        </CySimpleTable.Row>
                      );
                    })}
                  </View>
                </CySimpleTable>
              </View>
            </ScrollView>
          </View>
        </View>
      </ScrollView>
      {totalSource && showBottomTotalInfo && (
        <View className='comp-cy-table-col'>
          <View className='comp-cy-table-col-fixed'>
            <CySimpleTable.Row row={0}>
              {fixedColumns.map((item, index) => {
                return (
                  <CySimpleTable.Item key={`${item.dataIndex}${index}`} width={`${item.width ? item.width : '180'}px`}>
                    <View className='value total-value'>{totalSource[item.dataIndex]}</View>
                  </CySimpleTable.Item>
                );
              })}
            </CySimpleTable.Row>
          </View>
          <View className='comp-cy-table-col-sroll'>
            <ScrollView scrollX ref={scrollTotalRef}>
              <CySimpleTable.Row row={0}>
                <View className='comp-cy-table-titlecontent'>
                  {unFixedColumns.map((item, index) => {
                    return (
                      <CySimpleTable.Item
                        key={`${item.dataIndex}${index}`}
                        width={`${item.width ? item.width : '180'}px`}>
                        <View className='value total-value'>{totalSource[item.dataIndex]}</View>
                      </CySimpleTable.Item>
                    );
                  })}
                </View>
              </CySimpleTable.Row>
            </ScrollView>
          </View>
        </View>
      )}
    </View>
  );
};

export default CyTable;
