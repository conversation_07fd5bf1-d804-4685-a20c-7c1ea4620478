import {FC} from 'react';
import {ActivityIndicator, StyleSheet, Text, View} from 'react-native';
import './index.scss';

interface Props {
  finished: boolean;
  loading: boolean;
  dataLength: number;
}

/**
 * 组件名称：加载更多
 * 组件描述：上拉加载更多
 */
const CyLoadMore: FC<Props> = ({finished, loading, dataLength}) => {
  return (
    <View>
      {!finished && loading && dataLength !== 0 && (
        ////加载中。。。
        <View style={styles.loadingCon}>
          <ActivityIndicator size='small' color='#666' />
          <Text style={styles.loadingText}>加载中...</Text>
        </View>
      )}
      {!finished && !loading && dataLength !== 0 && (
        ////加载中。。。
        <View style={styles.loadingCon}>
          <Text style={styles.loadingText}>上拉加载更多</Text>
        </View>
      )}
      {finished && !loading && dataLength !== 0 && (
        //没有更多数据显示
        <View>
          <Text style={styles.noMoreDataText}>没有更多数据</Text>
        </View>
      )}
    </View>
  );
};

export default CyLoadMore;

const styles = StyleSheet.create({
  loadingCon: {
    display: 'flex',
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    height: 50,
  },
  loadingText: {
    color: '#666',
    fontSize: 14,
    marginLeft: 10,
  },
  noMoreDataText: {
    color: '#666',
    fontSize: 14,
    textAlign: 'center',
    height: 50,
    lineHeight: 50,
  },
});
