import { Image, View } from "@tarojs/components"
import styles from "./index.module.scss"
import CardItem from "../components/loanRepaymentItem"
import { useEffect, useRef, useState } from "react"
import { getHistoryRepaymentList } from '~services/loadAndRepayment'
import { hideLoading, router, showLoading } from "~utils/common"
import dayjs from "dayjs"
import Empty from "~components/Empty"
import emptyImg from '~images/repayment/<EMAIL>'
import FilterItem from "../components/filterItem"
import PullRefresh from "~components/PullRefresh"

type RepaymentStatus = 'SUCCESS' | 'LENDI' | 'FAIL'

export const STATUS: Record<RepaymentStatus, string> = {
  SUCCESS: '扣款成功',
  LENDI: '处理中',
  FAIL: '扣款失败'
}

export interface RepaymentRecordsItem {
  repaymentDay: string;
  repayAmt: number;
  repayStatus: RepaymentStatus;
  txnTypeName: string;
  cardNoLast: string;
  bankCardName: string;
  failReason: string;
}

export default () => {
  const currentDate = useRef<string>('')
  const [loading, setLoading] = useState<boolean>(false)
  const [refreshing, setRefreshing] = useState(false)
  const [list, setList] = useState<RepaymentRecordsItem[]>([])
  useEffect(() => {
    currentDate.current = new Date().getFullYear() + ''
    queryData()
  }, [])
  const queryData = async () => {
    try {
      showLoading();
      const res = await getHistoryRepaymentList({
        startDate: `${currentDate.current}01`,
        endDate: `${currentDate.current}12`,
      })
      let tempList: RepaymentRecordsItem[] = []
      res?.list.forEach((item: any) => {
        if (item.list && item.list.length) {
          tempList = [...tempList, ...item.list]
        }
      })
      setList(tempList)
    } catch (error) {
      console.log(error)
    } finally {
      hideLoading();
      setLoading(true)
      setRefreshing(false)
    }
  }

  const handleConfirm = (value: string) => {
    currentDate.current = value
    queryData()
  }

  const handleRefresh = () => {
    setRefreshing(true)
    queryData()
  }

  const handleToDetail = (item: RepaymentRecordsItem) => {
    router.push({
      url: `/modules/loanAndRepayment/repaymentRecordDetail/index?info=${JSON.stringify(item)}`,
    })
  }
  return (
    <View className={styles.repaymentRecordPage}>
      <FilterItem date={currentDate.current} onConfirm={handleConfirm} />
      {loading ? (
        <PullRefresh
          className={styles.scrollView}
          onRefresh={handleRefresh}
          refreshing={refreshing}
          refresherBackground="#f6f6f6"
        >
          {
            list.length ?
              list.map((item, index) => (
                <CardItem
                  key={index}
                  type='repayment'
                  source={item.txnTypeName}
                  amount={item.repayAmt}
                  date={dayjs(item.repaymentDay).format('YYYY.MM.DD')}
                  status={<View className={styles[`${item.repayStatus}`]}>{STATUS[item.repayStatus]}</View>}
                  onClick={() => handleToDetail(item)}
                />
              )) : <View className={styles.emptyContainer}>
                <Empty>
                  <Image className={styles.emptyImage} src={emptyImg} />
                  <View className={styles.emptyText}>暂无还款记录</View>
                </Empty>
              </View>
          }
        </PullRefresh>
      ) : null}
    </View>
  )
}