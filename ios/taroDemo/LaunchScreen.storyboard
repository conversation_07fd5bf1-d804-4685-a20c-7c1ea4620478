<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.Storyboard.XIB" version="3.0" toolsVersion="22505" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" launchScreen="YES" useTraitCollections="YES" useSafeAreas="YES" colorMatched="YES" initialViewController="01J-lp-oVM">
    <device id="retina4_7" orientation="portrait" appearance="light"/>
    <dependencies>
        <deployment identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="22504"/>
        <capability name="Safe area layout guides" minToolsVersion="9.0"/>
        <capability name="System colors in document resources" minToolsVersion="11.0"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <scenes>
        <!--View Controller-->
        <scene sceneID="EHf-IW-A2E">
            <objects>
                <viewController id="01J-lp-oVM" sceneMemberID="viewController">
                    <view key="view" contentMode="scaleToFill" id="Ze5-6b-2t3">
                        <rect key="frame" x="0.0" y="0.0" width="375" height="667"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                        <subviews>
                            <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleToFill" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="launch-bg.png" translatesAutoresizingMaskIntoConstraints="NO" id="Myo-pn-X5r">
                                <rect key="frame" x="0.0" y="0.0" width="375" height="667"/>
                            </imageView>
                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="长银五八跨平台应用" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="8k2-j2-pVm">
                                <rect key="frame" x="78" y="319" width="219" height="29"/>
                                <fontDescription key="fontDescription" type="system" pointSize="24"/>
                                <color key="textColor" systemColor="tertiarySystemGroupedBackgroundColor"/>
                                <nil key="highlightedColor"/>
                            </label>
                        </subviews>
                        <viewLayoutGuide key="safeArea" id="Bcu-3y-fUS"/>
                        <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                        <constraints>
                            <constraint firstItem="8k2-j2-pVm" firstAttribute="centerX" secondItem="Ze5-6b-2t3" secondAttribute="centerX" id="4Dy-mz-WVP"/>
                            <constraint firstAttribute="trailing" secondItem="Myo-pn-X5r" secondAttribute="trailing" id="FkW-53-PDB"/>
                            <constraint firstItem="Myo-pn-X5r" firstAttribute="leading" secondItem="Ze5-6b-2t3" secondAttribute="leading" id="Y2m-Pj-Od1"/>
                            <constraint firstItem="8k2-j2-pVm" firstAttribute="centerY" secondItem="Ze5-6b-2t3" secondAttribute="centerY" id="fRh-o7-Vz1"/>
                            <constraint firstItem="Myo-pn-X5r" firstAttribute="top" secondItem="Ze5-6b-2t3" secondAttribute="top" id="j3l-bz-Gwc"/>
                            <constraint firstAttribute="bottom" secondItem="Myo-pn-X5r" secondAttribute="bottom" id="v8b-hv-0Wr"/>
                        </constraints>
                    </view>
                </viewController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="iYj-Kq-Ea1" userLabel="First Responder" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="52.173913043478265" y="375"/>
        </scene>
    </scenes>
    <resources>
        <image name="launch-bg.png" width="1125" height="2436"/>
        <systemColor name="systemBackgroundColor">
            <color white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
        </systemColor>
        <systemColor name="tertiarySystemGroupedBackgroundColor">
            <color red="0.94901960780000005" green="0.94901960780000005" blue="0.96862745100000003" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
        </systemColor>
    </resources>
</document>
