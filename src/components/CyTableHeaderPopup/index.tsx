import {FC} from 'react';
import {Icon, ScrollView, View} from '@tarojs/components';
import {
  TABLE_FIElD_APPROVE,
  TABLE_FIElD_COLLECTION,
  TABLE_FIElD_CONCERN,
  TABLE_FIElD_LITIGATION,
  TABLE_FIElD_MARKET,
  TABLE_FIElD_OFFLINE,
  TABLE_FIElD_ONLINE,
  TABLE_LOAN_QUALITY,
} from '../../utils/constant';
import './index.scss';

interface IItem {
  key: string;
  value: string;
}
/**
 * market-data 营销数据
 * offline  线下渠道客户
 * online   线上渠道客户
 * approve 审批人审批
 * collection  催收业绩统计
 * litigation 诉源治理情况
 */
type pageName =
  | 'market-data'
  | 'offline'
  | 'online'
  | 'approve'
  | 'collection'
  | 'litigation'
  | 'concern'
  | 'loan_quality';
interface Props {
  type: pageName;
  onClose: () => void;
}

/**
 * 组件名称：表格字段说明
 * 组件描述：表格字段说明
 */
const CyTableHeaderPopup: FC<Props> = ({type, onClose}) => {
  let list: IItem[] = [];
  switch (type) {
    case 'market-data':
      list = TABLE_FIElD_MARKET;
      break;
    case 'offline':
      list = TABLE_FIElD_OFFLINE;
      break;
    case 'online':
      list = TABLE_FIElD_ONLINE;
      break;
    case 'approve':
      list = TABLE_FIElD_APPROVE;
      break;
    case 'collection':
      list = TABLE_FIElD_COLLECTION;
      break;
    case 'litigation':
      list = TABLE_FIElD_LITIGATION;
      break;
    case 'concern':
      list = TABLE_FIElD_CONCERN;
      break;
    case 'loan_quality':
      list = TABLE_LOAN_QUALITY;
      break;

    default:
      break;
  }

  return (
    <View className='comp-cy-table-header-popup'>
      <View className='comp-cy-table-header-popup-bg'></View>
      <View className='comp-cy-table-header-popup-content'>
        <View className='comp-cy-table-header-popup-top'>
          <View className='comp-cy-table-header-popup-top-name'>字段名称</View>
          <View className='comp-cy-table-header-popup-top-disc'>定义说明</View>
          <View className='comp-cy-table-header-popup-top-close' onClick={onClose}>
            <Icon type='cancel' size={28} color='#fff' />
          </View>
        </View>
        <ScrollView className='comp-cy-table-header-popup-scroll' scrollY>
          <View>
            {list.map((item, index) => {
              return (
                <View className='comp-cy-table-header-popup-scroll-item' key={index}>
                  <View className='comp-cy-table-header-popup-scroll-item-title'>{item.key}</View>
                  <View className='comp-cy-table-header-popup-scroll-item-value'>{item.value}</View>
                </View>
              );
            })}
          </View>
        </ScrollView>
      </View>
    </View>
  );
};
export default CyTableHeaderPopup;
