<template name="error_template"><view class="js_dialog" id="iosDialog1"><view class="weui-mask"></view><view class="weui-dialog style2 rule"><view class="weui-icon_area"><view class="iconbg"></view><icon class="icon-box-img" type="warn" size="75"></icon></view><view class="weui-dialog__hd" wx:if="{{error_msg !== 'OCR识别失败' && error_msg !== '证件日期识别失败'}}"><strong class="weui-dialog__title">{{ErrorMsg}}</strong></view><view class="weui-dialog__hd" wx:else style="margin-bottom:0"><strong class="weui-dialog__title">{{ErrorMsg}}</strong><view><label style="color:#989898;" wx:if="{{error_msg == 'OCR识别失败'}}">请根据规范重新拍摄</label> <label style="color:#989898;" wx:if="{{error_msg == '证件日期识别失败'}}">请按规范重新拍摄</label><image src="https://s.beta.gtimg.com/GodIdent/huiyan-ui-new/images-wx/ocr-fail.png" style="width:100%;height:150px;margin:50rpx 0;"></image></view></view><view class="weui-dialog__ft"><view bindtap="switchDialog" class="{{Common.IsWxNative ? 'weui-dialog__btn weui-dialog__btn_primary color-green' : 'weui-dialog__btn weui-dialog__btn_primary'}}" hover-class="bg-gray">我知道了</view></view></view></view></template>