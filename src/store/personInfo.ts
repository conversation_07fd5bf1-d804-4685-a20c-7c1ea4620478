import Taro from '@tarojs/taro';
import { produce } from 'immer';
import { create } from 'zustand';
import { createJSONStorage, devtools, persist } from 'zustand/middleware';
import { getStoragePromise } from '../utils/common';

type PersonInfoStore = {
  baseInfo: API.baseInfoParams | null;
  companyInfo: (API.companyInfoParams & {occupationName: string}) | null;
  contactInfo: API.contactInfoFormData | null;
  setBaseInfo: (info: API.baseInfoParams) => void;
  setCompanyInfo: (info: API.companyInfoParams) => void;
  setContactInfo: (info: API.contactInfoFormData) => void;
  resetBaseInfo: () => void;
  resetCompanyInfo: () => void;
  resetContactInfo: () => void;
};

const usePersonInfoStore = create(
  persist(
    devtools<PersonInfoStore>((set, get) => ({
      baseInfo: null,
      companyInfo: null,
      contactInfo: null,
      setBaseInfo: (info: API.baseInfoParams) => {
        set(
          produce(state => {
            state.baseInfo = info;
          }),
          true,
          'setBaseInfo'
        )
      },
      setCompanyInfo: (info: API.companyInfoParams) => {
        set(
          produce(state => {
            state.companyInfo = info;
          }),
          true,
          'setCompanyInfo'
        )
      },
      setContactInfo: (info: API.contactInfoParams) => {
        set(
          produce(state => {
            state.contactInfo = info;
          }),
          true,
          'setContactInfo'
        )
      },
      resetBaseInfo: () => {
        set(
          produce(state => {
            state.baseInfo = null;
          }),
          true,
          'resetBaseInfo'
        )
      },
      resetCompanyInfo: () => {
        set(
          produce(state => {
            state.companyInfo = null;
          }),
          true,
          'resetCompanyInfo'
        )
      },
      resetContactInfo: () => {
        set(
          produce(state => {
            state.contactInfo = null;
          }),
          true,
          'resetContactInfo'
        )
      },

    })),
    {
      name: 'personInfo',
      storage: createJSONStorage(() => ({
        setItem: (key: string, data: string) => {
          Taro.setStorage({
            data,
            key,
          });
        },
        async getItem(key: string) {
          try {
            return await getStoragePromise(key);
          } catch (error) {
            return null;
          }
        },
        removeItem(key) {
          Taro.removeStorage({
            key,
          });
          return;
        },
      })),
    }
  )
)

export default usePersonInfoStore