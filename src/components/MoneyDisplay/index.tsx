import {Text, View, ViewProps} from '@tarojs/components';
import classNames from 'classnames';
import currencyUtil from 'currency.js';
import styles from './index.module.scss';

interface Props extends ViewProps {
  money: number | string;
  precision?: number;
  color?: string;
  currency?: boolean;
  size?: 'small' | 'normal' | 'secondary' | 'large' | 'xlarge' | 'form';
  /** 是否金额和单位在同一标签内 */
  currencyInline?: boolean;
}
const MoneyDisplay = ({money, precision = 2, color, currency = true, size = 'normal', currencyInline}: Props) => {
  const formattedMoney = currencyUtil(money).format({
    symbol: currencyInline ? '¥' : '',
    separator: ',',
    decimal: '.',
    precision,
  });
  return (
    <View className={classNames(styles.moneyDisplay, styles[size])}>
      {currency && !currencyInline ? (
        <Text style={{color}} className={`${styles.moneyUnit}`}>
          ¥
        </Text>
      ) : null}
      <Text style={{color}} className={`${styles.moneyNum}`}>{`${formattedMoney}`}</Text>
    </View>
  );
};
export default MoneyDisplay;
