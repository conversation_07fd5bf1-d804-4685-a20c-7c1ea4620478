import {View} from '@tarojs/components';
import styles from './index.module.scss';
import useUserStore from '~store/user';
import { showToast } from '~utils/common';

export default ({onClick}: {
  onClick?: () => void;
}) => {
  const { user } = useUserStore();
  
  const handleClick = () => {
    if (user.mobile) {
      onClick?.();
    } else {
      showToast('请登录');
    }
  }
  return (
    <View className={styles.addContainer} onClick={handleClick}>
      <View className={styles.addIcon}>+</View>
      添加银行卡
    </View>
  );
};
