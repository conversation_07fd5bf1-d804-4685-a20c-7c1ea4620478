package com.hncy58.financeapp.modules

import android.util.Log
import com.amap.api.location.AMapLocation
import com.amap.api.location.AMapLocationClient
import com.amap.api.location.AMapLocationClientOption
import com.amap.api.location.AMapLocationListener
import com.amap.api.maps.MapsInitializer
import com.facebook.react.bridge.Callback
import com.facebook.react.bridge.ReactApplicationContext
import com.facebook.react.bridge.ReactContextBaseJavaModule
import com.facebook.react.bridge.ReactMethod

@Suppress("unused")
class MapModule(val context: ReactApplicationContext) : ReactContextBaseJavaModule(),
        AMapLocationListener {
    private var locationClient: AMapLocationClient? = null
    private var mCallBack: Callback? = null;

    init {
        // 初始化地图
        initMap()
    }

    override fun getName(): String {
        return "LocationManager"
    }

    @ReactMethod
    fun getLocation(callBack: Callback) {
        mCallBack = callBack;
    }

    override fun onLocationChanged(location: AMapLocation?) {
        if (location?.errorCode == 0) {
            // 定位成功回调，location即为定位结果
            Log.e(
                    "location",
                    "latitude-->" + location.latitude + " longitude-->" + location.longitude
            );
            mCallBack?.invoke(location.latitude, location.longitude)
            // 处理定位数据
        } else {
            // 定位失败，locate.getErrorCode() 获取错误码，locate.getErrorInfo() 获取错误信息
        }
    }

    fun initMap() {
        if (locationClient != null) {
            locationClient?.onDestroy();
        }
        AMapLocationClient.updatePrivacyShow(context, true, true)
        AMapLocationClient.updatePrivacyAgree(context, true)
        MapsInitializer.updatePrivacyAgree(context, true)
        MapsInitializer.updatePrivacyShow(context, true, true)

        locationClient = AMapLocationClient(context)
        locationClient?.setLocationListener(this)

        val option = AMapLocationClientOption()
        option.locationMode = AMapLocationClientOption.AMapLocationMode.Hight_Accuracy
        option.setLocationCacheEnable(false)

        // 设置定位参数
        locationClient?.setLocationOption(option)

        // 启动定位
        locationClient?.startLocation()
    }

}