import request from '../http/request';
/**
 * 上传文件： 
 * docType: 文档类型 E01：身份证 E02：银行卡 E03: 与客户经理合影 E12: 手持身份证照片 E21：补录资料 E28: 资产信息 E29：外部结清证明，E33：优化建议， E20: 客户信息更新资料上传
 * eventCode: 事件编码
 * files: 上传的文件列表
 */
export const uploadDocument = ({
    docType = 'E01',
    eventCode,
    files
  }: API.UploadDocumentReq) => {
    return request<any>({
      url: '/api/document/upload',
      method: 'POST',
      data: {
        docType,
        eventCode,
        files
      },
    });
  };