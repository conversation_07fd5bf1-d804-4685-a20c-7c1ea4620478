import React, {FC, ReactNode} from 'react';
import {Image, Text, View} from '@tarojs/components';
import {CyButton} from 'hncy58-taro-components';
import emptyImg from '~images/repayment/<EMAIL>';
import styles from './index.module.scss';

interface EmptyProps {
  /**
   * 自定义图片地址
   */
  image?: string;
  /**
   * 自定义主要文字内容
   */
  text?: string;
  /**
   * 自定义次要文字内容
   */
  subText?: string;
  /**
   * 是否显示按钮
   */
  showButton?: boolean;
  /**
   * 按钮文字
   */
  buttonText?: string;
  /**
   * 按钮点击事件
   */
  onButtonClick?: () => void;
  /**
   * 图片插槽
   */
  imageSlot?: ReactNode;
  /**
   * 文字插槽
   */
  textSlot?: ReactNode;
  /**
   * 按钮插槽
   */
  buttonSlot?: ReactNode;
  /**
   * 自定义类名
   */
  className?: string;
  /**
   * 子元素
   */
  children?: ReactNode;
}

/**
 * 组件名称：空状态
 * 组件描述：自定义空状态界面，支持自定义图片、文字和按钮
 */
const Empty: FC<EmptyProps> = ({
  image = emptyImg,
  text = '一级标题',
  subText = '二级标题',
  showButton = false,
  buttonText = '按钮',
  onButtonClick,
  imageSlot,
  textSlot,
  buttonSlot,
  className = '',
  children,
}) => {
  return (
    <View className={`${styles['comp-empty']} ${className}`}>
      {children || (
        <View className={styles['comp-empty-text-container']}>
          {imageSlot || <Image className={styles['emptyImage']} src={image} mode='widthFix' />}
          {textSlot || (
            <>
              <Text className={styles['comp-empty-text']}>{text}</Text>
              {subText && <Text className={styles['comp-empty-subtext']}>{subText}</Text>}
            </>
          )}
        </View>
      )}
      {showButton &&
        (buttonSlot || (
          <CyButton className={styles['comp-empty-button']} onClick={onButtonClick} type='primary' round block>
            {buttonText}
          </CyButton>
        ))}
    </View>
  );
};

export default Empty;
