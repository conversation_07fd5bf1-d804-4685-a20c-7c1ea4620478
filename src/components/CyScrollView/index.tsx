import React, {CSSProperties, ForwardRefRenderFunction, ReactNode} from 'react';
import {ScrollView, ScrollViewProps} from '@tarojs/components';
import classNames from 'classnames';
import './index.scss';

interface Props {
  className?: string;
  style?: string | CSSProperties;
  children?: ReactNode;
  enhanced?: boolean;
  pagingEnabled?: boolean;
  showScrollbar?: boolean;
  scrollX?: boolean;
  scrollY?: boolean;
}

/**
 * 组件名称：滚动视图
 * 组件描述：滚动视图
 */
interface CyScrollViewRef {
  scrollToOffset: (x: number, y: number) => void;
}
const MyCyScrollView: ForwardRefRenderFunction<CyScrollViewRef, Props & ScrollViewProps> = (props, ref) => {
  React.useImperativeHandle(
    ref,
    () => ({
      scrollToOffset: (x: number, y: number) => {
        scrollViewRef.current?.scrollTo({x, y});
      },
    }),
    [],
  );
  const scrollViewRef = React.useRef<any>();
  return (
    <ScrollView className={classNames(['comp-cy-scroll-view', props.className])} {...props} ref={scrollViewRef}>
      {props.children}
    </ScrollView>
  );
};

const CyScrollView = React.forwardRef(MyCyScrollView);
export default CyScrollView;
