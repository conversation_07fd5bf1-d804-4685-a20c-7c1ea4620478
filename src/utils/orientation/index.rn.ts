import Orientation from 'react-native-orientation-locker';

export const lockToLandscape = () => {
  Orientation.lockToLandscape();
};

// export const unlockAllOrientations = () => {
//   Orientation.unlockAllOrientations();
// };

export const lockToPortrait = () => {
  Orientation.lockToPortrait();
};

Orientation.addDeviceOrientationListener(res => {
  console.log(res);
  console.log(Orientation.isLocked());
});
