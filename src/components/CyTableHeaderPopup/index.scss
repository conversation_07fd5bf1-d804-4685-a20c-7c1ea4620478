.comp-cy-table-header-popup {

    position: absolute;
    left: 0px;
    top: 0px;
    display: flex;
    flex-direction: column;
    width: 100%;
    height: 100%;
    z-index: 999;
    &-bg {
        width: 100%;
        background:#333;
        opacity: 0.6;
        height: 100%;
        z-index: 888;
    }
    &-content {
        background-color: #fff;
        position: absolute;
        left: 0px;
        bottom: 0px;
        height: 90%;
        width: 100%;
        z-index: 889;
    }

    &-top{
        display: flex;
        flex-direction: row;
        background-color: #1276d4;
        height: 100px;
        text-align: center;
        align-items: center;
        &-name{
            color: #fff;
            width: 30%;
            padding-left: 20px;
        }
        &-disc {
            color: #fff;
            width: 60%;
            padding-left: 10px;

        }
        &-close {
            color: #fff;
            width: 10%;
        }
    }
    &-scroll{
        flex: 1;
        // display: flex;
        // flex-direction: column;
        // height: 800px;
        &-item{
            display: flex;
            flex-direction: row;
            text-align: center;
            align-items: flex-start;
            padding: 20px;
            border-bottom: 2px solid #ddd;
            &-title{
                width: 30%;
            }
            &-value{
                width: 70%;

            }
        }
    }
}