
export type IRepaymentType = 'MCIP' | 'MCEP'; // 还款模式：按月付息 | 等额本金

export type IPickerItem = {
	value: string;
	label: string;
};

export interface IBankCardInfo {
	accountNo: string;
	acctId: string;
	available: boolean;
	bankBackgroundImgUrl: string;
	bankCode: string;
	bankIconImgUrl: string;
	bankMobileNo: string;
	bankName: string;
	canUpgrade: boolean;
	dayLimit: number;
	dayPayLimit: number;
	defaultFlag: boolean;
	singleLimit: number;
	singlePayLimit: number;
	maintenanceEndTime: string;
	maintenanceStartTime: string;
}

export interface ILendingTrailSchduleItem {
	currentTerm: number;
	totalAmount: number;
	principal: number;
	interest: number;
	fee: number;
	dueDate: string;
	startDate: string;
	days: number;
}

export interface ILoanInfo {
	loanAmount: number;
	loanTerm: number;
	rate: number;
	repaymentType: string;
}

export type ILendingStatus = 'SUCCESS' | 'FAILED' | 'PROCESSING';

export const enum smsSceneType {
	/** 账户注销 */
	M1001 = 'M1001',
	/** 双延固定利率协议分期申请 */
	M1002 = 'M1002',
	/** 分期申请 */
	M1003 = 'M1003',
	/** 借款 */
	M1004 = 'M1004',
	/** 设置交易密码 */
	M1005 = 'M1005',
	/** 登录 */
	M1007 = 'M1007',
	/** H5营销活动 */
	M1008 = 'M1008',
	/** 银行卡还款 */
	M1009 = 'M1009',
	/** 重新申请授信 */
	M1010 = 'M1010',
	/** 申请续签 */
	M1011 = 'M1011',
	/** 挂失解约 */
	M2019 = 'M2019',
	/** 修改预留手机号 */
	M2001 = 'M2001',
	/** 绑定邀请码 */
	M2002 = 'M2002',
	/** 快手客户表单收集 */
	M2003 = 'M2003',
	/** 调整预授信额度 */
	M2004 = 'M2004',
	/** 可降息 */
	M2005 = 'M2005',
	/** 分期还款 */
	M2006 = 'M2006',
	/** 分期还款计划 */
	M2007 = 'M2007',
	/** 选择固定还款日 */
	M2008 = 'M2008',
	/** 确认续签 */
	M2009 = 'M2009',
	/** 提前续签-确认续签 */
	M2010 = 'M2010',
	/** 重置密码 */
	M2011 = 'M2011',
	/** 密码设置 */
	M2012 = 'M2012',
	/** 验证手机号 */
	M2013 = 'M2013',
	/** 延期还款-联系信息填写 */
	M2104 = 'M2014',
	/** 延期还款原因 */
	M2105 = 'M2015',
	/** 选择延长期限 */
	M2106 = 'M2016',
	/** 新冠场景-选择具体原因 */
	M2107 = 'M2017',
	/** 关闭额度 */
	M2108 = 'M2018',
}