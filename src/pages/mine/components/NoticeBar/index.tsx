import {Image, View, ViewProps} from '@tarojs/components';
import classNames from 'classnames';
import styles from './index.module.scss';
import hormIcon from '~images/mine/<EMAIL>'

export interface Props extends ViewProps {
  notice: string;
  actionText?: string;
}

export default ({notice, actionText, onClick}: Props) => {
  return (
    <View className={styles.noticeBar} onClick={onClick}>
      <View className={classNames(styles.noticeText, styles.iconText)}>
        <Image className={styles.noticeIcon} src={hormIcon} />
        {notice}
      </View>
      <View className={classNames(styles.iconText, styles.actionText)}>
        {actionText + '>'}
        {/* <Image className={styles.actionIcon} src='https://picsum.photos/48/48' /> */}
      </View>
    </View>
  );
};
