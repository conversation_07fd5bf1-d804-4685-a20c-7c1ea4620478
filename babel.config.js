// babel-preset-taro 更多选项和默认值：
// https://github.com/NervJS/taro/blob/next/packages/babel-preset-taro/README.md
const plugins = [
  [
    "import",
    {
      libraryName: "@taroify/core",
      libraryDirectory: "",
      style: true,
    },
    "@taroify/core",
  ],
  [
    "import",
    {
      libraryName: "@taroify/icons",
      libraryDirectory: "",
      camel2DashComponentName: false,
      style: () => "@taroify/icons/style",
      customName: (name) => name === "Icon" ? "@taroify/icons/van/VanIcon" : `@taroify/icons/${name}`,
    },
    "@taroify/icons",
  ],
]
if (process.env.NODE_ENV === 'production' && !process.env.TARO_APP_MODE) {//去除打印日志
  plugins.push("transform-remove-console")
}

module.exports = {
  presets: [
    ['taro', {
      framework: 'react',
      ts: true,
      compiler: 'webpack5',
    }]
  ],
 "plugins": plugins
}
