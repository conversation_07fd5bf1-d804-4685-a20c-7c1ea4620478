import React, {useEffect, useRef, useState} from 'react';
import {Canvas} from '@tarojs/components';
import Taro from '@tarojs/taro';
import {showToast} from '~utils/common';
import styles from './index.module.scss';

interface ClockProps {
  size?: number; // 时钟大小
  className?: string; // 自定义类名
}

const Clock: React.FC<ClockProps> = ({size = 200, className = ''}) => {
  const canvasRef = useRef<any>(null);
  const canvasCtxRef = useRef<any>(null);
  const timerRef = useRef<NodeJS.Timeout>();

  // 初始化Canvas上下文
  const initCanvas = async () => {
    try {
      const query = Taro.createSelectorQuery();
      const canvas = await new Promise<any>(resolve => {
        query
          .select('#clock-canvas')
          .fields({node: true, size: true})
          .exec(res => {
            resolve(res[0]?.node);
          });
      });

      if (!canvas) {
        return;
      }
      const ctx = canvas.getContext('2d');
      const dpr = Taro.getSystemInfoSync().pixelRatio;
      canvas.width = size * dpr;
      canvas.height = size * dpr;
      ctx.scale(dpr, dpr);

      canvasRef.current = canvas;
      canvasCtxRef.current = ctx;
      // 启动角度更新
      drawClock();
      startAngleUpdate();
    } catch (error) {
      console.error('初始化Canvas失败:', error);
    }
  };

  // 启动角度更新 - 使用requestAnimationFrame
  const startAngleUpdate = () => {
    // 初始化时间
    let minuteAngle = 0;
    let hourAngle = 0;
    timerRef.current = setInterval(() => {
      // 每秒钟增加的角度
      const minuteAngleIncrement = (2 * Math.PI) / 60;
      const hourAngleIncrement = (2 * Math.PI) / 600;
      minuteAngle += minuteAngleIncrement;
      hourAngle += hourAngleIncrement;
      drawClock(hourAngle, minuteAngle);
    }, 200);
  };

  // 绘制时钟
  const drawClock = (hourAngle = 0, minuteAngle = 0) => {
    const ctx = canvasCtxRef.current;
    if (!ctx) return;

    const radius = size / 2;
    const centerX = radius;
    const centerY = radius;

    // 清除画布
    ctx.clearRect(0, 0, size, size);

    // 绘制外圈
    ctx.beginPath();
    ctx.arc(centerX, centerY, radius * 0.99, 0, Math.PI * 2);
    ctx.fillStyle = '#BBCDF2'; // 浅蓝色外圈
    ctx.fill();

    // 绘制内圈（表盘）
    ctx.beginPath();
    ctx.arc(centerX, centerY, radius * 0.85, 0, Math.PI * 2);
    ctx.fillStyle = '#FFFFFF'; // 白色表盘
    ctx.fill();

    // 绘制刻度
    drawMarkers(ctx, centerX, centerY, radius);

    // 绘制时针
    drawHourHand(ctx, centerX, centerY, radius, hourAngle);

    // 绘制分针
    drawMinuteHand(ctx, centerX, centerY, radius, minuteAngle);

    // 绘制中心点
    drawCenter(ctx, centerX, centerY);
  };

  // 绘制刻度
  const drawMarkers = (ctx: any, centerX: number, centerY: number, radius: number) => {
    ctx.save();
    ctx.lineWidth = 3;
    ctx.strokeStyle = '#D2D7E9'; // 浅灰色刻度

    // 绘制12、3、6、9点位置的短线
    for (let i = 0; i < 4; i++) {
      const angle = (i * Math.PI) / 2;
      const startX = centerX + Math.cos(angle) * (radius * 0.56);
      const startY = centerY + Math.sin(angle) * (radius * 0.56);
      const endX = centerX + Math.cos(angle) * (radius * 0.7);
      const endY = centerY + Math.sin(angle) * (radius * 0.7);

      ctx.beginPath();
      ctx.moveTo(startX, startY);
      ctx.lineTo(endX, endY);
      ctx.stroke();
    }
    ctx.restore();
  };

  // 绘制时针 - 直接使用角度
  const drawHourHand = (ctx: any, centerX: number, centerY: number, radius: number, angle: number) => {
    // 调整角度，使12点方向为0度
    const adjustedAngle = angle - Math.PI / 2;
    const handLength = radius * 0.4;

    ctx.save();
    ctx.beginPath();
    ctx.lineWidth = 4;
    ctx.lineCap = 'round';
    ctx.strokeStyle = '#394867'; // 深蓝色指针

    ctx.moveTo(centerX, centerY);
    ctx.lineTo(centerX + handLength * Math.cos(adjustedAngle), centerY + handLength * Math.sin(adjustedAngle));
    ctx.stroke();
    ctx.restore();
  };

  // 绘制分针 - 直接使用角度
  const drawMinuteHand = (ctx: any, centerX: number, centerY: number, radius: number, angle: number) => {
    // 调整角度，使12点方向为0度
    const adjustedAngle = angle - Math.PI / 2;
    const handLength = radius * 0.5;

    ctx.save();
    ctx.beginPath();
    ctx.lineWidth = 3;
    ctx.lineCap = 'round';
    ctx.strokeStyle = '#394867'; // 深蓝色指针

    ctx.moveTo(centerX, centerY);
    ctx.lineTo(centerX + handLength * Math.cos(adjustedAngle), centerY + handLength * Math.sin(adjustedAngle));
    ctx.stroke();
    ctx.restore();
  };

  // 绘制中心点
  const drawCenter = (ctx: any, centerX: number, centerY: number) => {
    ctx.beginPath();
    ctx.arc(centerX, centerY, 8, 0, Math.PI * 2);
    ctx.fillStyle = '#FFE5D0'; // 浅橙色中心点
    ctx.fill();
  };

  // 组件挂载时初始化Canvas
  useEffect(() => {
    initCanvas();

    return () => {
      // 组件卸载时清除动画和定时器
      if (timerRef.current) {
        clearInterval(timerRef.current);
      }
    };
  }, []);

  return (
    <Canvas
      type='2d'
      id='clock-canvas'
      className={`${styles.clockCanvas} ${className}`}
      style={{width: `${size}px`, height: `${size}px`}}
    />
  );
};

export default Clock;
