.repayment_container {
  width: 100%;
  background-color: #fff;
  border-radius: 16px;
  box-sizing: border-box;
}
.repayment_container.repayment_container_overdue {
  background: linear-gradient(0deg, #FFFFFF 12%, #FFFFFF 85%, $color-bg-badge-overdue-light 100%);
}

.repayment_main {
  display: flex;
  flex-direction: column;
  justify-content: center;
  text-align: center;
  padding: 64px 40px 0;
}
.repayment_title {
  position: relative;
  color: $color-text-normal;
  font-size: $font-size-normal;
  margin-bottom: 40px;
}
.tag-error {
  padding: 4px 16px;
  background-color: $color-bg-badge-overdue;
  border-radius: 8px;
  color: #fff;
  font-size: $font-size-sm;
}
.overdue {
  position: absolute;
  left: 420px;
  top: 0;
}

.repayment_money {
  margin-bottom: 30px;
  line-height: 1;
}

.repayment_day {
  color: $color-text-secondary;
  font-size: $font-size-sm;
  margin-bottom: 40px;
}

.repayment_tip {
  display: flex;
  flex-direction: column;
  padding: 22px 38px;
  margin-bottom: 64px;
  background-color: $color-bg-normal;
  border-radius: 8px;
  font-size: $font-size-sm;
  color: $color-text-secondary;
}
.repayment_tip.repayment_tip_overdue {
  color: $color-bg-badge-overdue;
  background-color: $color-bg-badge-overdue-light;
}

.repayment_btn {
  width: 480px;
  height: 100px;
  line-height: 100px;
  font-size: 32px;
  border-radius: 50px!important;
}

.repayment_detail {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  height: 100px;
  padding: 0 40px;
  margin-top: 48px;
  border-top: 1px solid #ddd;
  color: $color-text-normal;
  font-size: $font-size-normal;
  box-sizing: border-box;
  .repayment_detail_number {
    color: $color-text-secondary;
  }
}

.repayment_detail_left {
  display: flex;
  align-items: center;
}

.repaymentComposition {
  background-color: #f3f5ff;
  display: flex;
  height: 48px;
  margin-bottom: 8px;
  align-items: center;
  border-radius: 8px;
}

.repaymentCompositionItem {
  flex: 1;
  height: 24px;
  line-height: 24px;
  color: #7D83AE;
  text-align: center;
  font-size: 26px;

   & + .repaymentCompositionItem {
    border-left: 1px solid #ddd;
  }
}

.repaymentCompositionItem.repaymentCompositionMark {
  color: $color-error;
}