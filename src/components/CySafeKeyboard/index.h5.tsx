import {ForwardRefRenderFunction, forwardRef, useEffect, useImperativeHandle, useRef} from 'react';
import {Input, View} from '@tarojs/components';
import {document} from '@tarojs/runtime';
import Taro, {Page} from '@tarojs/taro';
import {CySafeKeyboardProps, CySafeKeyboardRef} from '.';
import './index.scss';

/**
 * 组件名称：密码键盘
 * 组件描述：密码键盘
 */
const CySafeKeyboard: ForwardRefRenderFunction<CySafeKeyboardRef, CySafeKeyboardProps> = (
  {
    onInputClick,
    onInputChangeCallBack,
    onDoneClick,
    onHideKeyboard,
    onShowKeyboard,
    onOnError,
    onKeyboardInit,
    bindinputChangeCallBack,
    serverRandom,
    sipId,
    maxLength,
    orderType,
    placeholder = '请输入',
    keyboardType = 0,
    ...props
  },
  ref,
) => {
  const passwordKeyBoard = useRef<any>(null);
  const currentInputID = useRef<any>(null);

  useImperativeHandle(ref, () => ({
    show: () => {
      handlerKeyboardShow();
    },
    hidden: () => {
      handlerKeyboardHidden();
    },
    clearInputValue: () => {
      return handlerClearInputValue();
    },
    getEncryptedInputValue: () => {
      return handlerGetEncryptedInputValue();
    },
    getEncryptedClientRandom: () => {
      return handlerGetEncryptedClientRandom();
    },
    checkInputValueMatch: (otherId: string) => {
      return handlerCheckInputValueMatch(otherId);
    },
  }));

  useEffect(() => {
    serverRandom && initPasswordInput();
  }, []);

  const setProperty = () => {
    passwordKeyBoard.current?.bindInputBox(sipId);
    if (!serverRandom) {
      Taro.showModal({title: '安全键盘加载失败'});
      return false;
    }
    if (CFCA_OK !== passwordKeyBoard.current?.setMaxLength(maxLength, sipId)) {
      console.log('setMaxLength error');
      Taro.showModal({title: '安全键盘加载失败'});
      return false;
    }
    if (CFCA_OK !== passwordKeyBoard.current?.setOutputType(OUTPUT_TYPE_ORIGINAL, sipId)) {
      console.log('setOutputType error');
      Taro.showModal({title: '安全键盘加载失败'});
      return false;
    }
    if (CFCA_OK !== passwordKeyBoard.current?.setEncryptState(true, sipId)) {
      console.log('setEncryptState error');
      Taro.showModal({title: '安全键盘加载失败'});
      return false;
    }
    if (CFCA_OK !== passwordKeyBoard.current?.setCipherType(CIPHER_TYPE_RSA, sipId)) {
      console.log('setCipherType error');
      Taro.showModal({title: '安全键盘加载失败'});
      return false;
    }
    if (CFCA_OK !== passwordKeyBoard.current?.setRandomType(KEYBOARD_DISORDER_ALL, sipId)) {
      console.log('setRandomType error');
      Taro.showModal({title: '安全键盘加载失败'});
      return false;
    }
    if (CFCA_OK !== passwordKeyBoard.current?.setServerRandom(serverRandom, sipId)) {
      console.log('setServerRandom error', passwordKeyBoard.current?.setServerRandom(serverRandom, sipId));
      Taro.showModal({title: '安全键盘加载失败'});
      return false;
    }
  };
  const initPasswordInput = () => {
    if (passwordKeyBoard.current === null && keyboardType !== undefined) {
      passwordKeyBoard.current = new CFCAKeyboard(keyboardType);
    }
    console.log('passwordKeyBoard', passwordKeyBoard, new CFCAKeyboard(keyboardType));
    setProperty();

    passwordKeyBoard.current?.hideKeyboard();
    passwordKeyBoard.current?.setServerRandom(serverRandom);
    // 设置点击完成回调
    console.log('passwordKeyBoard.current?.setDoneCallback', passwordKeyBoard.current?.setDoneCallback);
    passwordKeyBoard.current?.setDoneCallback((res: any) => {
      onDoneClick?.();
    });
    // TODO:h5中该方法有bug，输入变化时不会触发
    passwordKeyBoard.current?.setInputChangeCallback((val: string) => {
      onInputChangeCallBack?.(val);
    });
    initSipBoxNum();
  };

  const setUpEvent = (elem: any, eventType: any, handler: any) => {
    return elem?.attachEvent
      ? elem.attachEvent(`on${eventType}`, handler)
      : elem?.addEventListener
        ? elem.addEventListener(eventType, handler, false)
        : null;
  };
  const initSipBoxNum = () => {
    let sipBox = document.getElementById(sipId);
    setUpEvent?.(sipBox, 'focus', function (event: any) {
      sipBox?.blur();
      passwordKeyBoard.current?.bindInputBox(sipId);
      if (!serverRandom) {
        console.log('服务器随机数，请求失败');

        return;
      }
      if (CFCA_OK !== passwordKeyBoard.current?.setServerRandom(serverRandom, sipBox)) {
        console.log('数字键盘 加载异常');
      }

      if (currentInputID.current !== sipBox) {
        passwordKeyBoard.current?.clearInputValue();
        passwordKeyBoard.current?.hideKeyboard();

        setTimeout(() => {
          passwordKeyBoard.current?.showKeyboard();
          currentInputID.current = sipBox;
        }, 300);
      }
    });
  };

  const handlerKeyboardShow = () => {
    passwordKeyBoard.current?.showKeyboard();
  };

  const handlerKeyboardHidden = () => {
    passwordKeyBoard.current?.hideKeyboard();
  };

  const handlerClearInputValue = () => {
    passwordKeyBoard.current?.clearInputValue();
  };

  const handlerGetEncryptedInputValue = () => {
    console.log(sipId, passwordKeyBoard.current?.getEncryptedInputValue(sipId));
    return passwordKeyBoard.current?.getEncryptedInputValue(sipId);
  };

  const handlerGetEncryptedClientRandom = () => {
    return passwordKeyBoard.current?.getEncryptedClientRandom(sipId);
  };

  const handlerCheckInputValueMatch = (sipid2: string) => {
    return passwordKeyBoard.current?.checkInputValueMatch(sipId, sipid2);
  };

  console.log('serverRandom=', serverRandom, props);
  return (
    <Input
      onFocus={handlerKeyboardShow}
      onBlur={handlerKeyboardHidden}
      className='password-input'
      name='serverRandom'
      placeholder={placeholder}
      id={sipId}
      nativeProps={{readonly: true}}
      style={props.style}
    />
  );
};

export default forwardRef(CySafeKeyboard);
