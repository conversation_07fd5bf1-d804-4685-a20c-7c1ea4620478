import {Image, View} from '@tarojs/components';

const cache: any = {};
if (process.env.TARO_ENV === 'h5') {
  // 加载所有的svg图标
  function importAll(r: any) {
    r.keys().forEach((key: string) => (cache[key] = r(key)));
  }
  importAll(require.context('~icons', true, /\.svg$/));
}

export default ({
  iconName,
  width = 100,
  type = 'svg',
  src,
  icontype,
}: {
  iconName: string;
  width?: number;
  type?: string;
  src: string;
  icontype?: string;
}) => {
  console.log('cache', cache);
  if (icontype === 'image') {
  } else {
  }
  if (process.env.TARO_ENV === 'h5' && type === 'svg') {
    return (
      <svg aria-hidden='true' style={{width: `${width}px`}}>
        <use xlinkHref={`#${iconName}`} />
      </svg>
    );
  } else if (process.env.TARO_ENV === 'weapp' && type === 'svg') {
    return <Image mode='aspectFit' style={{width: `${width}px`}} src={src} />;
  } else if (process.env.TARO_ENV === 'weapp') {
    return <Image mode='aspectFit' style={{width: `${width}px`}} src={src} />;
  } else {
    return <Image mode='aspectFit' style={{width: `${width}px`}} src={src} />;
  }
};
