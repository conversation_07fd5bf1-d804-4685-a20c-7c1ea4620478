import React, {useState} from 'react';
import {CyButton, CyModal} from 'hncy58-taro-components';
import {Image, Text, View} from '@tarojs/components';
import Taro, {switchTab} from '@tarojs/taro';
import Button from '~components/Button';
import Card from '~components/Card';
import Modal from '~components/Modal';
import PageWrap from '~components/PageWrap';
import Steps, {Step} from '~components/Steps';
import Logo from '~images/credit-apply/credit-fail.png';
import {createModal} from '~utils/appendEl';
import styles from './index.module.scss';
import dayjs from 'dayjs';
import useUserStore from '~store/user';

const steps: Step[] = [
  {title: '身份核验', status: 'process'},
  {title: '完善信息', status: 'process'},
  {title: '申请额度', status: 'process'},
];
export default () => {
  const {amountCreditInfo} = useUserStore();
  const showCreditTips = () => {
    CyModal.create({
      title: '如何优化个人信用状况？',
      maskClosable: true,
      confirmText: '我知道了',
      content: (
        <>
          <View>1、保持良好还款记录：按时足额偿还信用卡账单、现有贷款等债务，避免逾期；</View>
          <View>2、合理把控贷款额度：量入为出，合理消费，不过度负债；</View>
          <View>3、控制征信查询次数：不要短时间内频繁申请贷款、信用卡等，定期关注个人信用情况；</View>
          <View>
            4、保管及更新个人信息：不要使用公共网络，不随意向陌生人透露身份证号、银行卡号、验证码等敏感信息，如信息过期需尽快办理变更业务。
          </View>
        </>
      ),
    });
  };
  const goHome = () => {
    switchTab({
      url: '/pages/check/index',
    });
  };
  return (
    <PageWrap className={styles.page_container}>
      <View className={styles.page_steps}>
        <Steps steps={steps} current={3} />
      </View>
      <View className={styles.page_content}>
        <Card title='授信拒绝' note='您的申请未通过审核' className={styles.card_item}>
          <View style={{textAlign: 'center'}}>
            <Image className={styles.waitImg} src={Logo} />
            <View className={styles.result_info}>
              <Text className={styles.result_text}>非常抱歉,您的</Text>
              <Text className={styles.result_status}>申请未通过</Text>
            </View>
            <View className={styles.result_tip}>建议保持良好征信，{dayjs(amountCreditInfo.expireDate).format('YYYY年MM月DD日')}后再次申请～ </View>
            <View onClick={showCreditTips} className={styles.result_credit}>
              如何优化个人征信？
            </View>
          </View>
        </Card>
        <CyButton block round type='primary' onClick={goHome} className={styles.bt_btn}>
          返回首页
        </CyButton>
      </View>
    </PageWrap>
  );
};
