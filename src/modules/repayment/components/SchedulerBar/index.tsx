import React, {ReactNode, useEffect, useState} from 'react';
import {Image, ITouchEvent, Text, View} from '@tarojs/components';
import SchedulerPlanCollapse from '../SchedulerPlanCollapse';
import styles from './index.module.scss';
import { getRepaymentPlanList } from '~services/repayment';
import arrowDownIcon from '~images/icon/<EMAIL>';
import arrowUpIcon from '~images/icon/<EMAIL>';

export interface SchedulerBarItem extends API.RepaymentPlanListItem {
  // dueDate: string;
  // totalAmount: number;
  principal?: number;
  interest?: number;
};

interface SchedulerBarProps {
  list: SchedulerBarItem[];
  onDetailClick?: (e: ITouchEvent, item: SchedulerBarItem) => void;
}


const SchedulerBar: React.FC<SchedulerBarProps> = ({list, onDetailClick}) => {
  const handleDetailClick = (e: ITouchEvent, item: SchedulerBarItem) => {
    e.stopPropagation();
    onDetailClick?.(e, item)
  }
  return (
    <View className={styles.schedulerBar}>
      <SchedulerPlanCollapse
        collapseList={list.map(item => ({
          title: (
            <>
              <Text className={styles.date}>{item.dueDate}</Text>
              <Text className={styles.label}>应还</Text>
              <Text className={styles.amount}>￥{item.totalAmount}</Text>
              <Image className={styles.arrowUpIcon} src={arrowDownIcon} />
              <Image className={styles.arrowDownIcon} src={arrowUpIcon} />
              <Text className={styles.detailBtn} onClick={e => handleDetailClick(e, item)}>
                详情
              </Text>
            </>
          ),
          content: (
            <>
              <View>本金：￥{item.principal ?? '--'}</View>
              <View>利息：￥{item.interest ?? '--'}</View>
            </>
          ),
        }))}
      />
    </View>
  );
};

export default SchedulerBar;
