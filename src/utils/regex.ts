export const max2DecimalsNumberRegex = /^\d+(\.\d{0,2})?$/;

// 校验密码有效性：必须包含字母、数字、特殊字符，且不少于8位
export const validatePassword = (password: string): boolean => {
  const regex = /^(?=.*[a-zA-Z])(?=.*[0-9])(?=.*[!@#$%^&*(),.?":{}|<>]).{8,}$/;
  return regex.test(password);
};

export const validateCertId = (certId: string): boolean => {
  const regex = /^([1-9]\d{5})(\d{4})(\d{2})(\d{2})(\d{3})(\d|X)$/;
  return regex.test(certId);
};

/**
 * 是否为整数
 * @param value
 * @returns
 */
export const isInteger = (value: string) => {
  const regex = /^\d+$/;
  return regex.test(value);
};

/**
 * 是否为最多2为小数
 * @param value
 * @returns
 */
export const isMax2DecimalsNumber = (value: string) => {
  const regex = max2DecimalsNumberRegex;
  return regex.test(value);
};

/**
 * 是否为100的整数
 * @param value
 * @returns
 */
export const isMultipleOf100 = (value: string | number) => {
  const strValue = typeof value === 'number' ? value.toString() : value;
  if (!isInteger(strValue)) {
    return false;
  }
  // 将字符串转换为数字，并判断是否为100的倍数
  const num = typeof value === 'string' ? parseInt(value, 10) : value;

  // 检查是否为NaN
  if (Number.isNaN(num)) {
    return false;
  }

  return num % 100 === 0;
};

export const regExpMap = {
  // 姓名
  name: /^(?:[\u4e00-\u9fa5·]{2,16})$/,
  // 手机号
  mobile: /^(?:(?:\+|00)86)?1\d{10}$/,
  // 数字
  number: /^[0-9]*$/,
  // 邮编校验
  postCode: /^\d{6}$/,
  // 特殊字符校验
  text: /^(?!.*[`~!#$%^&*()_\+<>?:"{}|~！#￥%……&*（）={}|《》？：“”【】、；‘’，。、\s+])[\u4E00-\u9FA5a-zA-Z0-9\x21-\x7E]+$/,
  // 只允许内容是中文或英文或数字，但不能全部是数字且内容中不允许出现空格
  words: /^(?!\d+$)[\u4e00-\u9fa5a-zA-Z]*[a-zA-Z\u4e00-\u9fa5\d]+[\u4e00-\u9fa5a-zA-Z\d]*$/
}