.inquiryContent {
  padding: 50px 44px;
  font-size: 28px;
  line-height: 1.5;
  background: linear-gradient(0deg, #FFFFFF 75%, #D4DCFF 100%);
  &.knowContainer {
    padding: 50px 55px;
  }
  .inquiryMain {
    padding: 0 18px;
  }
  .highlight {
    color: #2F54EB;
  }
  .fontweight {
    font-weight: 600;
  }
  .inquiryTip {
    font-family: Source <PERSON> SC;
    font-weight: 400;
    font-size: 28px;
    color: #333333;
    margin-bottom: 56px;
  }
  
  .inquiryTitle {
    font-family: Source <PERSON> Sans SC;
    font-weight: 600;
    font-size: 48px;
    color: #000000;
    line-height: 70px;
    margin-bottom: 56px;
    padding: 0 14px;
  }

  .inquiryText {
    font-family: Source <PERSON> San<PERSON> SC;
    font-weight: 400;
    font-size: 28px;
    color: #666666;
    line-height: 48px;
  }
  .inquiryContainer {
    margin-top: 40px;
    padding: 24px;
    box-sizing: border-box;
    border-radius: 8px;
    background: linear-gradient(90deg, #C2CEFF 1%, #FFFFFF 100%);
    .inquiryContact {
      font-family: Source <PERSON>;
      font-weight: 500;
      font-size: 28px;
      color: #333333;
      line-height: 48px;
    }
    .inquiryPhone {
      font-weight: 800;
      color: #2F54EB;
    }
  }


  .iknow {
    width: 100%;
    background-color: #2F54EB;
    color: #fff;
    text-align: center;
    height: 80px;
    line-height: 80px;
    border-radius: 50px;
    margin-top: 50px;
  }

  .buttonGroup {
    display: flex;
    justify-content: space-between;
    gap: 28px;
    font-size: 32px;
    margin-top: 30px;
    .yes {
      flex: 1;
      border: 1px solid #2F54EB;
      background-color: #fff;
      text-align: center;
      color: #2F54EB;
      height: 82px;
      line-height: 82px;
      border-radius: 50px;
    }
    .no {
      flex: 1;
      background-color: #2F54EB;
      border: 1px solid #2F54EB;
      color: #fff;
      text-align: center;
      height: 82px;
      line-height: 82px;
      border-radius: 50px;
    }
  }
}
