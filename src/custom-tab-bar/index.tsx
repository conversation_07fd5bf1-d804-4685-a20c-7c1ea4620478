import {Component, useState} from 'react';
import {CoverImage, CoverView} from '@tarojs/components';
import {getCurrentPage} from '@tarojs/runtime';
import Taro from '@tarojs/taro';
import iconHome from '~icons/icon-home.png';
import iconMine from '~icons/icon-mine.png';
import iconMineActive from '~icons/icon-mine-active.png';
import iconHomeActive from '~icons/icon-home-active.png';
import useMainStore from '~store/main';
import styles from './index.module.scss';
import { router } from '~utils/common';
const list = [
  {
    pagePath: '/pages/check/index',
    iconPath: iconHome,
    selectedIconPath: iconHomeActive,
    text: '首页',
  },
  {
    pagePath: '/pages/mine/index',
    iconPath: iconMine,
    selectedIconPath: iconMineActive,
    text: '我的',
  },
];
const color = '#999';
const selectedColor = '#666';
export default () => {
  const {tabbarIndex, setTabbarIndex} = useMainStore();

  const switchTab = (index: number, url: string) => {
    console.log('switchTab', index, url, tabbarIndex);
    setTabbarIndex(index);
    router.switchTab({url});
  };

  return (
    <CoverView className={styles['tab-bar']}>
      <CoverView className={styles['tab-bar-border']}></CoverView>
      {list.map((item, index) => {
        return (
          <CoverView key={index} className={styles['tab-bar-item']} onClick={() => switchTab(index, item.pagePath)}>
            <CoverImage src={tabbarIndex === index ? item.selectedIconPath : item.iconPath} />
            <CoverView style={{color: tabbarIndex === index ? selectedColor : color}}>{item.text}</CoverView>
          </CoverView>
        );
      })}
    </CoverView>
  );
};
