.repayment_container {
  padding: 30px 40px;
  background-color: #fff;
}

.repayment_way {
  display: flex;
  justify-content: space-between;
  padding-bottom: 33px;
}

.repayment_item {
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
  width: 320px;
  padding: 24px 38px 15px;
  background-color: #f6f6f6;
  border: 1px solid #f6f6f6;
  border-radius: 8px;
  box-sizing: border-box;
  .repayment_item_title {
    font-size: 28px;
    color: #333;
    line-height: 1;
    margin-bottom: 9px;
  }
  .repayment_item_note {
    font-size: 24px;
    color: #999;
    line-height: 1.2;
    text-align: center;
  }
}
.repayment_item.repayment_item_active {
  background-color: #FAFCFF;
  border: 1px solid #2F54EB;
  .repayment_item_title {
    color: #2F54EB;
  }
  .repayment_item_note {
    color: #85A5FF;
  }
}

.repayment_tip {
  width: 100%;
  padding: 20px 30px;
  color: #333;
  font-size: 24px;
  border: 1px solid #ddd;
  box-sizing: border-box;
  line-height: 1.2;
  margin-bottom: 50px;
  .repayment_r {
    color: #F5222D;
  }
}

.repayment_list{
  height: 750px;
  .repayment_list_cell {
    display: flex;
    line-height: 1;
    height: 160px;
    .repayment_cell_middle {
      position: relative;
      height: 100%;
      .repayment_cell_line{
        width: 1px;
        height: 50%;
        background-color: #ddd;
      }
      .repayment_cell_dot {
        position: absolute;
        top: 0;
        left: 50%;
        width: 20px;
        height: 20px;
        margin-left: -10px;
        border-radius: 50%;
        background-color: #ddd;
      }
    }
  }
}
.repayment_cell_left {
  width: 195px;
  text-align: right;
  margin-right: 34px;
  .repayment_cell_term {
    font-size: 28px;
    color: #333;
    margin-bottom: 12px;
  }
}
.repayment_cell_right {
  margin-left: 34px;
  .repayment_cell_money {
    font-size: 32px;
    color: #333;
    font-weight: bold;
    margin-bottom: 12px;
  }
}
.repayment_cell_note {
  font-size: 24px;
  color: #999;
  line-height: 1;
}