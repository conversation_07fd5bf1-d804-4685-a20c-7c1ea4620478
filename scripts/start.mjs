import {Command} from 'commander';
import inquirer from 'inquirer';
import shell from 'shelljs';

const commandProgram = new Command();

const InitPrompts = [
  {
    type: 'list',
    name: 'platform',
    message: '运行的平台',
    default: 'h5',
    choices: [
      {
        name: 'h5',
        value: 'h5',
      },
      {
        name: 'rn',
        value: 'rn',
      },
      {
        name: 'weapp',
        value: 'weapp',
      },
    ],
  },
  {
    type: 'list',
    name: 'apiEnv',
    message: '接口环境',
    default: 'bbit',
    choices: [
      {
        name: 'bbit',
        value: 'bbit',
      },
      {
        name: 'sit',
        value: 'sit',
      },
      {
        name: 'uat',
        value: 'uat',
      },
      {
        name: 'prod',
        value: 'production',
      },
    ],
  },
  {
    type: 'list',
    name: 'isBuild',
    message: '是否进行打包',
    default: false,
    choices: [
      {
        name: '否',
        value: false,
      },
      {
        name: '是',
        value: true,
      },
    ],
  },
];

/**添加页面 */
const startRun = async startOptions => {
  const {platform, apiEnv, isBuild} = startOptions;
  const taskName = `${isBuild ? 'build' : 'dev'}:${platform}-${apiEnv}`;
  console.log('%cstart.mjs line:54 taskName', 'color: #007acc;', taskName);
  shell.exec(`npm run ${taskName}`);
};

commandProgram.description('starting...').action(async () => {
  const startOptions = await inquirer.prompt(InitPrompts);
  startRun(startOptions);
});

commandProgram.parse();
