import React, {FC} from 'react';
import {Image, Text, View} from '@tarojs/components';
import arrowIcon from '../../assets/images/icon/<EMAIL>';
import styles from './index.module.scss';

export interface CellProps {
  // 标题
  title: React.ReactNode;
  // 值
  value?: React.ReactNode;
  // 占位符
  placeholder?: string;
  // 描述
  valueDesc?: React.ReactNode;
  // 图标路径
  icon?: string;
  // 是否可点击
  clickable?: boolean;
  // 点击事件回调
  onClick?: () => void;
  // 自定义样式
  style?: React.CSSProperties;
  // 自定义类名
  className?: string;
  // 自定义右侧内容
  right?: React.ReactNode;
}

const Cell: FC<CellProps> = ({
  title,
  value,
  placeholder,
  valueDesc,
  icon,
  clickable = false,
  onClick,
  style,
  className,
  right,
}) => {
  const handleClick = () => {
    if (clickable && onClick) {
      onClick();
    }
  };

  return (
    <View
      className={`${styles.cell} ${clickable ? styles.clickable : ''} ${className}`}
      style={style}
      onClick={handleClick}>
      <View className={styles.cell_content}>
        <View className={styles.cell_title}>{title}</View>
      </View>

      {right ? (
        <View className={styles.cell_arrow}>{right}</View>
      ) : (
        <View className={styles.cell_arrow}>
          {value ? (
            <View className={styles.cell_right}>
              <View className={styles.cell_arrow_text}>{value}</View>
              {valueDesc ? <View className={styles.cell_arrow_desc}>{valueDesc}</View> : null}
            </View>
          ) : (
            <View className={styles.cell_arrow_holder}>{placeholder}</View>
          )}
          <View className={`${styles.cell_arrow_container} ${clickable ? styles.ml : ''}`}>
            {clickable && <Image src={arrowIcon} className={styles.cell_arrow_icon} />}
          </View>
        </View>
      )}
    </View>
  );
};

export default Cell;
