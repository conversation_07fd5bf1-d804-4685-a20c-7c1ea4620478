import {View} from '@tarojs/components';
import styles from './index.module.scss';

interface RepaymentPlanItemProps {
  left?: React.ReactNode;
  right?: React.ReactNode;
}

const RepaymentPlanItem = ({right, left}: RepaymentPlanItemProps) => {
  return (
    <View className={styles.repayment_list_cell}>
      {left}
      <View className={styles.repayment_cell_middle}>
        <View className={`${styles.repayment_cell_lineTop}`}></View>
        <View className={`${styles.repayment_cell_lineBottom}`}></View>
        <View className={`${styles.repayment_cell_dot}`}></View>
      </View>
      {right}
    </View>
  );
};

export default RepaymentPlanItem;
