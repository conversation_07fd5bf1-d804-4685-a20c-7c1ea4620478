import { View } from '@tarojs/components';
import classNames from 'classnames';
import Section from '../Section';
import styles from './index.module.scss';
import { goWebviewLinkOrRoute, showToast } from '~utils/common';
import DownloadApp, { DownloadAppRef } from '~components/DownloadApp';
import { useRef } from 'react';
import debounce from '~utils/debounce';

// 将 URL 提取为常量，便于维护和多环境配置
const SERVICE_URLS = {
  ABOUT_US: 'https://www.hncy58test.com/aboutUs/0', // 关于我们
  PRODUCT_INTRO: 'https://www.hncy58test.com/product/PRODUCT_1/0', // 产品介绍
  NEWS_CENTER: 'https://www.hncy58test.com/list/COMPANY_NEWS',// 新闻中心
};

export default () => {
  const showDownloadAppRef = useRef<DownloadAppRef>(null);

  // 使用防抖包装点击事件
  const handleAboutUsClick = debounce(() => {
    try {
      goWebviewLinkOrRoute(SERVICE_URLS.ABOUT_US);
    } catch (error) {
      showToast('请稍后重试');
      console.error('跳转关于我们出错', error);
    }
  }, 300);

  const handleProductClick = debounce(() => {
    try {
      goWebviewLinkOrRoute(SERVICE_URLS.PRODUCT_INTRO);
    } catch (error) {
      showToast('请稍后重试');
      console.error('跳转产品介绍出错', error);
    }
  }, 300);

  const handleAppClick = debounce(() => {
    try {
      showDownloadAppRef.current?.showDownloadDialog();
    } catch (error) {
      showToast('请稍后重试');
      console.error('打开App下载弹窗出错', error);
    }
  }, 300);

  const handleNewsClick = debounce(() => {
    try {
      goWebviewLinkOrRoute(SERVICE_URLS.NEWS_CENTER);
    } catch (error) {
      showToast('请稍后重试');
      console.error('跳转新闻中心出错', error);
    }
  }, 300);

  return (
    <Section title='服务专区'>
      <View className={styles.content}>
        <View onClick={handleAboutUsClick} className={classNames(styles.serviceItem, styles.aboutus)}>
          关于我们
        </View>
        <View onClick={handleProductClick} className={classNames(styles.serviceItem, styles.production)}>
          产品介绍
        </View>
        <View onClick={handleAppClick} className={classNames(styles.serviceItem, styles.app)}>
          城一代App
        </View>
        <View onClick={handleNewsClick} className={classNames(styles.serviceItem, styles.kefu)}>
          新闻中心
        </View>
      </View>
      <DownloadApp ref={showDownloadAppRef} showLink={false} />
    </Section>
  );
};