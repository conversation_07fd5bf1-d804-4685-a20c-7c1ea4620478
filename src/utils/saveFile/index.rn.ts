import RNFS from 'react-native-fs';
import openFile from '../openFile';
import {open} from 'react-native-file-viewer';
export {pickSingle, types} from 'react-native-document-picker';


export default (filePath: string, base64: string) => {
  const fullName = `${RNFS.CachesDirectoryPath}/${filePath}`;
  return new Promise((resolve, reject) => {
    RNFS.exists(fullName)
      .then(exists => {
        if (exists) {
          console.log('存在');
          resolve('成功');
          openFile(fullName);
        } else {
          console.log('不存在');
          // 使用split('/')方法将路径分割成数组
          const parts = fullName.split('/');

          // 文件名是数组的最后一个元素
          const fileName = parts.pop() || parts.pop(); // 处理根目录下的文件名

          // 目录名是数组剩余的元素连接成字符串
          const dirName = parts.join('/') || '/'; // 如果是根目录，返回'/'

          console.log('文件名:', fileName);
          console.log('目录名:', dirName);

          if (dirName && fileName) {
            RNFS.mkdir(dirName)
              .then(() => {
                console.log('文件夹成功');
                RNFS.writeFile(`${fullName}`, base64, 'base64')
                  .then(() => {
                    resolve('成功');
                    openFile(fullName);
                  })
                  .catch(error => reject('文件写入失败'));
              })
              .catch(error => reject('创建文件失败'));
          } else {
            reject('文件名错误');
          }
        }
      })
      .catch(error => {
        reject('判断文件是否存在出错');
      });
  });
};

export const readFile = (path: string, type: string) => {
  return RNFS.readFile(path, type);
};

export const open = (path: string) => {
  return open(path);
};
