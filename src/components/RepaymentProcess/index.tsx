import {View} from '@tarojs/components';
import styles from './index.module.scss';

interface RepaymentPlanItemProps {
  left?: React.ReactNode;
  right?: React.ReactNode;
  active?: boolean;
}

const RepaymentProcessItem = ({right, left, active = false}: RepaymentPlanItemProps) => {
  return (
    <View className={styles.repayment_list_cell}>
      {left}
      <View className={styles.repayment_cell_middle}>
        <View className={`${styles.repayment_cell_lineTop} ${active ? styles.active_line : ''}`}></View>
        <View className={`${styles.repayment_cell_lineBottom} ${active ? styles.active_line : ''}`}></View>
        <View className={`${styles.repayment_cell_dot} ${active ? styles.active_dot : ''}`}></View>
      </View>
      {right}
    </View>
  );
};

export default RepaymentProcessItem;
