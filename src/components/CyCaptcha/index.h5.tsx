import {ForwardRefRenderFunction, forwardRef, useEffect, useImperativeHandle, useRef} from 'react';
import {PageInstance, getCurrentInstance} from '@tarojs/taro';
import {showToast} from '../../utils/common';
import './index.scss';

interface Props {
  onSuccess?: (ticket: string) => void;
  onCancel: () => void;
  onError: () => void;
}

export interface CyCaptchaT {
  show: () => void;
}
/**
 * 组件名称：人机校验组件
 * 组件描述：人机校验
 */
const CyCaptcha: ForwardRefRenderFunction<CyCaptchaT, Props> = ({onSuccess, onCancel, onError}, ref) => {
  const captch = useRef<any>();
  useImperativeHandle(ref, () => ({
    show: () => {
      handlerCaptchaShow();
    },
  }));
  // 获取页面实例
  useEffect(() => {
    if (!process.env.TARO_APP_CAPTCHA_ID) return;
    captch.current = new TencentCaptcha(process.env.TARO_APP_CAPTCHA_ID, rsp => {
      if (rsp.ret === 0) {
        const ticket = rsp.ticket;
        onSuccess?.(ticket);
      } else if (rsp.ret === 2) {
        console.log('用户主动关闭验证码！');
        onCancel();
      } else {
        console.log('验证失败！', rsp);
      }
    });
  }, []);
  // 弹出验证码
  const handlerCaptchaShow = () => {
    try {
      captch.current?.show();
    } catch (error) {
      // 进行业务逻辑，若出现错误需重置验证码，执行以下方法
      captch.current?.refresh();
    }
  };
  return <></>;
};

export default forwardRef(CyCaptcha);
