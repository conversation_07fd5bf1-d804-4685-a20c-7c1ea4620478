import Taro from '@tarojs/taro';
import {StartFaceParams} from '.';
import {getTecentFaceBizToken} from '../../services/face';
import {showToast} from '../common';
import {stringToArrayBufferFallback} from '../string';

const FACE_RECOGNITION_TYPE = '1';

export default async (params: StartFaceParams) => {
  let tencentBiztoken;
  const encodeStr = encodeURIComponent(`${params.certId},${params.name},${FACE_RECOGNITION_TYPE}`);
  const customerFaceData = Taro.arrayBufferToBase64(stringToArrayBufferFallback(encodeStr));
  try {
    const faceBiztokenRes = await getTecentFaceBizToken({
      customerFaceData,
    });
    if (faceBiztokenRes.result?.bizToken) {
      tencentBiztoken = faceBiztokenRes.result.bizToken;
    } else {
      tencentBiztoken = '';
    }
  } catch (e) {
    tencentBiztoken = '';
  }

  // 2. 调用腾讯活体识别
  if (tencentBiztoken) {
    return new Promise((resolve, reject) => {
      wx.startVerify({
        data: {
          token: tencentBiztoken, // 必要参数，BizToken
        },
        success: successRes => {
          // TODO: 上报日志
          console.log('face scucess', successRes);
          resolve(true);
        },
        fail: (err: any) => {
          // TODO: 上报日志
          console.log('face fail', err);
          err && showToast(err.ErrorMsg);
          reject(false);
        },
      });
    });
  } else {
    showToast('调用活体识别失败！');
    return false;
  }
};
