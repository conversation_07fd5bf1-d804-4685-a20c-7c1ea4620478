.periodsWrap {
  overflow: hidden;
  height: 72px;
}
.periodsScrollView {
  height: 80px;
}
.periodsContainer {
  display: flex;
  padding-bottom: 12px;
  box-sizing: content-box;
}

.periodsItem {
  position: relative;
  flex: 0 0 auto;
  width: 160px;
  height: 64px;
  line-height: 64px;
  text-align: center;
  background-color: $color-bg-main;
  border-radius: 8px;
  color: $color-text-normal;
  font-size: $font-size-sm;
  margin-right: 24px;
  &:last-child {
    margin-right: 0;
  }
  &.choosed {
    color: $color-primary;
    border: 1px solid $color-primary;
    background-color: #FAFCFF;
  }
  &.disabled {
    color: #bbb;
  }
  .choosedIcon {
    position: absolute;
    right: -1px;
    bottom: -1px;
    width: 40px;
    height: 40px;
  }
}

.block {
  height: 37px;
  width: 100%;
  margin-top: -12px;
  border-bottom: 1px solid $color-border-light;
}
.periodsBadge {
  bottom: 80%;
  font-size: $font-size-tiny;
  padding-left: 10px;
  padding-right: 10px;
}