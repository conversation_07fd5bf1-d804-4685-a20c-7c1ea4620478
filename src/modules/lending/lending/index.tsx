import { useEffect, useMemo, useRef, useState } from 'react';
import { <PERSON><PERSON>, Picker, Text, View } from '@tarojs/components';
import Taro, { useDidShow } from '@tarojs/taro';
import { CyButton, CyModal, CyPopup } from 'hncy58-taro-components';
import Agreement from '~components/Agreement';
import CellItem from '~components/CellItem';
import CellPickerItem from '~components/CellPickerItem';
import { PickerMode } from '~components/CellPickerItem';
import CyContractBar from '~components/CyContractBar';
// import Popup from '~components/Popup';
import CyVerificationCodeInput, { CyVerificationCodeInputRef } from '~components/CyVerificationCodeInput';
import MarqueeTip from '~components/MarqueeTip';
import PageContainer from '~components/PageWrap';
import Popup from '~components/Popup';
import WarnModal from '~components/WarnModal';
import useDict from '~hooks/useDict';
import useFace from '~hooks/useFace';
import { getBankCardList } from '~services/bankcard';
import { getLendingPopUp, lendingApply, lendingPreCheck, repaymentTrail } from '~services/lending';
import { checkVerifyCodeReq, sendVerifyCodeReq } from '~services/user';
import { hideLoading, router, showLoading, showModal, showToast } from '~utils/common';
import { transDateToChinese } from '~utils/date';
import { getLocation } from '~utils/location';
import { formatToFixedTwo } from '~utils/number';
import useUserStore from '../../../store/user';
import {
  ILendingTrailSchduleItem,
  ILoanInfo,
  IPickerItem,
  IRepaymentType,
  smsSceneType,
} from '../../../types/common-types';
import BankList from './components/BankList';
import CheckLoan from './components/CheckLoan';
import Coupon from './components/Coupon';
import Loan from './components/Loan';
import Periods from './components/Periods';
import Repayment from './components/Repayment';
import RepaymentDate from './components/RepaymentDate';
import TransactionPassword from './components/TransactionPassword';
import styles from './index.module.scss';
import useGetRepayment from './useGetRepayment';
import debounce from '~utils/debounce';
import useRequestError from '~hooks/useRequestError';
import CyPicker from '~components/CyPicker';

const ContractTypeCode2Item = {
  E53: {
    type: 'creditLine',
    name: '信用额度',
    nameEn: 'creditLine',
  },
};

const Lending = () => {
  const repaymentTypeMap = useGetRepayment();

  const { amountCreditInfo, user } = useUserStore();

  const [repaymentVisible, setRepaymentVisible] = useState(false);
  const [couponVisible, setCouponVisible] = useState(false);
  const [bankVisible, setBankVisible] = useState(false);
  const [loanVisible, setLoanVisible] = useState(false);
  const [dateVisible, setDateVisible] = useState(false);
  const [passwordVisible, setPasswordVisible] = useState(false);
  const [verifyCodeVisible, setVerifyCodeVisible] = useState(false);
  const [isAgreed, setIsAgreed] = useState<boolean>(false);
  const verifyCodeInputRef = useRef<CyVerificationCodeInputRef>(null);

  const amount = useRef<number | null>(null); // 借款金额
  const [isShowRender, setIsShowRender] = useState<boolean>(false);
  const [bankCardList, setBankCardList] = useState<API.IBankCardInfo[]>([]); // 银行卡列表
  const [bankCardInfo, setBankCardInfo] = useState<API.IBankCardInfo | null>(null); // // 银行卡信息
  const loanTerm = useRef<number>(0); // 借款期数
  const [relationshipMap, setRelationshipMap] = useState<IPickerItem[]>([]); // 借款用途集合
  const [loanPurposeValue, setLoanPurposeValue] = useState<string>(''); // 借款用途
  const [loanPurposeLabel, setLoanPurposeLabel] = useState<string>(''); // 借款用途
  const [repaymentType, setRepaymentType] = useState<IRepaymentType>(); // 还款方式
  const [repaymentPeriods, setRepaymentPeriods] = useState<number[]>([]); // 还款期数
  const [schedules, setSchedules] = useState<ILendingTrailSchduleItem[]>([]); // 还款计划列表
  const [firstLoanSchedule, setFirstLoanSchedule] = useState<ILendingTrailSchduleItem>(); // 首期借款信息
  const repaymentDate = useRef<number>(0); // 还款日
  const [fixedDueCalendar, setFixedDueCalendar] = useState<string>(''); // 还款日日历
  const [isEditDate, setIsEditDate] = useState<boolean>(true); //  是否可修改还款日
  const [loanInfo, setLoanInfo] = useState<ILoanInfo>({
    loanAmount: 0,
    loanTerm: 0,
    rate: 0,
    repaymentType: '',
  });
  const [canFixLoanDueDate, setCanFixLoanDueDate] = useState<boolean>(false); // 是否可修改固定还款日
  const [repaymentTypeFlag, setRepaymentTypeFlag] = useState<boolean>(false); // 还款方式变更，useEffect阻止首次执行
  const [needSupplyDocTypes, setNeedSupplyDocTypes] = useState<string[]>([]);

  const [modalVisible, setModalVisible] = useState<boolean>(false); // 反诈弹窗

  const position = useRef<API.IPosition>({
    latitude: '',
    longitude: '',
  });

  const { startFace } = useFace()

  useDidShow(() => {
    getBankCardListInfo();
  })

  useEffect(() => {
    // 反诈弹框检查
    (async () => {
      try {
        const hasPopup = await getLendingPopUp({ clickType: 'FIND' });
        hasPopup && setModalVisible(true);
      } catch (error) {
        console.log('反诈弹框检查失败', error);
      }
    })();

    initLoading();
    getLoanPurposeCode();
    // 设置默认还款方式 设置为默认 等额本金
    if (amountCreditInfo?.repaymentModeDTOS?.length) {
      const defalultItem =
        amountCreditInfo.repaymentModeDTOS.find(item => item.repaymentType === 'MCEP') ||
        amountCreditInfo.repaymentModeDTOS[0];
      const repaymentType = defalultItem?.repaymentType as IRepaymentType;
      const terms = [...(defalultItem?.terms || [])];
      setRepaymentPeriods(terms.reverse());
      setRepaymentType(repaymentType);
      loanTerm.current = Math.max(...terms);
    }
  }, []);

  useEffect(() => {
    if (repaymentTypeFlag && repaymentType) {
      setRepaymentTerms(repaymentType);
      handleRepaymentTrail();
    }
  }, [repaymentType]);

  /**
   * 处理反诈弹窗结果
   * @param result 用户选择的结果：'YES' | 'NO' | 'KNOW'
   */
  const handleResult = async (result: 'YES' | 'NO' | 'KNOW') => {
    try {
      await getLendingPopUp({ clickType: result });
      result === 'KNOW' && router.back();
    } catch (error) {
      console.log('反诈弹框操作失败', error);
    }
  };

  /**
   * 初始化加载数据
   */
  const initLoading = async () => {
    try {
      showLoading({ title: '加载中...' });
      await beforeLendingPreCheck();
    } catch (e) {
      console.log('请求错误', e);
    } finally {
      hideLoading();
    }
  };

  /**
   * 借款前检查
   * 检查用户是否可以进行借款，获取固定还款日等信息
   */
  const beforeLendingPreCheck = async () => {
    try {
      const res = await lendingPreCheck();
      if (res.existWaitLending) {
        router.replace({
          url: `/modules/lending/lending-result/index?status=LOAN_BLOCKING`,
        });
        return;
      }
      setCanFixLoanDueDate(res.canFixHistoryLoanDueDate);

      /**
       * 如果有固定还款日，则显示固定还款日并且无法修改
       * 如果没有固定还款日(值为0) 则显示当天日期，可修改
       * 如果有固定还款日并且大于23，如固定还款日是24，则显示24，可修改
       */
      if (res.fixedDueDate && res.fixedDueDate > 0 && res.fixedDueDate <= 23) {
        repaymentDate.current = res?.fixedDueDate;
        setIsEditDate(false);
      } else {
        setIsEditDate(true);
        // repaymentDate.current = res.fixedDueDate ? res.fixedDueDate : new Date().getDate();
      }
      setFixedDueCalendar(res.fixedDueCalendar);
      setNeedSupplyDocTypes(res.needSupplyDocTypes);
    } catch (error: any) {
      useRequestError({
        content: (<View style={{ textAlign: 'center' }}>借款前检查失败</View>),
      })
      console.log('请求错误', error);
    }
  };

  /**
   * 获取借款用途字典数据
   */
  const getLoanPurposeCode = async () => {
    const { dictMap } = await useDict('LOAN_PURPOSE');
    setRelationshipMap(dictMap);
  };

  /**
   * 获取可用优惠券列表
   */
  const getAvailableCoupons = async () => { };

  /**
   * 获取银行卡列表信息
   */
  const getBankCardListInfo = async () => {
    try {
      const res = await getBankCardList({
        requestFrom: 'lending',
      });
      setBankCardList(res);
      if (res && res.length) {
        const info = res.find(item => item.defaultFlag) || res[0]
        setBankCardInfo(info);
      }
    } catch (error) {
      console.log('请求错误', error);
    }
  };

  /** 银行卡是否需要升级弹窗 */
  const showBankCardUpgrade = (bankCardInfo: API.IBankCardInfo) => {
    CyModal.create({
      title: '提示',
      content: '该银行卡待升级，否则可能影响放款，请前往升级或者更换收款账户',
      confirmText: '去升级',
      cancelText: '更换账户',
      isShowCancel: true,
      maskClosable: false,
      onConfirm() {
        setBankVisible(false);
        router.push({
          url: `/modules/bankcard/bankcard-detail/index?bankCardInfo=${JSON.stringify(bankCardInfo)}`,
        });
      },
      onCancel() {
        setBankVisible(true)
      },
    })
  };

  /**
   * 发起借款试算
   * 根据借款金额、期数、还款方式等参数计算还款计划
   */
  const handleRepaymentTrail = async () => {
    try {
      Taro.showLoading({ title: '试算中...', mask: true });
      const body = {
        loanAmount: amount.current || '',
        loanTerm: loanTerm.current,
        repaymentType: repaymentType as IRepaymentType,
        fixedDueDate: repaymentDate.current == 0 ? '' : String(repaymentDate.current),
      };
      const res = await repaymentTrail(body);
      Taro.hideLoading();
      setSchedules(res.schedules || []);
      if (res.schedules && res.schedules.length) {
        setFirstLoanSchedule(res.schedules[0]);
      }
    } catch (error) {
      console.log('请求错误', error);
      Taro.hideLoading();
    }
  };

  /**
   * 处理借款金额变化
   * @param value 输入的借款金额
   */
  const handleAmountChange = (value: number | null) => {
    if (value != null) {
      setIsShowRender(true);
      amount.current = value;
      handleRepaymentTrail();
      if (bankCardInfo?.canUpgrade) {
        showBankCardUpgrade(bankCardInfo);
      }
    } else {
      setIsShowRender(false);
    }
  };

  /**
   * 人脸识别成功回调函数
   */
  const faceSuccessCb = async () => {
    // 获取位置信息
    getLocationInfo()
  };

  /**
   * 获取地理位置信息
   */
  const getLocationInfo = () => {
    getLocation({
      onSuccess: async data => {
        console.log('获取地理位置结果', data);
        if (data.longitude && data.latitude) {
          position.current.latitude = data.latitude + '';
          position.current.longitude = data.longitude + '';
        }

        setLoanInfo({
          loanAmount: amount.current ?? 0,
          loanTerm: loanTerm.current,
          rate: amountCreditInfo.rate,
          repaymentType: repaymentType as IRepaymentType,
        });
        setLoanVisible(true);
      },
      onFail: e => {
        console.log('获取地理位置失败', e);
      },
      usage: '用于借款'
    });
  }

  /**
   * 发起借款申请
   * 验证借款信息并获取地理位置后显示借款确认弹窗
   */
  const handleLoan = debounce(async () => {
    if (!isAgreed) {
      showToast('请先阅读并同意协议');
      return;
    }

    // 校验银行卡是否需要升级
    if (bankCardInfo?.canUpgrade) {
      showBankCardUpgrade(bankCardInfo)
      return
    }
    if (bankCardInfo?.singleLimit && amount.current && bankCardInfo.singleLimit < amount.current) {
      CyModal.create({
        title: '温馨提示',
        content: `输入借款金额超过收款银行卡单笔最大限额${bankCardInfo?.singleLimit}元， 请重新输入或更换银行卡`,
        isShowCancel: false,
        confirmText: '我知道了',
      });
      return;
    }
    if (bankCardInfo?.dayLimit && amount.current && bankCardInfo.dayLimit < amount.current) {
      CyModal.create({
        title: '温馨提示',
        content: `输入借款金额超过收款银行卡单日最大限额${bankCardInfo?.dayLimit}元， 请重新输入或更换银行卡`,
        isShowCancel: false,
        confirmText: '我知道了',
      });
      return;
    }

    startFace({
      requestFrom: 'lending',
      loanAmount: (amount.current || '') + '',
      faceSuccess: () => {
        faceSuccessCb();
      },
      faceFail: () => {
        showToast('活体识别失败');
      },
    })

  }, 300);

  /**
   * 提交借款申请数据
   * @param tradeRandomCode 交易随机码
   * @param tradePassword 交易密码
   */
  const submitData = async ({ tradeRandomCode, tradePassword }: { tradeRandomCode: string; tradePassword: string }) => {
    try {
      const body = {
        loanAmount: amount.current || '',
        loanTerm: loanTerm.current,
        purpose: loanPurposeValue,
        repaymentType: repaymentType as IRepaymentType,
        acctId: bankCardInfo?.acctId || '',
        smsSceneType: 'M1004',
        tradePassword,
        tradeRandomCode,
        longitude: position.current.longitude,
        latitude: position.current.latitude,
        fixedDueDate: repaymentDate.current,
      };
      console.log('发起借款请求参数', body);
      showLoading({ title: '借款申请中...', mask: true });
      const { billNo } = await lendingApply(body);
      setPasswordVisible(false);
      router.replace({
        url: `/modules/lending/lending-result/index?billNo=${billNo}&bankCardInfo=${JSON.stringify(bankCardInfo)}`,
      });
      hideLoading();
    } catch (error: any) {
      hideLoading();
      if (error.code !== '10088') {
        showModal({
          title: '温馨提示',
          content: error.message || '服务器出错了，请稍候再试!',
          confirmText: '确定',
          confirmColor: '#2F54EB',
          showCancel: false,
          success: function (res) {
            router.reLaunch({ url: '/pages/check/index' });
          },
        });
      } else {
        showToast(error.message || '服务器出错了，请稍候再试!');
      }
    }
  };

  /**
   * 处理交易密码输入
   * @param params 包含交易密码和随机码的参数对象
   */
  const handlePassword = (params: { tradePassword: string; tradeRandomCode: string }) => {
    submitData({
      tradePassword: params.tradePassword,
      tradeRandomCode: params.tradeRandomCode,
    });
  };

  /**
   * 处理借款期数选择
   * @param period 选择的借款期数
   */
  const handleSelectPeriod = (period: number) => {
    loanTerm.current = period;
    handleRepaymentTrail();
  };

  /**
   * 处理还款方式选择
   * @param type 选择的还款方式类型
   */
  const handleRepayment = (type: IRepaymentType, isClisose?: boolean) => {
    setRepaymentVisible(isClisose || false);
    setRepaymentType(type);
    setRepaymentTypeFlag(true);
  };

  /**
   * 处理银行卡选择
   * @param item 选择的银行卡信息
   */
  const handleBankCard = (item: API.IBankCardInfo) => {
    setBankCardInfo(item);
    if (item.canUpgrade) {
      showBankCardUpgrade(item)
      return
    }
    setBankVisible(false);
  };

  /**
   * 处理还款日选择
   * @param idx 选择的还款日期
   */
  const handleRepaymentDate = (idx: number | undefined) => {
    setDateVisible(false);
    if (idx) {
      repaymentDate.current = idx;
      handleRepaymentTrail();
    }
  };

  /**
   * 处理核对借款信息
   * 关闭借款信息弹窗，显示验证码输入弹窗
   */
  const handleCheckLoan = debounce(() => {
    setLoanVisible(false);
    setVerifyCodeVisible(true);
    verifyCodeInputRef.current?.sendCode();
  }, 300);

  /**
   * 设置还款期数选项
   * @param repaymentType 还款方式类型
   */
  const setRepaymentTerms = (repaymentType: IRepaymentType) => {
    const { terms } = amountCreditInfo.repaymentModeDTOS.find(item => item.repaymentType === repaymentType) || {};
    const temp = [...(terms ?? [])];
    setRepaymentPeriods(temp.reverse());
    if (!temp.includes(loanTerm.current)) {
      loanTerm.current = Math.max(...temp);
    }
  };

  /**
   * 渲染收款账户描述信息
   * @returns 包含单笔和单日借款限额的描述组件
   */
  const BankCardInfoDesc = () => {
    const singleLimit = bankCardInfo?.singleLimit ? bankCardInfo?.singleLimit / 10000 + '万' : '';
    const dayLimit = bankCardInfo?.dayLimit ? bankCardInfo?.dayLimit / 10000 + '万' : '';
    return <View className={styles.page_text}>{`单笔借款上限${singleLimit}，单日累计借款上限${dayLimit}`}</View>;
  };

  /**
   * 获取银行账户显示文本
   * @returns 格式化的银行卡显示文本
   */
  const getBankAccountDisplay = (): string => {
    if (!bankCardInfo) return '';
    const { bankName, desensitizeAccountNo } = bankCardInfo;
    if (!desensitizeAccountNo) return bankName || '未选择';
    return `${bankName}(${desensitizeAccountNo?.slice(-4)})`;
  };

  /**
   * 渲染首期还款信息
   * @returns 包含首期还款日期和金额的组件
   */
  const firstRepaymentInfo = () => {
    const firstDate = firstLoanSchedule?.dueDate || '';
    const firstAmount = firstLoanSchedule?.totalAmount || '0';
    return (
      <View className={styles.page_text}>
        首期<Text className={styles.page_text_b}>{transDateToChinese(firstDate)}</Text>，应还
        <Text className={styles.page_text_b}>{formatToFixedTwo(firstAmount)}元</Text>
      </View>
    );
  };

  /**
   * 发送短信验证码
   * @returns 发送是否成功的布尔值
   */
  const sendVerifyCode = async () => {
    console.log('发送验证码====', user.mobile, smsSceneType.M1004);
    try {
      await sendVerifyCodeReq({ smsSceneType: smsSceneType.M1004 });
      return true;
    } catch (e) {
      console.error('发送验证码失败', e);
      return false;
    }
  };

  /**
   * 处理验证码验证
   * @param code 用户输入的验证码
   */
  const handleVerify = async (code: string) => {
    console.log('验证码', code);
    try {
      await checkVerifyCodeReq({
        verificationCode: code,
        smsSceneType: smsSceneType.M1004,
      });
      setVerifyCodeVisible(false);
      setPasswordVisible(true);
      verifyCodeInputRef.current?.clearCode(true)
    } catch (error) {
      console.error('验证码验证失败', error);
      verifyCodeInputRef.current?.clearCode(false)
    }
  };

  /**
   * 提交数据前的校验
   * @returns 校验是否通过的布尔值
   */
  const submitDataCheck = () => {
    if (!amount.current) {
      showToast('请输入借款金额');
      return false;
    }
    if (!loanTerm.current) {
      showToast('请选择借款期限');
      return false;
    }

    if (!repaymentType) {
      showToast('请选择还款方式');
      return false;
    }
    if (!bankCardInfo) {
      showToast('请选择收款账号');
      return false;
    }
    if (!loanPurposeValue) {
      showToast('请选择借款用途');
      return false;
    }
    if (!repaymentDate.current) {
      showToast('请选择还款日');
      return false;
    }
    return true;
  };

  return (
    <PageContainer className={styles.page_lending}>
      <MarqueeTip
        darkIcon
        content='借款仅用于个人日常消费，禁止用于买房/车，投资股票等行为'
        color='#616C92'
        duration={10}
      />
      <View className={styles.page_content}>
        <View className={styles.page_card}>
          <Loan onAmountChange={handleAmountChange} />
        </View>
        {isShowRender ? (
          <>
            <View className={styles.page_card}>
              <CellItem
                title='借多久'
                value=''
                placeholder='提前还款无手续费'
                style={repaymentPeriods.length ? { borderBottom: 'none' } : {}}
              />
              {repaymentPeriods.length ? (
                <Periods
                  repaymentType={repaymentType ?? 'MCIP'}
                  selectedPeriod={loanTerm.current}
                  periods={repaymentPeriods}
                  onSelect={handleSelectPeriod}
                />
              ) : null}
              <CellItem
                title='还款日'
                placeholder='请选择还款日'
                value={repaymentDate.current ? `每月${repaymentDate.current}日` : ''}
                clickable={isEditDate}
                onClick={() => {
                  if (isEditDate) {
                    setDateVisible(true);
                  }
                }}
              />
              <CellItem
                title='怎么还'
                value={repaymentTypeMap[repaymentType ?? 'MCIP'] || ''}
                valueDesc={firstRepaymentInfo()}
                clickable
                onClick={() => setRepaymentVisible(true)}
              />
              <CellItem
                title='收款账户'
                value={getBankAccountDisplay()}
                valueDesc={BankCardInfoDesc()}
                clickable
                onClick={() => setBankVisible(true)}
              />
            </View>
            <View className={styles.page_card} style={{ paddingTop: 0, paddingBottom: 0 }}>
              <CyPicker
                options={relationshipMap}
                onChange={(value, label) => {
                  setLoanPurposeValue(value);
                  setLoanPurposeLabel(label);
                }}
              >
                <CellItem
                  title='借款用途'
                  value={loanPurposeLabel}
                  placeholder='请选择借款用途'
                  clickable
                />
              </CyPicker>
              {/* <CellPickerItem
                // mode={PickerMode.Selector}
                className={styles.CellItem}
                range={relationshipMap}
                value={loanPurposeLabel}
                title='借款用途'
                placeholder='请选择借款用途'
                onChange={(value, label) => {
                  setLoanPurposeValue(value);
                  setLoanPurposeLabel(label);
                }}
                clickable></CellPickerItem> */}
              {/* <CellItem
                title='优惠券'
                value='五折券'
                placeholder='请选择优惠券'
                valueDesc={
                  <View className={styles.page_text}>
                    可优惠<Text className={styles.page_text_r}>12321312元</Text>
                  </View>
                }
                clickable
                onClick={() => setCouponVisible(true)}></CellItem> */}
            </View>
            <CyContractBar
              className={styles.contractBar}
              justify='center'
              onChange={val => {
                setIsAgreed(val);
              }}
              lazy
              checked={isAgreed}
              mustView
              contracts={[
                {
                  code: 'E54',
                  params: {
                    loanAmount: amount.current || '',
                    loanTerm: loanTerm.current,
                    acctId: bankCardInfo?.acctId || '',
                    purpose: loanPurposeLabel,
                    billDay: repaymentDate.current,
                    rate: amountCreditInfo.rate,
                    repaymentType: repaymentType as IRepaymentType,
                  },
                  noCache: true,
                  check: submitDataCheck,
                },
                ...needSupplyDocTypes.map(code => ({
                  code,
                })),
              ]}
            />
            <View className={styles.page_bottom}>
              <CyButton round block type='primary' onClick={handleLoan}>
                下一步
              </CyButton>
            </View>
          </>
        ) : null}
      </View>
      <Repayment
        visible={repaymentVisible}
        amountRepaymentType={repaymentType}
        repaymentModes={amountCreditInfo?.repaymentModeDTOS}
        schedules={schedules}
        currentPeriod={loanTerm.current}
        onChange={handleRepayment}
        onClose={() => setRepaymentVisible(false)}
      />
      <Coupon visible={couponVisible} onClose={() => setCouponVisible(false)} />
      <BankList
        visible={bankVisible}
        bankCardList={bankCardList}
        onComplete={handleBankCard}
        onClose={() => setBankVisible(false)}
      />
      <CheckLoan
        visible={loanVisible}
        loanInfo={loanInfo}
        onConfirm={handleCheckLoan}
        onClose={() => setLoanVisible(false)}
      />
      <RepaymentDate
        visible={dateVisible}
        fixedDueCalendar={fixedDueCalendar}
        oncChange={handleRepaymentDate}
        onClose={() => setDateVisible(false)}
      />
      <TransactionPassword
        visible={passwordVisible}
        onComplete={handlePassword}
        onClose={() => setPasswordVisible(false)}
      />
      <Popup
        visible={verifyCodeVisible}
        title='输入验证码'
        onClose={() => setVerifyCodeVisible(false)}
        showClose
        closeOnMaskClick={false}>
        <CyVerificationCodeInput
          ref={verifyCodeInputRef}
          phone={user.mobile}
          onVerify={handleVerify}
          onSendCode={sendVerifyCode}
        />
      </Popup>
      <WarnModal visible={modalVisible} onResult={handleResult} />
    </PageContainer>
  );
};

export default Lending;
