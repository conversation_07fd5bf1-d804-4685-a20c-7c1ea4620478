.entryBar {
    display: flex;
    flex-direction: row;
    // justify-content: space-around;
    flex-wrap: wrap;
}
.entryItem {
    position: relative;
    display: flex;
    flex-direction: column;
    align-items: center;
    margin-top: 40px;
}
.entryIcon {
    width: 64px;
    height: 64px;
}
.entryText {
    color: #333;
    font-size: 28px;
    text-align: center;
    margin-top: 12px;
}

.small {
    .entryIcon {
        width: 48px;
        height: 48px;
    }
    .entryText {
        font-size: 28px;
    }
    .entryFigure {
        position: relative;
        width: 48px;
        height: 48px;
    }
}

.disableMask {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 1;
    font-size: 20px;
    color: #fff;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
}

.entryFigure {
    position: relative;
    width: 64px;
    height: 64px;
}