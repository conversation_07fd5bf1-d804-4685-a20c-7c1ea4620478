import React, {useEffect, useMemo} from 'react';
import {Image, ScrollView, View} from '@tarojs/components';
import {useDidShow} from '@tarojs/taro';
import Taro from '@tarojs/taro';
import PageWrap from '~components/PageWrap';
import {useUserRegisterInterceptor} from '~hooks/useUserRegisterInterceptor';
import grzlIcon from '~images/index/<EMAIL>';
import repaymentIcon from '~images/index/hk.png';
import jkjlIcon from '~images/index/<EMAIL>'
import hkjlIcon from '~images/index/<EMAIL>';
import sryIcon from '~images/index/<EMAIL>';
import topBg from '~images/index/top-bg.png';
import xkylIcon from '~images/index/<EMAIL>';
import useMainStore from '~store/main';
import {jumpToActivityPage} from '~utils/activity';
import useUserStore from '../../store/user';
import '../../styles/themes/index.scss';
import {goWebviewLinkOrRoute, router, showToast} from '../../utils/common';
import ActivitySection from './components/ActivitySection';
import ActivitySwiper from './components/ActivitySwiper';
import CreditCard from './components/CreditCard';
import EntryBar from './components/EntryBar';
import Header from './components/Header';
import LoginEntry from './components/LoginEntry';
import NoticeBar from './components/NoticeBar';
import PrivacyModal from './components/PrivacyModal';
import ProductSwiper from './components/ProductSwiper';
import ServiceSection from './components/ServiceSection';
import styles from './index.module.scss';
import { CyModal } from 'hncy58-taro-components';
import { getLocation } from '~utils/location';
import Card from '~components/Card';

const Index = () => {
  /** 1. 变量定义 */
  const {goLinkWithCreditInterceptor, goLinkWithLoginInterceptor} = useUserRegisterInterceptor();

  const indexEntries = [
    {icon: repaymentIcon, name: '还款', onClick: () => goLinkWithCreditInterceptor('/modules/repayment/entry/index')},
    {icon: jkjlIcon, name: '借款记录', onClick: () => goLinkWithCreditInterceptor(`/modules/loanAndRepayment/index/index?type=loan`)},
    {icon: hkjlIcon, name: '还款记录', onClick: () => goLinkWithCreditInterceptor(`/modules/loanAndRepayment/index/index?type=repayment`)},
    // {
    //   icon: sryIcon,
    //   name: '生日月',
    //   onClick: () => {
    //     goLinkWithLoginInterceptor('https://sit.hncy58test.com/activity/activity/wheel.html?activityId=206125951531098112', true);
    //   },
    // },
    // {
    //   icon: xkylIcon,
    //   name: '新客有礼',
    //   onClick: () => {
    //     goLinkWithLoginInterceptor('https://sit.hncy58test.com/activity/activity/task.html?activityId=206121814801260544', true);
    //   },
    // },
    {
      icon: grzlIcon,
      name: '个人资料',
      onClick: () => {
        goLinkWithCreditInterceptor('/modules/user/edit-message/index');
      },
    },
  ];

  /** 2. usestates + storestates */
  const {user, amountCreditInfo, updateUserInfo} = useUserStore();

  const isHasCreditAmount = useMemo(() => {
    return !!amountCreditInfo.creditAmount;
  }, [amountCreditInfo]);

  const isLogin = useMemo(() => {
    return !!user.mobile;
  }, [user]);
  /** 3. effects */
  useDidShow(() => {
    // 更新tabbar
    const {setTabbarIndex} = useMainStore.getState();
    setTabbarIndex(0); // 我的页面索引
    // Taro.getCurrentInstance().page?.getTabBar?.()?.setData?.({selected: 0});
    updateUserInfo();
  });
  /** 4. 方法定义 */
  const handleApply = () => {
    router.push({
      url: '/modules/credit_authorization/identify/index',
    });
  };

  /** 5. ui */
  return (
    <PageWrap className={styles.page} showStatusBarColor>
      <View className={styles.topBg}>
        <Image className={styles.topBgImg} src={topBg} />
      </View>
      <View className={styles.pageContent}>
        <Header />
        <View style={{zIndex: 9, position: 'relative'}}>
          <View className='vertical-gap'></View>
          {isHasCreditAmount && isLogin ? (
            <>
              {/* <NoticeBar /> */}
              <View className='vertical-gap'></View>
              <CreditCard onApply={handleApply} />
              <View className='vertical-gap'></View>
              <Card><EntryBar entries={indexEntries} entryItemStyle={{marginTop: 0}} /></Card>
              <View className='vertical-gap'></View>
              <ActivitySwiper />
            </>
          ) : (
            <>
              <CreditCard onApply={handleApply} />
              <Card><EntryBar entries={indexEntries} entryItemStyle={{marginTop: 0}} /></Card>
              <View className='vertical-gap'></View>
              <ActivitySwiper />
            </>
          )}
          {/* <View className='vertical-gap'></View> */}
          {/* <ProductSwiper /> */}
          {/* <View className='vertical-gap'></View> */}
          {/* <ActivitySection /> */}
          {!isLogin && <LoginEntry />}
          {/* <BillSummaryCard /> */}
          <View className='vertical-gap'></View>
          <ServiceSection />
          <PrivacyModal />
        </View>
      </View>
    </PageWrap>
  );
};

export default Index;
