// sm4js.d.ts

declare module '~utils/sm4js' {
  export interface Sm4Config {
    key: string; // 密钥，通常为 16 位十六进制字符串
    iv?: string; // 初始向量，可选，通常为 16 位十六进制字符串
    mode?: 'ecb' | 'cbc'; // 加密模式，可选，可以是 'ecb' 或 'cbc'
    cipherType?: 'hex' | 'base64'; // 输出类型，可选，可以是 'hex' 或 'base64'
  }

  export default class Sm4js {
    constructor(config: Sm4Config);

    encrypt(data: string): string;
    decrypt(data: string): string;
  }
}
