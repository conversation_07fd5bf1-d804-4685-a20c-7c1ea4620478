.container {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20px;
}

.title {
  font-size: 32px;
  font-weight: 500;
  color: #333;
  margin-bottom: 16px;
  align-self: flex-start;
}

.subtitle {
  font-size: 24px;
  color: #999;
  margin-bottom: 40px;
  align-self: flex-start;
}

.codeInputContainer {
  display: flex;
  justify-content: space-between;
  width: 100%;
  padding-bottom: 40px;
}

.inputWrapper {
  width: 88px;
  height: 112px;
  position: relative;
  border-radius: 16px;
  background-color: #f5f5f5;
  display: flex;
  justify-content: center;
  align-items: center;
  box-sizing: border-box;
  padding-bottom: 1px;
}

.focused {
  border: 1px solid #437DFF;
}

.codeInput {
  width: 100%;
  height: 100%;
  text-align: center;
  font-size: 36px;
  font-weight: 500;
  color: #333;
  background-color: transparent;
  padding: 0;
}

/* 当输入框有值时添加底部边框 */
// .inputWrapper:has(.codeInput:not([value=''])) {
//   border-bottom: 2px solid #437DFF;
//   background-color: #fff;
// }

.buttonContainer {
  width: 100%;
  display: flex;
  justify-content: center;
  margin-top: 20px;
}

.linkButtonContainer {
  justify-content: flex-end;
}