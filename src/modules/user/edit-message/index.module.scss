
.pageWrapper {
}

.supplementList {
  padding: 40px;
}

.listItem {
  display: flex;
  align-items: center;
  background-color: #ffffff;
  border-radius: 16px;
  padding: 64px 32px 52px 32px;
  margin-bottom: 24px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.05);
}

.itemIcon {
  width: 80px;
  height: 80px;
  margin-right: 32px;
}

.itemTextContainer {
  flex: 1;
}

.itemTitle {
  font-size: 36px;
  font-weight: 500;
  color: #333;
  margin-bottom: 8px;
}

.itemSubtitle {
  font-size: 24px;
  color: #999;
}

.itemArrow {
  width: 32px;
  height: 32px;
  color: #ccc;
}
