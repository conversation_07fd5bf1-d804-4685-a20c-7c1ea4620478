.page {
  background-color: #f5f5f5;
  min-height: 100vh;
  padding-bottom: 100px; // 为底部按钮留出空间
}

.container {
  padding: 16px;
}

.card {
  background-color: #fff;
  border-radius: 8px;
  padding: 48px 0 48px 40px;
  margin-top: 16px;
}


.title {
  font-size: 32px;
  font-weight: 600;
  color: #333;
}

.subtitle {
  font-size: 26px;
  color: #999;
  margin-top: 16px;
}

.highlight {
  color: $color-primary;
}

.uploadArea {
  display: flex;
  justify-content: center;
  align-items: center;
  background: url('../../../assets/images/credit-apply/<EMAIL>') no-repeat center center;
  background-size: 260px 180px;
  border-radius: 8px;
  height: 400px;
}

.previewPlaceholder {
  width: 80px;
  height: 60px;
  opacity: 0.5;
}

.uploadButton {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 90px;
    cursor: pointer;
    margin: 0 40px;
    border-radius: 8px;
    border: 1px dashed #d9d9d9;
    font-size: 26px;
}

.uploadButtonText {
    font-size: 26px;
  color: $color-primary;
}

.photoGrid {
  display: flex;
  flex-wrap: nowrap;
  gap: 8px;
  margin-top: 40px;
}

.photoWrapper {
  position: relative;
  overflow: visible;
}

.photoThumbnail {
  width: 160px;
  height: 160px; // 根据需要调整
  border-radius: 16px;
}

.deleteButton {
  position: absolute;
  top: -16px;
  right: -16px;
  width: 40px;
  height: 40px;
  background-color: rgba(0, 0, 0, 0.6);
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  cursor: pointer;
}

.deleteIcon {
  color: #fff;
  font-size: 12px;
  line-height: 16px;
}

.footer {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 30px;
}