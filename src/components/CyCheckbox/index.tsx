import {FC, ForwardRefRenderFunction, Ref, forwardRef, useImperativeHandle, useState} from 'react';
import {Checkbox, CheckboxGroup, Image, Label, Text, View} from '@tarojs/components';
import CheckedIcon from '~images/icon/<EMAIL>';
import UnCheckIcon from '~images/icon/uncheck.png';
import CyInput from '../CyInput';
import './index.scss';
import { showToast } from '~utils/common';

type CheckboxOption = {label: string; value: string; child?: React.ReactNode, disabled?: boolean;};

type Props = {
  options: CheckboxOption[];
  selectOptions?: string[];
  onChange?: (v: unknown) => void;
  name?: string;
  maxSelect?: number;
  maxSelectErrorMessage?: string;
};

export type CyCheckboxRef = {
  value: string[];
  selectAll: (maxNum?: number) => void;
  unSelectAll: () => void;
};

/**
 * 组件名称：单选框
 * 组件描述：单选组件
 */
const CyCheckbox: ForwardRefRenderFunction<CyCheckboxRef, Props> = ({selectOptions = [], options, name, maxSelect, maxSelectErrorMessage, onChange}, ref) => {
  const [currentVal, setCurrentVal] = useState<string[]>(selectOptions);
  useImperativeHandle(ref, () => ({
    get value() {
      return currentVal;
    },
    set value(newValue) {
      setCurrentVal(newValue);
    },
    selectAll(maxNum?: number) {
      if(maxNum && options && options.length > maxNum) {
        showToast(`最多只能选择${maxNum}笔`);
      }
      const newVal = options.map(it => it.value).slice(0, maxNum || options.length);
      changeHandler(newVal);
      setCurrentVal(newVal);
    },
    unSelectAll() {
      changeHandler([]);
      setCurrentVal([]);
    },
  }));

  const changeHandler = (val: any) => {
    const newVal = {
      target: {
        name,
        value: val,
      },
      detail: {
        value: val,
      },
    };
    onChange?.(newVal);
  };
  const handleCheck = (val: CheckboxOption) => {
    if(val.disabled) return;
    const newVal = currentVal.includes(val.value)
      ? currentVal.filter(it => it !== val.value)
      : [...currentVal, val.value];

    if (!currentVal.includes(val.value) && maxSelect && newVal.length > maxSelect) {
      showToast(maxSelectErrorMessage || `最多只能选择${maxSelect}笔`)
      return
    }
    changeHandler(newVal);
    setCurrentVal(newVal);
  };
  return (
    <View className='comp-cy-checkbox'>
      {options?.map(option => {
        const checked = currentVal.includes(option.value);
        return (
          <View onClick={() => handleCheck(option)} key={option.value} className='cy-checkbox-item'>
            <View className='cy-checkbox-lable'>
              {option.disabled ? <Text className='cy-checkbox-icon'></Text> : (checked ? <Image className='cy-checkbox-icon' src={CheckedIcon}></Image> : <Text className='cy-uncheckbox-icon'></Text>)}
              <Text className='cy-checkbox-text'>{option.label}</Text>
            </View>
            {option.child}
          </View>
        );
      })}
    </View>
  );
};

export default forwardRef(CyCheckbox);
