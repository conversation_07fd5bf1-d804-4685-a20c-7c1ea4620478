import Taro from '@tarojs/taro';
import {getSobotUrl} from '~services/cusServiceCenter';
import {getStoragePromise, router, setStoragePromise, showToast} from './common';

// 缓存有效期（毫秒）- 默认3天
const DAY_UNIT = 24 * 60 * 60 * 1000;
const DEFAULT_EXPIRE_TIME = 3;

// 客服中心URL缓存键名
export const CUSTOMER_CENTER_URL_KEY = 'customer_center_url';

/**
 * 从缓存中获取URL
 * @param key 缓存键名
 * @returns 如果缓存存在且有效则返回URL，否则返回null
 */
export const getUrlFromCache = async (key: string): Promise<string | null> => {
  try {
    const cacheStr = await getStoragePromise(key);
    if (cacheStr) {
      const cache = JSON.parse(cacheStr);
      const currentTime = new Date().getTime();
      // 检查缓存是否在有效期内
      if (cache.expireTime > currentTime) {
        return cache.url;
      }
    }
  } catch (error) {
    console.log('获取URL缓存失败', error);
  }
  return null;
};

/**
 * 将URL保存到缓存
 * @param key 缓存键名
 * @param url URL字符串
 * @param expireTime 过期时间（毫秒），默认为1天
 * @returns 保存成功返回true，否则返回false
 */
export const saveUrlToCache = async (
  key: string,
  url: string,
  expireTime: number = DEFAULT_EXPIRE_TIME,
): Promise<boolean> => {
  try {
    const currentTime = new Date().getTime();
    await setStoragePromise(
      key,
      JSON.stringify({
        url,
        cacheTime: currentTime,
        expireTime: currentTime + expireTime,
      }),
    );
    return true;
  } catch (error) {
    console.log('保存URL缓存失败', error);
    return false;
  }
};

/**
 * 获取客服中心URL，优先从缓存获取，缓存不存在或已过期则从服务器获取并更新缓存
 * @param forceRefresh 是否强制刷新缓存
 * @param expireTime 缓存过期时间（毫秒），默认为3天
 * @returns 客服中心URL
 */
export const getCustomerCenterUrl = async (
  forceRefresh: boolean = false,
  expireTime: number = DEFAULT_EXPIRE_TIME,
): Promise<string> => {
  try {
    // 如果不是强制刷新，则尝试从缓存获取
    if (!forceRefresh) {
      const cachedUrl = await getUrlFromCache(CUSTOMER_CENTER_URL_KEY);
      if (cachedUrl) {
        return cachedUrl;
      }
    }
    Taro.showLoading();
    // 缓存不存在、已过期或强制刷新，从服务器获取
    const response = await getSobotUrl();
    if (response && response.url) {
      // 保存到缓存
      await saveUrlToCache(CUSTOMER_CENTER_URL_KEY, response.url, expireTime * DAY_UNIT);
      return response.url;
    }
    throw new Error('获取客服中心URL失败');
  } catch (error: any) {
    showToast(error.message || '获取客服中心URL失败');
    console.log('获取客服中心URL失败', error);
    // 发生错误时，尝试返回缓存中的URL（即使已过期）
    const cachedUrl = await getUrlFromCache(CUSTOMER_CENTER_URL_KEY);
    if (cachedUrl) {
      return cachedUrl;
    }
    throw error;
  } finally {
    Taro.hideLoading();
  }
};

/**
 * 跳转客服中心
 */
export const goCusServiceCenter = async () => {
  const sobotUrl = await getCustomerCenterUrl();
  if (sobotUrl) {
    router.push({
      url: `/modules/webview/index/index?url=${encodeURIComponent(sobotUrl)}`,
    });
  }
};
