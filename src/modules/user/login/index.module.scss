.login {
    padding: 0 40px;
    background: url('../../../assets/images/login/bg.png') no-repeat;
    background-size: 100%;
}

.logo {
    width: 480px;
    height: 48px;
    margin-top: 100px;
}

.companyName {
    font-weight: bold;
    font-size: 32px;
    margin-top: 40px;
}

.companySubDesc {
    font-size: 24px;
    color: #9fa0a0;
}

.loginButton {
    font-size: 28px;
}

.fastLoginButton {
    margin-bottom: 32px;
}

.featureBar {
    display: flex;
    margin-top: 24px;
    margin-bottom: 140px;
    gap: 8px;
}

.featureItem {
    color: $color-text-secondary;
    font-size: 22px;
    border: 1px dashed $color-border-normal;
    border-radius: 4px;
    height: 40px;
    // width: 144px;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 0 8px;
}

.contractBar {
    margin-top: 52px;
}

.loginInput {
    height: 100px;
    font-size: 48px;
    font-weight: bold;
}

.custInputWrap {
    background-color: #f6f6f6;
    border-radius: 12px;
    margin-bottom: 24px;
}

.sendVerifyCodeBtn {
    font-size: 28px;
    color: $color-primary;
}

.featureIcon {
    width: 20px;
    height: 20px;
    margin-right: 8px;
}

.contractBar {
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 24px;
}

.contractBarLink {
    color: $color-primary;
}

.contractPopupFooter {
    display: flex;
    padding: 40px;
}

.contractbarConfirmBtn {
    flex: 1;
    margin-left: 20px;
}

.contractContent {
    padding: 0 40px 40px 40px;
}

.contract-bar-text {
    margin-left: 8px;
}