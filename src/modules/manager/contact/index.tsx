// src/modules/manager/contact/index.tsx
import React, { useState } from 'react';
import { View, Text, Button, Map } from '@tarojs/components';
import Taro from '@tarojs/taro';
import { mockManagers, Manager } from '~utils/mockManagers';
import styles from './index.module.scss';
import { CyButton, CyModal } from 'hncy58-taro-components';
import PageWrap from '~components/PageWrap';

// 计算两个经纬度点距离的辅助函数（哈弗辛公式，单位：km）
const calculateDistance = (lat1: number, lon1: number, lat2: number, lon2: number): number => {
  const R = 6371; // 地球半径
  const dLat = (lat2 - lat1) * Math.PI / 180;
  const dLon = (lon2 - lon1) * Math.PI / 180;
  const a = Math.sin(dLat / 2) * Math.sin(dLat / 2) +
            Math.cos(lat1 * Math.PI / 180) * Math.cos(lat2 * Math.PI / 180) *
            Math.sin(dLon / 2) * Math.sin(dLon / 2);
  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
  return R * c;
};

// 手机号脱敏函数
const maskPhone = (phone: string) => phone.replace(/(\d{3})\d{4}(\d{4})/, '$1****$2');

type ExtendedManager = Manager & { distance: number };

export default () => {
  const [managers, setManagers] = useState<ExtendedManager[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');

  const handleContact = async () => {
    // 弹出确认框
    const closeModal = CyModal.create({
      title: '提示',
      content: '需要获取您的位置信息，以便为您推荐最近的客户经理',
      onConfirm: async () => {
        setLoading(true);
        setError('');
        try {
          // 检查授权
          const authRes = await Taro.getSetting();
          if (!authRes.authSetting['scope.userLocation']) {
            await Taro.authorize({ scope: 'scope.userLocation' });
          }
          // 获取当前位置
          const location = await Taro.getLocation({ type: 'gcj02' });
          const { latitude, longitude } = location;
          // 模拟查询：计算距离并排序
          const sortedManagers = mockManagers
            .map(m => ({
              ...m,
              distance: calculateDistance(latitude, longitude, m.location.latitude, m.location.longitude),
            }))
            .sort((a, b) => a.distance - b.distance); // 按距离升序（最近先）
          setManagers(sortedManagers);
        } catch (err) {
          setError('获取位置失败，请检查授权');
          console.error(err);
        } finally {
          setLoading(false);
        }
      },
      onCancel: () => {
        // 取消不操作
      },
      isShowCancel: true,
      confirmText: '确认',
      cancelText: '取消',
    });
  };

  return (
    <PageWrap className={styles.container}>
      <CyButton onClick={handleContact} disabled={loading} type='primary' block round size='large'>
        {loading ? '加载中...' : '联系附近客户经理办理'}
      </CyButton>
      {error && <Text className={styles.error}>{error}</Text>}
      <View className={styles.list}>
        {managers.map((m, index) => (
          <View key={index} className={styles.item}>
            <View>
              <Text className={styles.label}>姓名:</Text>
              <Text className={styles.value}>{m.name}</Text>
            </View>
            <View>
              <Text className={styles.label}>手机:</Text>
              <Text className={styles.phone} onClick={() => Taro.makePhoneCall({ phoneNumber: m.phone })}>{maskPhone(m.phone)}</Text>
            </View>
            <View>
              <Text className={styles.label}>位置:</Text>
              <Text className={styles.value} onClick={() => Taro.openLocation(m.location)}>{m.location.name} (距离: {m.distance.toFixed(2)} km)</Text>
            </View>
          </View>
        ))}
      </View>
    </PageWrap>
  );
}; 