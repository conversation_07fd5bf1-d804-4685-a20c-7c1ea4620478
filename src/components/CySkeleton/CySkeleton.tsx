import React, { useMemo } from 'react';
import { View } from '@tarojs/components';
import classNames from 'classnames';
import styles from './index.module.scss';

export interface CySkeletonProps {
  /**
   * 骨架屏宽度，支持数字（单位px）或CSS宽度值（如'100%'）
   */
  width?: number | string;
  
  /**
   * 骨架屏高度，支持数字（单位px）或CSS高度值（如'100%'）
   */
  height?: number | string;
  
  /**
   * 圆角大小，支持数字（单位px）或CSS值
   */
  borderRadius?: number | string;
  
  /**
   * 是否显示动画效果
   * @default true
   */
  animated?: boolean;
  
  /**
   * 自定义类名
   */
  className?: string;
  
  /**
   * 骨架屏形状
   * @default 'rect'
   */
  shape?: 'rect' | 'circle' | 'square';
  
  /**
   * 自定义样式
   */
  style?: React.CSSProperties;
  
  /**
   * 是否使用渐变效果
   * @default true
   */
  gradient?: boolean;
  
  /**
   * 自定义渐变颜色
   */
  gradientColors?: [string, string, string];
}

/**
 * 高级骨架屏组件
 * 
 * 支持自定义宽高、形状、动画效果等
 */
const CySkeleton: React.FC<CySkeletonProps> = ({
  width,
  height,
  borderRadius,
  animated = true,
  className,
  shape = 'rect',
  style = {},
  gradient = true,
  gradientColors,
}) => {
  // 处理宽高值
  const formatSize = (size: string | number | undefined): string => {
    if (size === undefined) return 'auto';
    return typeof size === 'number' ? `${size}px` : size;
  };

  // 处理圆角值
  const getBorderRadius = useMemo(() => {
    if (borderRadius !== undefined) {
      return typeof borderRadius === 'number' ? `${borderRadius}px` : borderRadius;
    }
    
    // 根据形状设置默认圆角
    switch (shape) {
      case 'circle':
        return '50%';
      case 'square':
        return '4px';
      default:
        return '4px';
    }
  }, [borderRadius, shape]);

  // 合并样式
  const mergedStyle: React.CSSProperties = useMemo(() => {
    const baseStyle: React.CSSProperties = {
      width: formatSize(width),
      height: formatSize(height),
      borderRadius: getBorderRadius,
      ...style,
    };

    // 如果是正方形，确保宽高相等
    if (shape === 'square' && (width || height)) {
      const size = width || height;
      baseStyle.width = formatSize(size);
      baseStyle.height = formatSize(size);
    }

    // 如果是圆形，确保宽高相等
    if (shape === 'circle' && (width || height)) {
      const size = width || height;
      baseStyle.width = formatSize(size);
      baseStyle.height = formatSize(size);
    }

    return baseStyle;
  }, [width, height, getBorderRadius, style, shape]);

  // 自定义渐变样式
  const gradientStyle = useMemo(() => {
    if (!gradient) return {};
    
    const colors = gradientColors || ['#f0f0f0', '#e0e0e0', '#f0f0f0'];
    
    return {
      '--skeleton-start-color': colors[0],
      '--skeleton-middle-color': colors[1],
      '--skeleton-end-color': colors[2],
    } as React.CSSProperties;
  }, [gradient, gradientColors]);

  return (
    <View
      className={classNames(
        styles['cy-skeleton'],
        {
          [styles['cy-skeleton-animated']]: animated,
          [styles['cy-skeleton-gradient']]: gradient,
          [styles['cy-skeleton-circle']]: shape === 'circle',
        },
        className
      )}
      style={{ ...mergedStyle, ...gradientStyle }}
    />
  );
};

export default CySkeleton; 