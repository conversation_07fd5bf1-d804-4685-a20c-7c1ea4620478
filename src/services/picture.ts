import request from '../http/request';

/** 
 * 身份证ocr识别接口
 */
export const discernIdcard = (data: API.IDiscernIdcardReq) => {
    return request<API.IDiscernIdcardRes>({
      url: '/api/picture/discernIDCard',
      method: 'POST',
      data,
    });
};

/**
 * 保存身份证信息
 */
export const saveIdcardInfo = (data: API.ISaveIdcardInfoReq) => {
  return request<any>({
    url: '/api/picture/saveIdCardInfo',
    method: 'POST',
    data,
  });
};

/**
 * 校验身份证号和有效期的正确性
 */
export const validateIDcard = (data: API.IValidateIdcardReq) => {
  return request<boolean>({
    url: '/api/picture/validationYear',
    method: 'POST',
    data,
  });
};