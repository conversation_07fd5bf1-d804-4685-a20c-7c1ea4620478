import React, {CSSProperties, FC, PropsWithChildren, useContext, useEffect} from 'react';
import {CyTheme} from 'hncy58-taro-components';
import {ScrollView, View} from '@tarojs/components';
import {document} from '@tarojs/runtime';
import Taro from '@tarojs/taro';
import classNames from 'classnames';
import styles from './index.module.scss';

const theme2BarColor = {
  blue: '#2F54EB',
  red: '#EF3434',
};

interface PageContainerProps {
  className?: string;
  showStatusBarColor?: boolean;
  style?: CSSProperties;
}

// 页面容器，每个页面都需要包裹这个容器，实现跨页面的通用功能如 主题切换，遮罩等
const PageContainer: FC<PropsWithChildren & PageContainerProps> = ({children, className, showStatusBarColor, style}) => {
  const {theme, setTheme} = useContext(CyTheme.ThemeContext);

  useEffect(() => {
    if (!theme) return;
    console.log('theme change', theme);
    if (Taro.getEnv() === Taro.ENV_TYPE.WEB) {
      document.documentElement.className = theme;
    } else if (Taro.getEnv() === Taro.ENV_TYPE.WEAPP) {
      console.log(Taro.getApp());
      console.log(Taro.getCurrentPages());
      if (!showStatusBarColor) {
        return;
      }
      Taro.setNavigationBarColor({
        frontColor: '#ffffff',
        backgroundColor: theme2BarColor[theme],
        animation: {
          duration: 400,
          timingFunc: 'easeIn',
        },
      });
    }
  }, [theme]);

  const safeAreaHeight = Taro.getWindowInfo().safeArea?.height;
  const screenHeight = Taro.getWindowInfo().screenHeight;
  const statusBarHeight = Taro.getWindowInfo().statusBarHeight;
  let safeAreaBottom = 0;
  if (safeAreaHeight && statusBarHeight && screenHeight > safeAreaHeight) {
    safeAreaBottom = screenHeight - safeAreaHeight - statusBarHeight;
  }

  return <View style={style} className={classNames(theme, className, styles['page-container'])}>{children}</View>;
};
export default PageContainer;
