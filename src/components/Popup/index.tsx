import React, {useEffect, useState} from 'react';
import {CySafeArea} from 'hncy58-taro-components';
import {Image, View, ViewProps} from '@tarojs/components';
import ClearIcon from '~images/icon/<EMAIL>';
import styles from './index.module.scss';

interface PopupProps extends ViewProps {
  // 是否显示弹出层
  visible: boolean;
  // 弹出层标题
  title?: string;
  // 弹出层提示
  note?: string;
  titleAlign?: 'left' | 'center' | 'right';
  // 关闭弹出层的回调函数
  onClose?: () => void;
  // 内容区域自定义style
  bodyStyle?: React.CSSProperties;
  // 是否显示关闭按钮
  showClose?: boolean;
  // 是否点击蒙版关闭
  closeOnMaskClick?: boolean;
  // 显示模式：opacity - 透明度模式，destroy - DOM销毁模式
  mode?: 'opacity' | 'destroy';
}

const Popup: React.FC<PopupProps> = ({
  children,
  visible,
  title,
  note,
  titleAlign = 'center',
  onClose,
  bodyStyle,
  showClose = false,
  closeOnMaskClick = true,
  mode = 'opacity',
  ...otherProps
}) => {
  const [isVisible, setIsVisible] = useState(visible);
  const [shouldRender, setShouldRender] = useState(visible);

  useEffect(() => {
    console.log('popup visible=====', visible);
    
    if (mode === 'opacity') {
      // 透明度模式
      if (visible) {
        setIsVisible(true);
      } else {
        setTimeout(() => setIsVisible(false), 300); // 300ms 是过渡时间
      }
    } else {
      // DOM销毁模式
      if (visible) {
        setShouldRender(true);
        // 确保DOM渲染后再显示动画
        setTimeout(() => setIsVisible(true), 10);
      } else {
        setIsVisible(false);
        // 等待动画结束后销毁DOM
        setTimeout(() => setShouldRender(false), 300);
      }
    }
  }, [visible, mode]);

  const handleMaskClick = (e: any) => {
    e.stopPropagation();
    if (closeOnMaskClick && onClose) {
      if (mode === 'destroy') {
        setIsVisible(false);
      }
      onClose();
    }
  };

  const handleCloseButtonClick = () => {
    if (mode === 'destroy') {
      setIsVisible(false);
    }
    if (onClose) {
      onClose();
    }
  };

  // DOM销毁模式下，如果不需要渲染则返回null
  if (mode === 'destroy' && !shouldRender) {
    return null;
  }

  // 透明度模式下，根据isVisible状态控制样式
  const getVisibilityClass = () => {
    if (mode === 'opacity') {
      return isVisible ? styles.visible : styles.hidden;
    } else {
      // DOM销毁模式下，根据isVisible控制动画
      return isVisible ? styles.visible : styles.hidden;
    }
  };

  return (
    <View
      {...otherProps}
      className={`${styles.popup_overlay} ${getVisibilityClass()}`}
      onClick={handleMaskClick}>
      <View
        className={`${styles.popup_container} ${styles.bottom} ${getVisibilityClass()}`}
        style={bodyStyle}
        onClick={e => e.stopPropagation()}>
        <View className={styles.popup_main}>
          {(title || showClose) && (
            <View className={styles.popup_title_container} style={{textAlign: titleAlign}}>
              <View className={styles.popup_title}>{title}</View>
              {note && <View className={styles.popup_note}>{note}</View>}
              {showClose && (
                <View className={styles.popup_close} onClick={handleCloseButtonClick}>
                  <Image className={styles.popup_clearimg} src={ClearIcon}></Image>
                </View>
              )}
            </View>
          )}
          {children && <View className={styles.popup_content}>{children}</View>}
        </View>
        <CySafeArea position='bottom' />
      </View>
    </View>
  );
};

export default Popup;