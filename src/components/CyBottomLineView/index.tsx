import {CSSProperties, FC} from 'react';
import {ITouchEvent, View, ViewProps} from '@tarojs/components';
import './index.scss';

type Props = {
  title: string;
  onClick?: (event: ITouchEvent) => void;
};

/**
 * 组件名称：带下划线可点击控件
 * 组件描述：带下划线可点击控件
 */
const CyBottomLineView: FC<Props & ViewProps> = props => {
  const style: CSSProperties = {};
  if (props.style && typeof props.style !== 'string' && props.style.fontSize) {
    style.fontSize = props.style.fontSize;
  }
  return (
    <View className='comp-cy-bottom-line-clickable' {...props} style={style}>
      {props.title}
    </View>
  );
};

export default CyBottomLineView;
