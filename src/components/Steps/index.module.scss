.steps {
  display: flex;
}

.step {
  flex: 1 1;
  position: relative;
  .step_idtor {
    position: relative;
    display: flex;
    justify-content: center;
    align-items: center;
    width: 100%;
    height: 40px;
    &::after {
      content: "";
      position: absolute;
      width: 70%;
      height: 0;
      left: 65%;
      top: 50%;
      transform: translateY(-50%);
      border-top: 1px dashed #ccc;
    }
  }
  .step_content {
    text-align: center;
  }
  .step_current {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background-color: $color-primary;
    .step_round {
      width: 8px;
      height: 8px;
      margin-right: 4px;
      border-radius: 50%;
      background-color: #fff;
      &:last-child {
        margin-right: 0;
      }
    }
  }
  &:last-child .step_idtor::after{
    display: none;
  }
}
.step.current_line .step_idtor::after{
  border-color: #2F54EB;
}

.step_icon {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: #fff;
  &::after {
    display: block;
    content: "";
    width: 20px;
    height: 20px;
    border-radius: 50%;
    background-color: #ccc;
  }
}

.step_title {
  margin-top: 12px;
  font-size: 28px;
  color: #666;
  line-height: 1;
}
.step_desc {
  font-size: 22px;
  color: #999;
  margin-top: 8px;
}
.current {
  color: $color-primary;
}

.step_icon_status {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 40px;
  height: 40px;
  border-radius: 50%;
}
.step_icon_success {
  border: 1px solid #52c41a;
}
.step_icon_error {
  border: 1px solid #ff4d4f;
}
.setp_icon_img {
  width: 20px;
  height: 20px;
}

// .step.wait .step_icon {
//   background-color: #ccc;
// }

// .step.process .step_icon {
//   background-color: #2F54EB;
// }

// .step.finish .step_icon {
//   background-color: #52c41a;
// }

// .step.error .step_icon {
//   background-color: #ff4d4f;
// }

// .step.current .step_title {
//   color: #333;
// }

// .step:not(:last-child)::after {
//   content: '';
//   position: absolute;
//   top: 16px;
//   left: 50%;
//   width: 100%;
//   height: 2px;
//   background-color: #ccc;
//   z-index: -1;
// }

// .step.current:not(:last-child)::after {
//   background-color: #2F54EB;
// }