import {useState} from 'react';
import {WebView} from '@tarojs/components';
import {useLoad} from '@tarojs/taro';
import { setStoragePromise } from '~utils/common';

export default () => {
  const [webviewSrc, setWebviewSrc] = useState('');
  useLoad(({url}) => {
    setWebviewSrc(decodeURIComponent(url));
    console.log('url', url, decodeURIComponent(url)); // 打印出url，方便调试
  });
  const handleMessage = (e: any) => {
    console.log('getWebview Data', e);
    setStoragePromise('webviewData', JSON.stringify(e.detail || {}));
  }
  if (webviewSrc === '') return null;
  return <WebView onMessage={handleMessage} src={webviewSrc} />;
};
