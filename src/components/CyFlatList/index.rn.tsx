import {FC, ForwardRefRenderFunction, ReactNode, forwardRef, useImperativeHandle, useRef} from 'react';
import {FlatList, FlatListProps} from 'react-native';
import './index.scss';

interface Props {
  data?: any[];
  ListFooterComponent?: ReactNode;
  ListEmptyComponent?: ReactNode;
}

/**
 * 组件名称：长列表
 * 组件描述：长列表
 */
export interface CyFlatListRef {
  scrollToTop: () => void;
}
const CyFlatList: ForwardRefRenderFunction<CyFlatListRef, Props & FlatListProps<any>> = (props, ref) => {
  const flatRef = useRef<FlatList>(null);
  useImperativeHandle(ref, () => ({
    scrollToTop: () => {
      flatRef.current?.scrollToOffset({
        offset: 0,
        animated: false,
      });
    },
  }));
  return (
    <FlatList
      ref={flatRef}
      data={props.data}
      renderItem={props.renderItem}
      keyExtractor={props.keyExtractor}
      onEndReached={props.onEndReached}
      onRefresh={props.onRefresh}
      refreshing={props.refreshing}
      onEndReachedThreshold={props.onEndReachedThreshold}
      ListFooterComponent={props.ListFooterComponent}
      ListEmptyComponent={props.ListEmptyComponent}
    />
  );
};

export default forwardRef(CyFlatList);
