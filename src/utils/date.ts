import dayjs from 'dayjs';

export const getFirstDayStr = (dayType: string, str?: string, dateArg?: string) => {
  if (str === undefined) str = '-';
  let now;
  if (dateArg === undefined) {
    now = new Date();
  } else {
    now = new Date(dateArg);
  }
  if (dayType === '1') {
    //今日
    return getDayStr(now, str);
  } else if (dayType === '0') {
    //昨日
    now.setDate(now.getDate() - 1);
    return getDayStr(now, str);
  } else if (dayType === '2') {
    //本周
    let weekday = now.getDay() || 7;
    now.setDate(now.getDate() - weekday + 1);
    return getDayStr(now, str);
  } else if (dayType === '3') {
    //本月
    now.setDate(1);
    return getDayStr(now, str);
  } else if (dayType === '4' || dayType === '5') {
    //本年
    now.setDate(1);
    now.setMonth(0);
    return getDayStr(now, str);
  } else if (dayType === '6') {
    //本年yyyyMMddHHmmss
    return getDayHHStr(now, str);
  }
};

const getDayHHStr = (dateArg: Date, str: string) => {
  if (str === undefined) str = '-';
  let strTemp = ':';
  let now = new Date(dateArg);

  let year = now.getFullYear(); //年
  let month = now.getMonth() + 1; //月
  let day = now.getDate(); //日
  let hours = now.getHours(); //时
  let minutes = now.getMinutes(); //分
  let seconds = now.getSeconds(); //秒
  let formatMonth = month < 10 ? `0${month}` : month;
  let formatDay = day < 10 ? `0${day}` : day;
  return `${year}${str}${formatMonth}${str}${formatDay}` + ' ' + `${hours}${strTemp}${minutes}${strTemp}${seconds}`;
};

const getDayStr = (dateArg: Date, str: string) => {
  if (str === undefined) str = '-';
  let now = new Date(dateArg);

  let year = now.getFullYear(); //年
  let month = now.getMonth() + 1; //月
  let day = now.getDate(); //日
  let formatMonth = month < 10 ? `0${month}` : month;
  let formatDay = day < 10 ? `0${day}` : day;
  return `${year}${str}${formatMonth}${str}${formatDay}`;
};

/**今天 */
export const getToday = () => {
  return dayjs();
};

/**昨天 */
export const getYesterday = () => {
  return dayjs().subtract(1, 'day');
};

/**本周第一天 */
export const getFirstDayOfWeek = () => {
  return dayjs().day(0);
};

/**本周第一天 基于昨天 */
export const getFirstDayOfYesterdayWeek = () => {
  return dayjs().subtract(1, 'day').day(0);
};

/**本月第一天 */
export const getFirstDayOfMonth = () => {
  return dayjs().date(1);
};

/**本月第一天。基于昨天 */
export const getFirstDayOfYesterdayMonth = () => {
  return dayjs().subtract(1, 'day').date(1);
};

/**本年第一天 */
export const getFirstDayOfYear = () => {
  return dayjs().month(0).date(1);
};

/**本月第一天。基于昨天 */
export const getFirstDayOfYesterdayYear = () => {
  return dayjs().subtract(1, 'day').month(0).date(1);
};

export const getLastQuarterLastDate = (date: string) => {
  const currentMonth = parseInt(dayjs(date).format('MM'));
  const currentYear = parseInt(dayjs(date).format('YYYY'));

  let year, month;
  // 确定当前季度
  const quarter = Math.floor((currentMonth - 1) / 3) + 1;
  if (quarter === 1) {
    year = currentYear - 1;
    month = 12;
  } else if (quarter === 2) {
    year = currentYear;
    month = 3;
  } else if (quarter === 3) {
    year = currentYear;
    month = 6;
  } else {
    year = currentYear;
    month = 9;
  }
  const resDate = dayjs(`${year}-${month}-01`).endOf('month').format('YYYYMMDD');
  return resDate;
};

/**
 * 将日期转换成中文日期描述，支持 2020-02-02 20200909 2020/02/12 转换成 xx年xx月xx日
 * @param date string
 * @param isMonth Boolen 是否只返回到月
 * @returns string
 */
export const transDateToChinese = (date: string, isMonth = false) => {
  return date.replace(/(\d{4}).*(\d{2}).*(\d{2})/g, isMonth ? '$1年$2月' : '$1年$2月$3日')
}
