import {Toast} from '@taroify/core';
import {type ToastOptions} from '@taroify/core/toast';
import Taro, {getStorage, setStorage} from '@tarojs/taro';
import currency from 'currency.js';
import {IPickerItem} from 'src/types/common-types';
import {showPrivacyModal} from '../pages/check/components/PrivacyModal';
import {checkPrivacyCache} from './privacyCache';

/** 使用Promise改写获取storage方法 */
export const getStoragePromise = (key: string) => {
  return new Promise<string>((resolve, reject) => {
    try {
      getStorage<string>({
        key,
        success(res) {
          resolve(res.data);
        },
        fail(re) {
          resolve('');
        },
      }).catch(e => {
        console.log(e);
      });
    } catch (e) {
      // reject(e);
    }
  });
};

/** 使用Promise改写设置storage方法 */
export const setStoragePromise = (key: string, value: string) => {
  return new Promise<string>((resolve, reject) => {
    try {
      setStorage({
        key,
        data: value,
        success(res) {
          resolve('');
        },
        fail(re) {
          reject('');
        },
      });
    } catch (e) {
      reject(e);
    }
  });
};

export const showToast = (option: ToastOptions | string) => {
  try {
    // Taro.hideToast();
    Toast.close();
  } catch (e) {
    //
  }
  const duration = 2500;
  if (typeof option === 'string') {
    option = option.replace('failure:', '')
    Toast.open({
      message: option,
      duration,
    });
  } else {
    if(option.message) {
      option.message = (option.message as string)?.replace('failure:', '')
    }
    //设置默认值
    Toast.open({
      duration,
      ...option,
    });
  }
};

export const showLoading = (option?: Taro.showLoading.Option): Promise<TaroGeneral.CallbackResult> => {
  if (option === undefined) {
    option = {title: ''};
  } else if (option.title === undefined) {
    option.title = '加载中...';
  }
  option.mask = true;
  return Taro.showLoading(option);
};

export const hideLoading = (option?: Taro.hideLoading.Option): void => {
  return Taro.hideLoading(option);
};

export const showModal = (option: Taro.showModal.Option): Promise<Taro.showModal.SuccessCallbackResult> => {
  //设置默认值
  option.confirmColor || (option.confirmColor = '#0000ff');
  option.cancelColor || (option.cancelColor = '#999');

  return Taro.showModal(option);
};

// 重包装下Taro的路由方法，防止后续需要统一做处理
export const router = {
  // 检查隐私协议并执行路由跳转
  checkAndNavigate: async (navigateFunc: Function, params: any): Promise<any> => {
    const hasAgreed = await checkPrivacyCache();
    if (hasAgreed) {
      return navigateFunc(params);
    } else {
      // 显示隐私弹窗，并在用户同意后进行跳转
      showPrivacyModal({
        onAgree: () => {
          navigateFunc(params);
        },
      });
    }
  },
  // 特殊路由 不需要同意隐私政策
  specialRouters: ['/modules/agreement/privacy/index', '/modules/webview/index/index'],
  reLaunch: (params: Taro.reLaunch.Option) => {
    return router.checkAndNavigate(Taro.reLaunch, params);
  },
  replace: (params: Taro.redirectTo.Option) => {
    return router.checkAndNavigate(Taro.redirectTo, params);
  },
  push: (params: Taro.navigateTo.Option) => {
    if (router.specialRouters.includes(params.url?.split('?')[0])) {
      return Taro.navigateTo(params);
    } else {
      return router.checkAndNavigate(Taro.navigateTo, params);
    }
  },
  back: (params?: Taro.navigateBack.Option) => {
    return Taro.navigateBack(params);
  },
  switchTab: (params: Taro.switchTab.Option) => {
    return Taro.switchTab(params);
  },
};

/**
 * 复制至剪切板
 * @param copyText 复制内容
 * @param copyToast 提示内容
 * @returns
 */
export const copyToClipboard = (copyText?: string, copyToast?: string) => {
  if (copyText === undefined || copyText === '') {
    return;
  }
  Taro.setClipboardData({
    data: copyText,
    complete: () => {
      setTimeout(() => {
        showToast(copyToast ?? '已复制');
      }, 0);
    },
  });
};

/**
 * 判断是否为身份证号
 * @param value
 * @returns
 */
export const isCertId = (value?: string) => {
  if (value === undefined) {
    return false;
  }
  let reg = /^[0-9]{5,17}[0-9Xx]$/; /*定义验证表达式，判定为身份证后六位格式*/
  return reg.test(value); /*进行验证*/
};

/**
 * 判断是否为完整身份证号格式
 * @param value
 * @returns
 */
export const isFullCertId = (value?: string) => {
  if (value === undefined) {
    return false;
  }
  let reg = /^(\d{15}|\d{18}|\d{17}X)$/i;
  return reg.test(value); /*进行验证*/
};

/**
 * 判断是否为空
 * @param value
 * @returns
 */
export const isEmpty = (value?: string) => {
  if (value === undefined || value === '') {
    return true;
  }
  return false;
};

export const maskName = (nameStr: string | number) => {
  const name = `${nameStr}`;
  return name.charAt(0) + name.slice(1).replace(/./g, '*');
};

export const maskPhoneNumber = (phoneNumber: string | number) => {
  const mobile = `${phoneNumber}`;
  return mobile.replace(/(\d{3})\d{4}(\d{4})/, '$1****$2');
};

export const maskCert = (cert: string | number) => {
  const certStr = `${cert}`;
  return certStr.replace(/^(.{4})(?:\d+)(.{4})$/, '$1********$2');
};

/**
 * 对对象做剔除，剔除掉value为undefined的key
 */
export const cullObjectByValue = <T extends Record<string, any>>(obj: T, valueLike?: unknown[]) => {
  const newObj: Record<string, any> = {};
  Object.keys(obj).forEach(k => {
    const v = obj[k];
    if ((valueLike?.length && valueLike?.includes(v)) || (!valueLike && v !== undefined)) {
      newObj[k] = v;
    } else {
      // 不做复制
    }
  });
  return newObj;
};

/**
 * 对敏感数据做前端脱敏处理
 * @param val 待脱敏数据
 * @param type 数据类型，包括：手机号、身份证号、银行卡号等
 */
type DesensitizationType =
  | 'mobile'
  | 'certId'
  | 'bankcardNo'
  | 'certIdLastSix'
  | 'certIdShort'
  | 'certIdShort4-6'
  | 'name';
export const desensitization = (val: string | undefined, type: DesensitizationType) => {
  if (!val) return val;
  if (type === 'certId') {
    return val.replace(/^([\d]{4}).*([\dXx]{4})$/, '$1********$2');
  } else if (type === 'certIdShort') {
    return val.replace(/^([\d]{4}).*([\dXx]{4})$/, '$1***$2');
  } else if (type === 'certIdShort4-6') {
    return val.replace(/^([\d]{4}).*([\dXx]{6})$/, '$1***$2');
  } else if (type === 'mobile') {
    return val.replace(/^([\d]{3}).*([\d]{4})$/, '$1****$2');
  } else if (type === 'bankcardNo') {
    return val.replace(/^([\d]{4}).*([\d]{4})$/, '$1********$2');
  } else if (type === 'certIdLastSix') {
    // return val.replace(/^.{6}/, '***');
    return val.replace(/^([\d]{4}).*([\dXx]{6})$/, '***$2');
  } else if (type === 'name') {
    if (val.length > 2) {
      return `${val.charAt(0)}*${val.charAt(val.length - 1)}`;
    } else {
      return val.charAt(0) + val.slice(1).replace(/./g, '*');
    }
  } else {
    return val;
  }
};
/**
 * 字符串最后需要替换的元素
 * @param str 原字符串
 * @param lastChar 需要替换的最后一个字符串
 * @returns 替换后的字符串
 */
export const deleteChar: (str: string, lastChar: string) => string = (str, lastChar) => {
  // 获取最后一个字符
  const lastWord = str.slice(-1);
  if (lastChar === lastWord) {
    return deleteChar(str.slice(0, -1), lastChar); // 如果有继续删除
  } else {
    return str;
  }
};

/**
 * 元转万元
 * @param str 原字符串
 * @returns 万元后的数值
 */
export const convertToTenThousand: (str?: string, emptyStr?: string, needSeparator?: boolean) => string = (
  str,
  emptyStr = '0.00',
  needSeparator = true,
) => {
  if (!str) {
    return emptyStr;
  }
  if (parseFloat(str) === 0) return '0.00';

  if (!parseFloat(str)) {
    return str;
  }
  const num = Math.abs(parseFloat(str)) / 10000;
  if (needSeparator) {
    return (Math.round(num * 100) / 100).toLocaleString();
  } else {
    return (Math.round(num * 100) / 100).toString();
  }
};

/**
 * 添加千分符
 * @param str 原字符串
 * @returns 转换后的字符串
 */
export const addThousandSeparator: (str?: string, emptyStr?: string) => string = (str, emptyStr = '0.00') => {
  if (!str) {
    return emptyStr;
  }
  if (parseFloat(str) === 0) return '0.00';
  if (str === 'undefined') return emptyStr;
  if (!parseFloat(str)) return str;
  const resVal = Math.round(Math.abs(parseFloat(str)) * 100) / 100;
  return resVal.toLocaleString();
};

export const formatPercent: (str?: string) => string = str => {
  if (!str) return '0.00%';
  if (parseFloat(str) === 0) return '0.00%';
  if (!parseFloat(str)) return str;
  const resVal = Math.round(parseFloat(str) * 100) / 100;
  return `${resVal.toFixed(2)}%`;
};

/**
 * 百分比转换，乘以100, 0.1 -> 10%
 * @param str 原字符串
 * @returns 转换后的字符串
 * */
export const formatPercentX100: (str?: string) => string = str => {
  if (!str) return '0.00%';
  if (parseFloat(str) === 0) return '0.00%';
  if (!parseFloat(str)) return str;
  const resVal = parseFloat(str) * 100;
  return `${resVal.toFixed(4).replace(/\.?0+$/, '')}%`;
};

export const roundToTwoDecimals = (number: number) => {
  return Math.round(number * 100) / 100;
};

export const sleep = async (time: number) => {
  await new Promise(resolve => {
    setTimeout(() => {
      resolve(1);
    }, time);
  });
  return;
};

export const getItemsValue4Key = (items: {label: string; value: string}[], key: string) => {
  let value = '';
  for (let index = 0; index < items.length; index++) {
    const element = items[index];
    if (element.value === key) value = element.label;
  }
  return value;
};
export const convertToYi = (num: number = 0.0) => {
  return parseFloat((num / 100000000).toFixed(2));
};

export const convertToWan = (num: number = 0.0) => {
  return parseFloat((num / 10000).toFixed(2));
};

/**
 * 根据金额大小，返回金额单位
 * @param amount 金额
 * @returns 金额单位
 */
export const getAmountUnit = (amount: number): string => {
  if (amount >= 1000000) {
    return '';
  } else if (amount >= 100000 && amount < 1000000) {
    return '十万';
  } else if (amount >= 10000) {
    return '万';
  } else if (amount >= 1000) {
    return '千';
  } else if (amount >= 100) {
    return '百';
  } else if (amount >= 10) {
    return '十';
  } else {
    return '';
  }
};

/**
 * 通过value返回label
 * @param list [{label: '', value: ''}]
 * @param value string
 * @returns label
 */
export const getLabelByValue = (list: IPickerItem[], value: string) => {
  const findItem = (list || []).find(item => item.value === value);
  return findItem?.label || '';
};

/**
 * 写一个函数，将传入的对象中的值为空字符串的属性删除
 * @param obj 传入的对象
 * @returns 删除后的对象
 */
export const removeObjEmptyString = (obj: Record<string, any>) => {
  for (const key in obj) {
    if (obj[key] === '') {
      delete obj[key];
    }
  }
  return obj;
};

/**
 * 格式化金额显示
 * @param money 需要格式化的金额字符串
 * @param options 格式化选项
 * @param options.symbol 货币符号（如 ¥、$、€ 等）
 * @param options.precision 小数位数精度
 * @returns 格式化后的金额字符串
 * @example
 * formattedMoney('1234.56', {symbol: '¥', precision: 2}) // '¥1,234.56'
 * formattedMoney('1000', {symbol: '$', precision: 0}) // '$1,000'
 */
export const formattedMoney = (money: string, options?: {symbol?: string; precision?: number, separator?: string}) => {
  const {symbol = '¥', precision = 2, separator = ','} = options || {};
  return currency(money, {
    symbol: symbol || '',
    separator: separator,
    decimal: '.',
    precision,
  }).format();
};

/**
 * 提取日期字符串各部分，格式化为YYYYMMDD格式
 * @param dateStr 日期字符串，格式为YYYY-MM-DD或YYYY/MM/DD
 * @returns 格式化后的日期字符串，格式为YYYYMMDD
 * @example
 * formatDate('2022-1-01') // '20220101'
 * formatDate('2022/1/1') // '20220101'
 * formatDate('2022年01月01日') // '20220101'
 */
export const formatDateYYYYMMDD = (dateStr?: string): string => {
  if (!dateStr) return '';

  // 移除所有非数字字符，保留数字
  const numbers = dateStr.replace(/\D/g, '');

  // 如果数字长度不足8位，说明格式不正确
  if (numbers.length < 8) {
    // 尝试解析常见的日期格式
    const patterns = [
      /^(\d{4})[-\/年](\d{1,2})[-\/月](\d{1,2})[日]?$/,
      /^(\d{4})-(\d{1,2})-(\d{1,2})$/,
      /^(\d{4})\/(\d{1,2})\/(\d{1,2})$/,
      /^(\d{4})年(\d{1,2})月(\d{1,2})日$/,
    ];

    for (const pattern of patterns) {
      const match = dateStr.match(pattern);
      if (match) {
        const [, year, month, day] = match;
        return year + month.padStart(2, '0') + day.padStart(2, '0');
      }
    }

    // 如果都不匹配，返回自身
    return dateStr;
  }

  // 如果是8位数字，直接返回
  if (numbers.length === 8) {
    return numbers;
  }

  // 如果超过8位，取前8位
  return numbers.substring(0, 8);
};

/**
 * @description 更优雅的try...catch模式：接收一个Promise，并返回一个元组[error,data]
 * @param {Promise<T>} promise - 要处理返回的Promise
 * @returns {Promise<[Error | null,T | undefined]>}
 */
export function to<T>(promise: Promise<T>): Promise<[Error | null, T | undefined]> {
  return promise
    .then<[null, T]>((data: T) => [null, data])
    .catch<[Error, undefined]>((error: Error) => [error, undefined]);
}

/**
 * @description 结合to，更灵活的Promise.all处理：可选择失败时是否继续执行其他Promise
 * @param {Promise<T>[]} promises - Promise数组
 * @param {boolean} [continueOnError=false] - 当某个Promise失败时是否继续执行其他Promise
 * @returns {Promise<[Error | null, (T | undefined)[] | T[] | undefined]>}
 */
export function toAll<T>(
  promises: Promise<T>[],
  continueOnError: boolean = false,
): Promise<[Error | null, T[] | undefined]> | Promise<[Error | null, (T | undefined)[] | undefined]> {
  if (continueOnError) {
    // 每个Promise独立处理，即使某些失败也会返回所有结果
    return Promise.all(promises.map(promise => to(promise)))
      .then(results => {
        const hasError = results.some(([error]) => error !== null);
        const data = results.map(([, data]) => data);

        if (hasError) {
          // 可以选择返回第一个错误，或者自定义错误
          const firstError = results.find(([error]) => error !== null)?.[0];
          return [firstError || new Error('Some promises failed'), data] as [Error, (T | undefined)[]];
        }

        return [null, data] as [null, (T | undefined)[]];
      })
      .catch(error => [error, undefined] as [Error, undefined]);
  } else {
    // 传统Promise.all行为：任何一个失败就整体失败
    return to(Promise.all(promises));
  }
}


/**
 * 打开三方链接或路由
 * @param link 链接
 * @returns 
 */
export const goWebviewLinkOrRoute = (link: string) => {
    if (link.startsWith('https')) {
      router.push({
        url: `/modules/webview/index/index?url=${encodeURIComponent(link)}`,
      });
      return;
    }
    if (link) {
      router.push({
        url: link,
      });
      return;
    }
    showToast('功能开发中');
  };

  /**
   * 根据value将value项移动到最后一项
   * @param arr 
   * @param value 
   * @returns 
   */
  export const handleMap = (arr: IPickerItem[], value: string) => {
    const index = arr.findIndex(item => item.value === value)
    if (index !== -1) {
      const item = arr.splice(index, 1)[0];
      arr.push(item);
    }
    return arr
  }