import {Image, Text, View} from '@tarojs/components';
import arrowIcon from '~images/icon/<EMAIL>';
import unSelectIcon from '~images/icon/<EMAIL>';
import selectIcon from '~images/icon/<EMAIL>'
import styles from './index.module.scss';

interface CouponItemProps {
  index: number;
  expandedItems: number[];
  toggleExpand: (index: number) => void;
  isSelect?: boolean;
  onSelect?: () => void;
}

export default ({index, expandedItems, onSelect, isSelect, toggleExpand}: CouponItemProps) => {
  return (
    <View className={styles.coupon_item_content}>
      <View className={`${styles.coupon_item} ${expandedItems.includes(index) ? styles.item_shadow : ''}`} onClick={() => onSelect?.()}>
        <View className={styles.coupon_item_left}>
          <View className={styles.coupon_item_discount}>
            <Text className={styles.coupon_item_discount_text}>5</Text>折
          </View>
          <View className={styles.coupon_item_type}>利息折扣券</View>
        </View>
        <View className={styles.coupon_item_right}>
          <View className={styles.coupon_item_discountTitle}>5折折扣券</View>
          <View className={styles.coupon_item_date}>2021.01.01-2025.01.01</View>
          <View className={styles.coupon_item_detail} onClick={e => {
            e.stopPropagation()
            toggleExpand(index)
          }}>
            详细信息
            <Image
              src={arrowIcon}
              className={`${styles.coupon_item_arrow} ${expandedItems.includes(index) ? styles.arror_transform : ''}`}
            />
          </View>
          <Image src={isSelect ? selectIcon : unSelectIcon} className={styles.coupon_item_check} />
        </View>
      </View>
      <View className={`${styles.coupon_detail} ${expandedItems.includes(index) ? styles.expanded : ''}`}>
        <View className={styles.coupon_detail_content}>
          1.说明文字说明文字说明文字说明文字说明文字说明文字 2.说明文字说明文字说明文字说明文字说明文字说明文字说
          明文字说明文字
        </View>
      </View>
    </View>
  );
};
