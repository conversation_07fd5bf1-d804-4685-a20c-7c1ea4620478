import { error } from 'console';
import fs from 'fs';
import J<PERSON><PERSON><PERSON> from 'jszip';
import path from 'path';
import * as simpleGit from 'simple-git';
import {Compilation, Compiler} from 'webpack';

interface GitImageDiffZipPluginOptions {
  pattern: RegExp;
  zipName: string;
  outputPath: string;
  excludes: RegExp[];
}

/**
 * GitImageDiffZipPlugin 是一个 Webpack 插件，用于在构建过程中生成一个 ZIP 文件。
 * 该 ZIP 文件包含自上次标签（tag）以来修改的图片文件，并保留这些文件的目录结构。
 * 默认情况下，插件会从指定的基准路径（basePath）开始截取相对路径，并将这些文件打包进 ZIP 文件。
 */
class GitImageDiffZipPlugin {
  private options: Required<GitImageDiffZipPluginOptions>;
  private git: simpleGit.SimpleGit;

  constructor({pattern, zipName, outputPath, excludes}: GitImageDiffZipPluginOptions) {
    this.options = {
      pattern: pattern || /\.(png|jpe?g|gif|svg)$/i,
      zipName: zipName || 'modified-images.zip',
      outputPath: outputPath || 'dist',
      excludes: excludes || [/\/static\//],
    };
    this.git = simpleGit.default();
  }

  apply(compiler: Compiler) {
    compiler.hooks.emit.tapAsync('GitImageDiffZipPlugin', async (compilation: Compilation, callback: (error?: Error) => void) => {
      try {
        // 获取上一个 Tag
        const tags = await this.git.tags();
        console.error('start apply===:tags', tags);
        const previousTag = tags.all[tags.all.length - 1];

        if (!previousTag) {
          console.warn('No previous tag found. Skipping image diff zip creation.');
          return callback();
        }

        // 获取当前分支
        const currentBranch = await this.git.revparse(['--abbrev-ref', 'HEAD']);
        console.log('currentBranch', currentBranch);
        // 获取差异文件列表
        const diff = await this.git.diff([`${previousTag}..${currentBranch}`, '--name-only']);
        const changedFiles = diff.split('\n').filter(file => file.trim());
        console.log('changedFiles', changedFiles);
        // 筛选出图片文件，并筛选出除exclude规则外的
        const imageFiles = changedFiles.filter(file => {
          const isImg = this.options.pattern.test(path.extname(file).toLowerCase());
          const isNotExclude = !this.options.excludes.find(ex => ex.test(file));
          return isImg && isNotExclude;
        });

        if (imageFiles.length === 0) {
          console.log('No image files modified since last tag.');
          return callback(error as unknown as Error);
        }
        console.log('imageFiles=======', imageFiles);
        const zip = new JSZip();
        // 遍历所有生成的文件
        Object.keys(compilation.assets).forEach(filename => {
          // 匹配图片文件
          if (this.options.pattern.test(filename)) {
            // 找到打包的文件中和本次tag修改的图片中文件名相同的图片
            // 优化匹配逻辑（示例：提取文件名并忽略哈希）
            const target = imageFiles.find(imgFile => {
              const imgBaseName = path.basename(imgFile); // 获取原始文件名（如 'photo.png'）
              const assetBaseName = path.basename(filename); // 获取打包后的文件名（如 'photo.abc123.png'）
              const imgName = imgBaseName.replace(/\.\w+$/, ''); // 去除扩展名（'photo'）
              const assetName = assetBaseName.replace(/\.\w+$/, ''); // 去除扩展名（'photo.abc123'）
              return assetName.startsWith(imgName); // 匹配原始文件名前缀
            });
            if (target) {
              // 获取文件内容
              const fileContent = compilation.assets[filename].source();
              // 将文件内容添加到zip对象中
              zip.file(filename, fileContent);
            }
          }
        });
        // 生成 ZIP 文件（异步写入）
        const zipContent = await zip.generateAsync({ type: 'nodebuffer' });
        const outputPath = path.resolve(this.options.outputPath, this.options.zipName);
        await fs.promises.writeFile(outputPath, zipContent); // 改为异步写入

        return callback(); // 成功时调用无参callback
      } catch (error) {
        console.error('Error in GitImageDiffZipPlugin:', error);
        return callback(error as Error); // 错误时传递错误对象
      } finally {
        console.error('fin Error in GitImageDiffZipPlugin:');
        return callback();
      }
    });
  }
}

export default GitImageDiffZipPlugin;
