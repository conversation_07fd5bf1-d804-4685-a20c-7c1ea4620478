.popover-container {
  position: relative;
  display: inline-block;
}

.trigger-container {
  padding: 8px 16px;
}

.popover-content {
  position: absolute;
  background-color: white;
  padding: 10px 24px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  z-index: 9;
  color: #fff;
  font-size: 28px;
  white-space: nowrap;
  box-sizing: border-box;
  border-radius: 20px;
  line-height: 1;
  background: linear-gradient(90deg, $color-bg-badge-front 1%, $color-bg-badge 100%);
}
.popover-content-OVERDUE {
  background: $color-bg-badge-overdue;
  color: #fff;
}

.popover-top {
  bottom: 100%;
  left: 50%;
  transform: translateX(-50%);
}

.popover-bottom {
  top: 100%;
  left: 50%;
  transform: translateX(-50%);
}

.popover-left {
  top: 50%;
  right: 100%;
  transform: translateY(-50%);
}

.popover-right {
  top: 50%;
  left: 100%;
  transform: translateY(-50%);
}

.popover-arrow {
  position: absolute;
  width: 0;
  height: 0;
  border-style: solid;
  border-color: #43436c;
}
.popover-arrow-OVERDUE {
  border-color: $color-bg-badge-overdue;
}

.popover-arrow-top {
  bottom: -8px;
  left: 50%;
  transform: translateX(-50%);
  border-width: 10px 10px 0 10px;
  border-bottom-color: transparent;
  border-left-color: transparent;
  border-right-color: transparent;
}

.popover-arrow-bottom {
  top: -8px;
  left: 50%;
  transform: translateX(-50%);
  border-width: 0 10px 10px 10px;
  border-top-color: transparent;
  border-left-color: transparent;
  border-right-color: transparent;
}

.popover-arrow-left {
  right: -8px;
  top: 50%;
  transform: translateY(-50%);
  border-width: 10px 0 10px 10px;
  border-bottom-color: transparent;
  border-top-color: transparent;
  border-right-color: transparent;
}

.popover-arrow-right {
  left: -8px;
  top: 50%;
  transform: translateY(-50%);
  border-width: 10px 10px 10px 0;
  border-bottom-color: transparent;
  border-left-color: transparent;
  border-top-color: transparent;
}