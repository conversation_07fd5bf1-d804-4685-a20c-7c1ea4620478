import {FC} from 'react';
import {Button, ButtonProps, ITouchEvent} from '@tarojs/components';
import './index.scss';

type Props = {
  inline?: boolean;
};

/**
 * 组件名称：按钮组件
 * 组件描述：按钮组件
 */
const CyButton: FC<Props & ButtonProps> = (props: Props & ButtonProps) => {
  let className = 'cyButton';
  if (props.className) {
    className = `${className} ${props.className}`;
  }

  if (props.type) {
    className = `${className} ${props.type}`;
  }

  if (props.disabled) {
    className = `${className} cyButton--disabled`;
  }
  if (props.inline) {
    className = `${className} cyButton--inline`;
  }
  const onClick = (e: ITouchEvent) => {
    if (props.disabled) return;
    return props.onClick && props.onClick(e);
  };

  const hoverClass = props.hoverClass || 'cyButton--disabled';
  return (
    <Button hoverClass={hoverClass} className={className} {...props} style={props.style} onClick={onClick}>
      {props.children}
    </Button>
  );
};

export default CyButton;
