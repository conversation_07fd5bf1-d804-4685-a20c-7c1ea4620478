import {useEffect, useState} from 'react';
import {Canvas, View} from '@tarojs/components';
import {useDidShow} from '@tarojs/taro';
import Card from '~components/Card';
import List from '~components/List/List';
import ListItem from '~components/List/ListItem';
import PageWrap from '~components/PageWrap';
import IconBankcard from '~icons/icon-bankcard.png';
import IconPassword from '~icons/icon-password.png';
import IconUserinfo from '~icons/icon-userinfo.png';
import {creditApplySuccessViewTransfer} from '~services/credit';
import {lendingPreProgress} from '~services/lending';
import useUserStore from '~store/user';
import {router} from '~utils/common';
import goLendingPreProgress from '~utils/goLendingPreProgress';
import CreditDashboard from '../credit-success/components/CreditDashboard';
import styles from './index.module.scss';
import { CyButton } from 'hncy58-taro-components';
import { UploadInfoListCard } from '../components/UploadInfoListCard';

const CreditResult = () => {
  const {amountCreditInfo} = useUserStore();
  
  // 跳转去借钱
  const goLending = async () => {
    // 额度确认
    await creditApplySuccessViewTransfer();
    goLendingPreProgress('push');
  };

  /** 5. ui */
  return (
    <PageWrap className={styles['page-credit-result']}>
      <View className={styles['page-content']}>
        <Card title='恭喜您，额度申请成功' note='信用额度，点滴积累'>
          <CreditDashboard rate={amountCreditInfo.rate} currentAmount={amountCreditInfo.creditAmount} />
        </Card>
        <View style={{marginTop: 12}}>
          <UploadInfoListCard />
        </View>
        <CyButton type='primary' round style={{marginTop: 12}} block onClick={goLending}>
          去借钱
        </CyButton>
      </View>
    </PageWrap>
  );
};

export default CreditResult;
