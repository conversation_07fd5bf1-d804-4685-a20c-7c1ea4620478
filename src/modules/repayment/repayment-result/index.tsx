import {useState} from 'react';
import {Image, Text, View} from '@tarojs/components';
import resFail from '~images/lending/<EMAIL>'
import resSuccess from '~images/repayment/<EMAIL>'
import styles from './index.module.scss';
import { useLoad } from '@tarojs/taro';
import { getRepaymentState } from '~services/repayment';
import { CyButton } from 'hncy58-taro-components';
import PageWrap from '~components/PageWrap';
import Clock from '~components/ClockAniCanvas';
import { formattedMoney, router } from '~utils/common';
import usePolling from '~hooks/usePolling';
import CustomerServiceLink from '~components/CustomerServiceLink';
import useRepaymentStore from '~store/repayment';
const RepaymentResult = () => {
  /** 1. 变量定义 */
  const [result, setResult] = useState<API.RepaymentStatusCode>('PROCESSING');
  const [repaymentRegisterId, setRepaymentRegisterId] = useState<string>('');
  const [queryinfo, setQueryInfo] = useState<{
    repaymentRegisterId?: string;
    repaymentAccount?: string;
    repaymentAmount?: number;
  }>({});
  const resOpt: Record<API.RepaymentStatusCode, string> = {
    PROCESSING: '还款处理中，请您耐心等待',
    SUCCESS: '还款成功',
    FAILED: '还款失败',
  };
  const resNote: Record<API.RepaymentStatusCode, string> = {
    PROCESSING: '',
    SUCCESS: '我司已收到您的还款，并已为您实时恢复额度。实际还款金额以入账为准',
    FAILED: '还款失败，请您重新申请',
  };
  /** 2. usestates + storestates */

  /** 3. effects */
  useLoad(({repaymentRegisterId, repaymentAccount, repaymentAmount}) => {
    if (repaymentRegisterId) {
      setQueryInfo({repaymentRegisterId, repaymentAccount, repaymentAmount});
      setRepaymentRegisterId(repaymentRegisterId);
    }
  });

  usePolling({
    fetchFn: () => getRepaymentState({repaymentRegisterId}),
    onSuccess: ({state}) => {
      setResult(state);
      if(state === 'SUCCESS') {
        useRepaymentStore.getState().updateRepaymentSummary();
      }
    },
    interval: 4000,
    maxDuration: 12000,
    stopCondition: ({state}) => state !== 'PROCESSING',
  })

  /** 4. 方法定义 */
  const goHome = () => {
    router.reLaunch({url: '/pages/check/index'});
  };

  /** 5. ui */
  return (
    <PageWrap className={styles.page_lending_result}>
      {
        result === 'PROCESSING' ? (
          <Clock size={160} className={styles.waitClock} />
        ) : 
        <Image className={styles.result_img} src={
          result === 'SUCCESS' ? resSuccess : resFail } />
      }
      <View className={styles.result_value}>
        <View className={styles.result_text}>{resOpt[result]}</View>
        {resNote[result] && <View className={styles.result_note}>{resNote[result]}</View>}
      </View>

      {result !== 'PROCESSING' && (
        <View className={styles.lending_info}>
          <View className={styles.info_content}>
            <View className={styles.label}>还款金额</View>
            <View className={styles.value}>{formattedMoney(`${queryinfo.repaymentAmount}`, {symbol: ''})}元</View>
          </View>
          <View className={styles.info_content}>
            <View className={styles.label}>还款账户</View>
            <View className={styles.value}>{queryinfo.repaymentAccount}</View>
          </View>
        </View>
      )}

      {result === 'SUCCESS' ? (
        <View style={{width: '100%', marginTop: 20}}>
          <CyButton round type='primary' block onClick={goHome}>
            我知道了
          </CyButton>
        </View>
      ) : (
        <View className={styles.result_tip}>
          <CustomerServiceLink/>
        </View>
      )}
    </PageWrap>
  );
};

export default RepaymentResult;
