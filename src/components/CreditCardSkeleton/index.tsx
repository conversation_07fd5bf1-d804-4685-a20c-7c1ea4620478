import React from 'react';
import { View } from '@tarojs/components';
import './index.scss';

const CreditCardSkeleton: React.FC = () => {
  return (
    <View className="credit-card-skeleton">
      {/* 顶部装饰条 */}
      <View className="credit-card-skeleton__topbar skeleton-shimmer" />
      
      {/* 主要内容区域 */}
      <View className="credit-card-skeleton__content">
        {/* 标题骨架 */}
        <View className="credit-card-skeleton__subtitle skeleton-shimmer" />
        
        {/* 金额骨架 */}
        <View className="credit-card-skeleton__amount skeleton-shimmer" />
        
        {/* 描述文字骨架 */}
        <View className="credit-card-skeleton__desc skeleton-shimmer" />
        
        {/* 按钮区域骨架 */}
        <View className="credit-card-skeleton__buttons">
          <View className="credit-card-skeleton__button skeleton-shimmer" />
          <View className="credit-card-skeleton__button skeleton-shimmer" />
        </View>
        
        {/* 底部信息区域骨架 */}
        <View className="credit-card-skeleton__info-bar">
          <View className="credit-card-skeleton__info-item">
            <View className="credit-card-skeleton__info-label skeleton-shimmer" />
            <View className="credit-card-skeleton__info-value skeleton-shimmer" />
          </View>
          <View className="credit-card-skeleton__info-item">
            <View className="credit-card-skeleton__info-label skeleton-shimmer" />
            <View className="credit-card-skeleton__info-value skeleton-shimmer" />
          </View>
          <View className="credit-card-skeleton__info-item">
            <View className="credit-card-skeleton__info-label skeleton-shimmer" />
            <View className="credit-card-skeleton__info-value skeleton-shimmer" />
          </View>
        </View>
      </View>
      
      {/* 底部提示区域骨架 */}
      <View className="credit-card-skeleton__bottom">
        <View className="credit-card-skeleton__tip skeleton-shimmer" />
      </View>
    </View>
  );
};

export default CreditCardSkeleton;