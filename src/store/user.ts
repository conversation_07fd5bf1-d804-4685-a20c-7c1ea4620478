import Taro from '@tarojs/taro';
import {produce} from 'immer';
import {create} from 'zustand';
import {createJSONStorage, devtools, persist} from 'zustand/middleware';
import {getStoragePromise} from '../utils/common';
import { getAccessToken, getCustomerInfo, getUserCreditInfo } from '~services/user';
import { isShowRaiseCreditAmount } from '~services/raiseAmount';
import useMainStore from './main';
import useRepaymentStore from './repayment';

type UserState = {
  user: API.UserInfo;
  amountCreditInfo: API.AmountCreditInfo;
  isShowRaiseAmount: boolean;
  isHasAmountCreditInfo: () => boolean;
  // accessToken: string;
};
type UserActions = {
  // setAccessToken: (token: string) => void;
  setUser: (user: API.UserInfo) => void;
  setAmountCreditInfo: (amountInfo: API.AmountCreditInfo) => void;
  resetAmountCreditInfo: () => void;
  resetUserBaseInfo: () => void;
  logout: () => void;
  updateUserBaseInfo: () => void;
  updateUserInfo: () => void;
  getUserAmountInfo: () => void;
  updateIsShowRaiseAmount: () => void;
  setIsShowRaiseAmount: (isShowRaiseAmount: boolean) => void;
  wechatLogin: () => Promise<boolean>;
};
const initialUser = {
  username: '',
  name: '',
  certId: '',
  custUid: '',
  mobile: '',
  creditAmount: 0,
  creditState: 'success' as API.CreditStates,
  registerFlag: false,
};
const initAmountCreditInfo = {
  activated: false,
  availableAmount: 0,
  certId: '',
  creditAmount: 0,
  effAmount: 0,
  expireDate: '',
  frozenAmount: 0,
  loanCode: '1109',
  prodCode: 'P1109' as API.ProductCodes,
  rate: 0.0004,
  state: 'INIT' as API.CreditStates,
  stateDesc: '未授信',
  usedAmount: 0,
  repaymentModeDTOS: [],
  needCreditApplySuccessView: false,
};
// const initAccessToken = '';
const useUserStore = create(
  persist(
    devtools<UserState & UserActions>((set, get) => ({
      // accessToken: initAccessToken,
      user: initialUser,
      amountCreditInfo: initAmountCreditInfo,
      isShowRaiseAmount: false,
      isHasAmountCreditInfo: () => {
        return !get().amountCreditInfo?.state || !['NO_AMOUNT', 'NO_AMOUNT_LACK_CARD_PIC', 'EXPIRED', 'INIT'].includes(get().amountCreditInfo?.state);
      },
      setAmountCreditInfo: async (amountInfo: API.AmountCreditInfo) => {
        set(
          produce(state => {
            state.amountCreditInfo = amountInfo;
          }),
          true,
          'amountCreditInfo',
        );
      },
      setUser: user => {
        set(
          produce(state => {
            state.user = user;
          }),
          true,
          'setUser',
        );
      },
      resetUserBaseInfo: () => {
        set(
          produce(state => {
            state.user = initialUser;
          }),
          true,
          'resetUserBaseInfo',
        );
      },
      resetAmountCreditInfo: () => {
        set(
          produce(state => {
            state.amountCreditInfo = {
              ...initAmountCreditInfo,
              state: 'NO_AMOUNT'
            };
          }),
          true,
         'resetAmountCreditInfo',
        );
      },
      logout: () => {
        console.log('store logout');
        get().resetUserBaseInfo();
        get().resetAmountCreditInfo();
        useRepaymentStore.getState().resetRepaymentSummary();
      },
      // 更新用户信息，包含授信信息，注册信息, 提额信息等
      updateUserInfo: async () => {
        if(!useMainStore.getState().token) {
          return;
        }
        await get().updateUserBaseInfo();
        await get().getUserAmountInfo();
        get().updateIsShowRaiseAmount();
      },
      getUserAmountInfo: async () => {
        if(!get().user.registerFlag) {
          get().resetAmountCreditInfo();
          return;
        }
        const creditInfoRes = await getUserCreditInfo();
        if (creditInfoRes) {
          if(!creditInfoRes.state) {
            creditInfoRes.state = 'NO_AMOUNT';
          }
          get().setAmountCreditInfo(creditInfoRes);
        }
      },
      updateUserBaseInfo: async () => {
        const infoRes = await getCustomerInfo();
        get().setUser(infoRes);
      },
      updateIsShowRaiseAmount: async () => {
        if(!get().user.registerFlag) {
          return;
        }
        const res = await isShowRaiseCreditAmount();
        get().setIsShowRaiseAmount(res.menuVisible)
      },
      setIsShowRaiseAmount: (isShowRaiseAmount: boolean) => {
        set(
          produce(state => {
            state.isShowRaiseAmount = isShowRaiseAmount;
          }),
          true,
          'setIsShowRaiseAmount',
        );
      },
      wechatLogin: async () => {
        return new Promise((resolve, reject) => {
          Taro.login({
            success: async res => {
              if (res.code) {
                //发起网络请求
                try {
                  await getAccessToken({username: res.code});
                } catch (e) {
                  //
                }
              } else {
                console.log(`登录失败！${res.errMsg}`);
              }
              resolve(true)
            },
            fail: res => {
              reject(res)
            },
          });
        })
      },
      // login: async (token: string) => {
      //   get().setAccessToken(token);
      // },
    })),
    {
      name: 'userStore', // name of the item in the storage (must be unique)
      storage: createJSONStorage(() => ({
        setItem: (key: string, data: string) => {
          Taro.setStorage({
            data,
            key,
          });
        },
        async getItem(key: string) {
          try {
            return await getStoragePromise(key);
          } catch (error) {
            return null;
          }
        },
        removeItem(key) {
          Taro.removeStorage({
            key,
          });
          return;
        },
      })),
    },
  ),
);

export default useUserStore;
