import React from 'react';
import { View } from '@tarojs/components';
import { CySkeleton, SkeletonGroup } from './index';
import styles from './demo.module.scss';

/**
 * 骨架屏示例组件
 */
const SkeletonDemo: React.FC = () => {
  return (
    <View className={styles.container}>
      <View className={styles.section}>
        <View className={styles.title}>基础骨架屏</View>
        <CySkeleton width="100%" height={20} />
      </View>
      
      <View className={styles.section}>
        <View className={styles.title}>不同尺寸</View>
        <View className={styles.row}>
          <CySkeleton width={60} height={60} className={styles.item} />
          <CySkeleton width={80} height={80} className={styles.item} />
          <CySkeleton width={100} height={100} className={styles.item} />
        </View>
      </View>
      
      <View className={styles.section}>
        <View className={styles.title}>不同形状</View>
        <View className={styles.row}>
          <CySkeleton width={80} height={80} shape="square" className={styles.item} />
          <CySkeleton width={80} height={80} shape="circle" className={styles.item} />
          <CySkeleton width={80} height={20} shape="rect" className={styles.item} />
        </View>
      </View>
      
      <View className={styles.section}>
        <View className={styles.title}>无动画效果</View>
        <CySkeleton width="100%" height={20} animated={false} />
      </View>
      
      <View className={styles.section}>
        <View className={styles.title}>自定义渐变色</View>
        <CySkeleton 
          width="100%" 
          height={20} 
          gradientColors={['#e6f7ff', '#91d5ff', '#e6f7ff']} 
        />
      </View>
      
      <View className={styles.section}>
        <View className={styles.title}>列表骨架屏</View>
        <SkeletonGroup 
          avatar 
          rows={3} 
          rowHeight={16}
          rowGap={12}
          direction="row"
        />
      </View>
      
      <View className={styles.section}>
        <View className={styles.title}>卡片骨架屏</View>
        <View className={styles.card}>
          <CySkeleton width="100%" height={160} />
          <View className={styles.cardContent}>
            <SkeletonGroup 
              rows={2} 
              rowHeight={20}
              rowGap={16}
              rowWidth={['100%', '70%']}
            />
          </View>
        </View>
      </View>
      
      <View className={styles.section}>
        <View className={styles.title}>聊天消息骨架屏</View>
        <View className={styles.chatContainer}>
          <SkeletonGroup 
            avatar 
            avatarSize={40}
            rows={2} 
            rowHeight={16}
            rowGap={8}
            direction="row"
            className={styles.chatMessage}
          />
          
          <SkeletonGroup 
            avatar 
            avatarSize={40}
            rows={1} 
            rowHeight={16}
            direction="row-reverse"
            className={styles.chatMessage}
          />
          
          <SkeletonGroup 
            avatar 
            avatarSize={40}
            rows={3} 
            rowHeight={16}
            rowGap={8}
            direction="row"
            className={styles.chatMessage}
          />
        </View>
      </View>
    </View>
  );
};

export default SkeletonDemo;