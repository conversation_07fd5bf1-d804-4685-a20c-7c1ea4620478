import {useEffect, useState} from 'react';
import {ScrollView, View, Image} from '@tarojs/components';
import RepaymentBadge from '~modules/repayment/components/RepaymentBadge';
import styles from './index.module.scss';
import selectedIcon from '~images/lending/<EMAIL>'
import { IRepaymentType } from 'src/types/common-types';

interface PeriodsProps {
  periods: number[];
  repaymentType: IRepaymentType;
  selectedPeriod: number;
  onSelect?: (period: number) => void;
}
const Periods = ({repaymentType, selectedPeriod, periods, onSelect}: PeriodsProps) => {

  const [currentPeriod, setCurrentPeriod] = useState<number | null>(null);

  useEffect(() => {
    const findMaxPeriods = Math.max(...periods);
    setCurrentPeriod(selectedPeriod ? selectedPeriod : findMaxPeriods)
  }, [repaymentType, periods]);
  const isChoosed = (period: number) => {
    return (period === currentPeriod && repaymentType === 'MCIP') ||
      (period === currentPeriod && repaymentType !== 'MCIP')
      ? true
      : false;
  };
  const isDisabled = (period: number) => {
    return repaymentType === 'MCIP' && [36, 24].includes(period) ? true : false;
  };
  const handleChoose = (period: number) => {
    if (isDisabled(period)) return;
    setCurrentPeriod(period);
    onSelect?.(period);
  };
  return (
    <>
      <View className={styles.periodsWrap}>
        <ScrollView scrollX showScrollbar={false} className={styles.periodsScrollView}>
          <View className={styles.periodsContainer}>
            {periods.map((ite, index) => (
              <View
                className={`${styles.periodsItem} ${isDisabled(ite) ? styles.disabled : ''} ${isChoosed(ite) ? styles.choosed : ''}`}
                onClick={() => handleChoose(ite)}
              >
                {`${ite}个月`}
                {isChoosed(ite) ? <Image src={selectedIcon} className={styles.choosedIcon} /> : null}
              </View>
            ))}
          </View>
        </ScrollView>
      </View>
      <View className={styles.block}></View>
    </>
  );
};

export default Periods;
