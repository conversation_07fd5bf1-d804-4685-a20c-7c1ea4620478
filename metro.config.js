const { getDefaultConfig, mergeConfig } = require('@react-native/metro-config')
const { getMetroConfig } = require('@tarojs/rn-supporter')

/**
 * Metro configuration
 * https://facebook.github.io/metro/docs/configuration
 *
 * @type {import('metro-config').MetroConfig}
 */
module.exports = (async function (){
  const taroConfig = await getMetroConfig()

  const config = {

    // resolver: {
    //   resolveRequest: async (context, moduleName, platform) => {
    //     if (moduleName.endsWith('stylelint-taro-rn')) {
    //       return {
    //         filePath: './shime/stylelint-taro-rn.index.esm.js',
    //         type: 'sourceFile',
    //       };
    //     }
    //     // Optionally, chain to the standard Metro resolver.
    //     return taroConfig.resolver.resolveRequest(context, moduleName, platform);
    //   },
    // },
  }
  return mergeConfig(getDefaultConfig(__dirname), taroConfig, config)
})()