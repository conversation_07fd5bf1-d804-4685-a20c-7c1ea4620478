import {useRef, useState} from 'react';
import {PositionType, UseLocationParams} from '.';
import {getLocation} from '../../utils/location';

export default function useLocation(params?: UseLocationParams) {
  const [position, setPosition] = useState<PositionType>({
    lat: undefined,
    lng: undefined,
  });

  const startLocation = ({onSuccess, onFail}: UseLocationParams) => {
    getLocation({
      onSuccess: data => {
        console.log('获取地理位置结果', data);
        setPosition({
          lat: `${data.latitude}`,
          lng: `${data.longitude}`,
        });
        onSuccess(data);
        params?.onFail?.(data);
      },
      onFail: err => {
        console.log('获取地理位置失败');
        onFail(err);
        params?.onFail?.(err);
      },
      usage: ''
    });
  };
  return {
    startLocation,
    position,
  };
}
