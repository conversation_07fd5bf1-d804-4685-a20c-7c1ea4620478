import { useMemo } from "react";
import useUserStore from "~store/user"

const useGetRepayment = () => {
  const { amountCreditInfo } = useUserStore();

  const repaymentTypeMap = useMemo(() => {
    if (amountCreditInfo?.repaymentModeDTOS?.length) {
      const obj: Record<string, string> = {};
      amountCreditInfo.repaymentModeDTOS.forEach(item => {
        obj[item.repaymentType as string] = item.repaymentTypeName as string;
      })
      return obj;
    }
    return {};
  }, [amountCreditInfo])

  return repaymentTypeMap
}

export default useGetRepayment