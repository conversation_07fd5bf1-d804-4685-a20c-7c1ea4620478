//
//  MapManager.m
//  taroDemo
//
//  Created by captian on 2024/3/11.
//

#import "LocationManager.h"
@interface LocationManager()<AMapLocationManagerDelegate>

@property (nonatomic, strong) AMapLocationManager * locationManager;
@property (nonatomic, strong) NSDictionary * updatingLocationCache;
@property (nonatomic, assign) Boolean updatingLocation;
@property (nonatomic, assign) CLAuthorizationStatus authorizationStatus;

@end
@implementation LocationManager

// 类方法实现，返回单例实例
+ (instancetype)sharedInstance {
  static LocationManager *sharedInstance = nil;
  static dispatch_once_t onceToken;
  dispatch_once(&onceToken, ^{
    sharedInstance = [[self alloc] init];
    [AMapServices sharedServices].apiKey = @"2966867426008dc0c439701c6a438edf";
  });
  return sharedInstance;
}

// 重写 init 方法，确保只被调用一次
- (instancetype)init {
  self = [super init];
  if (self) {
    // 初始化代码
  }
  return self;
}

- (AMapLocationManager* )locationManager {
  if(_locationManager == nil) {
    [AMapLocationManager updatePrivacyAgree:AMapPrivacyAgreeStatusDidAgree];
    [AMapLocationManager updatePrivacyShow:AMapPrivacyShowStatusDidShow privacyInfo:AMapPrivacyInfoStatusDidContain];
    
    _locationManager = [[AMapLocationManager alloc] init];
    _locationManager.delegate = self;
    _locationManager.distanceFilter = kCLDistanceFilterNone;
//    _locationManager.locatingWithReGeocode = true;
    // 带逆地理信息的一次定位（返回坐标和地址信息）
    [_locationManager setDesiredAccuracy:kCLLocationAccuracyHundredMeters];
    //   定位超时时间，最低2s，此处设置为2s
    _locationManager.locationTimeout =2;
    //   逆地理请求超时时间，最低2s，此处设置为2s
    _locationManager.reGeocodeTimeout = 2;
    
    self.authorizationStatus = [CLLocationManager authorizationStatus];
  }
  return _locationManager;
}


//当前应用是否可以使用/申请定位服务
- (BOOL)locationServiceIsValid{
  if (self.authorizationStatus == kCLAuthorizationStatusDenied || self.authorizationStatus == kCLAuthorizationStatusRestricted) {
    return NO;
  }
  return YES;
}

//当前定位状态是否可用
- (BOOL)locationAuthStatusIsValid{
  if (self.locationServiceIsValid == NO) {
    return NO;
  }
  if (self.authorizationStatus == kCLAuthorizationStatusNotDetermined) {
    return NO;
  }
  return YES;
}

- (CLAuthorizationStatus)getAuthorizationStatus{
  return self.authorizationStatus;
}

//获取一次定位
- (BOOL)requestLocationWithReGeocode:(BOOL)withReGeocode completionBlock:(AMapLocatingCompletionBlock)completionBlock{
  return [self.locationManager requestLocationWithReGeocode:withReGeocode completionBlock:completionBlock];
}

//开始持续定位
- (void)startUpdatingLocation{
  self.updatingLocation = true;
  [self.locationManager startUpdatingLocation];
}

//关闭持续定位
- (void)stopUpdatingLocation{
  self.updatingLocation = false;
  [self.locationManager stopUpdatingLocation];
}

//获取持续定位位置
- (void)getUpdatingLocationInfoWithCompletionBlock:(GetLocatingCompletionBlock)completionBlock{
  //判断权限
  if(![self locationAuthStatusIsValid]){
    completionBlock(nil,[NSError errorWithDomain:@"getUpdatingLocation" code:3 userInfo:@{@"message":@"定位权限不足"}]);
    return;
  }
  
  if(self.updatingLocationCache){
    completionBlock(self.updatingLocationCache,nil);
  }else{
    if(!self.updatingLocation){
      completionBlock(nil,[NSError errorWithDomain:@"getUpdatingLocation" code:2 userInfo:@{@"message":@"请先开启持续定位"}]);
    }else{
      completionBlock(nil,[NSError errorWithDomain:@"getUpdatingLocation" code:1 userInfo:@{@"message":@"获取定位失败"}]);
    }
  }
}


#pragma mark    AMapLocationManagerDelegate
- (void)amapLocationManager:(AMapLocationManager *)manager doRequireLocationAuth:(CLLocationManager*)locationManager{
  [locationManager requestAlwaysAuthorization];
}

- (void)amapLocationManager:(AMapLocationManager *)manager didUpdateLocation:(CLLocation *)location reGeocode:(AMapLocationReGeocode *)reGeocode;
{
  NSLog(@"更新位置成功:{lat:%f; lon:%f; accuracy:%f}", location.coordinate.latitude, location.coordinate.longitude, location.horizontalAccuracy);
  NSDictionary * locationInfo =  @{
    @"latitude":@(location.coordinate.latitude),
    @"longitude":@(location.coordinate.longitude),
    @"time":@([[NSDate date] timeIntervalSince1970]),
  };
  self.updatingLocationCache = locationInfo;
}

- (void)amapLocationManager:(AMapLocationManager *)manager didFailWithError:(NSError *)error{
  NSLog(@"didFailWithError=%@",error);
}

- (void)amapLocationManager:(AMapLocationManager *)manager locationManagerDidChangeAuthorization:(CLLocationManager*)locationManager{
  NSLog(@"locationManagerDidChangeAuthorization");
  if (@available(iOS 14.0, *)) {
    self.authorizationStatus = locationManager.authorizationStatus;
  }else {
    self.authorizationStatus = [CLLocationManager authorizationStatus];
  }
  [[NSNotificationCenter defaultCenter] postNotificationName:@"LocationDidChangeAuthorizationLocation" object:nil userInfo:@{@"status":[NSString stringWithFormat:@"%d",self.authorizationStatus]}];
}
@end

