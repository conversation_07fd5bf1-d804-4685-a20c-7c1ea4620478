import {showToast} from '../common';

export type WXPayReqParams = {
  prepayId: string;
  nonceStr: string;
  timeStamp: string;
  sign: string;
  repaymentRegisterId: string;
};

export type AlipayReqParams = {};
export default {
  wxPay: async (wxpayParams: WXPayReqParams) => {
    showToast('该端暂未实现');
    console.log('wxPay');
    return false;
  },
  alipayPay: async (alipayParams: AlipayReqParams) => {
    showToast('该端暂未实现');
    console.log('alipayPay');
    return false;
  },
};
