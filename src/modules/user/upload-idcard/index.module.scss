.page-content {
    padding: 20px;
}

.page-top {
    position: fixed;
    top: 0;
    left: 0;
    background-color: #fff;
    padding: 24px 0;
    width: 100%;
    z-index: 9;
}

.tips {
    font-size: 24px;
    color: $color-text-secondary;
    text-align: center;
    padding: 12px 0;
    margin-top: 20px;
}

.card-tips-right {
    display: flex;
    align-items: center;
}

.top-tips {
    position: absolute;
    display: flex;
    justify-content: space-between;
    font-size: 24px;
    font-weight: normal;
    color: $color-text-secondary;
    padding-right: 40px;
    justify-content: flex-end;
    right: 0;
    top: 0;
}

.tips-icon {
    width: 24px;
    height: 24px;
    margin-right: 8px;
}

.mark {
    color: $color-primary;
}

.identity-input {
    background-color: #f6f6f6;
    width: 400px;
    height: 88px;
    text-align: center;
    border-radius: 44px;
    font-size: 30px;
    margin-top: 40px;
}
.identify-idcard-entry {
    display: block;
    width: 448px;
    height: 240px;
    margin: 48px auto 0;
}
.input-identify-card {
    background: url('../../../assets/images/credit-apply/idcard-input-bg.png') no-repeat;
    background-size: 100% 100%;
}
.bottom-section {
    padding-top: 120px;
    display: flex;
    align-items: center;
    flex-direction: column;
    justify-content: center;
    padding-bottom: 170px;
}

.idcardIcon {
    width: 32px;
    height: 32px;
    margin-right: 12px;
}

.idcardInputDesc {
    color: #a5adcc;
    font-size: 24px;
}

.row {
    display: flex;
    align-items: center;
    height: 122px;
    margin: 0 20px;
    padding: 0 28px;

    & + .row {
        border-top: 1px solid $color-border-light;
    }
}

.label {
    width: 140px;
    text-align: justify;
    text-align-last: justify;
    text-justify: inter-ideograph;
    font-size: 30px;
    font-weight: normal;
}

.value {
    font-size: 30px;
}

.canvas {
    width: 640px;
    height: 320px;
    margin: 0 auto;
}

.bottom-btn {}

.page-footer {
    padding: 20px 20px;
    box-sizing: border-box;
    background-color: $color-bg-normal;
}

.card-content {
    padding: 24px 20px 0 20px;
}

.identify-idcard-img {
    width: 670px;
    height: 360px;
    margin-top: 24px;
}

.idcard-pic-title {
    position: relative;
    color: #2b2c05;
    font-size: 28px;
    padding-left: 16px;

    &::before {
        content: '';
        position: absolute;
        left: 0;
        top: 50%;
        transform: translateY(-50%);
        width: 8px;
        height: 8px;
        display: block;
        background-color: $color-primary;
        border-radius: 50%;
    }
}

.upload-idcard-entry-wrapper {
    border: 2px dashed $color-border-light;
    margin: 30px;
    height: 400px;
}

.upload-idcard-entry-text {
    display: flex;
    justify-content: center;
    align-items: center;
    color: $color-primary;
    font-size: 28px;
    line-height: 32px;
    margin-top: 32px;
}

.upload-idcard-entry-icon {
   width: 32px;
   height: 32px;
   margin-right: 8px;
}

.modal-content {
    // padding: 20px 40px 60px 40px;
    font-size: 28px;
    color: $color-text-regular;
    line-height: 48px;  
}

.labelValueGap {
    padding: 0 12px;
}

.idcard-pic-content {
    position: relative;
}

.photo-icon {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 120px;
    height: 120px;
}
