{
  "extends": "@react-native/typescript-config/tsconfig.json",
  // 改写下complieOptions，避免没装jest依赖报错
  "compilerOptions": {
    "types": ["react-native","node"],
    "strict": true,
    "baseUrl": "./",
    "paths": {
      "~icons/*": ["src/assets/icons/*"],
      "~images/*": ["src/assets/images/*"],
      "~components/*": ["src/components/*"],
      "~store/*": ["src/store/*"],
      "~utils/*": ["src/utils/*"],
      "~services/*": ["src/services/*"],
      "~modules/*": ["src/modules/*"],
      "~hooks/*": ["src/hooks/*"],
      "~src/*": ["src/*"],
    } 
  },
  "include": ["typings","src"]
}
