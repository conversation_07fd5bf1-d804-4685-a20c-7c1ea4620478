import React, {Component, useEffect, useRef, useState} from 'react';
// import {CyButton, CyModal} from 'hncy58-taro-components';
// import SafeKeyboard from '@hncy58/rn-safe-keyboard/src/index.weapp.tsx';
// import {HostComponent, NativeMethods, Platform, UIManager, findNodeHandle} from 'react-native';
// import {MenuView} from '@react-native-menu/menu';
// import RNTCaptcha from '@hncy58/rn-t-captcha/js/NativeRNTCaptcha';
// import {SafeKeyboardView} from '@hncy58/safe-keyboard';
import {Button, ScrollView, Text, View, WebView} from '@tarojs/components';
import Taro from '@tarojs/taro';
import {z} from 'zod';
// import RNPrivateFace from 'rn-private-face/js/NativeRNPrivateFace';
// import RTNCalculator from 'rtn-calculator/js/NativeRTNCalculator';
// import RNSafeKeyboard, {SafeKeyboardRef} from 'rn-safe-keyboard';
// import RTNTextHelper from 'rtn-texthelper/js/NativeRTNTextHelper';
import PP from '~icons/404-pc.png';
import PPVG from '~icons/404-pc.svg';
import PPX from '~icons/<EMAIL>';
import CyCaptcha, {CyCaptchaT} from '../../components/CyCaptcha';
// import CyFace from '../../components/CyFace';
import CyIcon from '../../components/CyIcon';
import CyImage from '../../components/CyImage';
// import CySafeKeyboard, {type CySafeKeyboardRef} from '../../components/CySafeKeyboard';
import useLocation from '../../hooks/useLocation';
import {FaceCheckType} from '../../services/aliyunFace';
import {showToast} from '../../utils/common';
import startFace from '../../utils/face';
import {getLocation} from '../../utils/location';
import Pay from '../../utils/pay';
import styles from './test.module.scss';

const Test = () => {
  /** 1. 变量定义 */

  const [locationData, setLocationData] = useState<API.AddressComponent>();
  const {startLocation, position} = useLocation();
  const captchaRef = useRef<CyCaptchaT>(null);
  const mySchema = z.string();
  mySchema.parse('123');
  // const keyboardRef = useRef<CySafeKeyboardRef>(null);
  // const keyboardRef3 = useRef<SafeKeyboardRef>(null);
  // const keyboardRef = useRef<CySafeKeyboardRef>(null);
  // const keyboardRef2 = useRef<SafeKeyboardComponentRef>(null);

  const [serverRandom, setServerRandom] = useState('RUPWBTGJVRZUSHATKKATEULHHSJOBJEO');
  const [result, setResult] = useState<number | null>(null);

  // const TextRef = useRef<React.ElementRef<HostComponent<NativeProps>>>(null);
  const menuRef = useRef<any>(null);

  const getLocationClick = () => {
    getLocation({
      onSuccess: data => {
        console.log('获取地理位置结果', data);
        setLocationData(data);
      },
      onFail: e => {
        console.log('获取地理位置失败', e);
      },
    });
  };
  /** 2. usestates + storestates */

  /** 3. effects */
  useEffect(() => {
    // const textInfo = {
    //   text: '测试文本',
    //   fontSize: 12,
    // };
    // const result = RTNTextHelper?.measureTextSync(textInfo);
    // console.log('===RTNTextHelper.measureTextSync==', JSON.stringify(result));
    // RTNTextHelper?.measureTextAsync(textInfo)
    //   .then(res => {
    //     console.log('===RTNTextHelper.measureTextAsync==', JSON.stringify(res));
    //   })
    //   .catch(() => {
    //     // ignore
    //   });
  }, []);
  /** 4. 方法定义 */
  const handlerCaptchaSuccess = (ticket: string) => {
    //TODO:服务端验证
    console.log('ticket=', ticket);
  };

  const handlerCaptchaShow = () => {
    captchaRef?.current?.show();
  };

  const onDoneClick = () => {
    console.log('onDoneClick');
    // keyboardRef3.current && RNSafeKeyboardCommands.checkInputValueMatch(keyboardRef3.current, 'username');
    // keyboardRef3.current && RNSafeKeyboardCommands.getEncryptedInputValue(keyboardRef3.current);
    // keyboardRef3.current && RNSafeKeyboardCommands.getEncryptedClientRandom(keyboardRef3.current);
    // const check = keyboardRef2?.current?.checkInputValueMatch('password');
    // const encryptedInputValue = keyboardRef2?.current?.getEncryptedInputValue();
    // const random = keyboardRef2?.current?.getEncryptedClientRandom();
    // const inputValue = keyboardRef2?.current?.getInputValue();
    // console.log('check=', check);
    // console.log('encryptedInputValue=', encryptedInputValue);
    // console.log('random=', random);
    // console.log('inputValue=', inputValue);
  };

  const onInputChangeCallBack = (val: string) => {
    console.log('onInputChangeCallBack', val);
  };
  const onInputEncryptChangeCallback = (id: string, type: number, length: number) => {
    console.log('onInputEncryptChangeCallback', id, type, length);
  };
  const handleGet = () => {
    // TODO: 未通过
    // console.log('handleGet=', keyboardRef?.current);
    // console.log('getEncryptedInputValue', keyboardRef2?.current?.getEncryptedInputValue());
  };

  const show = () => {};

  const hidden = () => {
    // keyboardRef3.current && RNSafeKeyboardCommands.hidden(keyboardRef3.current);
  };

  const getEncryptedInputValue = () => {};
  const showIsHermesEnable = () => {
    // console.log('Hermes enabled:', (global.HermesInternal || null) !== null, 'Platform:', Platform.OS);
  };
  const handleMessage = () => {
    //
  };

  const [isOpen, setIsOpen] = useState(false);

  const handleOpen = () => setIsOpen(true);
  const handleClose = () => setIsOpen(false);
  // Taro.showModal({
  //   title: '标题',
  //   content: '内容',
  //   success(res) {
  //     if (res.confirm) {
  //       console.log('用户点击确定');
  //     } else if (res.cancel) {
  //       console.log('用户点击取消');
  //     }
  //   },
  // });
  /** 5. ui */
  return (
    <ScrollView className={styles['page-test']}>
      {/* <CyFace
        onSuccess={() => {
          showToast('活体识别成功');
          console.log('哈哈');
        }}>
        <View>发起活体识别</View>
      </CyFace> */}
      <View
        onClick={() => {
          startLocation({
            onSuccess: data => {
              console.log('获取地理位置成功', data);
              console.log(`从回调拿到经纬度：${data.position.lat},${data.position.lng}`);
            },
            onFail: err => {
              console.log('获取地理位置失败', err);
            },
            isRetry: true,
          });
        }}>
        经纬度：{position.lat}，{position.lng}
      </View>
      <View onClick={handlerCaptchaShow}>唤醒人机校验</View>
      <CyCaptcha onSuccess={handlerCaptchaSuccess} ref={captchaRef}></CyCaptcha>
      <View style={{display: 'flex', flexDirection: 'row'}}>
        <View>请输入交易密码</View>
        <View style={{flex: 1}}>
          <CySafeKeyboard
            style={{width: '100%', height: 60, lineHeight: 60}}
            id='password'
            sipId='password'
            ref={keyboardRef}
            isGrid
            placeholder='请输入账号密码'
            serverRandom={serverRandom}
            onDoneClick={onDoneClick}
            onInputChangeCallBack={onInputChangeCallBack}
            // onGetEncryptedInputValueCallBack={onInputEncryptChangeCallback}
            displayMode={1}
            maxLength={4}
            keyboardType={0}
            orderType={1}
          />
        </View>
      </View>
      <Button
        onClick={() => {
          console.log('getEncryptedInputValue', keyboardRef?.current?.getEncryptedInputValue());
        }}>
        获取EncryptedInputValue
      </Button>
      <Button
        onClick={() => {
          console.log('random', keyboardRef?.current?.getEncryptedClientRandom());
        }}>
        获取random
      </Button>
      <Button
        onClick={() => {
          console.log('比对', keyboardRef?.current?.checkInputValueMatch('password'));
        }}>
        比对
      </Button>
      <Button
        onClick={() => {
          keyboardRef?.current?.show();
        }}>
        显示键盘
      </Button>
      <Button
        onClick={() => {
          startFace({
            type: FaceCheckType.lending,
            loanAmount: 0,
            name: '王亚军',
            certId: '430422199411120358',
            frontImage: '',
            backImage: '',
          })
            .then(res => {
              console.log('face res=', res);
            })
            .catch(error => {
              console.log('face errr=', error);
              showToast(error.message);
            });
        }}>
        人脸识别
      </Button>
      <Button
        onClick={() => {
          Pay?.wxPay({
            prepayId: 'wx0f0e0e0e0e0e0e0e',
            nonceStr: 'wx0f0e0e0e0e0e0e0e',
            timeStamp: 'wx0f0e0e0e0e0e0e0e',
            sign: 'wx0f0e0e0e0e0e0e0e',
            repaymentRegisterId: 'wx0f0e0e0e0e0e0e0e',
          });
        }}>
        微信支付
      </Button>
      <Button
        onClick={() => {
          Pay?.alipayPay({
            prepayId: 'wx0f0e0e0e0e0e0e0e',
            nonceStr: 'wx0f0e0e0e0e0e0e0e',
            timeStamp: 'wx0f0e0e0e0e0e0e0e',
            sign: 'wx0f0e0e0e0e0e0e0e',
            repaymentRegisterId: 'wx0f0e0e0e0e0e0e0e',
          }).then(res => {
            console.log('res====', res);
          });
        }}>
        支付宝支付
      </Button>
      {/* <CyButton onClick={handleOpen} type='primary'>
        打开模态框
      </CyButton> */}
      {/* <Button
        onClick={() => {
          RNTCaptcha?.start();
        }}>
        人机交互
      </Button> */}

      <CyIcon iconName='404-pc' type='png' src={PP} />
      <CyIcon iconName='404-px' type='png' src={PP} />
      <CyIcon iconName='404-pc' type='svg' src={PPVG} />
      <CyIcon iconName='404-px' type='png' src={PPX} />
      {/* <CyModal
        isOpen={isOpen}
        onClose={handleClose}
        title='模态框标题2'
        //   footer={[
        //       <CyButton key='cancel' type='default' onClick={handleClose}>
        //           取消
        //       </CyButton>,
        //       <CyButton key='confirm' type='primary' onClick={handleClose}>
        //           确定
        //       </CyButton>
        //   ]}
        footerButtons={[
          {text: '选项1', onClick: handleClose},
          {text: '选项3', onClick: handleClose},
          {text: '选项2', onClick: handleClose, colored: true},
        ]}>
        <View className='modal-content'>
          <Text>这是模态框的内容</Text>
          <Text>你可以在这里放置任何内容</Text>
        </View>
      </CyModal> */}
    </ScrollView>
  );
};

export default Test;
