{"name": "hncy58-miniprogram", "version": "1.0.0", "private": true, "description": "湖南长银五八消费金融+", "templateInfo": {"name": "react-native", "typescript": true, "css": "Sass", "framework": "React"}, "scripts": {"build:weapp": "taro build --type weapp", "build:weapp-bbit": "taro build --type weapp --mode bbit", "build:weapp-sit": "taro build --type weapp --mode sit", "build:h5": "taro build --type h5", "build:h5-bbit": "taro build --type h5 --mode bbit", "build:h5-sit": "taro build --type h5 --mode sit", "build:h5-uat": "taro build --type h5 --mode uat", "build:rn": "taro build --type rn", "build:rn-bbit": "taro build --type rn --mode bbit", "build:rn-sit": "taro build --type rn --mode sit", "build:rn-uat": "taro build --type rn --mode uat", "build:harmony": "taro build --type harmony", "build:quickapp": "taro build --type quickapp", "dev:weapp": "npm run build:weapp -- --watch", "dev:weapp-bbit": "npm run build:weapp -- --watch --mode bbit --reset-cache", "dev:weapp-sit": "npm run build:weapp -- --watch --mode sit --reset-cache", "dev:weapp-uat": "npm run build:weapp -- --watch --mode uat --reset-cache", "dev:h5": "npm run build:h5 -- --watch --reset-cache", "dev:h5-bbit": "npm run build:h5-bbit -- --watch --reset-cache", "dev:h5-sit": "npm run build:h5-sit -- --watch --reset-cache", "dev:h5-uat": "npm run build:h5-uat -- --watch --reset-cache", "dev:rn": "npm run build:rn -- --watch --qr --reset-cache", "dev:rn-bbit": "npm run build:rn -- --watch --qr --mode bbit --reset-cache", "dev:rn-sit": "npm run build:rn -- --watch --qr --mode sit --reset-cache", "dev:rn-uat": "npm run build:rn -- --watch --qr --mode uat --reset-cache", "dev:harmony": "npm run build:harmony -- --watch", "dev:quickapp": "npm run build:quickapp -- --watch", "android": "react-native run-android", "ios": "react-native run-ios", "podInstall": "pod-install", "upgradePeerdeps": "install-peerdeps @tarojs/taro-rn@4.0.9 -o -P && install-peerdeps @tarojs/components-rn@4.0.9 -o -P && install-peerdeps @tarojs/router-rn@4.0.9 -o -P && pod-install", "start": "node scripts/start.mjs", "auto": "node ./scripts/create.mjs", "docs:dev": "vitepress dev docs", "docs:build": "vitepress build docs", "docs:serve": "vitepress serve docs"}, "browserslist": ["last 3 versions", "Android >= 4.1", "ios >= 8"], "engines": {"node": "^18.0.0 || >=20.0.0"}, "author": "", "dependencies": {"@amap/amap-jsapi-loader": "^1.0.1", "@babel/runtime": "^7.24.4", "@bam.tech/react-native-image-resizer": "^3.0.10", "@hookform/resolvers": "^3.3.4", "@react-native-async-storage/async-storage": "1.21.0", "@react-native-camera-roll/camera-roll": "^7.8.3", "@react-native-clipboard/clipboard": "^1.14.3", "@react-native-community/cli-platform-android": "^12.3.0", "@react-native-community/geolocation": "^3.4.0", "@react-native-community/netinfo": "11.1.0", "@react-native-community/slider": "4.4.2", "@react-native-masked-view/masked-view": "~0.3.1", "@react-native-menu/menu": "^1.2.2", "@react-native-picker/picker": "2.6.1", "@react-native-retu/bugly": "^0.1.5", "@react-native/gradle-plugin": "^0.73.0", "@shopify/react-native-skia": "^1.2.3", "@taro-hooks/plugin-auto-import": "^2.0.10", "@taro-hooks/plugin-react": "^2.0.10", "@taroify/core": "^0.8.1", "@tarojs/components": "4.0.9", "@tarojs/components-rn": "^4.0.9", "@tarojs/helper": "4.0.9", "@tarojs/plugin-framework-react": "4.0.9", "@tarojs/plugin-http": "4.0.9", "@tarojs/plugin-platform-alipay": "4.0.9", "@tarojs/plugin-platform-h5": "4.0.9", "@tarojs/plugin-platform-harmony-ets": "4.0.9", "@tarojs/plugin-platform-harmony-hybrid": "4.0.9", "@tarojs/plugin-platform-jd": "4.0.9", "@tarojs/plugin-platform-qq": "4.0.9", "@tarojs/plugin-platform-swan": "4.0.9", "@tarojs/plugin-platform-tt": "4.0.9", "@tarojs/plugin-platform-weapp": "4.0.9", "@tarojs/react": "4.0.9", "@tarojs/rn-supporter": "^4.0.9", "@tarojs/runtime": "4.0.9", "@tarojs/runtime-rn": "^4.0.9", "@tarojs/shared": "4.0.9", "@tarojs/taro": "4.0.9", "@tarojs/taro-rn": "^4.0.9", "@tarojs/vite-runner": "4.0.9", "@vant/area-data": "^2.0.0", "@vitejs/plugin-react": "^4.2.1", "axios": "^1.6.7", "classnames": "^2.5.1", "crypto-js": "^4.2.0", "currency.js": "^2.0.4", "dayjs": "^1.11.11", "expo": "~50.0.0", "expo-av": "~13.10.6", "expo-barcode-scanner": "~12.9.3", "expo-brightness": "~11.8.0", "expo-camera": "~14.1.3", "expo-file-system": "~16.0.9", "expo-image-picker": "~14.7.1", "expo-keep-awake": "~12.8.2", "expo-location": "~16.5.5", "expo-sensors": "~12.9.1", "hncy58-taro-components": "^1.0.0", "hncy58-utils": "^1.0.0", "immer": "^10.0.3", "react": "^18.2.0", "react-dom": "^18.2.0", "react-hook-form": "^7.51.2", "react-native": "^0.73.1", "react-native-device-info": "^14.0.0", "react-native-document-picker": "^9.3.0", "react-native-file-viewer": "^2.1.5", "react-native-fs": "^2.20.0", "react-native-gesture-handler": "~2.14.0", "react-native-image-zoom-viewer": "^3.0.1", "react-native-linear-gradient": "^3.0.0-alpha.1", "react-native-orientation-locker": "^1.7.0", "react-native-pager-view": "6.2.3", "react-native-permissions": "^4.1.5", "react-native-qrcode-svg": "^6.3.0", "react-native-root-siblings": "^5.0.1", "react-native-safe-area-context": "4.8.2", "react-native-screens": "~3.29.0", "react-native-skia": "^0.0.1", "react-native-splash-screen": "^3.3.0", "react-native-svg": "14.1.0", "react-native-update": "^10.13.2", "react-native-view-pdf": "^0.14.0", "react-native-webview": "13.6.4", "rn-fetch-blob": "^0.12.0", "svg-sprite-loader": "^6.0.11", "taro-hooks": "^2.0.10", "terser": "^5.4.0", "tsconfig-paths-webpack-plugin": "^4.0.1", "vite": "^4.2.0", "yup": "^1.4.0", "zod": "^3.22.4", "zustand": "^4.5.1"}, "resolutions": {"stylelint": "16.10.0", "@ant-design/react-native": "5.1.3"}, "devDependencies": {"@babel/core": "^7.24.4", "@babel/plugin-proposal-class-properties": "7.14.5", "@babel/preset-react": "^7.24.1", "@pmmmwh/react-refresh-webpack-plugin": "^0.6.1", "@react-native/metro-config": "^0.80.1", "@react-native/typescript-config": "^0.80.1", "@tarojs/cli": "4.1.3", "@tarojs/rn-runner": "^4.1.3", "@tarojs/taro-loader": "4.1.3", "@tarojs/webpack5-runner": "4.1.3", "@trivago/prettier-plugin-sort-imports": "^5.2.2", "@types/crypto-js": "^4.2.2", "@types/loader-utils": "^2.0.6", "@types/node": "^18", "@types/react": "^18.0.0", "@types/webpack-env": "^1.13.6", "@typescript-eslint/eslint-plugin": "^8.35.1", "@typescript-eslint/parser": "^8.35.1", "babel-plugin-import": "^1.13.8", "babel-plugin-transform-remove-console": "^6.9.4", "babel-preset-taro": "4.1.3", "commander": "^14.0.0", "eslint": "^9.30.1", "eslint-config-prettier": "^10.1.5", "eslint-config-taro": "4.1.3", "eslint-plugin-html": "^8.0.0", "eslint-plugin-import": "^2.12.0", "eslint-plugin-node": "^11.1.0", "eslint-plugin-prettier": "^5.1.3", "eslint-plugin-react": "^7.34.1", "eslint-plugin-react-hooks": "^5.2.0", "file-loader": "^6.2.0", "fs-extra": "^11.2.0", "glob": "^11.0.3", "handlebars": "^4.7.8", "inquirer": "^12.7.0", "install-peerdeps": "^3.0.3", "jszip": "^3.10.1", "loader-utils": "^3.2.1", "markdown-it-task-lists": "^2.1.1", "metro-react-native-babel-preset": "^0.77.0", "ora": "^8.0.1", "pod-install": "^0.3.10", "postcss": "^8.4.38", "postcss-lit": "^1.1.1", "prettier": "^3.2.5", "react-refresh": "^0.17.0", "sass": "^1.75.0", "shelljs": "^0.10.0", "simple-git": "^3.27.0", "stylelint": "16.21.1", "stylelint-config-standard": "^38.0.0", "stylelint-scss": "^6.2.0", "svgo-loader": "^4.0.0", "ts-node": "^10.9.1", "tsconfig-paths-webpack-plugin": "^4.1.0", "typescript": "^5.4.5", "url-loader": "^4.1.1", "vconsole": "^3.15.1", "vitepress": "^1.0.0-rc.44", "webpack": "5.99.9", "webpack-spritesmith": "^1.1.0"}}