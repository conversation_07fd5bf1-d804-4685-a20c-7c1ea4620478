import React, {FC, PropsWithChildren, useState} from 'react';
import {View, ViewProps} from '@tarojs/components';
import styles from './index.module.scss';
import classNames from 'classnames';
interface PageContainerProps extends ViewProps {
  title?: string | React.ReactNode;
  note?: string | React.ReactNode;
  titleDecorate?: React.ReactNode;
  type?: 'error' | 'normal';
  collapsible?: boolean;
  defaultExpanded?: boolean;
  titleStyle?: object;
}

const Card: FC<PropsWithChildren & PageContainerProps> = ({
  children,
  className,
  title,
  note,
  titleDecorate,
  type,
  collapsible = false,
  defaultExpanded = true,
  titleStyle,
  ...otherProps
}) => {
  const isShowTitle = title || note;
  const [expanded, setExpanded] = useState(defaultExpanded);

  const toggleExpand = () => {
    if (collapsible) {
      setExpanded(!expanded);
    }
  };

  return (
    <View className={classNames(styles.card_container, className, styles[`card_${type}`])} {...otherProps}>
      {isShowTitle ? (
        <View className={styles.card_header}>
          {title ? (
            <View className={styles.card_title} onClick={toggleExpand} style={titleStyle}>
              {title}
              {titleDecorate}
              {collapsible && (
                <View className={classNames(styles.card_arrow, expanded && styles.card_arrow_up)}>
                  <View className={styles.card_arrow_icon}></View>
                </View>
              )}
            </View>
          ) : null}
          {note && <View className={styles.card_tips}>{note}</View>}
        </View>
      ) : null}

      <View className={classNames(
        styles.card_content, 
        collapsible && styles.card_over_hidden, 
        collapsible && !expanded && styles.card_content_collapsed
      )}>
        {children}
      </View>
    </View>
  );
};
export default Card;
