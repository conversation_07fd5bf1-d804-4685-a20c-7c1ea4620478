import {CyButton} from 'hncy58-taro-components';
import {Text, View, ViewProps} from '@tarojs/components';
import MoneyDisplay from '~components/MoneyDisplay';
import RepaymentBadge from '~modules/repayment/components/RepaymentBadge';
import styles from './index.module.scss';
import classNames from 'classnames';
import { formattedMoney } from '~utils/common';

interface RepaymentProps extends ViewProps {
  /** 还款单位 */
  unit?: string;
  /** 还款日 */
  repaymentDate?: string;
  /** 还款期数 */
  repaymentNumber?: number;
  /** 还款金额 */
  money: number | string;
  /** 还款状态 */
  status?: API.OriginSummaryCategory;
  /** 逾期天数 */
  overdueDays?: number;
  /** 还款提示 */
  repaymentBadge?: string;
  /** 逾期笔数 */
  overdueNumer?: number;
  /** 还款构成 */
  composition?: {
    /** 本金 */
    principal: number | string;
    /** 利息 */
    interest: number | string;
    /** 罚息 */
    penalty: number | string;
  };
  /** 点击查看明细 */
  handleToDetail?: () => void;
  /** 点击还款 */
  handleToRepayment?: () => void;
}
const RepaymentCard = (props: RepaymentProps) => {
  const {
    unit = '元',
    repaymentDate = '',
    repaymentNumber = 0,
    money,
    status,
    overdueDays,
    repaymentBadge,
    handleToDetail,
    handleToRepayment,
    overdueNumer,
    composition,
  } = props;
  const isOverdue = status === 'OVERDUE';

  const handleToDetailClick = () => {
    handleToDetail?.();
  };

  const handleToRepaymentClick = () => {
    handleToRepayment?.();
  };
  return (
    <View className={`${styles.repayment_container}  ${isOverdue ? styles.repayment_container_overdue : ''}`}>
      <View className={styles.repayment_main}>
        <View className={styles.repayment_title}>
          {`当前应还（${unit}）`}
          {isOverdue && overdueDays ? <View className={classNames(styles.overdue, styles['tag-error'])}>{`已逾期${overdueDays}天`}</View> : null}
        </View>
        <View className={styles.repayment_money}>
          <MoneyDisplay money={money} color='#333' currency={false} size='xlarge'></MoneyDisplay>
        </View>
        {repaymentDate && !isOverdue ? <View className={styles.repayment_day}>{`还款日${repaymentDate}`}</View> : null}
        <View className={styles.repaymentComposition}>
          <View className={styles.repaymentCompositionItem}>本金：{formattedMoney(`${composition?.principal || 0}`, {symbol: ''})}</View>
          <View className={styles.repaymentCompositionItem}>利息：{formattedMoney(`${composition?.interest || 0}`, {symbol: ''})}</View>
          {
            composition?.penalty ? (
              <View className={classNames(styles.repaymentCompositionItem, styles.repaymentCompositionMark)}>罚息：{formattedMoney(`${composition?.penalty || 0}`, {symbol: ''})}</View>
            ) : null
          }
        </View>
        <View className={`${styles.repayment_tip} ${isOverdue ? styles.repayment_tip_overdue : ''}`}>
          {!isOverdue ? (
            <>
              <Text>还款日将自动从绑定银行卡中扣款，请预留足够金额</Text>
              <Text>还款日当天也可主动还款</Text>
            </>
          ) : (
            <>
              <Text>逾期将上报人行征信，并且会产生罚息</Text>
              <Text>请您尽快还款！</Text>
            </>
          )}
        </View>
        {status ? (
          <RepaymentBadge type={status} content={repaymentBadge} visible={!!repaymentBadge} >
            <CyButton disabled={status === 'AHEAD'} className={styles.repayment_btn} block round type='primary' onClick={handleToRepaymentClick}>
              立即还款
            </CyButton>
          </RepaymentBadge>
        ) : null}
      </View>
      {repaymentNumber ? (
        <View className={styles.repayment_detail} onClick={handleToDetailClick}>
          <View className={styles.repayment_detail_left}>
            <Text>本期详情</Text>
            {
              isOverdue? (
                <Text className={styles['tag-error']} style={{marginLeft: 8}}>{overdueNumer}笔借据逾期</Text>
              ) : null
            }
          </View>
          <View className={styles.repayment_detail_number}>{`共${repaymentNumber}笔 >`}</View>
        </View>
      ) : null}
    </View>
  );
};

export default RepaymentCard;
