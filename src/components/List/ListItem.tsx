import React, {FC, PropsWithChildren} from 'react';
import {Image, Text, View, ViewProps} from '@tarojs/components';
import arrowRight from '../../assets/images/icon/<EMAIL>';
import iconTrue from '../../assets/images/icon/<EMAIL>';
import styles from './index.module.scss';

interface AtListItemProps extends ViewProps {
  // 自定义样式
  className?: string;
  // 标题
  title?: string;
  // 备注
  note?: string;
  // 右侧文字
  extraText?: string;
  // 图表(接收图片路径)
  thumb?: string;
  // 是否有下边框
  hasBorder?: boolean;
  // 右侧图标是否成功
  isSuccess?: boolean;
  // 右侧图标
  rightIcon?: string;
  // 右侧成功图标
  rightSuccessIcon?: string;
  // 图标样式-自定义
  thumbClassName?: string;
}

/**
 * 使用示例
 *  <ListItem
      // 自定义样式类名
      className="custom-list-item"
      // 标题
      title="列表项标题"
      // 备注
      note="这是一个备注信息"
      // 右侧文字
      extraText="右侧文字"
      // 图表（接收图片路径）
      thumb="https://example.com/thumb.png"
      // 是否有下边框
      hasBorder={true}
      // 右侧图标是否成功
      isSuccess={true}
      // 右侧图标（默认为箭头图标）
      rightIcon="https://example.com/right-icon.png"
      // 右侧成功图标（默认为成功图标）
      rightSuccessIcon="https://example.com/success-icon.png"
    />
 */

const CustomListItem: FC<PropsWithChildren & AtListItemProps> = ({
  className,
  title,
  note,
  extraText,
  thumb,
  hasBorder = true,
  isSuccess = false,
  rightIcon = arrowRight,
  rightSuccessIcon = iconTrue,
  thumbClassName,
  ...otherProps
}) => {
  return (
    <View className={`${styles.cus_list_item} ${className} ${hasBorder ? styles.has_border : ''}`} {...otherProps}>
      <View className={styles.cus_list_item_left}>
        {thumb && <Image src={thumb} className={`${styles.cus_list_item_thumb} ${thumbClassName}`} />}
        <View className={styles.cus_list_item_info}>
          {title && <View className={`${styles.cus_list_item_title} ${note ? styles.cus_mb : ''}`}>{title}</View>}
          {note && <View className={styles.cus_list_item_note}>{note}</View>}
        </View>
      </View>
      <View className={styles.cus_list_item_right}>
        {extraText && (
          <Text className={`${styles.cus_list_item_right_text} ${isSuccess ? styles.cus_list_item_sucess : ''}`}>
            {extraText}
          </Text>
        )}
        <Image src={isSuccess ? rightSuccessIcon : rightIcon} className={styles.cus_list_item_right_img} />
      </View>
    </View>
  );
};

export default CustomListItem;
