import { lendingPreProgress } from "~services/lending";
import { router, showToast, showLoading, hideLoading } from "./common";
import Taro from "@tarojs/taro";

const Code2Url: Record<string, string> = {
  BIND_CARD: '/modules/bankcard/bankcard-binding/index',
  TRADE_PASSWORD: '/modules/user/set-password-verifycode/index',
  PERSONAL_INFO: '/modules/user/baseinfo/index',
  CONTACT: '/modules/user/contactinfo/index',
  LENDING: '/modules/lending/lending/index',
  REFILL_ADDRESS: '/modules/user/baseinfo/index',
  UPLOAD_CERT_PIC: '/modules/user/upload-idcard/index',
  CERTID_INFO: '/modules/user/upload-idcard/index',
}

/**
 * 借款前检查，跳转下一步的url
 */
const goLendingPreProgress = async (type: 'replace' | 'push' = 'replace', progressCode?: string) => {

  // 根据状态码跳转
  const goWithProgressCode = async (progressCode: string) => {
    if (progressCode) {
      if (Code2Url[progressCode]) {
        if (progressCode !== 'LENDING') {
          router[type]({
            url: Code2Url[progressCode] + '?from=lending',
          });
        } else {
          router.push({
            url: Code2Url[progressCode],
          });
        }
      } else {
        showToast('缺少部分资料，请先至“城一代App”或微信公众号补充')
      }
    } else {
      router[type]({
        url: '/modules/lending/lending/index',
      });
    }
  }

  try {
    if(progressCode) {
      goWithProgressCode(progressCode)
      return
    }
    showLoading()
    const res = await lendingPreProgress();
    goWithProgressCode(res.progressCode)
  } catch (error) {
    showToast('获取借款前检查失败');
  } finally {
    hideLoading();
  }
};

export default goLendingPreProgress;