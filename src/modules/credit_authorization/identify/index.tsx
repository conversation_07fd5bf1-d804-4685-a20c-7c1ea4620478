import {ComponentType, useEffect, useRef, useState} from 'react';
import {CyButton, CyModal, CySafeArea} from 'hncy58-taro-components';
import {Canvas, Image, Input, InputProps, Text, View} from '@tarojs/components';
import Taro, {createSelectorQuery, getFileSystemManager, useDidShow} from '@tarojs/taro';
import Card from '~components/Card';
import CyContractBar from '~components/CyContractBar';
import PageWrap from '~components/PageWrap';
import Steps, {Step} from '~components/Steps';
import useUserStore from '~store/user';
import {convertToTenThousand, formatDateYYYYMMDD, hideLoading, removeObjEmptyString, router, showLoading, showToast} from '~utils/common';
import {MaxCreditAmount} from '~utils/constant.ts';
import {validateByType} from '~utils/validate';
import styles from './index.module.scss';
import { discernIdcard, saveIdcardInfo, validateIDcard } from '~services/picture';
import SfzfImg from '~images/identify/<EMAIL>'
import SfzzImg from '~images/identify/<EMAIL>'
import photoIcon from '~icons/<EMAIL>'
import TipsIcon from '~images/identify/tips.png'
import idcardIcon from '~icons/icon-idcard.png'
import useFace from '~hooks/useFace';
import { getCustomerInfo, getIsNeedCompanyUpdate, register, updateComanyInfo } from '~services/user';
import { getLocation } from '~utils/location';
import { applyCredit } from '~services/credit';
import useMainStore from '~store/main';
import AntiFraudModal from './components/AntiFraudModal';
import iconScan from '~icons/icon-scan.png'
import iconArrowRight from '~icons/<EMAIL>'
import { uploadDocument } from '~services/document';
import useCompanyInfoStore from '~store/companyInfo';


interface IDCardInfo {
  certId: string;
  name: string;
}
interface IDCardScanInfo extends IDCardInfo {
  validity: string;
}

const contracts = [
  // {name: '《用户注册协议》', type: 'register'},
  {code: 'E08'},
  {code: 'E09'},
  {code: 'E43'},
  {code: 'E51'},
  {code: 'E69'},
];


const Identify = () => {
  /** 1. 变量定义 */
  const steps: Step[] = [
    {title: '身份核验', status: 'process'},
    {title: '完善信息', status: 'process'},
    {title: '申请额度', status: 'process'},
  ];
  

  /** 2. usestates + storestates */
  const [idcardApply, setIdCardApply] = useState(false);
  const [reconizeInfo, setReconizeInfo] = useState<API.IDiscernIdcardRes>({certId: '', name: '', validity: ''});
  const {user, setUser} = useUserStore();
  const identifyInfoRef = useRef<IDCardInfo>({certId: '', name: ''});
  const [agreementChecked, setAgreementChecked] = useState(false);
  const frontBase64 = useRef<string>();
  const endBase64 = useRef<string>();
  const validityRef = useRef<InputProps>();
  const nameRef = useRef<InputProps>();
  const certIdRef = useRef<InputProps>();
  const {companyInfo} = useCompanyInfoStore()

  const position = useRef<API.IPosition>({
    latitude: '',
    longitude: '',
  })
  const idcardFrontImg = useRef<string>();
  const idcardEndImg = useRef<string>();
  const [isNeedCompanyUpdate, setIsNeedCompanyUpdate] = useState<undefined | boolean>();
  const mainStore = useMainStore();
  const {startFace} = useFace();

  /** 3. effects */
  useEffect(() => {
    // TODO: 产品编码
    getIsNeedCompanyUpdateConfig()
  }, []);

  useDidShow(() => {
    if(companyInfo.monthInCome && useCompanyInfoStore.getState().isCompanyInfoUpdate) {
      useCompanyInfoStore.getState().setCompanyInfoUpdate(false);
      startGetLocation()
    }
  });

  /** 4. 方法定义 */
  const getIsNeedCompanyUpdateConfig = async () => {
    const isNeedCompanyUpdate = await getIsNeedCompanyUpdate({prodCode: 'P1109'})
    setIsNeedCompanyUpdate(isNeedCompanyUpdate);
  }

  /** 拍照 */
  const takePhoto = (type: API.DiscernIdcardIndex) => {
    showLoading();
    Taro.chooseImage({
      success(res) {
        const tempFilePaths = res.tempFilePaths;
        console.log('tempFilePaths====', tempFilePaths);
        getFileSystemManager().readFile({
          filePath: tempFilePaths[0],
          encoding: 'base64',
          success: async (res) => {
            try {
              const discernRes = await discernIdcard({
                base64Picture: res.data as string,
                idCardIndex: type,
                photoEventType: user.certId ? 'idcardUpdate' : 'register',
              })
              hideLoading();
              if(discernRes) {
                if(type === '1') {
                  idcardFrontImg.current = tempFilePaths[0];
                  frontBase64.current = res.data as string;
                } else {
                  idcardEndImg.current = tempFilePaths[0];
                  endBase64.current = res.data as string;
                }
                const replaceInfo = removeObjEmptyString(discernRes);
  
                replaceInfo.validity && validityRef.current && (validityRef.current.value = replaceInfo.validity);
                replaceInfo.name && nameRef.current && (nameRef.current.value = replaceInfo.name);
                replaceInfo.certId && certIdRef.current && (certIdRef.current.value = replaceInfo.certId);
  
                setReconizeInfo({
                  ...reconizeInfo,
                  ...replaceInfo,
                });
              }
            } catch (e) {
              console.log('takePhoto error', e)
              hideLoading()
            }
          },
        });
      },
      fail() {
        hideLoading()
      }
    });
  };

  const validate = async () => {
    if (!agreementChecked) {
      showToast('请阅读并勾选相关协议');
      return false;
    }
    const isIdCardFn = validateByType('idCard');
    const isNameFn = validateByType('name');
    if(idcardApply) {
      if (!isIdCardFn(reconizeInfo.certId) ||!isNameFn(reconizeInfo.name) || !reconizeInfo.validity) {
        showToast('请确认上传身份证图片是否正确');
        return false;
      }
      const validateIDcardRes = await validateIDcard({
        certId: reconizeInfo.certId,
        custName: reconizeInfo.name,
        validity: reconizeInfo.validity,
      }) 
      if(!validateIDcardRes) {
        showToast('身份证信息校验失败');
        return false
      }
    } else {
      // 已经注册过了跳过注册逻辑
      if (user.certId) {
        return true
      }
      if (!isIdCardFn(identifyInfoRef.current.certId) || !isNameFn(identifyInfoRef.current.name)) {
        showToast('请输入正确的姓名和身份证号');
        return false;
      }
    }
    return true;
  };

  const uploadIdcard = async () => {
    if(!frontBase64.current || !endBase64.current) {
      showToast('请先上传身份证图片');
      return;
    }
    await uploadDocument({
      files: [frontBase64.current, endBase64.current],
      eventCode: !user.certId ? 'USER_REGISTER' : 'SUPPLY_IDENTITY_MATERIAL'
    })
  }

  const registerAndUploadIDcard = async () => {
    showLoading({title: '注册中...'});
    const params: API.RegisterReqParams = {
      certId: idcardApply ? reconizeInfo.certId || '' : identifyInfoRef.current.certId || '',
      name: idcardApply ? reconizeInfo.name || '' : identifyInfoRef.current.name || '',
      mobile: user.mobile,
    }
    if(companyInfo.monthInCome) {
      params.companyInfo = companyInfo;
    }
    // 如果需要更新公司信息，但是公司信息没有填写，则跳转到公司信息页面
    if(isNeedCompanyUpdate && !companyInfo.monthInCome) {
      hideLoading();
      router.push({
        url: `/modules/credit_authorization/company-info/index`,
      });
      return;
    }
    // 有身份证进件, 先注册 + 上传身份证信息
    if(idcardApply) {
      if(!user.registerFlag) {
        await register(params);
      }
        await uploadIdcard();
        const [certValidPeriodStart, certValidPeriodEnd] = reconizeInfo.validity?.split('-') || [];
        await saveIdcardInfo({
          certAddress: reconizeInfo.address,
          sex: reconizeInfo.sex === '男' ? 1 : 0,
          birthDay: formatDateYYYYMMDD(reconizeInfo.birthday),
          certValidPeriodStart: formatDateYYYYMMDD(certValidPeriodStart),
          certValidPeriodEnd: formatDateYYYYMMDD(certValidPeriodEnd),
          name: reconizeInfo.name,
          issueAuthority: reconizeInfo.issueAuthority,
          certId: reconizeInfo.certId,
        })
        // 更新用户信息
        const infoRes = await getCustomerInfo();
        setUser({
          ...infoRes,
        });
    } else {
      // 无身份证进件, 直接注册
      if(!user.registerFlag) {
        await register(params);
        // 更新用户信息
        useUserStore.getState().updateUserBaseInfo();
      }
    }
    Taro.hideLoading();
  }

  const startApply = async () => {
    if(!position.current.latitude || !position.current.longitude) {
      showToast('请先获取位置信息');
      return;
    }
    Taro.showLoading({title: '提交申请中...'});
    try {
      // 注册 + 身份证信息更新
      await registerAndUploadIDcard();
      // 授信
      await applyCredit({
        longitude: position.current.longitude + '',
        latitude: position.current.latitude + '',
      });
      Taro.hideLoading();
      router.replace({
        url: '/modules/credit_authorization/credit-wait/index',
      });
    } catch(e) {
      Taro.hideLoading();
    }
  };

  const startGetLocation = async () => {
    getLocation({
      onSuccess: async data => {
        if (data.longitude && data.latitude) {
          position.current.latitude = data.latitude + '';
          position.current.longitude = data.longitude + '';
          startFace({
            requestFrom: 'certIdAuth',
            certId: idcardApply ? reconizeInfo.certId : identifyInfoRef.current.certId,
            name: idcardApply ? reconizeInfo.name : identifyInfoRef.current.name,
            faceSuccess: () => {
              startApply();
            },
            faceFail: () => {
              showToast('活体识别失败');
            },
          });
        }
      },
      onFail: e => {
        console.log('获取地理位置失败', e);
      },
      usage: '用于额度评估'
    });
  }
  const startSubmit = async () => {
    const validateRes = await validate();
    if(!validateRes) {
      return;
    }
    // 需要更新公司信息，先去更新
    if(isNeedCompanyUpdate && !companyInfo.monthInCome) {
      router.push({
        url: `/modules/credit_authorization/company-info/index`,
      });
      return;
    }
    startGetLocation();
  };

  const showPhotoTips = () => {
    CyModal.create({
      title: '温馨提示',
      content: (
        <View className={styles['modal-content']}>
          <View>1.选址光线充足的地方</View>
          <View>2.拍照时不要开闪光灯并准确对焦</View>
          <View>3.确保身份证边缘拍摄完整</View>
          <View>4.拍完后别忘了核实拍照信息哦～</View>
        </View>
      ),
    })
  }


  const changeReconizeInfo = (key: keyof IDCardScanInfo) => {
    setReconizeInfo({
      ...reconizeInfo,
      [key]: reconizeInfo[key],
    })
  }

  /** 5. ui */
  return (
    <PageWrap className={styles['page-identify']}>
      <View className={styles['page-top']}>
        <Steps steps={steps} current={1} />
      </View>
      <View className={styles['page-content']}>
        {!idcardApply ? (
          <>
            <Card
              className={styles['input-identify-card']}
              title={
                <View>
                  <Text>身份核验中...</Text>
                </View>
              }
              note={
                <Text>
                  输入身份信息，
                  <Text className={styles.mark}>最高申请{convertToTenThousand(`${MaxCreditAmount}`)}万额度</Text>
                </Text>
              }>
              <View className={styles.scanBar}  onClick={() => setIdCardApply(true)}>
                <View className={styles.scanBarLeft}>
                  <Image src={iconScan} className={styles.iconScan} />
                  <Text className={styles.mark}>扫描身份证</Text>
                  <Text className={styles.scanBarPlaceholderText}>自动识别免输入</Text>
                </View>
                <Image src={iconArrowRight} className={styles.iconArrowRight} />
              </View>
            </Card>
            <View className={styles.sectionTips}>身份证不在身边？可手动输入身份信息</View>
            <View className={styles.inputSection}>
                {/* <View className='flex-center'><Image className={styles.idcardIcon} src={idcardIcon} /><Text className={styles.idcardInputDesc}>输入身份信息</Text></View> */}
                  <View className={styles.inputIdcardRow}>
                    <Text className={styles.inputIdcardLabel}>姓名</Text>
                    <Input
                      placeholderStyle='color: #bbb'
                      onInput={e => {
                        if (e.detail.value) {
                          identifyInfoRef.current.name = e.detail.value;
                        }
                      }}
                      className={styles['identity-input']}
                      placeholder='请输入真实姓名'
                      defaultValue={user.name}
                      disabled={!!user.certId}
                    />
                  </View>
                  <View className={styles.inputIdcardRow}>
                    <Text className={styles.inputIdcardLabel}>身份证</Text>
                    <Input
                      placeholderStyle='color: #bbb'
                      onInput={e => {
                        if (e.detail.value) {
                          identifyInfoRef.current.certId = e.detail.value;
                        }
                      }}
                      maxlength={18}
                      type='idcard'
                      className={styles['identity-input']}
                      placeholder='请输入身份证号'
                      defaultValue={user.certId}
                      disabled={!!user.certId}
                    />
                  </View>
            </View>
          </>
        ) : (
          <>
            <Card
              title='身份信息认证中...'
              note={
                <View className={styles['top-tips']}>
                  <Text>
                    拍摄身份证信息，
                    <Text className={styles.mark}>最高申请{convertToTenThousand(`${MaxCreditAmount}`)}万额度</Text>
                  </Text>
                  <View className={styles['card-tips-right']} onClick={showPhotoTips}>
                    <Image src={TipsIcon} className={styles['tips-icon']}></Image>
                    <Text>查看拍照要求</Text>
                  </View>
                </View>
              }>
              <View className={styles['card-content']}>
                <View className='vertical-gap'></View>
                <View>
                  <View className={styles['idcard-pic-title']}>拍摄身份证人像面</View>
                  <View className={styles['idcard-pic-content']} onClick={() => takePhoto('1')}>
                    {
                      !idcardEndImg.current ? (
                        <Image src={photoIcon} className={styles['photo-icon']}></Image>
                      ) : null
                    }
                    <Image src={idcardFrontImg.current ? idcardFrontImg.current : SfzzImg}
                      className={styles['identify-idcard-img']}></Image>
                  </View>
                </View>
                <View className='vertical-gap'></View>
                <View>
                  <View className={styles['idcard-pic-title']}>拍摄身份证国徽面</View>
                  <View className={styles['idcard-pic-content']} onClick={() => takePhoto('2')} >
                    {
                      !idcardEndImg.current ? (
                        <Image src={photoIcon} className={styles['photo-icon']}></Image>
                      ) : null
                    }
                    <Image src={idcardEndImg.current ? idcardEndImg.current : SfzfImg} className={styles['identify-idcard-img']}></Image>
                  </View>
                </View>
                <View className='vertical-gap'></View>
              </View>
            </Card>
            <View className='vertical-gap'></View>
            {
              (reconizeInfo.certId || reconizeInfo.validity) ? 
              <Card title='请确认您的认证信息'>
                <View className={styles['row']}>
                  <View className={styles['label']}>姓名<Text></Text></View>
                  {/* <View className={styles['value']}>: {reconizeInfo.name}</View> */}
                  <Text className={styles.labelValueGap}>:</Text>
                  <Input ref={nameRef} className={styles['value']} onInput={() => changeReconizeInfo('name')} defaultValue={reconizeInfo.name}></Input>
                </View>
                <View className={styles['row']}>
                  <View className={styles['label']}>身份证号<Text></Text></View>
                  {/* <View className={styles['value']}>: {reconizeInfo.certId}</View> */}
                  <Text className={styles.labelValueGap}>:</Text>
                  <Input ref={certIdRef} type='idcard' className={styles['value']} onInput={() => changeReconizeInfo('certId')} defaultValue={reconizeInfo.certId}></Input>
                </View>
                {
                  reconizeInfo.validity ? (
                    <View className={styles['row']}>
                      <View className={styles['label']}>有效期<Text></Text></View>
                      {/* <View className={styles['value']}>: {reconizeInfo.validity}</View> */}
                      <Text className={styles.labelValueGap}>:</Text>
                      <Input ref={validityRef} className={styles['value']} onInput={() => changeReconizeInfo('validity')} defaultValue={reconizeInfo.validity}></Input>
                    </View>
                  ) : null
                }
              </Card> : null
            }
            <View className={styles.tips}>
              身份证不在身边？<Text className={styles['mark']} onClick={() => setIdCardApply(false)}>输入身份证信息</Text>
            </View>
          </>
        )}
        <View className='vertical-gap'></View>
        <CyContractBar
            justify='center'
            onChange={val => {
              setAgreementChecked(val);
            }}
            mustView
            checked={agreementChecked}
            contracts={contracts}
          />
        <View className={styles['page-footer']}>
          <CyButton block type='primary' onClick={startSubmit} round className={styles['bottom-btn']}>
            {isNeedCompanyUpdate === true ? '下一步' : '提交'}
          </CyButton>
        </View>
      </View>
      <AntiFraudModal />
    </PageWrap>
  );
};

export default Identify;
