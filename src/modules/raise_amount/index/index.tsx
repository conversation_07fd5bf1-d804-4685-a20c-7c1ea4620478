import {View, Text, Image} from '@tarojs/components';
import styles from './index.module.scss';
import PageWrap from '~components/PageWrap';
import touristIcon from '~images/raiseamount/<EMAIL>'
import intelligentIcon from '~images/raiseamount/<EMAIL>'
import arrowIcon from '~images/raiseamount/<EMAIL>'
import recordIcon from '~images/raiseamount/<EMAIL>'
import { router } from '~utils/common';
import { useLoad } from '@tarojs/taro';
import { CyModal } from 'hncy58-taro-components';



const RaiseAmount = () => {
  /** 1. 变量定义 */

  /** 2. usestates + storestates */
  useLoad(({ state }) => {
    if (state) {
      if (state === 'unprocessed'){
        CyModal.create({
          title: '温馨提示',
          content: '系统正在评估中, 请您耐心等待! 若提额成功, 我们会发送短信给您',
          confirmText: '我知道了',
          isShowCancel: false,
          maskClosable: false,
        })
      }
      if (state === 'unactived') {
        CyModal.create({
          title: '温馨提示',
          content: '您还未完成首次借款, 完成后再试试哦',
          confirmText: '去借款',
          cancelText: '返回',
          maskClosable: false,
          onConfirm: () => {
            router.reLaunch({
              url: '/pages/check/index'
            })
          },
        })
      }
    }
  });

  /** 3. effects */

  /** 4. 方法定义 */
  // 跳转到智能信用认证页面
  const goIntelligentPage = () => {
    router.push({
      url: '/modules/raise_amount/intelligent-entry/index'
    })
  };

  const goRecordList = () => {
    router.push({
      url: '/modules/raise_amount/history/index'
    })
  }

  /** 5. ui */
  return (
    <PageWrap className={styles['page-raise-amount']}>
      <View className={styles.cardContainer}>
        {/* 导游证认证卡片 */}
        {/* <View className={styles.card}>
          <View className={styles.cardContent}>
            <View className={styles.cardMain}>
              <View className={styles.titleRow}>
                <Image className={styles.icon} src={touristIcon} />
                <Text className={styles.title}>导游证认证</Text>
              </View>
              <View className={styles.subtitleRow}>
                <Text className={styles.subtitle}>导游专属，上传导游证可获得专属额度</Text>
              </View>
            </View>
            <Image src={arrowIcon} className={styles.arrow}></Image>
          </View>
        </View> */}
        
        {/* 智能信用认证卡片 */}
        <View className={styles.card} onClick={goIntelligentPage}>
          <View className={styles.cardContent}>
            <View className={styles.cardMain}>
              <View className={styles.titleRow}>
                <Image className={styles.icon} src={intelligentIcon} />
                <Text className={styles.title}>智能信用认证</Text>
              </View>
              <View className={styles.subtitleRow}>
                <Text className={styles.subtitle}>智能评估，依据综信用情况、综合资质等因素进行评估</Text>
              </View>
            </View>
            <Image src={arrowIcon} className={styles.arrow}></Image>
          </View>
        </View>
        
        {/* 提额记录按钮 */}
        <View className={styles.recordButton} onClick={goRecordList}>
          <Image src={recordIcon} className={styles.recordIcon}></Image>
          <Text className={styles.recordText}>提额记录</Text>
        </View>

        {/* 温馨提示 */}
        <View className={styles.tipContainer}>
          <View className={styles.tipTitle}>温馨提示:</View>
          <View className={styles.tipContext}>
            如果您已经提交资料或者认证成功后没有提额，说明此项资料暂未达到更高额度的要求。建议您保持良好的信用记录，按时还款，我们会不定期根据您的信用情况重新评估您的额度。
          </View>
        </View>
      </View>
    </PageWrap>
  );
};

export default RaiseAmount;
