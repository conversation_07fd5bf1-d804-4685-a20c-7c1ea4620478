.repaymentAmount {
  background-color: #fff;
}

.repaymentAmountMain {
  padding: 64px 0 34px 64px;
}

.repaymentAmountTitle {
  color: $color-text-normal;
  font-size: $font-size-normal;
  line-height: 1;
}

.repaymentAmountContainer {
  display: flex;
  align-items: flex-end;
  padding: 40px 0;
  .repaymentInput {
    font-size: 80px;
    font-weight: bold;
    line-height: 1;
    .money_input {
      max-width: 440px;
      min-height: 80px;
      color: #333;
      font-weight: bold;
      ::-webkit-input-placeholder {
        color: #999;
        font-size: 40px;
      }
    }
  }
  .editIcon {
    width: 24px;
    height: 24px;
    margin-left: 28px;
    border: 1px solid $color-primary;
  }
}

.repaymentAmountNote {
  color: $color-text-secondary;
  font-size: $font-size-sm;
}