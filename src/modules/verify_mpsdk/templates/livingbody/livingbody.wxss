#livingbody-guide-title { width: 100%; margin: 50rpx 0 60rpx; text-align: center; font-size: 35rpx; } .livingbody-guide-imgview { width: 100%; margin-bottom: 50rpx; text-align: center; } .livingbody-guide-imgview image { width: 398rpx; height: 560rpx; } #livingbody-guide-btnview { width: 100%; margin: 0 auto; } #livingbody-guide-btnview button { margin: 30rpx 30rpx 10rpx 30rpx; } #livingbody-guide-showdialog { width: 100%; margin-top: 26rpx; text-align: center; font-size: 14px; color: #007fff; } #livingbody-dialog-main { max-width: 1000rpx; background: none; } #livingbody-dialog-title { margin-bottom: 15px; padding: 15rpx 0; color: #fff; } #livingbody-dialog-bg { display: block; padding: 0 auto; } #livingbody-dialog-bg image { width: 561rpx; height: 753rpx; } #livingbody-dialog-bg button { width: 555rpx; margin: 25px auto; font-size: 18px; border-color: #fff; color: #fff; } /* * pu gai, cover-view exec width animation just run in devtools is ok, the real machine debugging is invaild * read number show every number process function already pu gai, wait wechat support this in the future */ /* #test { animation: upAndDown 4s linear infinite; } @keyframes upAndDown { 0% {width:0;} 100% {width:180px;} } */ .lvingbody-number-text { display: inline-block; width: 120rpx; line-height: 1em; color: white; } .livingbody-number-hint { position: absolute; bottom: 550rpx; display: inline-block; width: 100%; line-height: 1em; text-align: center; font-size: 120rpx; color: #2574ea; } .isInfinityDisplayHTNumberHint { bottom: 750rpx; } .livingbody-action-hint { position: absolute; bottom: 580rpx; display: inline-block; height: 92rpx; width: 100%; line-height: 1em; text-align: center; font-size: 92rpx; color: #2574ea; } .isInfinityDisplayHTActionHint { bottom: 780rpx; } .livingbody-number-pre { position: absolute; bottom: 535rpx; width: 100%; font-size: 92rpx; text-align: center; color: #2574ea; } .isInfinityDisplayHTNumberPre { bottom: 735rpx; } .livingbody-action-pre { position: absolute; bottom: 550rpx; width: 100%; font-size: 92rpx; text-align: center; color: #2574ea; } .isInfinityDisplayHTActionPre { bottom: 750rpx; } .livingbody-bg { position: absolute; bottom: 0; left: 0; width: 100%; height: 960rpx; } .isInfinityDisplayHTBottom { bottom: 200rpx; } .livingbody-title { position: absolute; top: 0; bottom: 955rpx; left: 0; width: 100%; display: flex; flex-direction: column; align-items: center; justify-content: center; font-size: 52rpx; color: #2574ea; background: #fff; } .isInfinityDisplayHTTitle { bottom: 1155rpx; } #livingbody-camera { position: fixed; top: 0; bottom: 0; width: 100%; } .livingbody-silent { position: absolute; bottom: 40rpx; width: 100%; text-align: center; } .livingbody-silent-view { display: flex; align-items: center; width: 122px; height: 36px; margin: 0 auto; font-size: 22px; border-radius: 50px; color: white; background: rgba(0, 0, 0, 0.5); } .livingbody-silent-hintone { display: flex; align-items: center; margin: 0 2px 0 22px; letter-spacing: 1px; font-size: 19px; } .livingbody-silent-hinttwo { display: flex; align-items: center; margin-left: 2px; } .livingbody-silent-prepare { display: flex; align-items: center; margin-left: 16px; } .verifyCurrentNumber { color: #2574ea; } #livingbody-process .scan { position: relative; width: 210rpx; height: 250rpx; margin: 160rpx auto 150rpx; } #livingbody-process .scan image { width: 210rpx; height: 250rpx; } #livingbody-process-m { width: 100%; } #livingbody-process-m progress { margin: 0 auto; width: 450rpx; border: 1px solid #fff; border-radius: 10px; } #livingbody-process-title { width: 100%; margin-top: 40rpx; text-align: center; font-size: 34rpx; } .livingbody-risk-tip { margin: 20rpx 30rpx; }