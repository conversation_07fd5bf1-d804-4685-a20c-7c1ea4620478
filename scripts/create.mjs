import {Command} from 'commander';
import fs from 'fs-extra';
import {glob} from 'glob';
import handlebars from 'handlebars';
import inquirer from 'inquirer';
import ora from 'ora';
import path from 'path';
import shell from 'shelljs';

const Regexps = {
  hyphen: /^[a-z][a-z|\-]+[a-z]$/,
  pascalCase: /^[A-Z][a-zA-Z0-9]*$/,
  cn: /(?:[\u3400-\u4DB5\u4E00-\u9FEA\uFA0E\uFA0F\uFA11\uFA13\uFA14\uFA1F\uFA21\uFA23\uFA24\uFA27-\uFA29]|[\uD840-\uD868\uD86A-\uD86C\uD86F-\uD872\uD874-\uD879][\uDC00-\uDFFF]|\uD869[\uDC00-\uDED6\uDF00-\uDFFF]|\uD86D[\uDC00-\uDF34\uDF40-\uDFFF]|\uD86E[\uDC00-\uDC1D\uDC20-\uDFFF]|\uD873[\uDC00-\uDEA1\uDEB0-\uDFFF]|\uD87A[\uDC00-\uDFE0])+/,
};

const commandProgram = new Command();

const TypePrompts = [
  {
    type: 'list',
    message: '新建页面/全局组件',
    default: '',
    name: 'type',
    default: 'page',
    choices: [
      {
        name: '页面',
        value: 'page',
      },
      {
        name: '全局组件',
        value: 'component',
      },
    ],
  },
];

const PagePrompts = [
  {
    name: 'fileName',
    message: '请输入新建页面的文件名,格式为aa-bb',
    default: '',
    validate(input) {
      if (Regexps.hyphen.test(input)) {
        return true;
      } else {
        return '请输入如aa-bb|aa形式的名称，最少3个字符！';
      }
    },
  },
  {
    name: 'name',
    message: '请输入页面名称，用于navigationBar，如：首页',
    default: '',
    validate(input) {
      if (Regexps.cn.test(input)) {
        return true;
      } else {
        return '中文名称，请最少包含一个中文！';
      }
    },
  },
];

const ComponentPrompts = [
  {
    name: 'fileName',
    message: '请输入全局组件文件名建议格式AaBb',
    default: '',
    validate(input) {
      if (Regexps.pascalCase.test(input)) {
        return true;
      } else {
        return '请输入如aa-bb|aa形式的名称，最少3个字符！';
      }
    },
  },
  {
    name: 'name',
    message: '请输入组件名称',
    default: '',
    validate(input) {
      if (Regexps.cn.test(input)) {
        return true;
      } else {
        return '中文名称，请最少包含一个中文！';
      }
    },
  },
  {
    name: 'desc',
    message: '请输入组件功能描述',
    default: '',
    validate(input) {
      if (Regexps.cn.test(input)) {
        return true;
      } else {
        return '中文描述，请最少包含一个中文！';
      }
    },
  },
];

/** 中划线转大驼峰  aa-bb => AaBb */
const hyphenToPascalCase = str => {
  return str.replace(/(^[a-z]|-[a-z])/g, function (match, group1) {
    return group1.replace('-', '').toUpperCase();
  });
};
/** 大驼峰转中划线 AaBb => aa-bb */
const pascalCaseToHyphen = str => {
  return str.replace(/([a-z])([A-Z])/g, '$1-$2').toLowerCase();
};

/**添加页面 */
const addPage = async param => {
  // 1. 添加page config
  if (fs.existsSync('./src/app.config.ts')) {
    shell.sed(
      '-i',
      '// #PAGE',
      `// ${param.name}\n    'pages/${param.fileName}/${param.fileName}',\n    // #PAGE`,
      './src/app.config.ts',
    );
  }
  // 2. 生成page files
  const pageFiles = await glob('scripts/templates/page/*');
  pageFiles.forEach(file => {
    const fileNametWithExt = path.basename(file).replace('index', param.fileName);
    const loading = ora(`生成${param.fileName}/${fileNametWithExt}`);
    try {
      if (fs.existsSync(file)) {
        const content = fs.readFileSync(file).toString();
        const template = handlebars.compile(content);
        const result = template({
          ...param,
          camelCaseFileName: hyphenToPascalCase(param.fileName),
        });
        fs.outputFileSync(`src/pages/${param.fileName}/${fileNametWithExt}`, result);
        loading.stop();
        loading.succeed(`生成页面${param.fileName}/${fileNametWithExt} 成功`);
      }
    } catch (e) {
      loading.stop();
      loading.fail(`生成页面${param.fileName}/${fileNametWithExt} 失败`);
    }
  });
};

const addComponent = async param => {
  const componentFiles = await glob('scripts/templates/component/*');
  componentFiles.forEach(file => {
    const fileName = path.basename(file);
    const loading = ora(`生成${param.fileName}/${fileName}`);
    try {
      if (fs.existsSync(file)) {
        const content = fs.readFileSync(file).toString();
        const template = handlebars.compile(content);
        const result = template({
          ...param,
          hyphenFileName: pascalCaseToHyphen(param.fileName),
        });
        fs.outputFileSync(`src/components/${param.fileName}/${fileName}`, result);
        loading.stop();
        loading.succeed(`生成组件${param.fileName}/${fileName} 成功`);
      }
    } catch (e) {
      loading.stop();
      loading.fail(`生成组件${param.fileName}/${fileName} 失败`);
    }
  });
};

commandProgram.description('添加页面或全局组件').action(async () => {
  console.log('start add a page:');
  const typeOptions = await inquirer.prompt(TypePrompts);
  if (typeOptions.type === 'page') {
    const addPageOptions = await inquirer.prompt(PagePrompts);
    addPage(addPageOptions);
  } else {
    const addComponentOptions = await inquirer.prompt(ComponentPrompts);
    addComponent(addComponentOptions);
  }
});

commandProgram.parse();
