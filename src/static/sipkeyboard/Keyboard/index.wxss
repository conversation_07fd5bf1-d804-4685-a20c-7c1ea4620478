/* sipkeyboard/Keyboard/index.wxss */
canvas {
  left: 0px;
  top: 0px;
}
.bubble {
  position: fixed;
  background: white;
  border: 1px solid #AAAAAA;
  box-shadow: 0 5px 10px 0 rgba(0,0,0,0.20);
  border-radius: 15px;
  padding-top: 5%;
  padding-bottom: 5%;
  z-index: 20000;
  font-size: 36px;
  color: black;
  text-align: center;
}
.bubble-dark {
  position: fixed;
  background: #3A3A3A;
  border: 1px solid #666666;
  box-shadow: 0 5px 10px 0 rgba(0,0,0,0.20);
  border-radius: 15px;
  padding-top: 5%;
  padding-bottom: 5%;
  z-index: 20000;
  font-size: 36px;
  color: #FFFFFF;
  text-align: center;
}
.cfca-keyboard{
  position: fixed;
  width: 100%;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 99999999;
  display: block;
  animation: keyboardPopupUpAnimation 150ms ease-out;
}

@keyframes keyboardPopupUpAnimation {
  0%   {transform: translateY(100%); opacity: 1}
  20%  {transform: translateY(80%); opacity: 1}
  40%  {transform: translateY(60%); opacity: 1}
  60%  {transform: translateY(40%); opacity: 1}
  80%  {transform: translateY(20%); opacity: 1}
  100% {transform: translateY(0%); opacity: 1}
}

@-webkit-keyframes keyboardPopupUpAnimation {
  0%   {transform: translateY(100%); opacity: 1}
  20%  {transform: translateY(80%); opacity: 1}
  40%  {transform: translateY(60%); opacity: 1}
  60%  {transform: translateY(40%); opacity: 1}
  80%  {transform: translateY(20%); opacity: 1}
  100% {transform: translateY(0%); opacity: 1}
}

@keyframes viewlinear{
  0% {
    color: blue;
  }
  25% {
    color: blue;
  }

  50% {
    color: white;
  }

  100% {
    color: white;
  }
}

.sip-input-normal{
  height: 100%;
}

.sip-input-normal{
  height: 100%;
  font-size: 31rpx;
}

.sip-input-focus{
  font-size: 31rpx;
}

.sip-input-focus::after{
  content: '|';
  font-size: 31rpx;
  animation-name: viewlinear;
  animation-duration: 1s;
  animation-timing-function: ease-in;
  animation-iteration-count: infinite;
}
.sip-topbar {
  position: relative;
  height: 48px;
  line-height: 48px;
  text-align: center;
  display: flex;
  flex-direction: row ;
  justify-content: center;
  align-items: center;
  background: #FFFFFF;
  box-shadow: inset 0 1px 0 0 #E8E8E8;
}

.sip-topbar-dark{
  position: relative;
  height: 48px;
  display: flex;
  flex-direction: row ;
  justify-content: center;
  align-items: center;
  background: #141414;
  box-shadow: inset 0 1px 0 0 #E8E8E8;
}

.sip-topbar-logo {
  width: 230px;
}

.sip-topbar-done {
  position: absolute;
  right: 0;
  top:0;
  bottom: 0;
  line-height: 48px;
  padding-left: 15px;
  padding-right: 15px;
  font-family: PingFangSC-Semibold;
  font-size: 18px;
  color: #1953A2;
  text-align: center;
}

.sip-topbar-done::before {
  content: '|';
  margin-right: 10px;
  opacity: 0.6;
  color: #666666;
}

.sip-topbar-done-dark{
  position: absolute;
  right: 0;
  top:0;
  bottom: 0;
   padding-top: 10px;
  padding-left: 15px;
  padding-right: 15px;
  font-family: PingFangSC-Semibold;
  font-size: 18px;
  color: #FFFFFF;
  text-align: center;
}

.sip-topbar-done-dark::before {
  content: '|';
  margin-right: 10px;
  opacity: 0.6;
  color: #666666;
}

.cfca-input-grid-wrap {
  display: flex;
  width: 100%;
  align-items: center;
  justify-content: center;
}

.cfca-input-grid-dot {
  display: flex;
  align-items: center;
  justify-content: center;
  text-align: center;
  color: #333333;
  box-sizing: border-box;
  width: 40px;
  height: 48px;
  background: #F6F6F6;
  border-radius: 16px;
  margin-right: 16px;
}

.cfca-input-grid-dot:nth-child(1) {
  border-left: none;
}
.cfca-input-grid-dot:last-child {
  margin-right: 0;
}
.sip-input-layout,
.sip-root{
  width: 100%;
  height: 100%;
}
.sip-input-layout {
  display: flex;
  flex-direction: column;
  justify-content: center;
  background: transparent;
}
.text-layout{
  overflow: scroll;
  max-lines: 1;
  width: 100%;
  text-align: center;
  font-size: 40rpx;
}

.sip-input-placeholder {
  font-size: 28rpx;
  color: #AAAAAA;
  height: 100rpx;
}