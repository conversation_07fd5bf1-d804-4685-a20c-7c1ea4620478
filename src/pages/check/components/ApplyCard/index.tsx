import {CyButton} from 'hncy58-taro-components';
import {Image, View} from '@tarojs/components';
import MarqueeTip from '~components/MarqueeTip';
import MoneyDisplay from '~components/MoneyDisplay';
import {router, showToast} from '~utils/common';
import {InterestPerday, InterestRateRange, MaxCreditAmount} from '~utils/constant';
import styles from './index.module.scss';

interface Props {
  onApply: () => void;
  isLogin: boolean;
}

export default ({onApply, isLogin}: Props) => {
  const goLogin = () => {
    router.replace({
      url: '/modules/user/login/index',
    });
  };
  return (
    <View className={styles.creditCard}>
      <View className={styles.creditCardTopbar}></View>
      <View className={styles.creditCardContent}>
        <View className={styles.creditCardSubtitle}>最高可借(元)</View>
        <MoneyDisplay
          currency={false}
          size='xlarge'
          color='#333'
          className={styles.creditAmount}
          money={MaxCreditAmount}></MoneyDisplay>
        <View className={styles.creditCardDesc}>
          年化利率（单利）{InterestRateRange[0]}%-{InterestRateRange[1]}%(10000元借1天利息{InterestPerday[0]}-
          {InterestPerday[1]}元)
        </View>
        {isLogin ? (
          <CyButton type='primary' block round onClick={onApply}>
            立即申请
          </CyButton>
        ) : (
          <CyButton type='primary' block round onClick={goLogin}>
            登录
          </CyButton>
        )}
      </View>
      <View className={styles.creditCardBottom}>
        <MarqueeTip
          backgroundColor='#fff'
          content='警惕非法金融中介陷阱，请勿轻信“代理维权”“减免债务”等诱惑！'
          duration={10}
        />
      </View>
    </View>
  );
};
