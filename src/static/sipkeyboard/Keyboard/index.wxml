<view class="sip-root">
  <view bindtap="inputClick" class="sip-input-layout" >
    <view wx:if="{{isGrid}}" class="cfca-input-grid-wrap">
      <view class="cfca-input-grid-dot" wx:for="{{maxLength}}" wx:key="this">
        <i>{{text[index]}}</i>
      </view>
    </view>
    <scroll-view wx:else class="text-layout" scroll-x="true" scroll-left="{{scrollLeft}}">
      <view class="{{textClass}}" selectable="false" >{{text}}</view>
    </scroll-view>
  </view>
</view>
<view wx-if="{{init}}" catchtap="moveCatch" catchtouchmove="moveCatch" class="cfca-keyboard" style="background-color:{{backgroundColor}};position:fixed;{{keyboardTop}}">
<view class="sip-topbar-dark" wx-if="{{displayMode}}">
  <image class="sip-topbar-logo" src="../image/dark/logo.png"></image>
  <view class="sip-topbar-done-dark" bindtap="doneClick">完成</view>
</view>
<view class="sip-topbar" wx-if="{{!displayMode}}">
  <!-- <image class="sip-topbar-logo " src="../image/light/logo.png"></image> -->
  <view class="sip-topbar-logo">安全键盘</view>
  <view class="sip-topbar-done" bindtap="doneClick">完成</view>
</view>
<canvas wx:if="{{isPC}}" catchtap="moveCatch"  style="width:{{canvasWidth}}px;height:{{canvasHeight}}px;" canvas-id="{{canvasId}}" disable-scroll="true" id="{{canvasId}}" bindtouchstart="bindtouchstart" bindtouchmove="bindtouchmove" bindtouchend="bindtouchend" bindtouchcancel="bindtouchcancel">
<cover-view wx-if="{{bubbleShowText}}" class="{{bubbleClass}}" style="width:{{bubbleStyle.width}}px;left:{{bubbleStyle.x}}px;bottom:{{bubbleStyle.y}}px;text-align:center" >
{{bubbleShowText}}
</cover-view>
</canvas>
<canvas wx:else catchtap="moveCatch"  style="width:{{canvasWidth}}px;height:{{canvasHeight}}px;"  type="2d" canvas-id="{{canvasId}}" disable-scroll="true" id="{{canvasId}}" bindtouchstart="bindtouchstart" bindtouchmove="bindtouchmove" bindtouchend="bindtouchend" bindtouchcancel="bindtouchcancel">
<cover-view wx-if="{{bubbleShowText}}" class="{{bubbleClass}}" style="width:{{bubbleStyle.width}}px;left:{{bubbleStyle.x}}px;bottom:{{bubbleStyle.y}}px;text-align:center" >
{{bubbleShowText}}
</cover-view>
</canvas>
<view wx:if="{{!isHideBottom}}" catchtap="moveCatch" style="height:{{bottomPadding}}px"></view>
</view>