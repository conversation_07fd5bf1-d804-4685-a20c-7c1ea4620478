{
  "prettier.configPath": "prettier.config.js",
  "prettier.enable": true,
  "editor.formatOnSave": false,
  "stylelint.enable": true,
  "eslint.enable": true,
  "editor.defaultFormatter": "esbenp.prettier-vscode",
  "eslint.validate": ["javascript", "javascriptreact", "typescript", "typescriptreact", "html"],
  "stylelint.configFile": ".stylelintrc",
  "stylelint.validate": ["css", "scss"],
  "editor.codeActionsOnSave": {
    "editor.formatOnSave": "always",
    "source.organizeImports": "never",
    "source.sortImports": "never",
    "source.fixAll": "always",
    "source.fixAll.eslint": "always",
    "source.fixAll.stylelint": "always"
  },
  "cSpell.words": [
    "childs",
    "qtfk",
    "tyhk",
    "xyhj",
    "zchj"
  ],
  "java.compile.nullAnalysis.mode": "automatic",
  "[typescriptreact]": {
    "editor.defaultFormatter": "vscode.typescript-language-features"
  },
  "easysass.compileAfterSave": false  //禁止保存自动生成css
}
