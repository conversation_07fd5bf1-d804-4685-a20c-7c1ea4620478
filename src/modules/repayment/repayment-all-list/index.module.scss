

.periodsContainer {
  
}

.topAmountShortcut {
  position: fixed;
  top: 0;
  width: 100%;
  z-index: 9;
}

.bill {
  padding: 240px 20px 240px 20px;
}

.footerBarText {
  display: flex;
  align-items: center;
  font-size: $font-size-normal;
  color: $color-text-normal;
}
.footerBarSubTextRow2,
.footerBarSubText {
  font-size: $font-size-tiny;
  color: $color-text-secondary;
}
.footerBarSubTextRow2 {
  margin-left: 40px;
}
.numContainer {
  margin-left: 20px;
}
.num {
  color: $color-primary;
}

.repaymentPrompt {
  padding: 20px;
  color: $color-text-normal;
  font-size: $font-size-normal;
  text-align: center;
}
.promptTitle {
  font-weight: bold;
  font-size: font-size-lg;
  margin-bottom: 20px;
}
.promptText {
  color: $color-primary;
}
.promptBtn {
  margin-top: 40px;
}
.advance {
  width: 100%;
  height: 88px;
  text-align: center;
  line-height: 88px;
  color: $color-text-secondary;
}