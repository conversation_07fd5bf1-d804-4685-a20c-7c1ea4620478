
import { <PERSON><PERSON>utton, CyModal, CyTheme } from 'hncy58-taro-components';
import { Button, Image, Text, View } from '@tarojs/components';
import classNames from 'classnames';
import MarqueeTip from '~components/MarqueeTip';
import MoneyDisplay from '~components/MoneyDisplay';
import useUserStore from '~store/user';
import { formatPercentX100, formattedMoney, router, showToast } from '~utils/common';
import { InterestPerday, InterestRateRange, MaxCreditAmount } from '~utils/constant';
import styles from './index.module.scss';
import { lendingPreProgress } from '~services/lending';
import CreditCardSkeleton from '~components/CreditCardSkeleton';
import goLendingPreProgress from '~utils/goLendingPreProgress';
import raiseAmountPng from '~images/index/<EMAIL>'
import questionIconPng from '~images/index/<EMAIL>'
import { useEffect } from 'react';
import toRaiseAmountCheck from '~utils/toRaiseAmountCheck';
import dayjs from 'dayjs';

interface IProps {
  onApply: () => void;
  isLoading?: boolean;
}

const goLogin = () => {
  router.push({
    url: '/modules/user/login/index',
  });
};

export default ({ onApply }: IProps) => {
  const { amountCreditInfo, user, getUserAmountInfo, isShowRaiseAmount } = useUserStore();

  // TODO: 额度等待状态判断
  const isWait = ['PROCESSING', 'MANUAL_CHECK'].includes(amountCreditInfo.state);
  // TODO: 额度拒绝状态判断
  const isReject = amountCreditInfo.state === 'REFUSE';
  // TODO: 额度冻结状态判断
  const isAllFrozen = amountCreditInfo.state === 'FROZEN';
  // TODO: 额度冻结状态判断
  const isNoAmount = ['NO_AMOUNT', 'NO_AMOUNT_LACK_CARD_PIC', 'EXPIRED'].includes(amountCreditInfo.state);
  // 电核中
  const isPhoneVerify = ['PHONE_VERIFY'].includes(amountCreditInfo.state);

  const goRejectPage = () => {
    router.push({
      url: '/modules/credit_authorization/credit-fail/index',
    });
  };

  const startLendingPreProgress = async () => {
    const res = await lendingPreProgress();
    console.log('res.progressCode', res.progressCode)
  };

  const goCreditWait = () => {
    router.push({
      url: '/modules/credit_authorization/credit-auditing/index',
    });
  };

  const goPhoneVerify = () => {
    // TODO: 修改文案
    CyModal.create({
      title: '温馨提示',
      content: '电核中，请您耐心等待，我们将尽快为您电核！',
      confirmText: '确认',
    })
  };

  const goLending = () => {
    if (amountCreditInfo.needCreditApplySuccessView) {
      router.push({
        url: '/modules/credit_authorization/credit-success/index',
      });
      return;
    }
    goLendingPreProgress('push');
  };

  // 额度在审核中时，定时拉取额度信息
  useEffect(() => {
    let timer: NodeJS.Timeout | undefined;
    if (isWait) {
      timer = setInterval(() => {
        getUserAmountInfo()
      }, 8000);
    } else {
      timer && clearInterval(timer);
    }
    return () => {
      timer && clearInterval(timer);
    }
  }, [amountCreditInfo.state])

  const showCreditAlert = () => {
    CyModal.create({
      title: '额度明细',
      content: (
        <View>
          <View className={styles.creditAlertRow}>
            <Text className={styles.creditAlertRowLabel}>预授信额度：</Text>
            <Text className={styles.creditAlertRowValue}>{formattedMoney(`${amountCreditInfo.creditAmount || 0}`)}</Text>
          </View>
          <View className={styles.creditAlertRow}>
            <Text className={styles.creditAlertRowLabel}>已使用额度：</Text>
            <Text className={styles.creditAlertRowValue}>{formattedMoney(`${amountCreditInfo.usedAmount || 0}`)}</Text>
          </View>
          <View className={styles.creditAlertRow}>
            <Text className={styles.creditAlertRowLabel}>冻结额度：</Text>
            <Text className={styles.creditAlertRowValue}>{formattedMoney(`${amountCreditInfo.frozenAmount || 0}`)}</Text>
          </View>
          <View className={styles.creditAlertRow}>
            <Text className={styles.creditAlertRowLabel}>预估可借：</Text>
            <Text className={styles.creditAlertRowValue}>{formattedMoney(`${amountCreditInfo.availableAmount || 0}`)}</Text>
          </View>
          <View className={styles.creditAlertRow}>
            <Text className={styles.creditAlertRowLabel}>有效授信额度：</Text>
            <Text className={styles.creditAlertRowValue}>{formattedMoney(`${amountCreditInfo.effAmount || 0}`)}</Text>
          </View>
          <View className={styles.creditAlertBtTips}>
            预估可借不是对您的贷款承诺。有效授信额度是根据您实际贷款表现、资信情况为您核定的、可预期的借款额度，最终借款金额以实际审批为准。
          </View>
        </View>
      ),
      confirmText: '确认',
    })
  }

  if (amountCreditInfo.state === 'INIT') {
    return <CreditCardSkeleton />
  }
  return (
    <View className={styles.creditCard}>
      <View className={classNames(styles.creditCardTopbar, isNoAmount || isWait || isReject ? styles.creditCardTopbarBg : '')}></View>
      {
        isWait ? (
          <View className={styles.creditCardContent}>
            <View className={styles.creditCardSubtitle}>最高可借(元)</View>
            <View className={styles.creditCardTitle}>额度审核中,请稍等</View>
            <View className={styles.creditCardDesc}>
              年化利率（单利）{InterestRateRange[0]}%-{InterestRateRange[1]}%(10000元借1天利息{InterestPerday[0]}-
              {InterestPerday[1]}元)
            </View>
            <View className={styles.creditCardOpt}>
              <CyButton type='primary' block round style={{ flex: 1 }} onClick={goCreditWait}>
                额度审核中
              </CyButton>
            </View>
          </View>
        ) : isReject ? (
          <View className={styles.creditCardContent} catchMove onClick={goRejectPage}>
            <View className={styles.creditCardSubtitle}>最高可借(元)</View>
            <MoneyDisplay
              currency={false}
              size='xlarge'
              color='#333'
              className={styles.creditAmount}
              money={MaxCreditAmount}></MoneyDisplay>
            <View className={classNames(styles.creditCardDesc, styles.cardMarkText)}>抱歉，您的额度审批申请未通过</View>
            <View className={styles.creditCardOpt}>
              <CyButton type='primary' block round style={{ flex: 1 }}>
                {dayjs(amountCreditInfo.expireDate).format('YYYY年MM月DD日')}后可再次申请
              </CyButton>
            </View>
          </View>
        ) : isNoAmount ? (
          <>
            <View className={styles.creditCardContent}>
              <View className={styles.creditCardSubtitle}>最高可借(元)</View>
              <MoneyDisplay
                currency={false}
                size='xlarge'
                color='#333'
                className={styles.creditAmount}
                money={MaxCreditAmount}></MoneyDisplay>
              <View className={styles.creditCardDesc}>
                年化利率（单利）{InterestRateRange[0]}%-{InterestRateRange[1]}%(10000元借1天利息{InterestPerday[0]}-
                {InterestPerday[1]}元)
              </View>
              <View className={styles.creditCardOpt}>
                <CyButton type='primary' style={{ flex: 1 }} block round onClick={ user.mobile ? onApply : goLogin}>
                  立即申请
                </CyButton>
              </View>
            </View>
          </>
        ) : isPhoneVerify ? (
          <View className={styles.creditCardContent}>
            <View className={styles.creditCardSubtitle}>最高可借(元)</View>
            <View className={styles.creditCardTitle}>电核中,请稍等</View>
            <View className={styles.creditCardDesc}>
                年化利率（单利）{InterestRateRange[0]}%-{InterestRateRange[1]}%(10000元借1天利息{InterestPerday[0]}-
                {InterestPerday[1]}元)</View>
            <View className={styles.creditCardOpt}>
              <CyButton type='primary' block round style={{ flex: 1 }} onClick={goPhoneVerify}>
                电核中
              </CyButton>
            </View>
          </View>
        ) : (
          <View className={styles.creditCardContent}>
            {isAllFrozen ? (
              <>
                <View className={styles.disabledCard}></View>
                <View className={classNames(styles.cardMarkText, styles.disabledCardMarkText)}>
                  (您的额度已冻结,如有借款请立即还款)
                </View>
              </>
            ) : null}
            <View className={styles.creditCardSubtitle}>
              { isAllFrozen ? '额度冻结' : '最高可借'}(元)
              <Image onClick={showCreditAlert} src={questionIconPng} className={styles.creditAlertEntryIcon} />
            </View>
            <MoneyDisplay
              currency={false}
              size='xlarge'
              color='#333'
              className={styles.creditAmount}
              money={isAllFrozen ? amountCreditInfo.creditAmount : amountCreditInfo.availableAmount}></MoneyDisplay>
            {(amountCreditInfo.frozenAmount && !isAllFrozen) ? <View className={styles.creditCardDesc}>
              {`(冻结额度: ${formattedMoney(`${amountCreditInfo.frozenAmount}`, { symbol: '' })}元)`}
            </View> : null}
            <View className={styles.creditCardOpt}>
              {
                isShowRaiseAmount ? (
                  <CyButton onClick={toRaiseAmountCheck} type='primary' plain block round className={styles.creditCardRaiseBtn}>
                    提升额度
                    <Image src={raiseAmountPng} className={styles.creditCardRaiseBtnIcon} />
                  </CyButton>
                ) : null}

              <CyButton onClick={goLending} style={{ flex: 1 }} type='primary' block round>
                去借钱
              </CyButton>
            </View>
            {
              amountCreditInfo.creditAmount > 0 ? (
                <>
                <View className={styles.creditStateBorder}></View>
                <View className={styles.creditStateBar}>
                  <View className={styles.creditStateItem}>
                    <View>预授信额度:</View>
                    <View>{formattedMoney(`${amountCreditInfo.creditAmount}`, {symbol: '', precision: 0})}</View>
                  </View>
                  <View className={styles.creditStateLine}></View>
                  <View className={styles.creditStateItem}>
                    <View>年利率(单利):</View>
                    <View>{formatPercentX100(`${amountCreditInfo.rate * 360}`)}</View>
                  </View>
                  <View className={styles.creditStateLine}></View>
                  <View className={styles.creditStateItem}>
                    <View>日利率:</View>
                    <View>{formatPercentX100(`${amountCreditInfo.rate}`)}</View>
                  </View>
                </View>
                </>
              ) : null
            }
          </View>
        )
      }
      {/* <View className={styles.creditCardBottom}>
        <MarqueeTip
          backgroundColor='#fff'
          content='警惕非法金融中介陷阱，请勿轻信“代理维权”“减免债务”等诱惑！'
          duration={10}
        />
      </View> */}
    </View>
  );
};
