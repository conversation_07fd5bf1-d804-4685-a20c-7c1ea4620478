import { getStoragePromise, setStoragePromise } from './common';

const PRIVACY_STORAGE_KEY = 'privacy_policy_accepted';
const PRIVACY_CACHE_EXPIRE_DAYS = 180; // 半年有效期（180天）

/**
 * 检查隐私政策缓存是否有效
 * @returns 如果缓存有效返回true，否则返回false
 */
export const checkPrivacyCache = async (): Promise<boolean> => {
  try {
    const privacyCacheStr = await getStoragePromise(PRIVACY_STORAGE_KEY);
    if (privacyCacheStr) {
      const privacyCache = JSON.parse(privacyCacheStr);
      const currentTime = new Date().getTime();
      // 检查缓存是否在有效期内
      if (privacyCache.expireTime > currentTime) {
        return true;
      }
    }
  } catch (error) {
    console.log('获取隐私政策缓存失败', error);
  }
  return false;
};

/**
 * 保存隐私政策缓存
 * @returns 保存成功返回true，否则返回false
 */
export const savePrivacyCache = async (): Promise<boolean> => {
  try {
    const currentTime = new Date().getTime();
    // 设置半年后过期时间
    const expireTime = currentTime + PRIVACY_CACHE_EXPIRE_DAYS * 24 * 60 * 60 * 1000;
    await setStoragePromise(
      PRIVACY_STORAGE_KEY,
      JSON.stringify({
        accepted: true,
        acceptTime: currentTime,
        expireTime: expireTime,
      })
    );
    return true;
  } catch (error) {
    console.log('保存隐私政策缓存失败', error);
    return false;
  }
};

/**
 * 清除隐私政策缓存
 * @returns 清除成功返回true，否则返回false
 */
export const clearPrivacyCache = async (): Promise<boolean> => {
  try {
    await setStoragePromise(PRIVACY_STORAGE_KEY, '');
    return true;
  } catch (error) {
    console.log('清除隐私政策缓存失败', error);
    return false;
  }
}; 