import {useEffect, useRef} from 'react';
import {CyButton} from 'hncy58-taro-components';
import {Text, View} from '@tarojs/components';
import Card from '~components/Card';
import Clock from '~components/ClockAniCanvas';
import Steps, {type Step} from '~components/Steps';
import PageWrap from '~components/pageWrap';
import {getUserCreditInfo} from '~services/user';
import useUserStore from '~store/user';
import {router} from '~utils/common';
import styles from './index.module.scss';

const steps: Step[] = [
  {title: '身份核验', status: 'process'},
  {title: '完善信息', status: 'process'},
  {title: '申请额度', status: 'process'},
];

export default () => {
  const timer = useRef<NodeJS.Timeout>();
  const count = useRef(0);
  const {setUser, setAmountCreditInfo} = useUserStore();
  const getCreditInfo = async () => {
    count.current += 1;
    const creditInfoRes = await getUserCreditInfo();
    if (creditInfoRes) {
      setAmountCreditInfo({
        ...creditInfoRes,
      });
      if (creditInfoRes.state === 'PASS') {
        clearInterval(timer.current);
        router.replace({
          url: '/modules/credit_authorization/credit-success/index',
        });
      } else if (creditInfoRes.state === 'REJECT') {
        clearInterval(timer.current);
        router.replace({
          url: '/modules/credit_authorization/credit-fail/index',
        });
      } else {
        if (count.current >= 2) {
          clearInterval(timer.current);
          router.replace({
            url: '/modules/credit_authorization/credit-auditing/index',
          });
        }
      }
    }
  };
  useEffect(() => {
    timer.current = setInterval(() => {
      getCreditInfo();
    }, 3000);
    return () => {
      clearInterval(timer.current);
    };
  }, []);

  const goBack = () => {
    router.back();
  };

  return (
    <PageWrap className={styles.page_container}>
      <View className={styles.step_wrap}>
        <Steps current={3} steps={steps}></Steps>
      </View>
      <View className='vertical-gap'></View>
      <Card title='额度审批中...' note='资料已提交，请耐心等待审核结果' className={styles.card_item}>
        <View style={{textAlign: 'center'}}>
          <Clock size={160} className={styles.waitClock} />
          <View className={styles.identity_info}>
            <Text className={styles.identity_info_name}>系统评估中，请您耐心等待</Text>
          </View>
          <View className={styles.verification_tip}>审批通过后会通过短信通知您，请注意查收 </View>
        </View>
      </Card>

      <CyButton className={styles.bt_btn} onClick={goBack} block round type='primary'>
        返回
      </CyButton>
    </PageWrap>
  );
};
