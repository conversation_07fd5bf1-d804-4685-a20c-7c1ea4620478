.container {
  padding: 0 20px;
}

.itemContainer {
  background-color: #fff;
  border-radius: 8px;
  margin-bottom: 16px;
  overflow: hidden;
  padding: 20px;
}

.itemHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 16px;
  position: relative;
}

.amountSection {
  display: flex;
  align-items: center;
}

.amount {
  font-size: 32px;
  font-weight: 500;
  color: #333;
}

.arrow {
  width: 24px;
  height: 24px;
  margin-left: 8px;
  transition: transform 0.3s ease;
}

// .arrowUp {
//   transform: rotate(180deg);
// }

.period {
  font-size: 28px;
  color: $color-text-normal;
}

.itemDetail {
  max-height: 0;
  overflow: hidden;
  transition: max-height 0.3s ease;
}

.expanded {
  max-height: 300px;
}

.detailRow {
  display: flex;
  margin-bottom: 12px;
  font-size: 28px;
  padding: 0 20px;
  color: $color-text-secondary;
}


.detailSortSummary {
  background-color: $color-bg-grey;
  color: $color-text-secondary;
  font-size: 24px;
  padding: 12px 30px;
}