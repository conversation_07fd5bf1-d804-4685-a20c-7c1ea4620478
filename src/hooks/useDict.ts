import { getDictMap } from '~services/common';
import { IPickerItem } from 'src/types/common-types';

const useDict = async (key: string) => {
    let dictMap = []
    let data = null
    try {
      const res = await getDictMap(key);
      data = res
    } catch (error) {
      console.log('获取字典集合错误', error);
    }

    for (const key in data) {
      if (Object.prototype.hasOwnProperty.call(data, key)) {
        dictMap.push({
          value: data[key],
          label: key,
        });
      }
    }

  return {dictMap};
}

/** 
 * 按照value值大小排序
 */
export const sortDictItems = (items: IPickerItem[], order: 'asc' | 'desc' = 'asc') => {
  return items.sort((a, b) => 
    order === 'asc' 
      ? Number(a.value) - Number(b.value) 
      : Number(b.value) - Number(a.value)
  )
}

export default useDict;