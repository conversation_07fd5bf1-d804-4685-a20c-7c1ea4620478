import {CSSProperties, FC} from 'react';
import {View} from '@tarojs/components';
import Taro from '@tarojs/taro';
import './index.scss';

type Props = {
  height?: string;
  backgroundColor?: string;
};

/**
 * 组件名称：分隔线
 * 组件描述：分隔线
 */
const CyLine: FC<Props> = ({height = '20px', backgroundColor = '#ffffff'}) => {
  const style: CSSProperties = {backgroundColor};

  if (typeof height === 'number') {
    style.flex = height;
  } else {
    if (height.endsWith('px') || height.endsWith('PX')) {
      style.height = Taro.pxTransform(parseInt(height.substring(0, height.length - 2)));
    } else {
      style.height = Taro.pxTransform(parseInt(height));
    }
  }
  return <View className='comp-cy-line' style={style}></View>;
};

export default CyLine;
