import {useRef, useState} from 'react';
import {Image, Text, View, Input} from '@tarojs/components';
import Taro from '@tarojs/taro';
import ClearIcon from '~images/lending/<EMAIL>';
import {formattedMoney, getAmountUnit} from '~utils/common';
import debounce from '~utils/debounce';
import {isMultipleOf100} from '~utils/regex';
import styles from './index.module.scss';
import {showToast} from '~utils/common';
import useUserStore from '~store/user';
import { formatToFixedTwo } from '~utils/number';
interface LoanProps {
  // 是否有优惠券
  hasCoupon?: boolean;
  onAmountChange?: (amount: number | null) => void;
}

export default ({hasCoupon = false, onAmountChange}: LoanProps) => {
  const inputRef = useRef<any>(null);
  const [amount, setAmount] = useState<number | null>(null);
  const [unit, setUnit] = useState('');
  
  const {amountCreditInfo} = useUserStore();
  const maxAmount = amountCreditInfo?.availableAmount ?? '200000';

  const handleInput = debounce((e: any) => {
    const inputAmount = e.detail.value;
    const parsedAmount = parseFloat(inputAmount);
    setUnit(getAmountUnit(parsedAmount));
    setAmount(inputAmount);

    if (inputAmount && !handleCheck(inputAmount)) {
      onAmountChange && onAmountChange(null);
      return
    };
    if (!Number.isNaN(parsedAmount)) {
      onAmountChange && onAmountChange(parsedAmount);
    } else {
      onAmountChange && onAmountChange(null);
      setUnit('');
    }
    Taro.hideKeyboard();
  }, 1000);
  const handleCheck = (value: number) => {
    if (value > Number(maxAmount)) {
      showToast(`您当前预估可借${formatToFixedTwo(maxAmount)}元,请重新输入借款金额`);
      return false;
    }
    if (value < 100) {
      showToast('最低借款金额¥100.00');
      return false;
    }
    if (!isMultipleOf100(value)) {
      showToast('请输入100的倍数');
      return false;
    }
    return true;
  };
  const handleClear = () => {
    onAmountChange && onAmountChange(null);
    setUnit('');
    if (inputRef.current) {
      inputRef.current.value = '';
      inputRef.current.focus();
    }
  };
  const handleLoanAll = () => {
    const amountAll = Number(maxAmount);
    if (amountAll) {
      inputRef.current.value = amountAll;
      setAmount(amountAll);
      setUnit(getAmountUnit(amountAll));
      if (handleCheck(amountAll)) {
        onAmountChange && onAmountChange(amountAll);
      } else {
        onAmountChange && onAmountChange(null);
      }
    }
  };
  return (
    <View className={styles.loan}>
      <View className={styles.loan_title}>借款金额</View>
      {unit && <View className={styles.loan_money_unit}>{unit}</View>}
      <View className={styles.loan_money}>
        <View className={styles.flex_bteween}>
          <View className={styles.loan_money_input_main}>
            <Text className={styles.loan_money_input_unit}>￥</Text>
            <View className={styles.loan_money_input}>
              <Input
                ref={inputRef}
                type='number'
                maxlength={14}
                onInput={handleInput}
                className={styles.money_input}
                placeholder={`预估可借${formattedMoney(`${maxAmount}`, {symbol: ''})}元`}
                placeholderStyle='color: #999; font-size: 14px;'
                placeholderClass={styles.money_input_placeholder}
              />
            </View>
          </View>
          <View className={styles.flex}>
            {amount && <Image className={styles.loan_money_clear} src={ClearIcon} onClick={handleClear}></Image>}
            <View className={styles.loan_money_allin}>
              <Text onClick={handleLoanAll}>全部借出</Text>
            </View>
          </View>
        </View>
      </View>
      <View className={styles.loan_money_tip}>
        按日计息，单笔￥100.00起借，提前还款无手续费{hasCoupon && <View className={styles.coupon}>有优惠劵</View>}
      </View>
    </View>
  );
};
