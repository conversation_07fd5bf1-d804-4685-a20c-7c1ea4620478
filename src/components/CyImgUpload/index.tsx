import {ForwardRefRenderFunction, forwardRef, useImperativeHandle, useState} from 'react';
import {Image, View} from '@tarojs/components';
import Taro from '@tarojs/taro';
import clearPng from '~images/icon/clear.png';
import {cullObjectByValue} from '../../utils/common';
import {SelectPhotoResult, SizeType, selectPhoto} from '../../utils/photo';
import {EventType, FormField} from '../CyForm';
import './index.scss';

interface FileItem {
  path: string;

  size: number;
}

interface File {
  url: string;

  file?: FileItem;
}

type OperateType = 'add' | 'remove';
type TTarget = {
  value: any;
  target: any;
  type: 'imgUpload';
};

interface ImgUploadProps extends FormField {
  value?: SelectPhotoResult[];
  multiple?: boolean;
  count?: number; // 最多可选图片的张数
  aspectRatio?: [number, number]; // 图片宽高比
  sourceType?: string[];
  sizeType?: Array<keyof SizeType>;
  onFail?: (error: Error) => void;
  name?: string;
}

/**
 * 组件名称：图片上传表单组件
 * 组件描述：用于表单中图片提交，支持多图上传
 */
const CyImgUpload: ForwardRefRenderFunction<unknown, ImgUploadProps> = (props, ref) => {
  const {
    value = [],
    multiple = true,
    count = 3,
    aspectRatio = [1, 1],
    sourceType,
    sizeType,
    name,
    onChange,
    onFail,
  } = props;
  const [currentVal, setCurrentVal] = useState<SelectPhotoResult[]>(value);
  useImperativeHandle(ref, () => ({
    get value() {
      return currentVal;
    },
    set value(newValue) {
      setCurrentVal(newValue);
    },
  }));

  const addImg = () => {
    const params = cullObjectByValue({
      multiple,
      count: count - currentVal.length,
      sourceType,
      sizeType,
    });

    selectPhoto({
      ...params,
      onSuccess: res => {
        const newVal = currentVal.concat(res);
        updateVal(newVal);
      },
      onFail,
    });
  };

  const onPreview = (idx: number) => {
    Taro.previewImage({
      current: currentVal[idx].data || currentVal[idx].file,
      urls: currentVal.map(file => file.file || file.data),
    });
  };
  const updateVal = (val: SelectPhotoResult[]) => {
    const e = {
      target: {
        name,
        value: val,
      },
      detail: {
        value: val,
      },
    };
    onChange?.(e as unknown as EventType);
    setCurrentVal(val);
  };
  const removeImg = (idx: number) => {
    const newVal = currentVal.filter((file, curIdx) => curIdx !== idx);
    updateVal(newVal);
  };
  return (
    <View className='comp-img-upload' style={props.style}>
      {currentVal.map((file, idx) => (
        <View key={idx} className='uploader-item' style={{aspectRatio: aspectRatio[0] / aspectRatio[1]}}>
          <Image onClick={() => removeImg(idx)} className='remove-icon' src={clearPng}></Image>
          <Image
            onClick={() => onPreview(idx)}
            className='uploader-image'
            src={file.file || file.data}
            style={{width: '100%', height: '100%'}}></Image>
        </View>
      ))}
      {currentVal.length < count ? (
        <View className='add-image' onClick={addImg} style={{aspectRatio: aspectRatio[0] / aspectRatio[1]}}>
          <View className='add-image2'>+</View>
        </View>
      ) : null}
    </View>
  );
};

export default forwardRef(CyImgUpload);
