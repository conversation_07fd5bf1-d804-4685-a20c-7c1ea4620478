import {CSSProperties, FC} from 'react';
import {View, ViewProps} from '@tarojs/components';
import Taro from '@tarojs/taro';
import './index.scss';

interface Props {
  showHeader?: boolean;
  showFooter?: boolean;
  headerBgColor?: string;
  footerBgColor?: string;
}

/**
 * 组件名称：安全区域
 * 组件描述：安全区域
 */
const CySafeArea: FC<Props & ViewProps> = ({
  children,
  showHeader = true,
  showFooter = true,
  headerBgColor = 'transparent',
  footerBgColor = 'transparent',
}) => {
  const safeAreaHeight = Taro.getWindowInfo().safeArea?.height;
  const screenHeight = Taro.getWindowInfo().screenHeight;
  const statusBarHeight = Taro.getWindowInfo().statusBarHeight;
  let safeAreaBottom = 0;
  if (safeAreaHeight && statusBarHeight && screenHeight > safeAreaHeight) {
    safeAreaBottom = screenHeight - safeAreaHeight - statusBarHeight;
  }
  return (
    <>
      {showHeader && (
        <View className='comp-cy-safe-area' style={{height: statusBarHeight, backgroundColor: headerBgColor}}></View>
      )}
      {children}
      {showFooter && (
        <View className='comp-cy-safe-area' style={{height: safeAreaBottom, backgroundColor: footerBgColor}}></View>
      )}
    </>
  );
};

export default CySafeArea;
