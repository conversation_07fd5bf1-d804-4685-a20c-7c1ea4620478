import request from '../http/request';

/** 获取密码键盘随机码 */
export const getRandomCode = (keyboardType?: string) => {
    return request<any>({
      url: '/api/password/randomCode',
      method: 'GET',
      params: {
        keyboardType,
      },
    });
};

/** 设置交易密码 */
export const setTradePassword = (data: API.SetTradeReq) => {
  return request<any>({
    url: '/api/password/trade',
    method: 'POST',
    data,
  });
};

/** 修改交易密码 */
export const updateTradePassword = (data: API.UpdateTradeReq) => {
  return request<any>({
    url: '/api/password/modifyTradePwd',
    method: 'POST',
    data,
  });
}

/** 重置交易密码 */
export const resetTradePassword = (data: API.ResetTradeReq) => {
  return request<any>({
    url: '/api/password/trade/reset',
    method: 'POST',
    data,
  });
}