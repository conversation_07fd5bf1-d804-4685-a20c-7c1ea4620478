.page-raise-amount {
  background-color: #f5f5f5;
  min-height: 100vh;
  padding-top: 32px;
}

.cardContainer {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 32px;
}

.card {
  width: 670px;
  height: 240px;
  border-radius: 24px;
  box-shadow: 1px 1px 8px 0px rgba(189,189,189,0.4);
  overflow: hidden;
  background: #ffffff;
}

.cardContent {
  display: flex;
  align-items: center;
  padding: 36px 40px 36px 55px;
  position: relative;
}

.cardMain {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.titleRow {
  display: flex;
  align-items: center;
  gap: 12px;
}

.iconWrapper {
  flex-shrink: 0;
}


.icon {
  width: 96px;
  height: 96px;
}

.title {
  font-size: 40px;
  font-weight: 500;
  color: #333;
  line-height: 32px;
  flex: 1;
}

.subtitle {
  font-size: 24px;
  color: #999999;
  line-height: 32px;
}

.arrow {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  margin-left: 100px;
}

.arrowIcon {
  font-size: 18px;
  color: #cccccc;
  font-weight: 300;
}

.recordButton {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-top: 8px;
}

.recordIcon {
  width: 24px;
  height: 24px;
}

.recordText {
  font-size: 28px;
  color: $color-primary;
  margin-left: 12px;
}

.tipContainer {
  position: absolute;
  bottom: 69px;
  padding: 0 56px;
  color: #999;
}

.tipTitle {
  font-size: 30px;
  line-height: 48px;
  margin-bottom: 12px;
}

.tipContext {
  font-size: 24px;
  line-height: 36px;
}