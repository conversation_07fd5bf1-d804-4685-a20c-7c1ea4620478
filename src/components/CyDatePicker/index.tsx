import {FC, useState} from 'react';
import {Picker, View} from '@tarojs/components';
import dayjs from 'dayjs';
import './index.scss';

type DateMode = 'year-month';

interface Props {
  onChange?: (yearMonth: string) => void;
  mode?: DateMode;
  children?: React.ReactNode;
}

/**
 * 组件名称：日期选择器
 * 组件描述：日期选择
 */
const CyDatePicker: FC<Props> = ({onChange, children, mode = 'year-month'}) => {
  const getFullMonths = () => {
    const startMonth = 1;
    const months = Array.from({length: 12}, (_, index) => {
      return `${startMonth + index}月`;
    });
    return months;
  };
  const getYears = () => {
    const currentYear = new Date().getFullYear();
    const startYear = currentYear - 15;
    // 生成后续10年的数组
    const years = Array.from({length: 15}, (_, index) => {
      return `${startYear + index + 1}年`;
    });
    return years;
  };
  /**
   * 获取当年的月份
   * @returns 当前时间已有的月份
   */
  const getCurrentMonths = () => {
    const currentMonth = new Date().getMonth();
    const startMonth = 1;
    const months = Array.from({length: currentMonth}, (_, index) => {
      return `${startMonth + index}月`;
    });
    return months;
  };

  const year = getYears();
  const currentMonths = getCurrentMonths();
  const fullMonths = getFullMonths();

  const [yearIndex, setYearIndex] = useState(year.length - 1);
  const [monthIndex, setMonthIndex] = useState(currentMonths.length - 1);
  const [monthColumns, setMonthColumns] = useState(currentMonths);

  const onPickerChange = (e: any) => {
    console.log(e);
    const yearValue = year[e.detail.value[0]];
    const monthValue = monthColumns[e.detail.value[1]];
    const resValue = `${yearValue.replace('年', '')}-${monthValue.replace('月', '')}`;
    const yearMonth = dayjs(resValue, 'YYYY-MM').format('YYYY-MM');
    console.log('🚀 ~ onChange ~ yearMonth:', resValue);
    console.log('🚀 ~ onChange ~ yearMonth:', yearMonth);
    onChange?.(resValue);
  };
  const onColumnChange = (e: {detail: {column: number; value: number}}) => {
    if (e.detail.column === 0) {
      const index = e.detail.value;
      // 根据选择年份，改变月份数组
      setYearIndex(index);
      if (index === year.length - 1) {
        setMonthColumns(currentMonths);
        // 如果选择的年份中没有已经选择的月份，则选择当年最大的月份
        if (monthIndex > currentMonths.length - 1) {
          setMonthIndex(currentMonths.length - 1);
        }
      } else {
        setMonthColumns(fullMonths);
      }
    } else {
      const index = e.detail.value;
      setMonthIndex(index);
    }
  };
  return (
    <View>
      <Picker
        mode='multiSelector'
        range={[year, monthColumns]}
        value={[yearIndex, monthIndex]}
        onChange={onPickerChange}
        onColumnChange={onColumnChange}>
        {children}
      </Picker>
    </View>
  );
};

export default CyDatePicker;
