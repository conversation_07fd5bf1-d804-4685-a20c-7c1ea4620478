import {FC, ReactNode, useCallback} from 'react';
import {View, ViewProps} from '@tarojs/components';
import {router} from '../../utils/common';
import throttle from '../../utils/throttle';
import './index.scss';

export type NavigateStyle = 'push' | 'redirect' | 'reLaunch' | 'switchTab';
interface Props extends ViewProps {
  url: string;
  children: ReactNode;
  className?: string;
  navigateStyle?: NavigateStyle;
}

/**
 * 组件名称：链接组件
 * 组件描述：链接组件，点击进行跳转，默认展示蓝色
 */
const CyLink: FC<Props> = ({url, children, style, className, navigateStyle}) => {
  const goUrl = throttle(() => {
    switch (navigateStyle) {
      case 'push':
        router.push({
          url,
        });
        break;
      case 'redirect':
        router.replace({
          url,
        });
        break;
      case 'reLaunch':
        router.reLaunch({
          url,
        });
        break;
      case 'switchTab':
        router.switchTab({
          url,
        });
        break;
      case undefined:
        router.push({
          url,
        });
        break;
    }
  });
  return (
    <View onClick={goUrl} className={`comp-cy-link ${className}`} style={style}>
      {children}
    </View>
  );
};

export default CyLink;
