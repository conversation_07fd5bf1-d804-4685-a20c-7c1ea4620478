.popup_overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: flex-end;
  z-index: 99;
  transition: opacity 0.3s ease-in-out;
  opacity: 0;
  pointer-events: none;
}
.popup_overlay.visible {
  opacity: 1;
  pointer-events: auto;
}
.popup_overlay.hidden {
  opacity: 0;
  pointer-events: none;
}

.popup_container {
  position: fixed;
  z-index: 99;
  background-color: white;
  width: 100%;
  box-sizing: border-box;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  transform: translateY(100%);
  transition: transform 0.3s ease-in-out, opacity 0.3s ease-in-out;
  opacity: 0;
  border-radius: 24px 24px 0 0;
}
.popup_container.visible {
  transform: translateY(0);
  opacity: 1;
}
.popup_container.hidden {
  transform: translateY(100%);
}

.popup_container.bottom {
  bottom: 0;
}

.popup_main {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.popup_title_container {
  display: flex;
  flex-direction: column;
  justify-content: center;
  width: 100%;
  min-height: 100px;
  line-height: 1;
}
.popup_title {
  font-size: 32px;
  color: #333;
}
.popup_note {
  font-size: 24px;
  color: #999;
  margin-top: 8px;
}
.popup_close {
  position: absolute;
  top: 38px;
  right: 40px;
  color: #ccc;
  padding: 10px;
  .popup_clearimg {
    width: 24px;
    height: 24px;
  }
}
.popup_content {
  flex: 1;
  max-height: 1000px;
  overflow: scroll;
  -webkit-overflow-scrolling: touch;
}