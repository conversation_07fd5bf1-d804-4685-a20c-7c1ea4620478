import {useState} from 'react';
import {Image, ScrollView, Text, View} from '@tarojs/components';
import Taro, {getFileSystemManager} from '@tarojs/taro';
import {CyButton, CySafeArea} from 'hncy58-taro-components';
import Card from '~components/Card';
import PageWrap from '~components/PageWrap';
import removeIcon from '~images/credit-apply/<EMAIL>';
import {uploadDocument} from '~services/document';
import {getFileExtension} from '~utils/photo';
import styles from './index.module.scss';
import { router, showToast } from '~utils/common';

const UploadManagerPhoto = () => {
  // const [uploadedPhotos, setUploadedPhotos] = useState<string[]>([]);
  const [files, setFiles] = useState<string[]>([]); // 存储上传的文件信息

  const maxUploads = 5;
  // const remainingUploads = maxUploads - uploadedPhotos.length;

  const handleUpload = () => {
    if (files.length >= maxUploads) {
      Taro.showToast({
        title: '最多上传5张照片',
        icon: 'none',
      });
      return;
    }

    Taro.chooseImage({
      count: maxUploads - files.length,
      success(res) {
        const tempFilePaths = res.tempFilePaths;
        console.log('tempFilePaths====', tempFilePaths);
        const extName = getFileExtension(tempFilePaths[0]);
        getFileSystemManager().readFile({
          filePath: tempFilePaths[0],
          encoding: 'base64',
          success: async fileRes => {
            console.log('fileRes====', fileRes);
            const base64Str = `data:image/${extName};base64,${fileRes.data}`;
            setFiles([...files, base64Str]);
          },
        });
      },
    });
  };

  const handleDelete = (index: number) => {
    const newPhotos = files.filter((_, i) => i !== index);
    setFiles(newPhotos);
  };

  const handleSubmit = async () => {
    console.log('提交的照片:', files);
    await uploadDocument({
      docType: 'E03',
      files: files,
      eventCode: 'SAVE_CUSTOMER_DOCUMENT',
    });
    showToast({
      message: '提交成功',
      icon: 'success',
      duration: 2000,
    })
    router.back()
  };

  const handlePreview = (idx: number) => {
    Taro.previewImage({
      current: files[idx],
      urls: files,
    });
  };

  return (
    <PageWrap className={styles.page}>
      <View className={styles.container}>
        {/* 上传卡片 */}
        <Card title='上传客户经理合影' note='请确保上传的照片清晰可见'>
          <View className={styles.uploadArea}>
            <Image
              src='https://via.placeholder.com/80x60/f0f0f0/cccccc?text=Preview'
              className={styles.previewPlaceholder}
            />
          </View>

          <View className={styles.uploadButton} onClick={handleUpload}>
            <Text className={styles.uploadButtonText}>+ 上传/拍摄合影照片</Text>
          </View>
        </Card>

        {/* 已上传照片卡片 */}
        <Card className={styles.card}>
          <Text className={styles.title}>已上传照片</Text>
          <View className={styles.subtitle}>
            今日已上传 <Text className={styles.highlight}>{files.length}</Text> 张, 还可以上传{' '}
            <Text className={styles.highlight}>{maxUploads - files.length}</Text> 张
          </View>

          <ScrollView scrollX>
            <View className={styles.photoGrid}>
              {files.map((photo, index) => (
                <View key={index} className={styles.photoWrapper} onClick={() => handlePreview(index)}>
                  <Image src={photo} className={styles.photoThumbnail} mode='aspectFit' />
                  <Image
                    src={removeIcon}
                    className={styles.deleteButton}
                    onClick={e => {
                      e.stopPropagation();
                      handleDelete(index);
                    }}></Image>
                </View>
              ))}
            </View>
          </ScrollView>
        </Card>
      </View>

      <View className={styles.footer}>
        <CyButton round type='primary' block onClick={handleSubmit}>
          提交
        </CyButton>
        <CySafeArea style={{background: 'transparent'}} position='bottom'></CySafeArea>
      </View>
    </PageWrap>
  );
};

export default UploadManagerPhoto;
