import {useEffect, useMemo, useRef, useState} from 'react';
import {CyButton} from 'hncy58-taro-components';
import {Text, View} from '@tarojs/components';
import CellItem from '~components/CellItem';
import MoneyDisplay from '~components/MoneyDisplay';
import PageWrap from '~components/PageWrap';
import Popup from '~components/Popup';
import BankList from '~modules/lending/lending/components/BankList';
import Coupon from '~modules/lending/lending/components/Coupon';
import RepaymentAmount from '../components/RepaymentAmount';
import RepaymentFooterBar from '../components/RepaymentFooterBar';
import styles from './index.module.scss';
import { activeTrial, aheadTrial, bankcardPay, getPayWay, getPrepareLoanInfo, getRepaymentState } from '~services/repayment';
import useUserStore from '~store/user';
import CyVerificationCodeInput, { CyVerificationCodeInputRef } from '~components/CyVerificationCodeInput';
import { formattedMoney, router, showToast } from '~utils/common';
import { useDidShow, useLoad } from '@tarojs/taro';
import Taro from '@tarojs/taro';
import { sendVerifyCodeReq } from '~services/user';
import { smsSceneType } from '../../../types/common-types';
import { getBankCardList } from '~services/bankcard';
import currency from 'currency.js';

export default () => {
  const [couponVisible, setCouponVisible] = useState(false);
  const [bankVisible, setBankVisible] = useState(false);
  const [verifyCodeVisible, setVerifyCodeVisible] = useState(false);
  const [paywayList, setPaywayList] = useState<API.IBankCardInfo[]>([]);
  const [currentBankCardInfo, setCurrentBankCardInfo] = useState<API.IBankCardInfo>();
  const [queryInfos, setQueryInfos] = useState<{
    loanNos?: string;
    repayAmount?: number;
    category?: API.RepayableSummaryCategory
  }>({});
  const [payAmount, setPayAmount] = useState<number>(0);
  const [finPayAmount, setFinPayAmount] = useState<number>(0);
  const [loansInfo, setLoansInfo] = useState<API.PrepareLoanInfoRes>();
  const [trailInfo, setTrailInfo] = useState<API.AheadTrailRes | API.ActiveTrailRes>();
  const [isAmoutInputFocus, setIsAmoutInputFocus] = useState(false);
  const verifyCodeInputRef = useRef<CyVerificationCodeInputRef>(null);

  const userStore = useUserStore()

  const startTrial = async () => {
    if(!queryInfos.loanNos || !payAmount) {
      console.log('参数错误====', queryInfos.loanNos, payAmount);
      showToast('参数错误')
      return
    }
    try {
      Taro.showLoading({title: '试算中', mask: true})
      // 主动还款
      if(queryInfos.category === 'AHEAD') {
        const aheadRes = await aheadTrial({
          loanNos: queryInfos.loanNos,
          repayPrincipal: payAmount,
        })
        setFinPayAmount(aheadRes.repayTotalAmount);
        setTrailInfo(aheadRes)
      } else {
        const activeRes = await activeTrial({
          loanNos: queryInfos.loanNos,
          repayAmount: payAmount,
        })
        setFinPayAmount(activeRes.repayTotalAmount);
        setTrailInfo(activeRes)
      }
  
      if(!queryInfos.category) return;
      const curLoaninfo = await getPrepareLoanInfo({
        loanNos: queryInfos.loanNos,
        repayableSummaryCategory: queryInfos.category
      })
      Taro.hideLoading()
      setLoansInfo(curLoaninfo);
    } catch (error) {
      Taro.hideLoading()
    }
  }

  const startGetPayWay = async () => {
    // 获取还款方式
    const {bankCardInfo}  = await getPayWay({
      principalAcru: payAmount,
    })

    const bankcardList = await getBankCardList({
      requestFrom: 'repayment'
    })
    const otherBankCard = bankcardList.filter(item => item.acctId !== bankCardInfo.acctId);
    setPaywayList([bankCardInfo, ...otherBankCard]);
    setCurrentBankCardInfo(bankCardInfo);
  }

  const startBankcardPay = async () => {
    if(!currentBankCardInfo?.acctId) {
      showToast('请选择还款账户')
      return;
    }
    try {
      // 银行卡还款
      setVerifyCodeVisible(true);
      verifyCodeInputRef.current?.sendCode()
    } catch (error) {
      console.log('还款失败 error', error);
    }
  }

  const bankcardPayConfirm = async (verificationCode: string) => {
    try {
      Taro.showLoading({
        title: '还款申请中',
        mask: true
      })
      const isPrepay = queryInfos.category === 'AHEAD';
      const {repaymentRegisterId} = await bankcardPay({
        repaymentCategory: queryInfos.category || '',
        acctId: currentBankCardInfo?.acctId || '',
        loanNos: queryInfos.loanNos,
        smsSceneType: smsSceneType.M1009,
        verificationCode,
        repayAmount: isPrepay ? payAmount : finPayAmount,
      });
      Taro.hideLoading()
      verifyCodeInputRef.current?.clearCode(true)
      router.replace({
        url: `/modules/repayment/repayment-result/index?repaymentRegisterId=${repaymentRegisterId}&repaymentAccount=${curbankString}&repaymentAmount=${finPayAmount}`,
      })
    } catch (error) {
      Taro.hideLoading()
      verifyCodeInputRef.current?.clearCode(false)
      console.log('还款失败 error', error);
    }
  }


// 发送验证码
const sendVerifyCode = async () => {
  try {
    await sendVerifyCodeReq({smsSceneType: smsSceneType.M1009});
    return true;
  } catch (e) {
    console.error('发送验证码失败', e);
    return false;
  }
};

// 验证码验证
const handleVerify = (code: string) => {
  console.log('验证码', code);
  bankcardPayConfirm(code);
  setVerifyCodeVisible(false);
  // 这里添加验证码验证逻辑
};


  useEffect(() => {
    if(verifyCodeVisible) {
      verifyCodeInputRef.current?.clearCode(true)
      verifyCodeInputRef.current?.focus()
    }
  }, [verifyCodeVisible])

  useEffect(() => {
    setPayAmount(Number(queryInfos.repayAmount));
  }, [queryInfos.loanNos])

  useEffect(() => {
    if(payAmount > 0) {
      startGetPayWay();
      startTrial();
    }
  }, [payAmount])

  const isAllPayOrAhead = useMemo(() => {
    return Number(queryInfos.repayAmount) === Number(payAmount) || queryInfos.category === 'AHEAD';
  }, [payAmount])

  useDidShow(() => {
    startGetPayWay()
  })

  const bankcardConfirm = (item: API.IBankCardInfo) => {
    console.log('selectBank', item)
    setCurrentBankCardInfo(item);
    setBankVisible(false);
    // setVerifyCodeVisible(true);
  }

  const curbankString = useMemo(() => {
    if (currentBankCardInfo) {
      return `${currentBankCardInfo.bankName}(${currentBankCardInfo.desensitizeAccountNo?.slice(-4) || ''} )`;
    }
    return null
  }, [currentBankCardInfo])

  const handleSelectCoupon = () => {
    // TODO:
    showToast('功能开发中');
    // setCouponVisible(true);
  };

  useLoad(({loanNos, repayAmount, category}) => {
    setQueryInfos({
      loanNos,
      repayAmount,
      category
    })
  })

  const canEditAmount = useMemo(() => {
    const isCombine = !!queryInfos.loanNos?.includes(',');
    const isPrepay = queryInfos.category === 'AHEAD';
    return !(isCombine && isPrepay);
  }, [queryInfos])

  const handleAmountChangeConfirm = (e: any) => {
    const val = e.detail.value;
    setIsAmoutInputFocus(false);
    if(Number(val) > Number(queryInfos.repayAmount)) {
      showToast('不能超过应还金额')
      setPayAmount(Number(queryInfos.repayAmount));
      return false;
    }
    
    if(loansInfo?.allowLeastRepaymentPrincipal && Number(val) < Number(loansInfo?.allowLeastRepaymentPrincipal)) {
      showToast(`最低还款金额为￥${loansInfo?.allowLeastRepaymentPrincipal}`)
      setPayAmount(Number(queryInfos.repayAmount));
      return false;
    }

    if(Number(val)%1 !== 0 && val !== queryInfos?.repayAmount) {
      showToast('部分还款暂不支持小数')
      setPayAmount(Number(queryInfos.repayAmount));
      return false;
    }
    setPayAmount(Number(val));
    return true;
  }

  // const handleAmountChange = (e: any) => {
  //   const val = e.detail.value;
  //   console.log('val', val, e);
  //   return true
  // }

  const repaymentComposition = useMemo(() => {
    if(queryInfos.category === 'AHEAD') {
      return {
        principal: (trailInfo as API.AheadTrailRes)?.repayPrincipal,
        interest: (trailInfo as API.AheadTrailRes)?.repayInterest,
        penalty: 0,
      }
    }
    return {
      principal: loansInfo?.repayablePrincipal,
      interest: loansInfo?.repayableInterest,
      penalty: currency(loansInfo?.expirePenalty || 0).add(loansInfo?.expireInterest || 0).value,
    }
  }, [loansInfo, queryInfos])


  if(!queryInfos.loanNos) {
    return null
  }
  
  return (
    <PageWrap className={styles.confirmView}>
      <RepaymentAmount onFocus={() => setIsAmoutInputFocus(true)} onBlur={handleAmountChangeConfirm} amount={queryInfos.repayAmount} isEdit={canEditAmount} amountTitle='还款金额（元）' amountNote={`最低还款金额${formattedMoney(`${loansInfo?.allowLeastRepaymentPrincipal || 0}`, {precision: 2}) ?? 0}`} />
      <View className={styles.operation}>
        {
          isAllPayOrAhead ? (
              <View className={styles.page_card}>
              {/* <CellItem
                title='优惠券'
                // value='五折券'
                placeholder='请选择优惠券'
                // valueDesc={<View className={styles.page_primay}>合计优惠利息 ¥200.25</View>}
                clickable
                onClick={handleSelectCoupon}></CellItem> */}
              <CellItem
                title='还款明细'
                value={`含本金${formattedMoney(`${repaymentComposition?.principal ?? 0}`, {precision: 2})}`}
                valueDesc={<View className={styles.page_normal}>利息 {formattedMoney(`${repaymentComposition?.interest ?? 0}`, {precision: 2})} 罚息{formattedMoney(`${repaymentComposition?.penalty ?? 0}`, {precision: 2})}</View>}
                ></CellItem>
            </View>
          ) : null
        }
        <View className={styles.page_card}>
          <CellItem
            title='实还金额'
            value={<View className={styles.page_amount}>¥{finPayAmount}</View>}
            // valueDesc={<View className={styles.page_del}>¥11000.53</View>}
          ></CellItem>
          <CellItem placeholder="请选择银行卡" title='付款方式' value={curbankString} clickable onClick={() => setBankVisible(true)} />
        </View>
      </View>
      <RepaymentFooterBar
        content={
          <>
            <View className={styles.footerBarText}>
              还款实付：
              <MoneyDisplay money={finPayAmount || 0} color='#2F54EB' />
            </View>
          </>
        }
        disabled={isAmoutInputFocus || Number(finPayAmount) == 0 }
        btnText='立即还款'
        onBtnClick={() => {
          startBankcardPay();
        }}
      />
      <Coupon visible={couponVisible} onClose={() => setCouponVisible(false)} />
      <BankList title='选择还款账户' bankCardList={paywayList} visible={bankVisible} onClose={() => setBankVisible(false)} onComplete={bankcardConfirm} />
      <Popup visible={verifyCodeVisible} title='输入验证码' onClose={() => setVerifyCodeVisible(false)}>
        <CyVerificationCodeInput 
          ref={verifyCodeInputRef}
          phone={userStore.user.mobile} 
          onVerify={handleVerify}
          onSendCode={sendVerifyCode}
        />
      </Popup>
    </PageWrap>
  );
};

