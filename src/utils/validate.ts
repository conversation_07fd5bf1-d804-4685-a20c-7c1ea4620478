// 校验规则配置
type RuleConfig = {
  pattern?: RegExp;
  validator?: (value: string) => boolean;
};

type RuleType = 'phone' | 'idCard' | 'name' | 'address'

type Validator = (value: string) => boolean;

const rules: Record<RuleType, RuleConfig> = {
  phone: {
    pattern: /^1[3-9]\d{9}$/,
  },
  idCard: {
    pattern: /^[1-9]\d{5}(18|19|20)?\d{2}(0[1-9]|1[0-2])(0[1-9]|[12]\d|3[01])\d{3}[\dXx]$/,
  },
  name: {
    pattern: /^[\u4e00-\u9fa5·]{2,20}$/,
  },
  address: {
    validator: (value: string) => value.length > 0 && value.length <= 100,
  },
  // 可以继续扩展更多类型
};

export const validateByType = (type: keyof typeof rules) => (value?: string) => {
  const rule = rules[type];
  if (!rule) {
    throw new Error(`Unknown validation type: ${type}`);
  }
  if (!value) {
    return false;
  }
  if (rule.pattern) {
    return rule.pattern.test(value);
  }
  if (rule.validator) {
    return rule.validator(value);
  }
  throw new Error(`No pattern or validator defined for type: ${type}`);
};

// 使用示例：
// const isPhone = validateByType('phone');
// isPhone('13812345678'); // true/false

// 表单校验
export type ValidationRule = {
  required?: boolean;
  pattern?: RegExp;
  validator?: (value: any) => boolean;
  message: string;
};

type ValidationResult = {
  valid: boolean;
  errors?: Record<string, string>;
};
export const validateForm = (
  data: Record<string, any>,
  rules: Record<string, ValidationRule[]>,
): ValidationResult => {
  const errors: Record<string, string> = {};
  
  for (const [field, rule] of Object.entries(rules)) {
    const value = data[field];
    
    rule.forEach((rulesItem: ValidationRule) => {
      if ((value == null || value === '') && !rulesItem.required) {
        return; // 非必填并且值为空时，跳过当前规则项的校验
      }

      let isValid = true;
      // 必填校验
      if (rulesItem.required && (value == null || value === '' || value.length === 0)) {
        isValid = false;
      }

      if (value != null && value !== '') {
        // 正则校验
        if (isValid && rulesItem.pattern && typeof value === 'string' && !rulesItem.pattern.test(value)) {
          isValid = false;
        }
        
        // 自定义校验
        if (isValid && rulesItem.validator && !rulesItem.validator(value)) {
          isValid = false;
        }
      }
      
      
      // 记录错误
      if (!isValid && !errors[field]) {
        errors[field] = rulesItem.message;
      }

    })
  }
  
  return {
    valid: Object.keys(errors).length === 0,
    errors: Object.keys(errors).length > 0 ? errors : undefined
  };
};