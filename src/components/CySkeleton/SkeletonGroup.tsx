import React from 'react';
import { View } from '@tarojs/components';
import classNames from 'classnames';
import CySkeleton from './index';
import styles from './SkeletonGroup.module.scss';

export interface SkeletonGroupProps {
  /**
   * 是否显示动画效果
   * @default true
   */
  animated?: boolean;
  
  /**
   * 自定义类名
   */
  className?: string;
  
  /**
   * 子元素
   */
  children?: React.ReactNode;
  
  /**
   * 行数
   * @default 0
   */
  rows?: number;
  
  /**
   * 行高
   * @default 16
   */
  rowHeight?: number;
  
  /**
   * 行间距
   * @default 12
   */
  rowGap?: number;
  
  /**
   * 行宽度，支持数组指定每行宽度，或者函数动态计算
   * @default '100%'
   */
  rowWidth?: string | number | Array<string | number> | ((index: number) => string | number);
  
  /**
   * 是否显示头像
   * @default false
   */
  avatar?: boolean;
  
  /**
   * 头像大小
   * @default 40
   */
  avatarSize?: number;
  
  /**
   * 头像形状
   * @default 'circle'
   */
  avatarShape?: 'circle' | 'square';
  
  /**
   * 是否使用渐变效果
   * @default true
   */
  gradient?: boolean;
  
  /**
   * 自定义渐变颜色
   */
  gradientColors?: [string, string, string];
  
  /**
   * 布局方向
   * @default 'column'
   */
  direction?: 'row' | 'column' | 'row-reverse';
  
  /**
   * 自定义样式
   */
  style?: React.CSSProperties;
}

/**
 * 骨架屏组合组件
 * 
 * 支持自动生成多行骨架屏、头像等复杂布局
 */
const SkeletonGroup: React.FC<SkeletonGroupProps> = ({
  animated = true,
  className,
  children,
  rows = 0,
  rowHeight = 16,
  rowGap = 12,
  rowWidth = '100%',
  avatar = false,
  avatarSize = 40,
  avatarShape = 'circle',
  gradient = true,
  gradientColors,
  direction = 'column',
  style,
}) => {
  // 生成行骨架
  const renderRows = () => {
    if (rows <= 0) return null;
    
    return Array(rows)
      .fill(null)
      .map((_, index) => {
        let width: string | number = '100%';
        
        // 处理行宽度
        if (Array.isArray(rowWidth)) {
          width = rowWidth[index % rowWidth.length] || '100%';
        } else if (typeof rowWidth === 'function') {
          width = rowWidth(index);
        } else {
          width = rowWidth;
        }
        
        // 最后一行宽度短一些
        if (index === rows - 1 && typeof width === 'string' && width === '100%' && rows > 1) {
          width = '60%';
        }
        
        return (
          <CySkeleton
            key={`skeleton-row-${index}`}
            width={width}
            height={rowHeight}
            animated={animated}
            gradient={gradient}
            gradientColors={gradientColors}
            className={styles['skeleton-group-row']}
            style={{ marginBottom: index === rows - 1 ? 0 : rowGap }}
          />
        );
      });
  };

  // 渲染头像
  const renderAvatar = () => {
    if (!avatar) return null;
    
    return (
      <CySkeleton
        width={avatarSize}
        height={avatarSize}
        shape={avatarShape}
        animated={animated}
        gradient={gradient}
        gradientColors={gradientColors}
        className={styles['skeleton-group-avatar']}
      />
    );
  };

  return (
    <View
      className={classNames(
        styles['skeleton-group'],
        styles[`skeleton-group-${direction}`],
        className
      )}
      style={style}
    >
      {renderAvatar()}
      
      <View className={styles['skeleton-group-content']}>
        {renderRows()}
        {children}
      </View>
    </View>
  );
};

export default SkeletonGroup; 