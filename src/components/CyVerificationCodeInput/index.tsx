import React, { FC, useState, useRef, useEffect, forwardRef, useImperativeHandle } from 'react';
import { View, Text, Input } from '@tarojs/components';
import Taro from '@tarojs/taro';
import CyInput from '../CyInput';
import CyVerificationCodeButton, {CyVerificationRef} from '../CyVerificationCodeButton';
import styles from './index.module.scss';
import classNames from 'classnames';
import { showToast } from '~utils/common';

interface Props {
  className?: string;
  phone: string;  // 手机号
  onVerify?: (code: string) => void;  // 验证码验证回调
  onSendCode?: () => Promise<boolean>;  // 发送验证码回调
  validTime?: number;  // 验证码有效时间（分钟）
  noHeader?: boolean;  // 是否显示头部
  linkStyleBtn?: boolean;  // 是否使用链接样式的按钮
  inputWrapperBg?: string;
}

// 定义组件暴露的方法
export interface CyVerificationCodeInputRef {
  clearCode: (success: boolean) => void; // 清空验证码
  focus: () => void; // 聚焦到隐藏输入框
  sendCode: () => void;
  code: string; // 验证码
}

/**
 * 组件名称：验证码输入框
 * 组件描述：手机验证码输入组件
 */
const CyVerificationCodeInput = forwardRef<CyVerificationCodeInputRef, Props>((
  {
    className,
    phone,
    onVerify,
    onSendCode,
    validTime = 10,
    noHeader,
    linkStyleBtn,
    inputWrapperBg,
  }, ref
) => {
  // 存储验证码字符串
  const [code, setCode] = useState<string>('');
  // 存储每个位置的数字，使用数组便于在中间位置插入空值
  const [codeArray, setCodeArray] = useState<(string | null)[]>([null, null, null, null, null, null]);
  const [isSending, setIsSending] = useState<boolean>(false);
  const [sendSuccess, setSendSuccess] = useState<boolean>(false);
  const [isFocused, setIsFocused] = useState<boolean>(false); // 输入框是否聚焦
  const [cursorPosition, setCursorPosition] = useState<number>(0); // 当前光标位置
  const hiddenInputRef = useRef<any>(null);
  const sendCodeButtonRef = useRef<CyVerificationRef>(null);

  // 更新code字符串，用于验证和提交
  useEffect(() => {
    // 将codeArray转换为字符串，过滤掉null值
    const codeString = codeArray.filter(c => c !== null).join('');
    setCode(codeString);
  }, [codeArray]);

  // 暴露方法给父组件
  useImperativeHandle(ref, () => ({
    clearCode: (success?: boolean) => {
      setTimeout(() => {
        setCode('');
        setCodeArray([null, null, null, null, null, null]);
        setCursorPosition(0);
        success && sendCodeButtonRef.current?.resetTimer();
      }, 150);
    },
    focus: () => {
      // 聚焦到隐藏输入框
      hiddenInputRef.current?.focus();
    },
    sendCode: () => {
      sendCodeButtonRef.current?.handleSend();
    },
    code: code
  }));

  // 当验证码长度为6时，触发验证回调
  useEffect(() => {
    if (code.length === 6) {
      if (sendSuccess) {
        // 只有在成功发送验证码后才触发验证回调
        onVerify?.(code);
      } else {
        // 未发送验证码时，提示用户先获取验证码
        showToast('请先获取验证码');
        setTimeout(() => {
          // 清空输入
          setCode('');
          setCodeArray([null, null, null, null, null, null]);
          setCursorPosition(0);
        }, 150);
      }
      Taro.hideKeyboard();
    }
  }, [code, sendSuccess]);

  // 处理隐藏输入框变化
  const handleInputChange = (e: any) => {
    const value = e.detail.value;
    
    // 只允许输入数字
    const numericValue = value.replace(/\D/g, '');
    
    // 复制当前的codeArray
    const newCodeArray = [...codeArray];
    
    // 如果是删除操作
    if (numericValue.length < code.length) {
      // 检查当前光标位置是否有字符
      if (cursorPosition < 6 && newCodeArray[cursorPosition] !== null) {
        // 当前位置非空，删除当前位置的字符，光标不移动
        newCodeArray[cursorPosition] = null;
        // 光标位置保持不变
        setCursorPosition(cursorPosition);
      } else {
        // 当前位置为空，向前移动光标并删除前一个字符
        const newPosition = Math.max(0, cursorPosition - 1);
        if (newPosition < 6) {
          newCodeArray[newPosition] = null;
          // 光标位置保持在删除的位置
          setCursorPosition(newPosition);
        }
      }
    } 
    // 如果是输入操作
    else if (numericValue.length > code.length) {
      const inputChar = numericValue.charAt(numericValue.length - 1);
      // 在当前光标位置插入字符
      if (cursorPosition < 6) {
        newCodeArray[cursorPosition] = inputChar;
        
        // 更新光标位置
        let nextPosition = cursorPosition + 1;
        
        // 检查是否有空位
        const emptyPositions = newCodeArray.map((val, idx) => val === null ? idx : -1).filter(idx => idx !== -1);
        
        // 如果有空位，自动聚焦到第一个空位
        if (emptyPositions.length > 0) {
          nextPosition = emptyPositions[0];
        }
        
        setCursorPosition(Math.min(6, nextPosition));
      }
    }
    
    setCodeArray(newCodeArray);
  };

  // 处理输入框聚焦
  const handleInputFocus = () => {
    setIsFocused(true);
  };

  // 处理输入框失焦
  const handleInputBlur = () => {
    setIsFocused(false);
  };

  // 处理显示区域点击
  const handleDisplayClick = () => {
    hiddenInputRef.current?.focus();
  };

  // 处理单个格子点击
  const handleDigitClick = (index: number) => {
    setCursorPosition(index);
    hiddenInputRef.current?.focus();
  };

  // 发送验证码
  const handleSendCode = async () => {
    if (isSending) return;
    
    setIsSending(true);
    try {
      const success = await onSendCode?.() || false;
      setSendSuccess(success);
      
      // 如果发送成功，聚焦到隐藏输入框
      if (success) {
        hiddenInputRef.current?.focus();
        // 设置光标位置为当前code长度
        const firstEmptyIndex = codeArray.findIndex(c => c === null);
        setCursorPosition(firstEmptyIndex !== -1 ? firstEmptyIndex : codeArray.length);
      }
    } catch (error) {
      console.error('发送验证码失败', error);
    } finally {
      setIsSending(false);
    }
  };

  // 格式化手机号，中间4位用*代替
  const formatPhone = (phone: string) => {
    if (!phone || phone.length < 11) return phone;
    return `${phone.slice(0, 3)}****${phone.slice(7)}`;
  };

  return (
    <View className={`${styles.container} ${className || ''}`}>
      {
        noHeader ? null : (
          <>
            <View className={styles.title}>请输入手机验证码</View>
            <View className={styles.subtitle}>
              请输入发送至 {formatPhone(phone)} 的6位验证码，有效期{validTime}分钟
            </View>
          </>
        )
      }
      
      {/* 隐藏的输入框 */}
      <Input
        className={styles.hiddenInput}
        type="number"
        maxlength={6}
        value={code}
        onInput={handleInputChange}
        onFocus={handleInputFocus}
        onBlur={handleInputBlur}
        ref={hiddenInputRef}
        cursor-spacing="400rpx" 
        always-embed={true}
        style={{
          position: 'absolute',
          left: '-9999px',
          opacity: 0,
          pointerEvents: 'none'
        }}
      />
      
      {/* 显示区域 */}
      <View className={styles.codeInputContainer} onClick={handleDisplayClick}>
        {Array(6).fill(null).map((_, index) => (
          <View 
            key={index} 
            className={`${styles.inputWrapper} ${
              isFocused && cursorPosition === index ? styles.focused : ''
            }`}
            style = {{
              ...(inputWrapperBg ? {backgroundColor: inputWrapperBg} : {})
            }}
            onClick={(e) => {
              e.stopPropagation();
              handleDigitClick(index);
            }}
          >
            <Text className={styles.codeText}>
              {codeArray[index] || ''}
            </Text>
          </View>
        ))}
      </View>
      
      <View className={classNames(styles.buttonContainer, linkStyleBtn ? styles.linkButtonContainer : '')}>
        <CyVerificationCodeButton
          ref={sendCodeButtonRef}
          sendSuccess={sendSuccess}
          onSend={handleSendCode}
          onCountdownEnd={() => setSendSuccess(false)}
          type={linkStyleBtn? 'text' : 'button'}
        />
      </View>
    </View>
  );
});

export default CyVerificationCodeInput;