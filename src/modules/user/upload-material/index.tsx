import {useState} from 'react';
import {Image, ScrollView, Text, View} from '@tarojs/components';
import Taro, {getFileSystemManager, useLoad} from '@tarojs/taro';
import {CyButton, CySafeArea} from 'hncy58-taro-components';
import Card from '~components/Card';
import PageWrap from '~components/PageWrap';
import removeIcon from '~images/credit-apply/<EMAIL>';
import {uploadDocument} from '~services/document';
import {getFileExtension} from '~utils/photo';
import styles from './index.module.scss';
import { hideLoading, router, showLoading, showToast } from '~utils/common';
import { removeBase64Prefix } from '~utils/string';

const DOC_TYPE_MAP: Partial<Record<API.DocType, {title: string, uplpadDesc: string}>> = {
  'E03': {
    title: '客户经理合影',
    uplpadDesc: '上传/拍摄合影照片'
  },
  'E28': {
    title: '资产信息',
    uplpadDesc: '上传/拍摄资产证明照片'
  },
  'E29': {
    title: '外部结清证明',
    uplpadDesc: '上传/拍摄结清证明照片'
  },
  'E21': {
    title: '其他资料',
    uplpadDesc: '上传/拍摄照片'
  },
}

const UploadManagerPhoto = () => {
  // const [uploadedPhotos, setUploadedPhotos] = useState<string[]>([]);
  const [files, setFiles] = useState<string[]>([]); // 存储上传的文件信息
  const [curUploadInfo, setCurUploadInfo] = useState<{
    type: API.DocType,
    title: string; 
    uplpadDesc: string;
  }>();

  const maxUploads = 5;
  // const remainingUploads = maxUploads - uploadedPhotos.length;

  const handleUpload = () => {
    if (files.length >= maxUploads) {
      Taro.showToast({
        title: `最多上传${maxUploads}张照片`,
        icon: 'none',
      });
      return;
    }

    Taro.chooseImage({
      count: maxUploads - files.length,
      success: async (res) => {
        const tempFilePaths = res.tempFilePaths;
        showLoading();
        await Promise.all(tempFilePaths.map((filePath) => {
          console.log('filePath====', filePath)
          return new Promise((resolve, reject) => {
            const extName = getFileExtension(filePath);
            getFileSystemManager().readFile({
              filePath: filePath,
              encoding: 'base64',
              success: async fileRes => {
                const base64Str = `data:image/${extName};base64,${fileRes.data}`;
                setFiles((curFiles) => [...curFiles, base64Str]);
                resolve(true)
              },
              fail: () => {
                reject(false)
              }
            });
          })
        }))
        hideLoading()
      },
    });
  };

  const handleDelete = (index: number) => {
    const newPhotos = files.filter((_, i) => i !== index);
    setFiles(newPhotos);
  };

  const handleSubmit = async () => {
    showLoading()
    try {
      await uploadDocument({
        docType: curUploadInfo?.type,
        files: files.map(b64 => removeBase64Prefix(b64)),
        eventCode: 'SAVE_CUSTOMER_DOCUMENT',
      });
      hideLoading()
      showToast({
        type: 'success',
        message: '提交成功',
        duration: 2000,
      })
      setTimeout(() => {
        router.back()
      }, 2000)
    } catch(e) {
      console.log('upload material errr', e)
      hideLoading()
    }
  };

  const handlePreview = (idx: number) => {
    Taro.previewImage({
      current: files[idx],
      urls: files,
    });
  };

  useLoad((query) => {
    const curType = DOC_TYPE_MAP[query.type as API.DocType] ;
    console.log('upload material', query, curType)
    if(curType?.title) {
      setCurUploadInfo({
        type: query.type,
        title: curType.title,
        uplpadDesc: curType.uplpadDesc
      })
    }
  })

  return (
    <PageWrap className={styles.page}>
      <View className={styles.container}>
        {/* 上传卡片 */}
        <Card title={curUploadInfo?.title} note='请确保上传的照片清晰可见'>
          <View onClick={handleUpload}>
            <View className={styles.uploadArea}>
              <Image
                src='https://via.placeholder.com/80x60/f0f0f0/cccccc?text=Preview'
                className={styles.previewPlaceholder}
              />
            </View>

            <View className={styles.uploadButton}>
              <Text className={styles.uploadButtonText}>+ {curUploadInfo?.uplpadDesc}</Text>
            </View>
          </View>
        </Card>

        {/* 已上传照片卡片 */}
        <Card className={styles.card}>
          <Text className={styles.title}>已上传照片</Text>
          <View className={styles.subtitle}>
            今日已上传 <Text className={styles.highlight}>{files.length}</Text> 张, 还可以上传{' '}
            <Text className={styles.highlight}>{maxUploads - files.length}</Text> 张
          </View>

          <ScrollView scrollX>
            <View className={styles.photoGrid}>
              {files.map((photo, index) => (
                <View key={index} className={styles.photoWrapper} onClick={() => handlePreview(index)}>
                  <Image src={photo} className={styles.photoThumbnail} mode='aspectFit' />
                  <Image
                    src={removeIcon}
                    className={styles.deleteButton}
                    onClick={e => {
                      e.stopPropagation();
                      handleDelete(index);
                    }}></Image>
                </View>
              ))}
            </View>
          </ScrollView>
        </Card>
      </View>

      <View className={styles.footer}>
        <CyButton round type='primary' block onClick={handleSubmit} disabled={files.length === 0}>
          提交
        </CyButton>
        <CySafeArea style={{background: 'transparent'}} position='bottom'></CySafeArea>
      </View>
    </PageWrap>
  );
};

export default UploadManagerPhoto;
