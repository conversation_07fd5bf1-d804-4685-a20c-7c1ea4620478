module.exports = {
  tabWidth: 2, // 每个缩进级别使用 2 个空格
  useTabs: false, // 不使用制表符，只使用空格
  arrowParens: 'avoid',
  bracketSameLine: false,
  bracketSpacing: false,
  singleQuote: true,
  trailingComma: 'all',
  jsxSingleQuote: true,
  printWidth: 120,
  importOrder: ["^react(.*)$", "^@", "^[a-z]", "^~" ,"^[./]"],
  importOrderSeparation: false,
  importOrderSortSpecifiers: true,
  jsxBracketSameLine: true,
  endOfLine: 'lf',
  plugins: [
    '@trivago/prettier-plugin-sort-imports'
  ],
  braceStyle: "1tbs"
};