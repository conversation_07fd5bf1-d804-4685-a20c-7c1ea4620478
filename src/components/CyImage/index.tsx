import React, {FC, useState} from 'react';
import {Image, ImageProps} from '@tarojs/components';
import namedPng from '~images/transparent.png';
import './index.scss';

type Props = {
  placeholderConponent?: boolean;
  placeholderUrl?: string;
  clickReloadWhenError?: boolean;
};

/**
 * 组件名称：图片组件
 * 组件描述：图片组件
 */
const CyImage: FC<Props & ImageProps> = (props: Props & ImageProps) => {
  const {placeholderConponent, placeholderUrl, clickReloadWhenError} = props;

  const [showPlaceholder, setShowPlaceholder] = useState(true);
  const [reloadFlag, setReloadFlag] = useState(0);

  const error = (e: any) => {
    console.log('加载图片失败', e);
    setShowPlaceholder(true);
  };

  const placeholderClick = () => {
    if (clickReloadWhenError) {
      setReloadFlag(flag => flag + 1);
    }
  };

  const click = () => {};

  const load = () => {
    setShowPlaceholder(false);
  };

  //成功加载后隐藏占位
  let placeholderClassName = showPlaceholder ? '' : 'hidden';
  if (props.className) {
    placeholderClassName = `${placeholderClassName} ${props.className}`;
  }

  //加载前假隐藏真实图片
  let realClassName = showPlaceholder ? 'faker_hidden' : '';
  if (!showPlaceholder && props.className) {
    realClassName = `${realClassName} ${props.className}`;
  }
  let url = props.src;

  if (clickReloadWhenError) {
    url = `${props.src}?r=${reloadFlag}`;
  }

  return !placeholderConponent ? (
    <Image {...props} onClick={click} onLoad={load} style={props.style}></Image>
  ) : (
    <>
      <Image
        {...props}
        className={placeholderClassName}
        src={placeholderUrl ? placeholderUrl : namedPng}
        onClick={placeholderClick}
        style={props.style}></Image>
      <Image
        {...props}
        className={realClassName}
        src={url}
        onError={error}
        onClick={click}
        onLoad={load}
        style={showPlaceholder ? {} : props.style}></Image>
    </>
  );
};

export default CyImage;
