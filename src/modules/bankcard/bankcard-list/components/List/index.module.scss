.listLayout {
  display: flex;
  flex-direction: column;
  padding: 0 40px;
  min-height: calc(100vh - 40px);
  box-sizing: border-box;
}

.listTop {
  display: flex;
  justify-content: space-between;
  align-items: center;
  line-height: 1;
  padding: 32px 0 40px 0;
  .listTitle {
    color: $color-text-normal;
    font-size: $font-size-normal;
    display: flex;
    .listTag {
      display: flex;
      align-items: center;
      justify-content: center;
      color: #fff;
      font-size: 18px;
      background: linear-gradient(90deg, #545484 1%, #313153 100%);
      border-radius: 16px;
      width: 140px;
      height: 32px;
      margin-left: 10px;
      .infoIcon {
        width: 16px;
        height: 16px;
        margin-right: 8px;
      }
    }
  }
  .listNum {
    color: $color-text-secondary;
    font-size: $font-size-sm;
  }
}

.listContent {
  flex: 1;
  // overflow: auto;
}
.listScroll {
  height: 100%;
}