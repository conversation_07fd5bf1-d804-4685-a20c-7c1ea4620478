/**
 * cfca 键盘初始化的配置文件
 */
let completeKeyboard = null;
let numberKeyboard = null;

function initInputNum() {
  for (let i = 0; i < arguments.length; i++) {
    $(`#${arguments[i]}`).removeAttr('readonly'); //移除键盘的readonly属性，防止对安全键盘产生影响
    initNumberKeyboard(arguments[i]);
    initSipBoxNum(arguments[i]);
  }
}

function initNumberKeyboard(SIPBox) {
  if (numberKeyboard == null) {
    numberKeyboard = new CFCAKeyboard(KEYBOARD_TYPE_NUMBER);
  }
  if ('tradePasswordInput' == SIPBox) {
    numberKeyboard.setDoneCallback(doneCallBack);
  }
  numberKeyboard.bindInputBox(SIPBox);
  setProperty(SIPBox);
  numberKeyboard.hideKeyboard();
}

function doneCallBack(SIPBox) {
  document.getElementById('tradePasswordDiv').style.display = 'none';
  let pswd = null;
  let pswdRc = null;
  let encryptedInputValue = numberKeyboard.getEncryptedInputValue('tradePasswordInput');
  let errorCode = numberKeyboard.getErrorCode('tradePasswordInput').toString(16);
  if (errorCode == CFCA_OK) {
    pswd = encryptedInputValue;
  } else if ((errorCode = '1003')) {
    alert('请输入6位交易密码', function () {
      document.getElementById('tradePasswordDiv').style.display = 'block';
      $('#tradePasswordInput').trigger('focus');
    });
    return false;
  } else {
    alert('加载异常');
    return false;
  }
  let encryptedClientRandom = numberKeyboard.getEncryptedClientRandom('tradePasswordInput');
  errorCode = numberKeyboard.getErrorCode('tradePasswordInput').toString(16);
  if (errorCode != CFCA_OK) {
    alert('加载异常');
    return false;
  } else {
    pswdRc = encryptedClientRandom;
  }
  console.log(`${pswd}--${pswdRc}`);
  loading.open('校验中，请稍候');
  $ajaxSubmit(
    '/frontend/tradepassword/tradePassword/checkTradePassword',
    {
      pswd,
      pswdRc,
    },
    function (json) {
      if (json.status == 'success') {
        $('#pswd').val(pswd);
        $('#pswdRc').val(pswdRc);
        sessionStorage.setItem($('#mobile').val(), '1');
        loading.close();
        let isNoModifySubmit = $('#isNoModifySubmit');
        let repaymentDateIsNoModifySubmit = $('#repaymentDateIsNoModifySubmit');
        let rateCutAcknowIsNoModifySubmit = $('#rateCutAcknowIsNoModifySubmit');
        if (!isEmpty(isNoModifySubmit)) {
          isAllowReservationRate();
        } else if (!isEmpty(repaymentDateIsNoModifySubmit)) {
          isAllowPrepareRepaymentDate();
        } else if (!isEmpty(rateCutAcknowIsNoModifySubmit)) {
          isAllowRateCutAcknow();
        } else {
          document.forms[0].submit();
        }
      } else if (json.status == 'fail') {
        loading.close();
        if (json.isContinuedInputTradePassword == 'false' || json.isContinuedInputTradePassword == false) {
          expirTime = json.tradePasswordExpirTime.split(' ');
          let date = expirTime[0].split('-');
          let time = expirTime[1].split(':');
          let a = getdaysinmonth(`${date[0]}-${date[1]}`);
          let curr = parseInt(date[2]) + 1;
          let currMonth;
          let currDays;
          if (curr > a) {
            currMonth = parseInt(date[1]) + 1;
            currDays = 1;
          } else {
            currMonth = date[1];
            currDays = parseInt(date[2]) + 1;
          }
          confirm(
            '提示',
            `非常抱歉， 您已输入 5 次错误密码信息，为保障您的资金交易安全， 暂停输入交易密码， 直到${date[0]}年${currMonth}月${currDays}日${time[0]}时${time[1]}分。您也可前往找回密码立即解锁！ `,
            [
              {
                txt: '确定',
                color: true,
                callback() {
                  window.location.href = '/frontend/loan/salariatLoan/creditInfo';
                },
              },
              {
                txt: '找回密码',
                color: false,
                callback() {
                  window.location.href = '/frontend/tradepassword/tradePassword/toPasswordAdmin';
                },
              },
            ],
          );
        } else {
          // $dialog.loading.close();
          alert(`密码输入错误，剩余输入次数${json.effectNum}次！`, function () {
            document.getElementById('tradePasswordDiv').style.display = 'block';
            $('#tradePasswordInput').trigger('focus');
            numberKeyboard.clearInputValue(SIPBox);
          });
        }
      } else {
        alert(json.message, function () {
          // $dialog.loading.close();
          document.getElementById('tradePasswordDiv').style.display = 'block';
          $('#tradePasswordInput').trigger('focus');
          numberKeyboard.clearInputValue(SIPBox);
        });
      }
    },
  );
}

function initSipBoxNum(sipboxId) {
  let sipBox = document.getElementById(sipboxId);
  setUpEvent(sipBox, 'focus', function (event) {
    sipBox.blur();
    numberKeyboard.bindInputBox(sipboxId);
    let serverRandom = document.getElementById('serverRandom').getAttribute('value');
    if (CFCA_OK != numberKeyboard.setServerRandom(serverRandom, sipboxId)) {
      alert('加载异常');
      return false;
    }
    if (sipBox.id.substring(0, 6) == 'SIPBox') {
      $('.click-tips-font').removeClass('click-tips-font');
      $('.click-tips-bg').removeClass('click-tips-bg');
      $('.click-tips-img').removeAttr('style');
      //添加对应的样式
      let elem = document.getElementById(sipBox.id);
      let parent = elem.parentNode.parentNode;
      parent.children[0].style.display = 'block';
      parent.children[1].className += ' click-tips-font';
      parent.children[2].className += ' click-tips-bg';
    }
    numberKeyboard.showKeyboard();
  });
}

function initInputComplete() {
  for (let i = 0; i < arguments.length; i++) {
    initCompleteKeyboard(arguments[i]);
    initSipBoxComplete(arguments[i]);
  }
}

function initCompleteKeyboard(SIPBox) {
  if (completeKeyboard == null) {
    completeKeyboard = new CFCAKeyboard(KEYBOARD_TYPE_COMPLETE);
  }
  completeKeyboard.bindInputBox(SIPBox);
  setProperty(SIPBox);
  completeKeyboard.hideKeyboard();
}

function initSipBoxComplete(sipboxId) {
  let sipBox = document.getElementById(sipboxId);
  setUpEvent(sipBox, 'focus', function (event) {
    sipBox.blur();
    completeKeyboard.bindInputBox(sipboxId);
    let serverRandom = document.getElementById('serverRandom').getAttribute('value');
    if (CFCA_OK != completeKeyboard.setServerRandom(serverRandom, sipboxId)) {
      alert('加载异常');
      return false;
    }
    if (sipBox.id.substring(0, 6) == 'SIPBox') {
      $('.click-tips-font').removeClass('click-tips-font');
      $('.click-tips-bg').removeClass('click-tips-bg');
      $('.click-tips-img').removeAttr('style');
      //添加对应的样式
      let elem = document.getElementById(sipBox.id);
      let parent = elem.parentNode.parentNode;
      parent.children[0].style.display = 'block';
      parent.children[1].className += ' click-tips-font';
      parent.children[2].className += ' click-tips-bg';
      completeKeyboard.showKeyboard();
    }
  });
}

function getVersion() {
  alert(`Version: ${getCFCAKeyboardVersion()}`);
}

function setUpEvent(elem, eventType, handler) {
  return elem.attachEvent
    ? elem.attachEvent(`on${eventType}`, handler)
    : elem.addEventListener
      ? elem.addEventListener(eventType, handler, false)
      : null;
}

setUpEvent(document, 'touchstart', function (e) {
  let elem = e.srcElement || e.target;
  let noNeedHideIds = [
    'CompleteKeyboard',
    'NumberKeyboard',
    'SIPBox1',
    'SIPBox2',
    'SIPBox3',
    'tradePasswordInput',
    'clearSIPBox1',
    'clearSIPBox2',
    'clearSIPBox3',
    'getSIPBox1Value',
    'getSIPBox2Value',
    'getSIPBox3Value',
    'checkInputValueMatch',
    'getVersion',
    'mobile',
    'imgPreviewMask',
  ];
  while (elem) {
    if (noNeedHideIds.indexOf(elem.id) !== -1) {
      return;
    }
    elem = elem.parentNode;
  }
  if (numberKeyboard != undefined && numberKeyboard != null) {
    numberKeyboard.hideKeyboard();
  }
});

function setProperty(sipboxId) {
  let keyboard = getKeyboard(sipboxId);
  keyboard.bindInputBox(sipboxId);
  let serverRandom = document.getElementById('serverRandom').getAttribute('value');
  if (!serverRandom) {
    alert('页面加载失败 ');
    return false;
  }
  if (CFCA_OK != keyboard.setMinLength(6, sipboxId)) {
    alert('页面加载失败');
    return false;
  }
  if (CFCA_OK != keyboard.setMaxLength(6, sipboxId)) {
    alert('页面加载失败');
    return false;
  }
  if (CFCA_OK != keyboard.setOutputType(OUTPUT_TYPE_ORIGINAL, sipboxId)) {
    alert('页面加载失败');
    return false;
  }
  if (CFCA_OK != keyboard.setEncryptState(true, sipboxId)) {
    alert('页面加载失败');
    return false;
  }
  if (CFCA_OK != keyboard.setServerRandom(serverRandom, sipboxId)) {
    alert('页面加载失败');
    return false;
  }
  if (CFCA_OK != keyboard.setCipherType(CIPHER_TYPE_RSA, sipboxId)) {
    alert('页面加载失败');
    return false;
  }
  if (CFCA_OK != keyboard.setRandomType(KEYBOARD_DISORDER_NONE, sipboxId)) {
    alert('页面加载失败');
    return false;
  }
}
function getKeyboard(sipboxId) {
  numberKeyboard.bindInputBox(sipboxId);
  return numberKeyboard;
}

// function $dialog(){
// 	function alert(message,func){
// 		console.log(message);
// 		func()
// 	}
// }
