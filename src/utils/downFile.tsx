import Taro, {getFileSystemManager} from '@tarojs/taro';

interface DownloadResult {
  success: boolean;
  message: string;
  filePath?: string;
}

/**
 * 文件下载并打开
 * @param fileData 文件流数据
 * @param fileName 文件名
 * @param fileType 文件类型
 * @returns Promise<DownloadResult>
 */
export const downLoadFile = async (
  fileData: string | Blob,
  fileName: string,
  fileType: string,
): Promise<DownloadResult> => {
  try {
    // 1. 检查文件是否已存在
    const fs = getFileSystemManager();
    const filePath = `${Taro.env.USER_DATA_PATH}/${fileName}`;
    console.log('filePath:', filePath);
    try {
      // 检查文件是否存在
      fs.accessSync(filePath);
      // 文件存在,直接打开
      await openFile(filePath, fileType);
      return {
        success: true,
        message: '文件打开成功',
        filePath,
      };
    } catch (error) {
      // 文件不存在,需要下载
      console.log('文件不存在,开始下载');
    }

    // 2. 将文件流写入临时文件
    if (typeof fileData === 'string') {
      fs.writeFileSync(filePath, fileData, 'base64');
    } else {
      // Blob类型直接写入
      const reader = new FileReader();
      reader.readAsArrayBuffer(fileData);
      await new Promise((resolve, reject) => {
        reader.onload = () => {
          try {
            fs.writeFileSync(filePath, reader.result);
            resolve(true);
          } catch (err) {
            reject(err);
          }
        };
        reader.onerror = reject;
      });
    }

    // 3. 打开文件
    await openFile(filePath, fileType);

    return {
      success: true,
      message: '文件下载并打开成功',
      filePath,
    };
  } catch (error: any) {
    console.error('文件下载或打开失败:', error);
    return {
      success: false,
      message: '文件下载或打开失败: ' + error.message,
    };
  }
};

/**
 * 打开文件
 * @param filePath 文件路径
 */
const openFile = async (filePath: string, fileType: any): Promise<void> => {
  try {
    await Taro.openDocument({
      filePath,
      fileType,
      showMenu: true,
    });
  } catch (error: any) {
    throw new Error('文件打开失败: ' + error.message);
  }
};

export default downLoadFile;
