import { Image, View, ViewProps } from '@tarojs/components';
import styles from './index.module.scss';
import bankcardIcon from '~images/bankcard/<EMAIL>';
import upgradeIcon from '~images/bankcard/<EMAIL>';

interface TListProps extends API.IBankCardInfo {
  isDetail?: boolean;
  onClick?: () => void;
}

export default (props: TListProps & ViewProps) => {
  const { isDetail = false } = props;

  const singleLimitText = (props.singleLimit / 10000).toFixed(2);
  const dayLimitText = (props.dayLimit / 10000).toFixed(2);

  const toDetail = (e: any) => {
    e.stopPropagation();
    if (!isDetail) {
      props.onClick?.();
    }
  };

  return (
    <View className={styles.itemContainer}>
      <View
        className={`${styles.bankcardListItem}`}
        style={{ backgroundImage: `url(${props.bankBackgroundImgUrl})` }}
        onClick={toDetail}>
        <View className={styles.bankcardListItemLeft}>
          <Image src={props.bankIconImgUrl} className={styles.bankcardListItemImg} />
          <View className={styles.bankcardInfo}>
            <View className={styles.bankcardName}>
              <View className={styles.bankcardNameText}>{props.bankName}</View>
              {props.defaultFlag && <View className={styles.isDefault}>默认</View>}
            </View>
            <View className={styles.bankcardDesc}>
              {`单笔借款限额${singleLimitText}万, 单日借款限额${dayLimitText}万`}
            </View>
          </View>
        </View>
        <View className={styles.bankcardNo}>{props.desensitizeAccountNo}</View>
        {/* <Image src={bankcardIcon} className={styles.bankcardIcon} /> */}
        {!isDetail && <View className={styles.bankcardDetail}>详情 &gt;</View>}
        <View className={styles.itemBgContainer}></View>
      </View>

      {props.canUpgrade && <Image src={upgradeIcon} className={styles.upgradeIcon} />}
    </View>
  );
};