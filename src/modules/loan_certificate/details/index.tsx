import { useEffect, useState } from 'react';
import { Image, Text, View } from '@tarojs/components';
import { useRouter } from '@tarojs/taro';
import Taro from '@tarojs/taro';
import { CyButton } from 'hncy58-taro-components';
import { CyModal } from 'hncy58-taro-components';
import PageWrap from '~components/PageWrap';
import { getAllDetail, getLendingDetail } from '~services/loanCertificate';
import { router } from '~utils/common';
import downLoadFile from '~utils/downFile';
import styles from './index.module.scss';
import CySkeleton from '~components/CySkeleton';
const LoanCertificateDetails = () => {
  const { type, securityCode = '', contractCode = '', loanNo = '' } = useRouter().params;
  const [imgBase64, setImgBase64] = useState<string>();
  const [pdfBase64, setPdfBase64] = useState<string>();
  const [singleSecurityCode, setSingleSecurityCode] = useState<string>();
  const title = type === 'all' ? '全部结清证明' : '单笔结清证明';
  useEffect(() => {
    fetch();
  }, [type, securityCode, contractCode, loanNo]);

  useEffect(() => {
    if (type) {
      Taro.setNavigationBarTitle({ title });
    }
  }, [type]);

  const fetchWithRetry = async (apiCall: () => Promise<any>, retryDelay = 5000, maxRetries = 1) => {
    let retries = 0;

    const execute = async (): Promise<any> => {
      try {
        return await apiCall();
      } catch (error: any) {
        if (retries < maxRetries) {
          retries++;
          console.log(`生成中，${retryDelay / 1000}秒后重试...`);
          return new Promise(resolve => {
            setTimeout(() => {
              resolve(execute());
            }, retryDelay);
          });
        }
        throw error;
      }
    };

    return execute();
  };

  const fetchLendingDetail = async (loanNo: string) => {
    try {
      const res = await fetchWithRetry(() => getLendingDetail({ loanNo }));
      setImgBase64(res.imgBase64);
      setPdfBase64(res.pdfBase64);
      setSingleSecurityCode(res.securityCode);
    } catch (error: any) {
      const { message } = error;
      CyModal.create({
        title: '温馨提示',
        content: message.replace('failure:', ''),
        confirmText: '确定',
        onConfirm: () => {
          router.back();
        },
      });
    }
  };

  const fetch = async () => {
    try {
      Taro.showLoading({ title: '生成中...', mask: true });
      if (type === 'all') {
        if (securityCode && contractCode) {
          const res = await getAllDetail({ securityCode, contractCode });
          setImgBase64(res.imgBase64);
          setPdfBase64(res.pdfBase64);
        }
      } else {
        if (loanNo) {
          await fetchLendingDetail(loanNo);
        }
      }
    } catch (error) {
      console.log('获取失败', error);
    } finally {
      Taro.hideLoading();
    }
  };

  const pdfDownLoad = () => {
    if (pdfBase64) {
      const fileName = `${title}_${type === 'all' ? securityCode : singleSecurityCode}.pdf`;
      downLoadFile(pdfBase64, fileName, 'pdf').then(res => {
        console.log('res:', res);
      });
    }
  };

  return (
    <PageWrap className={styles['loan-certificate-wrapper']}>
      <View className={styles['loan-certificate-container']}>
        <View className={styles['loan-certificate-container-image']}>
          {
            imgBase64 ? (
              <Image mode='widthFix' src={`data:image/jpeg;base64,${imgBase64}`} style={{ width: '100%' }} />
            ) : (
              <CySkeleton height={'100%'} style={{ marginBottom: 10, flex: 1 }} />
            )
          }
        </View>
        <View className={styles['loan-certificate-container-content']}>
          <View className={styles['loan-certificate-container-content-item']}>
            <View className={styles['loan-certificate-container-content-item-left']}>
              <Text className={styles['loan-certificate-container-content-item-left-label']}>结清证明防伪码：</Text>
              <Text className={styles['loan-certificate-container-content-item-left-value']}>
                {type === 'all' ? securityCode : (singleSecurityCode ?? '-')}
              </Text>
            </View>
            <CyButton
              type='primary'
              size='small'
              round
              onClick={() =>
                Taro.setClipboardData({ data: type === 'all' ? securityCode : (singleSecurityCode ?? '-') })
              }>
              复制
            </CyButton>
          </View>
          <View className={styles['loan-certificate-container-content-item-text']}>
            关注“长银58金融”公众号，输入结清证明4个字，即可前往验证结清证明真伪。
          </View>
        </View>
        <CyButton
          type='primary'
          round
          block
          className={styles['loan-certificate-container-button']}
          onClick={pdfDownLoad}>
          下载含公章版
        </CyButton>
        <View className={styles['loan-certificate-container-button-text']}>
          当前预览的是无公章版本，下载后的文件含公章
        </View>
      </View>
    </PageWrap>
  );
};

export default LoanCertificateDetails;
