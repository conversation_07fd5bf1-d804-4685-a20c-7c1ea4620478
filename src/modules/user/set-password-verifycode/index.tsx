import { useEffect, useRef, useState } from 'react';
import { <PERSON><PERSON><PERSON>on, CySafeArea } from 'hncy58-taro-components';
import { Checkbox, CheckboxGroup, Image, Input, Picker, Text, View } from '@tarojs/components';
import Taro, { App, useLoad } from '@tarojs/taro';
import Card from '~components/Card';
import CellItem from '~components/CellItem';
import CellPickerItem from '~components/CellPickerItem';
import CyCheckbox from '~components/CyCheckbox';
import PageWrap from '~components/PageWrap';
import Steps, { Step } from '~components/Steps';
import contactIcon from '~icons/icon-clock.png';
import { getLabelByValue, router, showToast } from '~utils/common';
import styles from './index.module.scss';
import { checkVerifyCodeReq, sendVerifyCodeReq, updateContactInfo } from '~services/user'
import useDict from '~hooks/useDict';
import { IPickerItem, smsSceneType } from '~src/types/common-types';
import useUserStore from '~store/user';
import CyVerificationCodeInput, { CyVerificationCodeInputRef } from '~components/CyVerificationCodeInput';

const infoSteps: Step[] = [
  {
    title: '基本信息',
    status: 'process',
  },
  {
    title: '单位信息',
    status: 'process',
  },
  {
    title: '联系人信息',
    status: 'process',
  },
];

export default () => {
  const user = useUserStore((state) => state.user);
  const type = useRef<string>('');
  const verifyCodeInputRef = useRef<CyVerificationCodeInputRef>(null);

  useLoad((query) => {
    if (query.type) {
      type.current = query.type;
    }
    if(query.type === 'reset') {
      Taro.setNavigationBarTitle({
        title: '重置交易密码',
      });
    }
    if(query.immediate) {
      verifyCodeInputRef.current?.sendCode();
      verifyCodeInputRef.current?.focus();
    }
  })

  const sendVerifyCode = async () => {
    console.log('发送验证码====', user.mobile, smsSceneType.M1004)
    try {
      await sendVerifyCodeReq({smsSceneType: smsSceneType.M1005});
      return true;
    } catch (e) {
      console.error('发送验证码失败', e);
      return false;
    }
  };

  const handleVerify = async (code: string) => {
    try {
      await checkVerifyCodeReq({
        verificationCode: code,
        smsSceneType: smsSceneType.M1005,
      });
      verifyCodeInputRef.current?.clearCode(true)
      router.replace({
        url: '/modules/user/set-password/index' + (type.current ? `?type=${type.current}` : ''),
      });
    } catch (error) {
      console.error('验证码验证失败', error);
      verifyCodeInputRef.current?.clearCode(false)
    }
  }
  return (
    <PageWrap className={styles.setPasswordVerifycodePage}>
      <View className={styles.header}>
        <View className={styles.title}>请输入手机验证码</View>
        <View className={styles.subtitle}>请输入发送至<Text className={styles.mark}>{user.mobile || ''}</Text>的6位验证码</View>
      </View>
      <View className={styles.tip}>为保障您的资金安全，请勿向他人透露交易密码</View>
      <View className={styles.cardContent}>
        <CyVerificationCodeInput 
          inputWrapperBg='#fff'
          ref={verifyCodeInputRef}
          phone={user.mobile} 
          onVerify={handleVerify}
          onSendCode={sendVerifyCode}
          noHeader
          linkStyleBtn
        />
        <CyButton className={styles.btBtn} round block type='primary' onClick={() => {handleVerify(verifyCodeInputRef.current?.code || '')}}>
          下一步
        </CyButton>
      </View>
    </PageWrap>
  );
};
