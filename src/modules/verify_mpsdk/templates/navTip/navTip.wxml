<template name="navTip"><view id="{{Common.IsWxNative ? 'navTipNative' : 'navTip'}}" wx:if="{{isJustOcr || isJustSms}}"><view class="navText navActive" wx:if="{{curPage === 2 && isJustSms}}">{{navTitle.smsTitle}}</view><view class="navText navActive" wx:if="{{curPage === 3 && isJustOcr}}">{{navTitle.ocrTitle}}</view></view><view id="{{Common.IsWxNative ? 'navTipNative' : 'navTip'}}" wx:elif="{{curPage === 2}}"><view class="navText navActive">{{navTitle.smsTitle}}</view><view class="navLine"></view><view class="navText" wx:if="{{!isHideOcrPage}}">{{navTitle.ocrTitle}}</view><view class="navLine" wx:if="{{!isHideOcrPage}}"></view><view class="navText">{{navTitle.livingbodyTitle}}</view><view class="navLine"></view><view class="navText">{{navTitle.resultTitle}}</view></view><view id="{{Common.IsWxNative ? 'navTipNative' : 'navTip'}}" wx:elif="{{curPage === 3}}"><view class="navText navActive" wx:if="{{!isHideSmsPage}}">{{navTitle.smsTitle}}</view><view class="navLine" wx:if="{{!isHideSmsPage}}"></view><view class="navText navActive">{{navTitle.ocrTitle}}</view><view class="navLine"></view><view class="navText">{{navTitle.livingbodyTitle}}</view><view class="navLine"></view><view class="navText">{{navTitle.resultTitle}}</view></view><view id="{{Common.IsWxNative ? 'navTipNative' : 'navTip'}}" wx:elif="{{curPage === 4 && isHideSmsPage && isHideOcrPage}}" style="padding:38rpx 70px;"><view class="navText navActive">{{navTitle.livingbodyTitle}}</view><view class="navLine"></view><view class="navText">{{navTitle.resultTitle}}</view></view><view id="{{Common.IsWxNative ? 'navTipNative' : 'navTip'}}" wx:elif="{{curPage === 4}}"><view class="navText navActive" wx:if="{{!isHideSmsPage}}">{{navTitle.smsTitle}}</view><view class="navLine" wx:if="{{!isHideSmsPage}}"></view><view class="navText navActive" wx:if="{{!isHideOcrPage}}">{{navTitle.ocrTitle}}</view><view class="navLine" wx:if="{{!isHideOcrPage}}"></view><view class="navText navActive">{{navTitle.livingbodyTitle}}</view><view class="navLine"></view><view class="navText">{{navTitle.resultTitle}}</view></view><view id="{{Common.IsWxNative ? 'navTipNative' : 'navTip'}}" wx:elif="{{(curPage === 5 || curPage === 6) && isHideSmsPage && isHideOcrPage}}" style="padding:38rpx 70px;"><view class="navText navActive">{{navTitle.livingbodyTitle}}</view><view class="navLine"></view><view class="navText navActive">{{navTitle.resultTitle}}</view></view><view id="{{Common.IsWxNative ? 'navTipNative' : 'navTip'}}" wx:elif="{{curPage === 5 || curPage === 6}}"><view class="navText navActive" wx:if="{{!isHideSmsPage}}">{{navTitle.smsTitle}}</view><view class="navLine" wx:if="{{!isHideSmsPage}}"></view><view class="navText navActive" wx:if="{{!isHideOcrPage}}">{{navTitle.ocrTitle}}</view><view class="navLine" wx:if="{{!isHideOcrPage}}"></view><view class="navText navActive">{{navTitle.livingbodyTitle}}</view><view class="navLine"></view><view class="navText navActive">{{navTitle.resultTitle}}</view></view></template>