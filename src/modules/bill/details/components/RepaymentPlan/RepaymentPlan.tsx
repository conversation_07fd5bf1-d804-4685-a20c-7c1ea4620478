import {useCallback, useEffect, useState} from 'react';
import {Text, View} from '@tarojs/components';
import Taro from '@tarojs/taro';
import Card from '~components/Card';
import {RepaymentHistoryEnum, getLoanRepaymentHistory, getLoanRepaymentPlan} from '~services/bill';
import {TABS} from '../../const';
import PlanContent from '../PlanContent/PlanContent';
import RecordContent from '../RecordContent/RecordContent';
import styles from './RepaymentPlan.module.scss';

type TabOption = {
  key: string;
  label: string;
};

type TabsProps = {
  tabs: TabOption[];
  activeTab: string;
  onTabChange: (tab: string) => void;
};

type RepaymentPlanProps = {
  remainPrincipal: number;
  loanNo: string;
};

const Tabs = ({tabs, activeTab, onTabChange}: TabsProps) => {
  return (
    <View className={styles.tabs}>
      {tabs.map(tab => (
        <View
          key={tab.key}
          className={activeTab === tab.key ? styles.tabActive : styles.tab}
          onClick={() => onTabChange(tab.key)}>
          {tab.label}
        </View>
      ))}
    </View>
  );
};

const RepaymentPlan = ({remainPrincipal, loanNo}: RepaymentPlanProps) => {
  const [activeTab, setActiveTab] = useState('plan');
  const [planList, setPlanList] = useState<API.LoanPlan[]>([]);
  const [repaymentHistoryList, setRepaymentHistoryList] = useState<API.RepaymentRecord[]>([]);

  useEffect(() => {
    requestGetLoanRepaymentPlan();
    requestGetLoanRepaymentHistory();

    return () => {
      Taro.eventCenter.off('updateCurrentTerm');
    };
  }, [loanNo]);

  const requestGetLoanRepaymentPlan = () => {
    getLoanRepaymentPlan({loanNo}).then(res => {
      console.log('获取还款计划成功：', res);
      const currentTerm = res.schedules.filter(item => item.currentStatus).length;
      Taro.eventCenter.trigger('updateCurrentTerm', currentTerm);
      setPlanList(res.schedules);
    });
  };

  const requestGetLoanRepaymentHistory = () => {
    getLoanRepaymentHistory({loanNo, stateCategory: RepaymentHistoryEnum.SUCCESS}).then(res => {
      console.log('获取还款记录成功：', res);
      setRepaymentHistoryList(res.list);
    });
  };

  const renderContent = () => {
    switch (activeTab) {
      case 'plan':
        return (
          <View className={styles.planContent}>
            {planList.length > 0 ? (
              <PlanContent remainPrincipal={remainPrincipal} planList={planList} />
            ) : (
              <View className={styles.noRecord}>
                <Text>暂无还款计划</Text>
              </View>
            )}
          </View>
        );
      case 'record':
        return (
          <View className={styles.recordContent}>
            {repaymentHistoryList.length > 0 ? (
              <RecordContent repaymentHistoryList={repaymentHistoryList} />
            ) : (
              <View className={styles.noRecord}>
                <Text>暂无还款记录</Text>
              </View>
            )}
          </View>
        );
      default:
        return null;
    }
  };

  return (
    <Card className={styles.repaymentPlan}>
      <Tabs tabs={TABS} activeTab={activeTab} onTabChange={setActiveTab} />
      <View className={styles.content}>{renderContent()}</View>
    </Card>
  );
};

export default RepaymentPlan;
