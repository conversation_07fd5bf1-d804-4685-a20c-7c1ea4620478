import {forwardRef, Ref, useCallback, useEffect, useImperativeHandle, useState} from 'react';
import {Text} from '@tarojs/components';
import classNames from 'classnames';
import styles from './index.modules.scss';

interface Props {
  onClick?: () => void;
  immediate?: boolean;
}

export interface CountDownLinkBtnRef {
  startCountDown: () => void;
}

let timer: any;

export default forwardRef(({onClick, immediate}: Props, ref: Ref<CountDownLinkBtnRef>) => {
  const [countdown, setCountdown] = useState(59);
  const startCountDown = useCallback(() => {
    console.log('startCountDown====', countdown);
    setCountdown(59);
    timer = setInterval(() => {
      setCountdown(count => {
        if(count <= 0) {
          timer && clearInterval(timer);
          return 0;
        }
        return count - 1;
      });
    }, 1000);
  }, []);

  useEffect(() => {
    if (immediate) {
      startCountDown();
    }
    return () => {
      timer && clearInterval(timer);
    };
  }, []);
  const clilckHandler = (e: any) => {
    console.log('e', countdown);
    if (countdown <= 0) {
      onClick?.();
    }
  };
  // 暴露外部方法
  useImperativeHandle(ref, () => ({
    startCountDown,
  }));
  return (
    <Text
      className={classNames(styles.sendVerifyCodeBtn, {
        [styles.disabled]: countdown > 0,
      })}
      onClick={clilckHandler}>
      {countdown > 0 ? `${Math.round(countdown)}秒后重发` : '重新发送验证码'}
    </Text>
  );
});
