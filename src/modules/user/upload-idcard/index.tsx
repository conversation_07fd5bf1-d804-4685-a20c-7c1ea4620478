import { Image, Input, InputProps, Text, View } from "@tarojs/components";
import Card from "~components/Card";
import PageWrap from "~components/PageWrap"
import useUserStore from "~store/user"
import styles from './index.module.scss'
import TipsIcon from '~images/identify/tips.png'
import { CyButton, CyModal } from "hncy58-taro-components";
import { useRef, useState } from "react";
import Taro, { getFileSystemManager, useLoad } from "@tarojs/taro";
import { discernIdcard, saveIdcardInfo, validateIDcard } from "~services/picture";
import { formatDateYYYYMMDD, hideLoading, removeObjEmptyString, router, showLoading, showToast } from "~utils/common";
import SfzfImg from '~images/identify/<EMAIL>'
import SfzzImg from '~images/identify/<EMAIL>'
import photoIcon from '~icons/<EMAIL>'
import { validateByType } from "~utils/validate";
import goLendingPreProgress from "~utils/goLendingPreProgress";
import { uploadDocument } from "~services/document";

interface IDCardScanInfo {
    certId: string;
    name: string;
    validity: string;
}

const showPhotoTips = () => {
    CyModal.create({
      title: '温馨提示',
      content: (
        <View className={styles['modal-content']}>
          <View>1.选址光线充足的地方</View>
          <View>2.拍照时不要开闪光灯并准确对焦</View>
          <View>3.确保身份证边缘拍摄完整</View>
          <View>4.拍完后别忘了核实拍照信息哦～</View>
        </View>
      ),
    })
}


export default () => {
    const {user, logout} = useUserStore();
    const idcardFrontImg = useRef<string>();
    const idcardEndImg = useRef<string>();
    const frontBase64 = useRef<string>();
    const endBase64 = useRef<string>();
    const validityRef = useRef<InputProps>();
    const nameRef = useRef<InputProps>();
    const certIdRef = useRef<InputProps>();
    const [reconizeInfo, setReconizeInfo] = useState<API.IDiscernIdcardRes>({certId: '', name: '', validity: ''});
    const isFromLending = useRef(false)
    
    const changeReconizeInfo = (key: keyof IDCardScanInfo) => {
        setReconizeInfo({
          ...reconizeInfo,
          [key]: reconizeInfo[key],
        })
      }
    
    useLoad((query) => {
      if(query.from === 'lending') {
        isFromLending.current = true;
      }
    })
    
    
    const takePhoto = (type: API.DiscernIdcardIndex) => {
        showLoading();
        Taro.chooseImage({
          success(res) {
            const tempFilePaths = res.tempFilePaths;
            getFileSystemManager().readFile({
              filePath: tempFilePaths[0],
              encoding: 'base64',
              success: async (res) => {
                try {
                  const discernRes = await discernIdcard({
                    base64Picture: res.data as string,
                    idCardIndex: type,
                    photoEventType: 'idcardUpdate'
                  })
                  hideLoading();
                  if(discernRes) {
                    if(type === '1') {
                      idcardFrontImg.current = tempFilePaths[0];
                      frontBase64.current = res.data as string;
                    } else {
                      idcardEndImg.current = tempFilePaths[0];
                      endBase64.current = res.data as string;
                    }
                    const replaceInfo = removeObjEmptyString(discernRes);
      
                    replaceInfo.validity && validityRef.current && (validityRef.current.value = replaceInfo.validity);
                    replaceInfo.name && nameRef.current && (nameRef.current.value = replaceInfo.name);
                    replaceInfo.certId && certIdRef.current && (certIdRef.current.value = replaceInfo.certId);
      
                    setReconizeInfo({
                      ...reconizeInfo,
                      ...replaceInfo,
                    });
                  }
                } catch(e) {
                  hideLoading();
                  console.log('readFileErr====', e);
                }
              },
              fail: async () => {
                hideLoading();
              }
            });
          },
        });
    };

    const validate = async () => {
      const isIdCardFn = validateByType('idCard');
      const isNameFn = validateByType('name');
      
      if (!isIdCardFn(reconizeInfo.certId) ||!isNameFn(reconizeInfo.name) || !reconizeInfo.validity) {
        showToast('请确认上传身份证图片是否正确');
        return false;
      }
      const validateIDcardRes = await validateIDcard({
        certId: reconizeInfo.certId,
        custName: reconizeInfo.name,
        validity: reconizeInfo.validity,
      }) 
      if(!validateIDcardRes) {
        showToast('身份证信息校验失败');
        return false
      }
      return true;
    };

     // TODO: 上传身份证图片
    const uploadIdcard = async () => {
      if(!frontBase64.current || !endBase64.current) {
        showToast('请先上传身份证图片');
        return;
      }
      await uploadDocument({
        files: [frontBase64.current, endBase64.current],
        eventCode: 'SUPPLY_IDENTITY_MATERIAL'
      })
    }

    const startSubmit = async () => {
      const validateRes = await validate();
      if(!validateRes) {
        return;
      }
      try {
        Taro.showLoading({
          title: '提交中...',
          mask: true,
        });
        await uploadIdcard();
        const [certValidPeriodStart, certValidPeriodEnd] = reconizeInfo.validity?.split('-') || [];
        await saveIdcardInfo({
          certAddress: reconizeInfo.address,
          sex: reconizeInfo.sex === '男' ? 1 : 0,
          birthDay: formatDateYYYYMMDD(reconizeInfo.birthday),
          certValidPeriodStart: formatDateYYYYMMDD(certValidPeriodStart),
          certValidPeriodEnd: formatDateYYYYMMDD(certValidPeriodEnd),
          name: reconizeInfo.name,
          issueAuthority: reconizeInfo.issueAuthority,
          certId: reconizeInfo.certId,
        })
        hideLoading();
        showToast({
          message: '身份证信息提交成功',
          type: 'success',
        });

        setTimeout(() => {
          if(isFromLending.current) {
            goLendingPreProgress();
          } else {
            router.back()
          }
        }, 2000)
      } catch(e) {
        hideLoading();
        console.log('uploadIdcardErr====', e);
      }
    };
    
    return (
        <PageWrap className={styles['page-content']}>
            <Card
                title='上传身份证'
                titleDecorate={
                  <View className={styles['top-tips']}>
                      <View className={styles['card-tips-right']} onClick={showPhotoTips}>
                        <Image src={TipsIcon} className={styles['tips-icon']}></Image>
                        <Text>查看拍照要求</Text>
                      </View>
                  </View>
                }
                note={
                  <View>
                      <View className={styles['card-tips-right']} onClick={showPhotoTips}>
                        <Text>认证信息：</Text>
                        <Text className={styles.mark}>{user.name}  {user.certId}</Text>
                      </View>
                  </View>
                }>
                <View className={styles['card-content']}>
                <View className='vertical-gap'></View>
                <View>
                    <View className={styles['idcard-pic-title']}>拍摄身份证人像面</View>
                    <View className={styles['idcard-pic-content']} onClick={() => takePhoto('1')}>
                      {
                        !idcardEndImg.current ? (
                          <Image src={photoIcon} className={styles['photo-icon']}></Image>
                        ) : null
                      }
                      <Image
                          src={idcardFrontImg.current ? idcardFrontImg.current : SfzzImg}
                          className={styles['identify-idcard-img']}></Image>
                    </View>
                </View>
                <View className='vertical-gap'></View>
                <View>
                    <View className={styles['idcard-pic-title']}>拍摄身份证国徽面</View>
                  <View className={styles['idcard-pic-content']} onClick={() => takePhoto('2')} >
                    {
                      !idcardEndImg.current ? (
                        <Image src={photoIcon} className={styles['photo-icon']}></Image>
                      ) : null
                    }
                    <Image src={idcardEndImg.current ? idcardEndImg.current : SfzfImg} className={styles['identify-idcard-img']}></Image>
                  </View>
                </View>
                <View className='vertical-gap'></View>
                </View>
            </Card>
            {
                (reconizeInfo.certId || reconizeInfo.validity) ? 
                  <>
                    <View className="vertical-gap"></View>
                    <Card title='请确认您的认证信息'>
                      <View className={styles['row']}>
                          <View className={styles['label']}>姓名<Text></Text></View>
                          {/* <View className={styles['value']}>: {reconizeInfo.name}</View> */}
                          <Text className={styles.labelValueGap}>:</Text>
                          <Input ref={nameRef} className={styles['value']} onInput={() => changeReconizeInfo('name')} defaultValue={reconizeInfo.name}></Input>
                      </View>
                      <View className={styles['row']}>
                          <View className={styles['label']}>身份证号<Text></Text></View>
                          {/* <View className={styles['value']}>: {reconizeInfo.certId}</View> */}
                          <Text className={styles.labelValueGap}>:</Text>
                          <Input ref={certIdRef} type='idcard' className={styles['value']} onInput={() => changeReconizeInfo('certId')} defaultValue={reconizeInfo.certId}></Input>
                      </View>
                      {
                          reconizeInfo.validity ? (
                          <View className={styles['row']}>
                              <View className={styles['label']}>有效期<Text></Text></View>
                              {/* <View className={styles['value']}>: {reconizeInfo.validity}</View> */}
                              <Text className={styles.labelValueGap}>:</Text>
                              <Input ref={validityRef} className={styles['value']} onInput={() => changeReconizeInfo('validity')} defaultValue={reconizeInfo.validity}></Input>
                          </View>
                          ) : null
                      }
                    </Card> 
                  </>
                  : null
            }
            <View className="vertical-gap"></View>
            <CyButton type='primary' size='large' block round onClick={startSubmit}>
              提交
            </CyButton>
        </PageWrap>
    )
}