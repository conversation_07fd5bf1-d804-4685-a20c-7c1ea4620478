import request from '../http/request';

const MAP_KEY = '2983adc3d375b41557b6a433bcd15fec';

/**
 * 高德逆地理
 */
export const getLocationRegeo = (param: API.getLocationDetailParams) => {
  return request<API.getLocationDetailResData>({
    url: 'https://restapi.amap.com/v3/geocode/regeo',
    method: 'GET',
    params: {
      key: MAP_KEY,
      location: `${param.lon},${param.lat}`,
    },
  });
};

/**
 * 高德逆地理
 */
export const getLocationRegeoPois = (param: API.getLocationDetailParams) => {
  return request<API.getLocationPoisResData>({
    url: 'https://restapi.amap.com/v3/geocode/regeo',
    method: 'GET',
    params: {
      key: MAP_KEY,
      location: `${param.lon},${param.lat}`,
      extensions: 'all',
      roadlevel: '1',
    },
  });
};

/**
 * 高德搜索
 */
export const getLocationSearch = (keywords: string) => {
  return request<API.getLocationSearchResData>({
    url: 'https://restapi.amap.com/v5/place/text',
    method: 'GET',
    params: {
      key: MAP_KEY,
      keywords,
      region: '',
      city_limit: true,
    },
  });
};
