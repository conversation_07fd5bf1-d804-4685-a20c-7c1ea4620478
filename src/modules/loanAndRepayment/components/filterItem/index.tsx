import { Image, View } from "@tarojs/components"
import styles from './index.module.scss'
import { <PERSON>er, Popup } from "@taroify/core";
import { IPickerItem } from "~src/types/common-types";
import { useMemo, useState } from "react";
import iconUp from '~images/icon/<EMAIL>'
import { set } from "zod";



interface FilterItemPros {
  date: string;
  onConfirm: (value: string) => void;
}
export default ({
  date,
  onConfirm,
}: FilterItemPros) => {
  const [visible, setVisible] = useState<boolean>(false)

   const handleClose = () => {
    setVisible(false)
  }

  const handleConfirm = (value: any, options: any[]) => {
    onConfirm?.(value[0])
    setVisible(false)
  }

  const range = useMemo(() => {
    const currentYear = new Date().getFullYear() + 1;
    const startYear = 2017;
    const years: IPickerItem[] = [];

    for (let year = startYear; year <= currentYear; year++) {
      years.push({
        label: `${year}年`,
        value: year.toString(),
      });
    }

    return years;
  }, [])
  return (
    <View className={styles.filterContainer}>
      {date &&
        <View className={styles.filterMain} onClick={() => setVisible(true)}>
          <View>{date + '年'}</View>
          <Image src={iconUp} className={styles.filterIcon} />
        </View>
      }
      <Popup open={visible} rounded placement='bottom' onClose={handleClose}>
        <Popup.Backdrop />
        <Picker value={date} columns={range} onCancel={handleClose} onConfirm={handleConfirm} />
      </Popup>
    </View>
  )
}