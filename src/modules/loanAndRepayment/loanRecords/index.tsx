import { Image, View } from "@tarojs/components"
import styles from "./index.module.scss"
import CardItem from "../components/loanRepaymentItem"
import { useEffect, useRef, useState } from "react"
import { getLoanList } from '~services/loadAndRepayment'
import dayjs from "dayjs"
import { hideLoading, router, showLoading, showToast } from "~utils/common"
import Empty from "~components/Empty"
import emptyImg from '~images/repayment/<EMAIL>'
import FilterItem from "../components/filterItem"
import PullRefresh from "~components/PullRefresh"

type channelType = 'wechat' | 'wechatmp' | 'fas' | 'h5' | 'ucs' | 'UCS';
interface LoanRepaymentProps {
  loanNo: string;
  billNo: string;
  loanStatusName: string;
  loanStatus: string;
  startDate: string;
  principal: number;
  accessChannel: channelType,
  curTerm: string;
  totalTerm: string;
  overDueDays: number
}

const CHANNELTYPE: Record<channelType, string> = {
  'wechat': '微信公众号借款',
  'fas': '城一代APP借款',
  'h5': 'E钱庄借款',
  'ucs': '小程序借款',
  'UCS': '小程序借款',
  'wechatmp': '小程序借款'
}

export default () => {
  const [loading, setLoading] = useState(false)
  const [refreshing, setRefreshing] = useState(false)
  const [list, setList] = useState<LoanRepaymentProps[]>([])
  const [tempList, setTempList] = useState<LoanRepaymentProps[]>([])
  const currentDate = useRef<string>('')

  useEffect(() => {
    currentDate.current = dayjs(new Date()).format('YYYY')
    queryData()
  }, [])

  const queryData = async () => {
    //TODO: 下拉刷新
    try {
      showLoading();
      const res = await getLoanList({
        fieldName: 'startDate',
        sort: 'desc'
      })
      setList(res.list || [])
      setTempList((res.list || []).filter((item: LoanRepaymentProps) => String(item.startDate).startsWith(currentDate.current + '年')))
    } catch (error) {
      console.log(error)
    } finally {
      hideLoading()
      setLoading(true)
      setRefreshing(false)
    }
  }

  const handleToDetailBill = (item: LoanRepaymentProps) => {
    if (item.loanNo) {
      router.push({
        url: `/modules/bill/details/index?loanNo=${item.loanNo}`,
      });
    } else if (item.billNo) {
      router.push({
        url: `/modules/bill/details/index?billNo=${item.billNo}`,
      });
    } else {
      showToast('暂无法查询');
    }
  }

  const handleConfirm = (value: string) => {
    showLoading()
    setTimeout(() => {
      currentDate.current = value
      const filterList = list.filter((item: LoanRepaymentProps) => {
        return String(item.startDate).startsWith(currentDate.current)
      })
      setTempList(filterList)
      hideLoading()
    }, 500)
  }

  const formatDate = (dateStr: string) => {
    return dateStr.replace(/年|月/g, '.').replace(/日/g, '').replace(/\.$/, '');

  }
  const handleRefresh = () => {
    setRefreshing(true)
    queryData()
  }

  return (
    <View className={styles.loanRecordPage}>
      <FilterItem date={currentDate.current} onConfirm={handleConfirm} />
      {loading ? (
        <PullRefresh
          className={styles.scrollView}
          onRefresh={handleRefresh}
          refreshing={refreshing}
          refresherBackground="#f6f6f6"
        >
          {
            tempList.length ?
              tempList.map((item, index) => (
                <CardItem
                  key={index}
                  type="loan"
                  amount={item.principal}
                  source={CHANNELTYPE[item.accessChannel]}
                  // date={dayjs(item.startDate, 'YYYY年MM月DD日').format('YYYY.MM.DD')}
                  date={formatDate(item.startDate ?? '')}
                  status={item.loanStatus === 'OVRDU' ?
                    <View style={{ color: '#F5222D' }}>{`${item.loanStatusName}${Math.abs(item.overDueDays)}天`}</View> :
                    item.loanStatusName}
                  desc={
                    item.curTerm ?
                      <View className={styles.loanDesc}>{`当前第${item.curTerm}期 / 共${item.totalTerm}期`}</View> :
                      <View className={styles.loanDesc}>{`共${item.totalTerm}期`}</View>
                  }
                  clickable
                  onClick={() => handleToDetailBill(item)}
                />
              )) : <View className={styles.emptyContainer}>
                <Empty>
                  <Image className={styles.emptyImage} src={emptyImg} />
                  <View className={styles.emptyText}>暂无借款记录</View>
                </Empty>
              </View>
          }
        </PullRefresh>
      ) : null}
    </View>
  )
}