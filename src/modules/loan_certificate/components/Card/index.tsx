import {Image, Text, View} from '@tarojs/components';
import {CyButton} from 'hncy58-taro-components';
import MoneyDisplay from '~components/MoneyDisplay';
import arrowRight from '~images/icon/<EMAIL>';
import styles from './index.module.scss';

export interface CardProps {
  leftItems: Array<{
    label?: string;
    value?: string | number;
    isMoney?: boolean;
    labelClass?: string;
    valueClass?: string;
    [key: string]: any;
  }>;
  onView?: (leftItems: CardProps['leftItems']) => void;
}

const Card = ({leftItems, onView}: CardProps) => {
  return (
    <View className={styles['card']}>
      <View style={styles['card-left']}>
        {leftItems.map((item, index) => (
          <View key={index} className={styles['card-left-item']}>
            <Text className={item.labelClass}>{item.label}</Text>
            {item.isMoney ? (
              <MoneyDisplay size='secondary' money={item.value ?? ''} precision={2} color='#333' />
            ) : (
              <Text className={item.valueClass}>{item.value}</Text>
            )}
          </View>
        ))}
      </View>
      <View style={styles['card-right']} onClick={() => onView?.(leftItems)}>
        <Text className={styles['card-right-text']}>查看</Text>
        <Image src={arrowRight} className={styles.itemIcon} />
      </View>
    </View>
  );
};

export default Card;
