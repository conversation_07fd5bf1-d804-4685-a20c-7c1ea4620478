import {CSSProperties, FC, useEffect, useRef, useState} from 'react';
import {View} from '@tarojs/components';
import {SV<PERSON>enderer, SvgChart} from '@wuba/react-native-echarts';
import {Bar<PERSON><PERSON>, LineChart} from 'echarts/charts';
import type {BarSeriesOption, LineSeriesOption} from 'echarts/charts';
import {GridComponent, LegendComponent, TooltipComponent} from 'echarts/components';
import type {ComposeOption} from 'echarts/core';
import * as echarts from 'echarts/core';
import echartEvents from '../../utils/echartEvents';
import './index.scss';

type ECOption = ComposeOption<BarSeriesOption | LineSeriesOption>;
interface Props {
  option: ECOption;
  customerToolTip?: (data: any) => React.ReactNode; //自定义tooltip
}
// 注册扩展组件
echarts.use([
  TooltipComponent,
  GridComponent,
  LegendComponent,
  SV<PERSON>enderer,
  // ...
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON>hart,
]);
/**
 * 组件名称：图表
 * 组件描述：图表
 */
const CyEcharts: FC<Props & {width: number; height: number}> = ({option, width, height, customerToolTip}) => {
  //图标宽高
  const chartWidth = useRef(width);
  const chartHeight = useRef(height);

  //提示框宽高
  const [toolTipSize, setToolTipSize] = useState({width: 0, height: 0});

  const chartRef = useRef<any>(null);
  const toolTipRef = useRef<any>(null);
  const chart = useRef<echarts.ECharts>();
  useEffect(() => {
    outsideHiddenToolTip();
    chart?.current?.clear();
    chart?.current?.setOption(option);
  }, [option]);

  useEffect(() => {
    chart?.current?.resize({width, height});
    chartWidth.current = width;
    chartHeight.current = height;
  }, [width, height]);

  useEffect(() => {
    if (chartRef.current) {
      chart.current = echarts.init(chartRef.current, 'light', {
        renderer: 'svg',
        width,
        height,
      });
      chart.current.on('showTip', handleShow);
      chart.current.on('hideTip', handleHide);
      chart?.current?.setOption(option);

      echartEvents.on('hiddenToolTip', outsideHiddenToolTip);
    }
    return () => {
      console.log('dispose');
      chart?.current?.off('showTip', handleShow);
      chart?.current?.off('hideTip', handleHide);
      chart?.current?.dispose();
      chart.current = undefined;
      echartEvents.off('hiddenToolTip', outsideHiddenToolTip);
    };
  }, []);
  const outsideHiddenToolTip = () => {
    // handleHide();
    //隐藏tooltip 和坐标线
    chart?.current?.dispatchAction({
      type: 'updateAxisPointer',
    });
  };
  //tip显示
  const handleShow = (e: any) => {
    setTipOption(old => {
      return {...old, show: true, top: e.y, left: e.x, currentTipData: e};
    });
  };

  //tip隐藏
  const handleHide = () => {
    setTipOption(old => {
      return {...old, show: false, currentTipData: undefined};
    });
  };

  const [tipOption, setTipOption] = useState<{
    show: boolean;
    top: number;
    left: number;
    currentTipData: any;
  }>({show: false, top: 0, left: 0, currentTipData: undefined});

  //设置偏移量
  const getToolTipStyle = () => {
    const style: CSSProperties = {};
    if (toolTipSize.width === 0) {
      //没有计算出来大小时，不显示
      style.opacity = 0;
    } else {
      if (tipOption.left > chartWidth.current / 2) {
        console.log('tipOption.left=', tipOption.left);
        console.log('chartWidth.current=', chartWidth.current);
        console.log('toolTipWidth.current=', toolTipSize.width);
        style.left = Math.min(
          Math.max(tipOption.left + 20 - toolTipSize.width, 0),
          chartWidth.current - toolTipSize.width,
        );
      } else {
        style.left = tipOption.left - 20;
      }
      style.top = (chartHeight.current - toolTipSize.height) / 2;
    }
    return style;
  };

  const measure = () => {
    const current = toolTipRef.current as any;
    if (current && current.measureInWindow) {
      current.measureInWindow((x: number, y: number, width1: number, height1: number) => {
        if (width1 && height1 && toolTipSize.width !== width1 && toolTipSize.height !== height1) {
          setToolTipSize({width: width1, height: height1});
        }
      });
    }
  };
  measure();
  return (
    // 选择你偏爱的图表组件
    // return <SkiaChart ref={chartRef} />;
    <View className='echart'>
      <SvgChart ref={chartRef} />
      {tipOption.show && customerToolTip && (
        <View className='tooltips-container' style={{width: chartWidth.current, height: chartHeight.current}}>
          <View>
            <View className='tooltips' style={getToolTipStyle()} ref={toolTipRef}>
              {customerToolTip(tipOption.currentTipData)}
            </View>
            <View style={{flex: 1}}></View>
          </View>
        </View>
      )}
    </View>
  );
};

export default CyEcharts;
