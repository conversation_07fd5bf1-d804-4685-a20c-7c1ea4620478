# 原生接入

## iOS

### 环境搭建

- 苹果官网下载最新的 xcode （当前使用的版本为 15.2）
- 安装包管理工具 cocoapod

```
brew install cocoapods
```

- 安装项目依赖

```
yarn upgradePeerdeps
```

- 安装调试工具

```
brew install --cask flipper
```

```
npm install -g ios-deploy
```

- 运行项目

```
yarn dev:rn --mode uat
```

- 打包项目

```
yarn upgradePeerdeps

yarn build:rn --platform ios

export SKIP_BUNDLING=1

cd ios && bundle update && bundle exec fastlane build_release method

```

## android

### 环境搭建

- 下载最新的 android studio （当前使用的版本为 Hedgehog）
- 安装 jdk （当前使用的版本为 17）并设置环境变量

- 安装项目依赖

```
yarn upgradePeerdeps
```

- 安装调试工具

```
brew install --cask flipper
```

```
npm install -g ios-deploy
```

- 运行项目

```
yarn dev:rn --mode uat
```

- 打包项目

```
yarn upgradePeerdeps

yarn build:rn --platform android

cd android

./gradlew assembleRelease

```
