.pageTop {
    background-color: #fff;
    width: 100%;
}

.pageContent {
    flex: 1;
    width: 710px;
    margin: 20px auto;
}

.CellItem {
    height: 120px;
    box-sizing: border-box;
    margin: 0 20px;
}

.rightInput {
    width: 100%;
}

.inputPlaceholder {
    color: #999;
    font-size: 28px;
}

.baseinfoPage {
    padding-bottom: 180px;
}

.rightClickable {
    display: flex;
    align-items: center;
    color: #333;
}

.rightClickablePlaceholder {
    color: $color-text-secondary;
}

.rightClicakableArrow {
    width: 24px;
    height: 24px;
    margin-left: 12px;
}

.pdt20 {
    padding-top: 20px;
}

.bottom-fixed {
  padding-bottom: 40px;
}

.input {
    width: 100%;
    text-align: right;
  }