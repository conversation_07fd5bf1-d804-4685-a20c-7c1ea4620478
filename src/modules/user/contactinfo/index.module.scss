.pageTop {
    background-color: #fff;
    width: 100%;
}

.pageContent {
    flex: 1;
    width: 710px;
    margin: 20px auto;
}

.CellItem {
    height: 120px;
    box-sizing: border-box;
    margin: 0 20px;
}

.rightInput {
    width: 100%;
}

.inputPlaceholder {
    color: #999;
    font-size: 28px;
}

.baseinfoPage {
    padding-bottom: 180px;
}


.contactImg {
    width: 32px;
    height: 32px;
    margin-left: 16px;
}

.contractBar {
    display: flex;
    align-items: flex-start;
    padding: 20px 60px;
    font-size: 24px;
    line-height: 48px;
    color: $color-text-secondary;
}

.checkboxItem {
    // transform: scale(.6);
}

.pdt20 {
    padding-top: 20px;
}

.bottom-fixed {
  padding-bottom: 40px;
}