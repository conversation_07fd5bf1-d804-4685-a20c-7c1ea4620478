import {FC, useEffect, useState} from 'react';
import {View} from '@tarojs/components';
import CyButton from '../CyButton';
import {EventType, FormField} from '../CyForm';
import './index.scss';

type Option = {
  value: string | number | boolean;
  label: string;
  desc?: string;
};

interface Props extends FormField {
  options: Option[];
  v?: string | number | boolean;
  count?: number;
}

/**
 * 组件名称：单选组件
 * 组件描述：目前支持按钮形式
 */
const CySelectBox: FC<Props> = ({options, onChange, name, v, count}) => {
  const [value, setValue] = useState<string | number | boolean>();
  const onSelect = (val: string | number | boolean) => {
    setValue(val);
    const e = {
      target: {
        name,
        value: val,
      },
      detail: {
        value: val,
      },
    };
    onChange?.(e as unknown as EventType);
  };

  useEffect(() => {
    setValue(v);
  }, [v]);

  let widthClass = 'four';
  if (count) {
    if (count === 4) {
      widthClass = 'four';
    } else if (count === 3) {
      widthClass = 'three';
    } else if (count === 2) {
      widthClass = 'two';
    }
  }
  return (
    <View className={`flex-center comp-cy-radio ${count ? 'wrap' : ''}`}>
      {options.map(option => (
        <View key={`${option.value}`} className={count ? widthClass : ''}>
          <CyButton
            size='mini'
            key={`${option.value}`}
            onClick={e => onSelect(option.value)}
            type={value === option.value ? 'primary' : 'default'}
            className={value === option.value ? 'radio-item checked ' : 'radio-item unchecked'}>
            <View className={value === option.value ? 'checked ' : 'unchecked'}>{option.label}</View>
          </CyButton>
        </View>
      ))}
    </View>
  );
};

export default CySelectBox;
