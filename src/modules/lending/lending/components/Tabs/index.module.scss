.tabs_container {
  .tabs_header {
    display: flex;
    justify-content: space-around;
    align-items: center;
    background-color: #fff;

    .tab_item {
      font-size: 28px;
      color: #666;
      padding: 16px 40px 20px;
      position: relative;
      transition: all 0.3s ease-in-out;

      &.active {
        color: #007aff;
        font-weight: bold;

        &::after {
          content: '';
          position: absolute;
          bottom: 0;
          left: 50%;
          transform: translateX(-50%);
          width: 60px;
          height: 4px;
          background-color: #007aff;
        }
      }
    }
  }

  .tabs_content_wrapper {
    display: flex;
    align-items: stretch;
  }
  .tabs_content {
    display: flex;
    flex: 1;
    overflow-y: auto;
  }

  .tab_panel {
    width: 100%;
    flex: 1;
  }
}