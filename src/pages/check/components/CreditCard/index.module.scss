.creditCard {
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    // height: 640px;
    background-color: #fff;
    box-shadow: 0px 0px 8px 0px rgba(153,153,153,0.4);
    border-radius: 16px;
    overflow: hidden;
}
.creditCardTopbar {
    height: 80px;
}
.creditCardTopbarBg {
    background: url('../../../../assets/images/index/<EMAIL>') no-repeat;
    background-size: 100% 80px;
}
.creditAmount {
    font-weight: bold;
    font-size: 80px;
    color: #333333;
    line-height: 70px;
}

.creditCardSubtitle {
    font-size: 28px;
    color: $color-text-secondary;
}

.creditAlertEntryIcon {
    width: 24px;
    height: 24px;
    padding: 0 16px;
}

.creditCardTitle {
    font-size: 48px;
    margin-top: 20px;
    font-weight: 500;
    color: $color-text-normal;
}

.creditCardContent {
    position: relative;
    text-align: center;
    width: 100%;
    padding: 8px 18px 0px 18px;
    box-sizing: border-box;
}

.disabledCard {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: #fff;
    opacity: 0.5;
    z-index: 9;
}

.disabledCardMarkText {
    position: absolute;
    top: 0;
    right: 0;
    left: 0;
    text-align: center;
    color: $color-error;
    font-size: 24px;
    margin-top: 210px;
    opacity: 1;
    z-index: 9;
}

.cardMarkText {
    color: $color-error!important;
    font-size: 24px!important;
}

.creditCardDesc {
    font-size: 24px;
    color: $color-text-secondary;
    padding-top: 20px;
    line-height: 36px;
    height: 36px;
}

.creditCardBottom {
    margin: 0 20px;
    border-top: 1px solid $color-border-light;
}

.creditStateBar {
    display: flex;
    height: 72px;
    align-items: center;
    justify-content: center;
    // color: $color-text-secondary;
    color: #A1A6BC;
    font-size: 24px;
    border-radius: 8px;
}

.creditStateItem {
    display: flex;
    justify-content: center;
    // flex: 1;

    // & + .creditStateItem {
    //     border-left: 1px solid $color-border-light;
    // }
}
.creditStateLine {
    width: 1px;
    height: 16px;
    margin: 0 18px;
    background-color: #868CA7;
}

.creditStateBorder {
    margin-top: 30px;
    height: 1px;
    background: linear-gradient(90deg, #FFFFFF 0%, #C3CBEB 50%, #FFFFFF 100%);
}

.creditCardOpt {
    display: flex;
    overflow: hidden;
    margin: 40px 0 20px 0;
    padding: 0 22px;
}

.creditCardRaiseBtn {
    margin-right: 30px;
    width: 200px;
    overflow: visible;
}

.creditCardRaiseBtnIcon {
    position: absolute;
    top: 0;
    right: -20px;
    width: 56px;
    height: 80px;
}

.creditAlertBtTips {
    font-size: 22px;
    color: $color-text-secondary;
    line-height: 28px;
    margin-top: 12px;
}

.creditAlertRow {
    font-size: 28px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.creditAlertRowLabel {
    flex: 1;
    text-align: right;
}

.creditAlertRowValue {
    flex: 1;
    text-align: left;
}