import {FC} from 'react';
import {Text, View} from '@tarojs/components';
import CyActivityIndicator from '../CyActivityIndicator';
import './index.scss';

interface Props {
  finished: boolean;
  loading: boolean;
  dataLength: number;
}

/**
 * 组件名称：加载更多
 * 组件描述：上拉加载更多
 */
const CyLoadMore: FC<Props> = ({finished, loading, dataLength}) => {
  return (
    <View className='comp-cy-load-more'>
      {!finished && loading && dataLength !== 0 && (
        ////加载中。。。
        <View className='comp-cy-load-more-loading'>
          <CyActivityIndicator size='small' color='#666' />
          <Text className='comp-cy-load-more-text'>加载中...</Text>
        </View>
      )}
      {!finished && !loading && dataLength !== 0 && (
        ////加载中。。。
        <View className='comp-cy-load-more-loading'>
          <Text className='comp-cy-load-more-text'>上拉加载更多</Text>
        </View>
      )}
      {finished && !loading && dataLength !== 0 && (
        //没有更多数据显示
        <View>
          <Text className='comp-cy-load-more-no'>没有更多数据</Text>
        </View>
      )}
    </View>
  );
};

export default CyLoadMore;
