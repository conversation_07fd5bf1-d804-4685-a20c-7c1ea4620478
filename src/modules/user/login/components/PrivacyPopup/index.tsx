import {CyButton} from 'hncy58-taro-components';
import {BaseEventOrig, ScrollView, View, ViewProps} from '@tarojs/components';
import Popup from '~components/Popup';
import PrivacyAgreement from '~components/PrivacyAgreement';
import styles from './index.module.scss';

export interface PrivacyPopupProps extends ViewProps {
  visible: boolean;
  onClose: () => void;
  onAgree: () => void;
  onDisagree: () => void;
  isUseWechatPhone: boolean;
  getPhoneCallBack: (e: BaseEventOrig) => void;
}

export default ({visible, onClose, onAgree, onDisagree, isUseWechatPhone, getPhoneCallBack}: PrivacyPopupProps) => {
  return (
    <Popup title='用户协议及隐私政策' className={styles.privacyPopup} visible={visible} onClose={onClose}>
      <ScrollView scrollY style={{height: 300}}>
        <PrivacyAgreement />
      </ScrollView>

      <View className={styles.privacyPopupFooter}>
        <CyButton
          block
          style={{flex: 1, marginRight: 8}}
          type='default'
          onClick={() => {
            onAgree();
            onClose();
          }}>
          不同意
        </CyButton>
        <CyButton
          block
          style={{flex: 2}}
          {...(isUseWechatPhone
            ? {
                openType: 'getPhoneNumber',
                onGetPhoneNumber: getPhoneCallBack,
              }
            : null)}
          type='primary'
          onClick={() => {
            onDisagree();
            onClose();
          }}>
          同意
        </CyButton>
      </View>
    </Popup>
  );
};
