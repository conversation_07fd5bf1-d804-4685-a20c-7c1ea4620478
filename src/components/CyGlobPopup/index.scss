.comp-cy-glob-popup {
    display: flex;
    align-items: center;
    justify-content: center;
    position: absolute;
    width: 100%;
    height: 100%;
    left: 0;
    top: 0;
}
.comp-cy-popup-mask {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba($color: #000000, $alpha: .5);
}

.top {
    top: 0;
    width: 100%;
    left: 0;
}

.right {
    top: 0;
    right: 0;
    height: 100%;
}

.bottom {
    bottom: 0;
    width: 100%;
    left: 0;
}

.center {
    position: relative;
}

.left {
    top: 0;
    left: 0;
    height: 100%;
}

.content {
    position: absolute;
}

.taro_router > .taro_page.taro_tabbar_page, .taro_router > .taro_page.taro_page_show.taro_page_stationed {
    display: block!important;
}

.picker-content {
    background-color: #fff;
    padding: 10px 20px;
    font-size: 28px;
}

.picker-list {
    background-color: #fff;
    display: flex;
    flex-direction: column;
    width: 600px;
    max-height: 1000px;
    text-align: left;

    &__label {
        display: flex;
        flex-direction: row;
        height: 80px;
        justify-content: flex-start;
    }
}

.picker-footer {
    display: flex;
    flex-direction: row;
    font-size: 28px;
    height: 80px;
    align-items: center;
    justify-content: flex-end;
}

.picker-btn {
    padding: 0 20px;
}

.alert-box {
    background-color: #fff;
    width: 640px;
    border-radius: 20px;
    font-size: 36px;
}

.alert-content {
    padding: 20px;
    font-size: 32px;
    max-height: 600px;
}

.alert-footer {
    display: flex;
    height: 100px;
    align-items: center;
    flex-direction: row;
}

.footer-btn {
    flex: 1;
    display: flex;
    height: 100%;
    align-items: center;
    justify-content: center;
    border-top: 1px solid #aaa;

    & + .footer-btn {
        border-left: 1px solid #aaa;
    }
}

.confirm-btn {
    color: $color-primary;
}

.alert-title {
    text-align: center;
    font-size: 38px;
    padding-top: 20px;
    color: #333333;
}

.sheet-content {
    background-color: #fff;
    // padding: 10px 20px;
    font-size: 28px;
    border-top-right-radius: 32px;
    border-top-left-radius: 32px;
}
.sheet-title-container {
    padding: 0 40px;
    align-items: center;
}
.sheet-img {
    width: 40px;
    height: 40px;
}
.sheet-title {
    flex: 1;
    background-color: #fff;
    padding: 10px 20px;
    font-size: 32px;
    text-align: center;
    margin-left: 40px;
}

.sheet-item {
    font-size: 28px;
    color: black;
    border-top: 1px solid #eeeeee;
    text-align: center;
}

.sheet-list {
    background-color: #fff;
    display: flex;
    flex-direction: column;
    width: 750px;
    // height: 300px;
    text-align: left;
}

.confirm-box {
    background-color: #fff;
    width: 640px;
    border-radius: 16px;
    font-size: 32px;
    padding-top: 30px;
    display: flex;
    flex-direction: column;
    // height: 400px;
}

.confirm-title {
    text-align: center;
    font-size: 32px;
    color:#333333;
    font-weight: bold;
    padding: 20px 0;
}

.confirm-content {
    font-size: 32px;
    color:#333333;
    text-align: center;
    flex: 1;
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 20px 32px;
}

.confirm-footer {
    display: flex;
    height: 100px;
    align-items: center;
    flex-direction: row;
    margin-top: 20px;
}

.confirm-footer-btn {
    flex: 1;
    display: flex;
    height: 100%;
    align-items: center;
    justify-content: center;
    border-top: 2px solid #ddd;
}

.cancel-btn {
    flex: 1;
    display: flex;
    height: 100%;
    align-items: center;
    justify-content: center;
    border-right: 2px solid #ddd;
}

.picker-content {
    border-radius: 8px;
    padding-top: 16px;
}

.picker-title {
    font-size: 32px;
    text-align: center;
    height: 88px;
    display: flex;
    justify-content: center;
    align-items: center;
    border-bottom: 1px solid #ddd;
}

.picker-list__radio {
    width: 36px;
    height: 36px;
    margin-right: 8px;
}

.search-selector-dialog {
    width: 600px;
    background-color: #fff;
    padding: 20px;
    height: 800px;
    border-radius: 8px;
}

.search-selector-content {
    padding: 20px 0;
}

.search-selector-item {
    padding: 20px 10px;
    border-bottom: 1px solid #f0f0f0;
}

.search-selector-radio_label {
    padding: 20px 0;
    border-bottom: 1px solid #f1f1f1;
}

.search-selector-radio {
    width: 30px;
    height: 30px;
}

.upgrade-content {
    padding: 45px;
    font-size: 28px;
    max-height: 600px;
    line-height: 45px;
}

.upgrade-progress {
    display: flex;
    height: 80px;
    width: 100%;
    flex-direction: row;
    align-items: center;
    margin-bottom: 20px;
}

.upgrade-progress-bar {
    padding: 20px;
}

.upgrade-footer {
    display: flex;
    height: 100px;
    align-items: center;
    flex-direction: row;
}

.upgrade-cancel-btn {
    flex: 1;
    display: flex;
    height: 100%;
    align-items: center;
    justify-content: center;
    border-top: 0.5px solid #aaa;
    border-right: 0.5px solid #aaa;
}

.upgrade-confirm-btn {
    flex: 1;
    display: flex;
    height: 100%;
    align-items: center;
    justify-content: center;
    border-top: 0.5px solid #aaa;
    color: $color-primary;
}

.overdue-content-dialog {
    width: 600px;
    background-color: #ffffff;
    padding: 20px;
    height: 600px;
    border-radius: 8px;
}

.overdue-content-row {
    margin-top: 20px;
    display: flex;
    flex-direction: row;
    align-items: center;
    width: 100%;
    height: 80px;
    border-bottom-color: #ddd;
    border-bottom-width: 1px;
    border-style: solid;
}

.confirm-right {
    font-size: 32px;
    color: #666666;
    text-align: right;
    flex: 1;
    display: flex;
    justify-content: right;
    align-items: right;
    padding: 20px 32px;
}