type DebounceCallback<T extends any[]> = (...args: T) => void;
function throttle<T extends any[]>(callback: DebounceCallback<T>, limit = 500): DebounceCallback<T> {
  let inThrottle: boolean = false;

  return (...args: T) => {
    if (!inThrottle) {
      callback(...args);
      inThrottle = true;
      setTimeout(() => (inThrottle = false), limit);
    } else {
      console.log('已阻止');
    }
  };
}
export default throttle;
