import {CSSProperties, FC, useEffect, useState} from 'react';
import {Text, View} from '@tarojs/components';
import './index.scss';

type Props = {
  tabs: string[];
  currentIndex?: number;
  onChange?: (index: number) => void;
  activeColor?: string;
  color?: string;
  fontSize?: number;
  indicatorShow?: boolean;
  indicatorColor?: string;
  indicatorActiveColor?: string;
  indicatorHeight?: number;
  indicatorWidth?: number;
};

/**
 * 组件名称：选项卡组件
 * 组件描述：选项卡
 */
const CyTab: FC<Props> = ({
  tabs,
  currentIndex = 0,
  activeColor = '#437DFF',
  color = '#999999',
  onChange,
  indicatorShow = true,
  indicatorColor = 'transparent',
  indicatorActiveColor = '#437DFF',
  indicatorHeight = 2,
  fontSize = 15,
  indicatorWidth,
}) => {
  const [activeIndex, setActiveIndex] = useState(currentIndex);
  const onClick = (index: number) => {
    setActiveIndex(index);
    onChange && onChange(index);
  };

  useEffect(() => {
    setActiveIndex(currentIndex);
  }, [currentIndex]);
  const style: string | CSSProperties = {
    color,
    fontSize,
  };
  const activeStyle: string | CSSProperties = {
    color: activeColor,
    fontSize,
  };
  if (indicatorShow) {
    style.borderBottomWidth = indicatorHeight;
    style.borderBottomColor = indicatorColor;
    activeStyle.borderBottomWidth = indicatorHeight;
    activeStyle.borderBottomColor = indicatorActiveColor;
    activeStyle.borderBottomStyle = 'solid';
    if (indicatorWidth) {
      style.width = indicatorWidth;
      activeStyle.width = indicatorWidth;
    }
  }
  return (
    <View className='flex comp-cy-tab'>
      {tabs.map((title, index) => (
        <View
          className='flex comp-cy-tab-item'
          // style={index === activeIndex ? activeStyle : style}
          key={index}
          onClick={() => {
            // console.log('activeIndex=', activeIndex);
            // if (index !== activeIndex)
            onClick(index);
          }}>
          <View className='comp-cy-tab-text' style={{color: index === activeIndex ? indicatorActiveColor : '#333333'}}>
            {title}
          </View>
          {indicatorShow && (
            <View className='comp-cy-tab-indicator' style={index === activeIndex ? activeStyle : style}></View>
          )}
        </View>
      ))}
    </View>
  );
};

export default CyTab;
