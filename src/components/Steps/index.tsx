import {Image, View} from '@tarojs/components';
import failIcon from '../../assets/images/icon/<EMAIL>';
import successIcon from '../../assets/images/icon/<EMAIL>';
import styles from './index.module.scss';

export interface Step {
  // 标题 必传
  title: string;
  // 步骤状态 可选
  status?: 'process' | 'finish' | 'error';
  // 描述 可选
  desc?: string;
}

interface StepsProps {
  // 步骤集合
  steps: Step[];
  // 当前步骤
  current: number;
}

/**
 * 使用示例
 * <steps
 *  // 步骤集合
 *  steps=[
 *    {title: '步骤一', status: 'process', desc: '描述'},
 *    {title: '步骤二', status: 'finish', desc: '描述'},
 *    {title: '步骤三', status: 'error', desc: '描述'},
 *  ]
 *  // 当前步骤
 *  current={1}
 * />
 */

const StatusIcon = ({status}: {status: Step['status']}) => {
  return (
    <>
      {status === 'finish' ? (
        <View className={`${styles.step_icon_status} ${styles.step_icon_success}`}>
          <Image className={styles.setp_icon_img} src={successIcon} />
        </View>
      ) : status === 'error' ? (
        <View className={`${styles.step_icon_status} ${styles.step_icon_error}`}>
          <Image className={styles.setp_icon_img} src={failIcon} />
        </View>
      ) : (
        <View className={styles.step_current}>
          <View className={styles.step_round}></View>
          <View className={styles.step_round}></View>
          <View className={styles.step_round}></View>
        </View>
      )}
    </>
  );
};

const Steps = ({steps, current}: StepsProps) => {
  return (
    <View className={styles.steps}>
      {steps.map((step, index) => (
        <View key={index} className={`${styles.step} ${index < current - 1 ? styles.current_line : ''}`}>
          <View className={styles.step_idtor}>
            {index < current ? <StatusIcon status={step.status} /> : <View className={styles.step_icon}></View>}
          </View>
          <View className={styles.step_content}>
            {step.title && (
              <>
                <View className={`${styles.step_title} ${index < current ? styles.current : ''}`}>{step.title}</View>
                <View className={styles.step_desc}>{step.desc}</View>
              </>
            )}
          </View>
        </View>
      ))}
    </View>
  );
};

export default Steps;
