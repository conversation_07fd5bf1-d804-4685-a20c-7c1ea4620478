export default (filePath: string, base64: string) => {};

export const readFile = (path: string, type: string) => {
  return new Promise((resolve, reject) => {
    resolve('');
  });
};
export const open = (path: string) => {
  return new Promise((resolve, reject) => {
    resolve('');
  });
};

export const pickSingle = (params: {mode: string; type: string[]; copyTo: string}) => {
  return new Promise<{name: string; path: string; type: string; uri: string; fileCopyUri: string; data: string}>(
    (resolve, reject) => {
      resolve({name: '', path: '', type: '', uri: '', fileCopyUri: '', data: ''});
    },
  );
};

export const types = {
  pdf: 'pdf',
};
