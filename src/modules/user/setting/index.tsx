import { Button, View } from "@tarojs/components"
import PageWrap from "~components/PageWrap"
import styles from './index.module.scss'
import Taro from "@tarojs/taro"
import useUserStore from "~store/user"
import { showToast, router, goWebviewLinkOrRoute } from "~utils/common"
import { signout } from "~services/user"

export default () => {
    const {user, logout} = useUserStore();

    const handleSignout = async () => {
      if (!user.mobile) {
        return;
      }
      await signout();
      logout();
      showToast('退出登录成功');
      Taro.navigateBack();
    };

    if(!user.mobile) {
        return null;
    }

    return (
        <PageWrap>
            <View className={styles.settingRow} onClick={handleSignout}>退出登录</View>
            <Button></Button>
        </PageWrap>
    )
}