import {View} from '@tarojs/components';
import styles from './index.module.scss';

interface LeftCellProps {
  currentTerm: number;
  dueDate: string;
  days: number;
}

const LeftCell = ({currentTerm, dueDate, days}: LeftCellProps) => {
  return (
    <View className={styles.repayment_cell_left}>
      <View className={styles.repayment_cell_term}>{`第${currentTerm}期`}</View>
      <View className={styles.repayment_cell_note}>{dueDate}</View>
      <View className={styles.repayment_cell_note}>{`(共${days}天)`}</View>
    </View>
  );
};

export default LeftCell; 