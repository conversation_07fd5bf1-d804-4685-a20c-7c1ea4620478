.cy-spinner {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  
  &--center {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    z-index: 9999;
  }

  &__icon {
    border: 4px solid #f3f3f3;
    border-top: 4px solid $color-primary;
    border-radius: 50%;
    animation: cy-spinner-rotate 1s linear infinite;
  }

  &__text {
    margin-top: 16px;
    font-size: 28px;
    color: #666;
    text-align: center;
  }

  // 不同大小的样式
  &--small {
    .cy-spinner__icon {
      width: 32px;
      height: 32px;
      border-width: 3px;
    }
    
    .cy-spinner__text {
      margin-top: 8px;
      font-size: 24px;
    }
  }

  &--medium {
    .cy-spinner__icon {
      width: 48px;
      height: 48px;
      border-width: 4px;
    }
    
    .cy-spinner__text {
      margin-top: 12px;
      font-size: 28px;
    }
  }

  &--large {
    .cy-spinner__icon {
      width: 64px;
      height: 64px;
      border-width: 6px;
    }
    
    .cy-spinner__text {
      margin-top: 16px;
      font-size: 32px;
    }
  }
}

@keyframes cy-spinner-rotate {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}