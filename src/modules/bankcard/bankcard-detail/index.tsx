import { <PERSON><PERSON>, Switch, View } from "@tarojs/components"
import styles from './index.module.scss'
import PageContainer from "~components/PageWrap"
import Card from '~components/Card';
import Cell from '~components/CellItem';
import BankCardItem from "../bankcard-list/components/List/ListItem"
import { useEffect, useRef, useState } from "react";
import { CyButton, CyModal, CyPopup } from "hncy58-taro-components";
import { useLoad } from "@tarojs/taro";
import { hideLoading, router, showLoading, showModal, showToast } from "~utils/common";
import { formatMillesimal } from "~utils/number";
import { deleteBankCard, setDefaultBankCard } from "~services/bankcard";
import CyVerificationCodeInput, { CyVerificationCodeInputRef } from '~components/CyVerificationCodeInput';
import useUserStore from "~store/user";
import { sendUpgradeBankCardSms, confirmUpgradeBankCard } from "~services/bankcard";
import CyContractPreviewHtml, { CyContractPreviewHtmlRef, ContractItemProps } from '~components/ContractPreviewHtml';
import CyContractBar from "~components/CyContractBar";


const BankCardDetail = () => {
  const { user } = useUserStore();
  
  const [isDefault, setIsDefault] = useState(false)
  const [bankcardInfo, setBankcardInfo] = useState({} as API.IBankCardInfo)
  const [applyNo, setApplyNo] = useState('')
  const [verifyCodeVisible, setVerifyCodeVisible] = useState(false);
  const verifyCodeInputRef = useRef<CyVerificationCodeInputRef>(null);
  
  const [isAgreed, setIsAgreed] = useState<boolean>(false); // 是否同意补充协议
  const [isAgreedCutPayment, setIsAgreedCutPayment] = useState<boolean>(false); // 是否同意委托扣款协议
  const [contracts, setContracts] = useState<ContractItemProps[]>([]); // 补充协议
  const cutPayment = [{code: 'E24'}] // 委托扣款协议

  const verifyCode = useRef<string>('');


  useLoad(({ bankCardInfo }) => {
    if (bankCardInfo) {
      setBankcardInfo(JSON.parse(bankCardInfo))
      setIsDefault(bankCardInfo.isDefault)
    }
  })


  const handleUpgrade = () => {
    if (!isAgreedCutPayment) {
      showToast('请先阅读并同意委托扣款协议')
      return
    }
    showTips()
  }

  const showTips = () => {
    CyModal.create({
      title: '温馨提示',
      content: (
        <View className={styles.bankcardDetailTips}>因支付系统安全升级，您需要完成协议支付升级流程，否则将无法正常还款。为避免给您的贷款使用造成影响，请您及时完成协议支付升级操作</View>
      ),
      onConfirm: () => {
        setVerifyCodeVisible(true)
        verifyCodeInputRef.current?.sendCode()
      },
    })
  }

  const showSuccessTips = () => {
    CyModal.create({
      title: '提示',
      content: (<View className={styles.bankcardDetailTips}>已完成支付升级</View>),
      confirmText: `确定`,
      onConfirm: () => {
        router.back();
      },
    })
  }

  const handleDeleteBankcard = () => {
    CyModal.create({
      title: '温馨提示',
      content: (<View className={styles.bankcardDetailTips}>确定要删除该银行卡吗？</View>),
      isShowCancel: true,
      confirmText: `确定`,
      cancelText: '取消',
      onConfirm: () => {
        toDelete();
      }
    })
  }

  const toDelete = async () => {
    try {
      showLoading()
      await deleteBankCard({ accountId: bankcardInfo.acctId + '' })
      showToast('删除成功')
      setTimeout(() => {
        router.back();
      }, 2000)
    } catch (error) {
      console.log(error)
    } finally {
      hideLoading()
    }
  };

  const handleSetDefault = (value: boolean) => {
    setIsDefault(value)
    CyModal.create({
      title: '温馨提示',
      content: (<View className={styles.bankcardDetailTips}>是否确认设置该银行卡为默认的借款、还款银行卡？</View>),
      isShowCancel: true,
      confirmText: `确认`,
      cancelText: '取消',
      onConfirm: () => {
        toSetDefault();
      },
      onCancel: () => {
        setIsDefault(false)
      },
    })
  }

  const toSetDefault = async () => {
    try {
      await setDefaultBankCard({ accountId: bankcardInfo.acctId + '' })
      CyModal.create({
        title: '提示',
        content: (<View className={styles.bankcardDetailTips}>设置默认银行卡成功。如需更换单笔借据的扣款银行卡，请到“我的账单”-"账单详情"里点击“还款银行卡”。</View>),
        confirmText: `确定`,
        onConfirm: () => {
          setBankcardInfo({ ...bankcardInfo, defaultFlag: true })
        },
      })
    } catch (error) {
      console.log(error)
      setIsDefault(false)
    }
  }

  const handleVerify = async (code: string) => {
    verifyCode.current = code
    if (contracts.length && !isAgreed) {
      showToast('请先阅读并同意补充协议')
      return
    }
    console.log('验证码', code);
    toUpgrade()
  };

  const toUpgrade = async () => { 
    try {
      await confirmUpgradeBankCard({ applyNo: applyNo, acctId: bankcardInfo.acctId, verificationCode: verifyCode.current })
      showSuccessTips()
      setVerifyCodeVisible(false);
      verifyCodeInputRef.current?.clearCode(true)
    } catch (error) { 
      showToast('银行卡升级失败')
      verifyCodeInputRef.current?.clearCode(false)
    }
  };

  // 发送验证码
  const sendVerifyCode = async () => {
    try {
      const { applyNo, needSupplyDocTypes } = await sendUpgradeBankCardSms({ acctId: bankcardInfo.acctId });
      setApplyNo(applyNo);
      if (needSupplyDocTypes && needSupplyDocTypes.length) {
        const contractMap = needSupplyDocTypes.map(code => ({
          code
        }))
        setContracts(contractMap)
      }
      return true;
    } catch (e) {
      console.error('发送验证码失败', e);
      return false;
    }
  };

  return (
    <PageContainer className={styles.bankcardDetailContainer}>
      <View className={styles.bankcard}>
        <BankCardItem isDetail {...bankcardInfo} />
      </View>
      <Card className={styles.pageCard}>
        <Cell className={styles.cardCell} title='银行预留手机号码' value={bankcardInfo.desensitizeBankMobileNo} />
        <Cell className={styles.cardCell} title='单笔借款限额' value={formatMillesimal(bankcardInfo.singleLimit) || ''} />
        <Cell className={styles.cardCell} title='每日借款限额' value={formatMillesimal(bankcardInfo.dayLimit) || ''} />
        <Cell className={styles.cardCell} title='单笔还款限额' value={formatMillesimal(bankcardInfo.singlePayLimit) || ''} />
        <Cell className={styles.cardCell} title='每日还款总额' value={formatMillesimal(bankcardInfo.dayPayLimit) || ''} />
      </Card>
      {!bankcardInfo.defaultFlag ? <Card className={styles.pageCard} style={{ margin: 0 }}>
        <Cell
          className={styles.cardCell}
          title='设为默认银行卡'
          right={
            <>
              <Switch checked={isDefault} onChange={e => handleSetDefault(e.detail.value)} />
            </>
          }
        />
      </Card> : null}
      {bankcardInfo.canUpgrade ? <View className={styles.agreementContainer}>
        {cutPayment.length ? <View className={styles.agreement}>
          <CyContractBar
            justify='center'
            onChange={val => {
              setIsAgreedCutPayment(val);
            }}
            mustView
            checked={isAgreedCutPayment}
            contracts={cutPayment}
          />
        </View> : null}
      </View> : null}
      <View className={styles.bottomBtn}>
        {bankcardInfo.canUpgrade ? <CyButton round block type='primary' onClick={handleUpgrade}>
          支付升级
        </CyButton> : null}
        <View className={styles.deleteBtn} onClick={handleDeleteBankcard}>删除银行卡</View>
      </View>
      <CyPopup visible={verifyCodeVisible} title='输入验证码' onClose={() => setVerifyCodeVisible(false)}>
        <CyVerificationCodeInput
          ref={verifyCodeInputRef}
          phone={bankcardInfo.desensitizeBankMobileNo}
          onVerify={handleVerify}
          onSendCode={sendVerifyCode}
        />
        {contracts.length ? <CyContractBar
          justify='center'
          onChange={val => {
            setIsAgreed(val);
            if (val && verifyCode && verifyCode.current.length === 6) {
              toUpgrade()
            }
          }}
          lazy
          checked={isAgreed}
          contracts={contracts}
        /> : null}
      </CyPopup>
    </PageContainer>
  )
}

export default BankCardDetail