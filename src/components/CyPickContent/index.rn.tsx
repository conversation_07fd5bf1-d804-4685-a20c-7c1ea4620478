import {CSSProperties, FC} from 'react';
import {Pressable} from 'react-native';
import {ViewProps} from '@tarojs/components';
import './index.scss';

type Props = {
  // onLongPress: any;
  // style: any;
  // role: any;
  // onTouchStart: any;
  // onTouchMove: any;
  // onTouchEnd: any;
  // onTouchCancel: any;
  // ref: any;
};

/**
 * 组件名称：pick内容组件
 * 组件描述：包裹pick组件
 */
const CyPickContent: FC<Props & ViewProps> = props => {
  return <Pressable {...props}></Pressable>;
};

export default CyPickContent;
