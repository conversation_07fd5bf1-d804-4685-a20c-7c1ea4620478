import {useEffect, useState} from 'react';
import {CyButton} from 'hncy58-taro-components';
import {Button, Image, View} from '@tarojs/components';
import Popup from '~components/Popup';
import arrowIcon from '~images/icon/arrow-right.png';
import unSelectIcon from '~images/icon/<EMAIL>';
import selectIcon from '~images/icon/<EMAIL>'
import bankIcon from '~images/index/kqb.png';
import AddBankIcon from '~images/lending/<EMAIL>'
import styles from './index.module.scss';
import {IBankCardInfo} from 'src/types/common-types';
import { router, showToast } from '~utils/common';
import { eventCenter } from '@tarojs/taro';
import { getBankCardList } from '~services/bankcard';

export interface BankListProps {
  visible?: boolean;
  bankCardList: API.IBankCardInfo[];
  onClose?: () => void;
  onComplete?: (item: API.IBankCardInfo) => void;
  title?: string;
}

const goAddCard = () => {
  router.push({
    url: '/modules/bankcard/bankcard-binding/index'
  });
};

export default (props: BankListProps) => {
  const {visible = false, bankCardList = [], onClose, title} = props;
  const [currentBankCardInfo, setCurrentBankCardInfo] = useState<API.IBankCardInfo | undefined>(bankCardList.find(item => item.defaultFlag))

  const [currentBankCardList, setCurrentBankCardList] = useState<API.IBankCardInfo[]>([])
  
  useEffect(() => {
    if (bankCardList.length > 0) {
      setCurrentBankCardList(bankCardList)
      setCurrentBankCardInfo(bankCardList.find(item => item.defaultFlag))
    }

    eventCenter.on('refreshBankCardList', getBankCardListInfo)

    return () => {
      eventCenter.off('refreshBankCardList', getBankCardListInfo)
    }

  }, [bankCardList])

  /**
   * 获取银行卡列表信息
   */
  const getBankCardListInfo = async () => {
    try {
      const res = await getBankCardList({
        requestFrom: 'lending',
      });
      setCurrentBankCardList(res);
    } catch (error) {
      console.log('请求错误', error);
    }
  };

  const handleSelect = (item: API.IBankCardInfo) => {
    if (item.available) {
      setCurrentBankCardInfo(item)
    }
  }
  
  const handleClick = () => {
    if (currentBankCardInfo) {
      props.onComplete?.(currentBankCardInfo)
    }
  }

  return (
    <Popup title={title || '选择收款账户'} visible={visible} onClose={onClose} showClose bodyStyle={{height: '55vh'}}>
      <View className={styles.bank_container}>
        {currentBankCardList.map((item, index) => (
          <View className={styles.bank_item} key={index} onClick={() => handleSelect(item)}>
            <View className={styles.bank_item_left}>
              <Image src={item.bankIconImgUrl} className={styles.bank_item_icon} />
              <View className={styles.bank_item_info}>
                <View className={styles.bank_item_top}>
                  <View className={styles.bank_item_bankName}>{`${item.bankName}(${item.desensitizeAccountNo?.slice(-4)})`}</View>
                  {!item.available ? <View className={styles.bank_item_available}>维护中</View> : null}
                </View>
                <View className={styles.bank_item_limit}>{`单笔限额${item.singleLimit / 10000}万元，单日限额${item.dayLimit / 10000}万元`}</View>
                {!item.available ? (
                  <>
                    <View className={`${styles.bank_errTips} ${styles.mgt22}`}>{`${item.bankName}系统维护中，请选择其他银行卡借款`}</View>
                    <View className={styles.bank_errTips}>{`维护时间:${item?.maintenanceStartTime || ''} - ${item?.maintenanceEndTime || ''}`}</View>
                  </>
                ) : null}
              </View>
            </View>
            <View className={styles.bank_item_check}>
              <Image src={currentBankCardInfo?.acctId === item.acctId ? selectIcon : unSelectIcon} className={styles.bank_item_checkIcon} />
            </View>
          </View>
        ))}
        <View className={styles.bank_item} onClick={goAddCard}>
          <View className={styles.bank_item_left}>
            <Image src={AddBankIcon} className={styles.bank_item_icon} />
            <View className={styles.bank_item_bankName}>添加/选择其他银行卡</View>
          </View>
          <View className={styles.bank_item_check}>
            <Image src={arrowIcon} className={styles.bank_item_checkIcon} />
          </View>
        </View>
        <View className={styles.bank_btn}>
          <CyButton round block type='primary' onClick={handleClick}>
            确定
          </CyButton>
        </View>
      </View>
    </Popup>
  );
};
