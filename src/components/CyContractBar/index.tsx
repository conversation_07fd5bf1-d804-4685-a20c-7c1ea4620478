import React, {CSSProperties, ForwardRefRenderFunction, useEffect, useRef, useState} from 'react';
// CyButton、CyCheckbox、CyPopup 为自定义 UI 组件
import {CyButton, CyCheckbox, CyCheckboxRef, CyPopup} from 'hncy58-taro-components';
import {Text, View} from '@tarojs/components';
// 合同预览组件及其 ref 类型
import CyContractPreviewHtml, { CyContractPreviewHtmlRef } from '~components/ContractPreviewHtml';
import styles from './index.module.scss';
import { showToast } from '~utils/common';
import useMainStore from '~store/main';
import { ContractCode2Info } from '~utils/constant';

// 合同项类型定义
/**
 * 单个合同项
 */
type ContractItem = {
  code: string; // 合同唯一标识
  name?: string; // 合同名称
  params?: Record<string, unknown>; // 额外参数
  check?: () => boolean; // 校验函数，返回是否可勾选
  noCache?: boolean; // 是否不缓存
};

/**
 * 组件 Props 类型
 */
type Props = {
  checked?: boolean; // 是否已勾选
  contracts: ContractItem[]; // 合同列表
  mustView?: boolean; // 是否必须先查看合同
  banViewDetail?: boolean; // 是否禁止查看详情
  onChange: (checked: boolean) => void; // 勾选状态变化回调
  className?: string; // 外部样式类
  style?: string | CSSProperties; // 外部样式
  justify?: 'flex-start' | 'center'; // 对齐方式
  onContractClick?: (contract: ContractItem) => void; // 合同点击回调
  prefix?: boolean; // 是否显示“我已阅读并同意”前缀
  activeMode?: boolean; // 是否激活高亮模式
  activeType?: string; // 当前激活合同 code
  previewHeight?: number; // 预览弹窗高度
  lazy?: boolean; // 是否懒加载合同内容
};

/**
 * 组件 Ref 类型
 */
export interface ContractBarRef {
  changeSign: (v: boolean) => void; // 外部控制勾选状态
  startRead: () => void; // 外部触发阅读
}

/**
 * 合同条款栏组件
 */
const CyContractBar: ForwardRefRenderFunction<ContractBarRef, Props> = (
  {
    onChange,
    className,
    style,
    contracts,
    checked: propChecked,
    mustView,
    justify = 'flex-start',
    onContractClick,
    prefix = true,
    activeMode,
    activeType,
    previewHeight = 300,
    lazy = false,
  },
  ref,
) => {
  // 勾选状态
  const [signed, setSigned] = useState<boolean>(propChecked || false);
  // 合同弹窗是否可见
  const [contractVisible, setContractVisible] = useState(false);
  // 当前单个合同（用于单合同弹窗）
  const [singleContract, setSingleContract] = useState<ContractItem>();
  // 勾选框 ref
  const checkboxRef = useRef<CyCheckboxRef>(null);
  // 合同预览 ref
  const previewRef = useRef<CyContractPreviewHtmlRef>(null);
  // 合同内容是否已加载
  const [isLoaded, setIsLoaded] = useState(false);
  // 全局配置
  const {config} = useMainStore();
  // 阅读倒计时（强制阅读秒数）
  const [readCountdown, setReadCountdown] = useState(config.forceReadSeconds);

  // 暴露给父组件的 ref 方法
  React.useImperativeHandle(ref, () => ({
    // 外部控制勾选状态
    changeSign: (v: boolean) => {
      setSigned(v);
    },
    // 外部触发阅读
    startRead: () => {
      checkboxRef.current?.clickCheckbox?.();
    },
  }));

  // 勾选状态变化时，通知父组件
  useEffect(() => {
    onChange?.(signed);
  }, [signed, onChange]);

  /**
   * 勾选框变化事件
   * 1. 校验所有合同 check
   * 2. 必须先阅读时弹窗
   * 3. 直接勾选
   */
  const onCheckboxChange = (e: any) => {
    if (e.touched) {
      for (const contract of contracts) {
        if (contract.check && !contract.check?.()) {
          checkboxRef.current?.setChecked(false);
          return;
        }
      }
    }
    if (mustView && e.detail.value && e.touched) {
      if (lazy) {
        previewRef.current?.loadContracts?.().then(() => {
          setIsLoaded(true);
        });
      }
      setContractVisible(true);
      checkboxRef.current?.setChecked(false);
    } else {
      setSigned(e.detail.value);
    }
  };

  /**
   * 合同弹窗关闭事件
   * 关闭后重置单合同和滚动
   */
  const onContractClose = () => {
    setContractVisible(false);
    setTimeout(() => {
      setSingleContract(undefined);
      previewRef.current?.resetScroll();
    }, 300);
  };

  /**
   * 合同弹窗倒计时副作用
   * 弹窗打开且内容加载后启动倒计时，关闭时重置
   */
  useEffect(() => {
    let timer: NodeJS.Timeout | null = null;
    if (contractVisible && isLoaded) {
      timer = setInterval(() => {
        setReadCountdown(readCountdown => {
          if(readCountdown > 0) {
            return readCountdown - 1
          } else {
            timer && clearInterval(timer);
            return 0
          }
        });
      }, 1000);
    }
    if (!contractVisible) {
      timer && clearInterval(timer);
      setReadCountdown(config.forceReadSeconds);
    }
    return () => {
      timer && clearInterval(timer);
    };
  }, [contractVisible, isLoaded, config.forceReadSeconds]);

  // 弹窗标题
  const title = singleContract ? ContractCode2Info[singleContract.code].name.replace(/《|》/g, '') : '协议详情';

  return (
    <View className={`${styles['contract-bar']} ${styles[`justify-${justify}`]} ${className}`} style={style}>
      {/* 勾选框 */}
      <CyCheckbox ref={checkboxRef} size={18} onChange={onCheckboxChange} />
      <View className={styles['contract-bar-text']}>
        {/* 前缀文案 */}
        {prefix ? <Text>我已阅读并同意</Text> : null}
        {/* 合同列表渲染，每个合同可点击查看详情 */}
        {contracts.map(contract => (
          <Text
            key={contract.code}
            onClick={() => {
              if (onContractClick) {
                onContractClick?.(contract);
              } else {
                if (!contract.check || contract.check?.()) {
                  setContractVisible(true);
                  setSingleContract(contract);
                }
              }
            }}
            className={
              activeMode
                ? activeType === contract.code
                  ? styles['comp-cy-link']
                  : styles['comp-cy-inactive']
                : styles['comp-cy-link']
            }>
            {contract.name || ContractCode2Info[contract.code]?.name}
          </Text>
        ))}
      </View>
      {/* 合同弹窗，支持单合同和多合同模式 */}
      <CyPopup title={title} visible={contractVisible} onClose={onContractClose}>
        <View className={styles.contractContent}>
          <CyContractPreviewHtml
            onLoadEnd={() => {
              setIsLoaded(true);
            }}
            ref={previewRef}
            lazy={lazy}
            singleShowType={singleContract?.code}
            style={{height: previewHeight}}
            contracts={contracts}
          />
        </View>
        {/* 多合同弹窗底部操作栏 */}
        {!singleContract ? (
          <View className={styles.contractPopupFooter}>
            <CyButton
              block
              type='default'
              onClick={() => {
                setContractVisible(false);
                setSigned(false);
              }}>
              不同意
            </CyButton>
            <CyButton
              disabled={readCountdown > 0}
              block
              className={styles['cy-contractbar-confirm-btn']}
              type='primary'
              onClick={() => {
                setContractVisible(false);
                checkboxRef.current?.setChecked(true);
                setSigned(true);
              }}>
              同意{readCountdown > 0 ? `(${readCountdown}s)` : ''}
            </CyButton>
          </View>
        ) : null}
      </CyPopup>
    </View>
  );
};

// 导出带 ref 的组件
export default React.forwardRef(CyContractBar);
