import {Dimensions} from 'react-native';
import {getUniqueIdSync, getVersion} from 'react-native-device-info';

export const getNativeVersion = () => {
  return `${getVersion()}(30)`;
};

export const getNativeDeviceId = () => {
  const deviceId = getUniqueIdSync();
  if (deviceId && typeof deviceId === 'string') {
    return deviceId;
  }
  return '';
};

export const getScreenHeight = () => {
  return Dimensions.get('window').height;
};

export const getScreenWidth = () => {
  return Dimensions.get('window').width;
};
