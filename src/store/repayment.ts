import Taro from '@tarojs/taro';
import {produce} from 'immer';
import {create} from 'zustand';
import {devtools, subscribeWithSelector} from 'zustand/middleware';
import { getRepaymentSummary } from '~services/repayment';
import useUserStore from './user';

type RepaymentState = {
    repaymentSummary: API.RepaymentSummaryRes | undefined;
    updateRepaymentSummary: () => Promise<void>;
    setRepaymentSummary: (repaymentSummary: API.RepaymentSummaryRes) => void;
    resetRepaymentSummary: () => void;
};

const useRepaymentStore = create(
  subscribeWithSelector(
    devtools<RepaymentState>((set, get) => ({
        repaymentSummary: undefined,
        setRepaymentSummary: (repaymentSummary: API.RepaymentSummaryRes | undefined) => {
            set({repaymentSummary});
        },
        updateRepaymentSummary: async () => {
            if(!useUserStore.getState().isHasAmountCreditInfo()) return;
            try {
                const res = await getRepaymentSummary();
                set({repaymentSummary: res});
            } catch (error) {
                console.error(error);
            }
        },
        resetRepaymentSummary: () => {
            set({repaymentSummary: undefined});
        }
    })),
  )
);


export default useRepaymentStore;