import {Text, View} from '@tarojs/components';
import MoneyDisplay from '~components/MoneyDisplay';
import PageWrap from '~components/PageWrap';
import RepaymentAmount from '../components/RepaymentAmount';
import RepaymentBill from '../components/RepaymentBill';
import RepaymentFooterBar from '../components/RepaymentFooterBar';
import styles from './index.module.scss';
import { useEffect, useRef, useState } from 'react';
import { getRepaymentList } from '~services/repayment';
import { useLoad } from '@tarojs/taro';
import CyCheckbox, { CyCheckboxRef } from '~components/CyCheckbox';
import { formattedMoney, router, showToast } from '~utils/common';
import Taro from '@tarojs/taro';
import { CyCheckbox as Checkbox } from 'hncy58-taro-components'
import currency from 'currency.js';

export default () => {
  
  const [repaymentList, setRepaymentList] = useState<API.ListLoanDetail[]>([]);
  const [repaymentOtherInfo, setRepaymentOtherInfo] = useState<Omit<API.RepaymentListRes, 'list'>>()
  const [repayableSummaryCategory, setRepayableSummaryCategory] = useState<API.RepayableSummaryCategory>();
  const [checkedList, setCheckedList] = useState<string[]>([])
  const [btSummary, setBtSummary] = useState<{
    total: number;
    num: number;
    principal: number;
    interest: number;
    penalty: number;
  }>()
  const listCheckboxRef = useRef<CyCheckboxRef>(null)

  useLoad(async ({type}) => {
    try {
      Taro.showLoading();
      setRepayableSummaryCategory(type)
      const {list, ...others} = await getRepaymentList({repayableSummaryCategory: type})
      Taro.hideLoading();
      setRepaymentList(list)
      setRepaymentOtherInfo(others)
    } catch (e) {
      Taro.hideLoading();
    }
  })

  const handleCheckboxChange = (val: any) => {
    setCheckedList(val.detail?.value)
  }

  useEffect(() => {
    const summary = {
      total: 0,
      num: 0,
      principal: 0,
      interest: 0,
      penalty: 0,
    }
    if(Array.isArray(checkedList)) {
      checkedList.forEach((item: string) => {
        const findItem = repaymentList.find((ite) => ite.loanNo === item)
        summary.total = currency(summary.total).add(findItem?.repayableAmount || 0).value
        summary.principal = currency(summary.principal).add(findItem?.repayablePrincipal || 0).value
        summary.interest = currency(summary.interest).add(findItem?.repayableInterest || 0).value
        summary.penalty = currency(summary.penalty).add(findItem?.repayablePenaltyInterest || 0).value
        summary.num += 1
      })
    }
    setBtSummary(summary) 
  }, [checkedList])

  const goConfirmPage = () => {
    if(repaymentOtherInfo?.maxRecordNum && checkedList?.length > repaymentOtherInfo?.maxRecordNum) {
      showToast(`最多只能选择${repaymentOtherInfo?.maxRecordNum}笔账单进行还款`)
      return
    }
    console.log('formattedMoney== to pay', formattedMoney(`${btSummary?.total || 0}`, {symbol: '', precision: 2, separator: ''}))
    router.push({
      url: `/modules/repayment/repayment-confirm/index?loanNos=${checkedList.join(',')}&category=${repayableSummaryCategory}&repayAmount=${formattedMoney(`${btSummary?.total || 0}`, {symbol: '', precision: 2, separator: ''})}`,
    })
  }

  const onSelectAllClick = (e: any) => {
    if(!e.touched) return;
    if(e.detail?.value) {
      listCheckboxRef.current?.selectAll?.(repaymentOtherInfo?.maxRecordNum)
    } else {
      listCheckboxRef.current?.unSelectAll?.()
    }
  }

  return (
    <PageWrap className={styles.periodsContainer}>
      <View className={styles.topAmountShortcut}><RepaymentAmount amount={repaymentOtherInfo?.sumAmount} amountTitle='剩余待还金额（元）' /></View>
      <View className={styles.bill}>

      <CyCheckbox ref={listCheckboxRef} options={repaymentList?.map((ite, index) => {
          return {
            label: '',
            child:  (
              <RepaymentBill status={ite.loanStatus} principal={ite.principal} remainTerms={ite.remainTerms} key={index} amount={repayableSummaryCategory === 'AHEAD' ? ite.remainPrincipal : ite.repayableAmount} date={ite.startDate} loanNo={ite.loanNo}/>
            ),
            value: ite.loanNo
          }
        })} maxSelect={repaymentOtherInfo?.maxRecordNum} maxSelectErrorMessage={`最多只能选择${repaymentOtherInfo?.maxRecordNum}笔账单进行还款`} onChange={handleCheckboxChange}></CyCheckbox>
      </View>
      <RepaymentFooterBar
        tips={
          repaymentOtherInfo?.enableRepay === false ? `提示：当前为自动扣款时间段(${repaymentOtherInfo?.startTime}-${repaymentOtherInfo?.endTime})，暂不支持主动还款，建议您稍后再试` :  ''
        }
        content={
          <>
            <View className='flex'><Checkbox onChange={onSelectAllClick} /><Text>全选</Text></View>
            <View className={styles.footerBarText}>
              合计：
              <MoneyDisplay money={btSummary?.total || 0} color='#2F54EB' />
              <Text className={styles.numContainer}>
                共<Text className={styles.num}>{btSummary?.num}</Text>笔
              </Text>
            </View>
            <View className={styles.footerBarSubText}>含: 本金：¥{btSummary?.principal.toFixed(2)}, 利息: ¥{btSummary?.interest.toFixed(2)}</View>
            {btSummary?.penalty && btSummary.penalty > 0 ? <View className={styles.footerBarSubTextRow2}>罚息: ¥{btSummary?.penalty.toFixed(2)}</View> : null}
          </>
        }
        btnText='立即还款'
        disabled={!checkedList?.length || !repaymentOtherInfo?.enableRepay}
        onBtnClick={goConfirmPage}
      />
    </PageWrap>
  );
};
