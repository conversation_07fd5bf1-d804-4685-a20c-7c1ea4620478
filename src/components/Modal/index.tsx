import React, {useEffect, useMemo, useState} from 'react';
import {View} from '@tarojs/components';
import Taro from '@tarojs/taro';
import styles from './index.module.scss';

type ButtonItem = {
  text: string;
  type?: 'primary' | 'default';
  onClick?: () => void;
};

export interface ModalProps {
  // 是否显示弹窗
  visible?: boolean;
  // 标题
  title?: React.ReactNode;
  // 内容
  content?: React.ReactNode;
  // footer配置
  footer?: React.ReactNode;
  // 自定义渲染
  modalRender?: React.ReactNode;
  // 按钮配置
  buttons?: ButtonItem[];
  // 关闭回调
  onClose?: () => void;
  // 点击蒙层是否关闭
  maskClosable?: boolean;
  isShowCancel?: boolean;
  onCancel?: () => void;
  onConfirm?: () => void;
  confirmText?: string;
  cancelText?: string;
}

const Modal: React.FC<ModalProps> = ({
  visible,
  title,
  content,
  buttons = [],
  onClose,
  maskClosable = false,
  isShowCancel = false,
  onCancel,
  onConfirm,
  confirmText,
  cancelText,
  footer,
  modalRender,
}) => {
  const isControlComponent = visible !== undefined;
  const [isVisible, setIsVisible] = useState(false);
  const [isActive, setIsActive] = useState(false);

  const open = () => {
    setIsVisible(true);
    setTimeout(() => {
      setIsActive(true);
    }, 300);
  };

  const close = async () => {
    setIsActive(false);
    await new Promise(resolve =>
      setTimeout(() => {
        setIsVisible(false);
        resolve(true);
      }, 300),
    );
  };

  useEffect(() => {
    if (!isControlComponent) {
      return;
    }
    if (visible) {
      open();
    } else {
      close();
    }
  }, [visible]);

  useEffect(() => {
    if (!isControlComponent) {
      open();
      return;
    }
    if (visible) {
      open();
    } else {
      close();
    }
  }, []);

  const handleClose = async () => {
    if (!isControlComponent) {
      await close();
    }
    onClose?.();
  };

  const handleMaskClick = () => {
    if (maskClosable) {
      handleClose();
    }
  };

  const handleButtonClick = async (onClick?: () => void) => {
    if (onClick) {
      await onClick();
      return;
    }
    handleClose();
  };

  const finButtons = useMemo(() => {
    const defaultButtons: ButtonItem[] = [
      {
        text: confirmText || '确定',
        type: 'primary',
        onClick: () => {
          handleClose();
          onConfirm?.();
        },
      },
    ];
    if (buttons?.length) return buttons;
    if (isShowCancel) {
      defaultButtons.unshift({
        text: cancelText || '取消',
        onClick: () => {
          handleClose();
          onCancel?.();
        },
      });
    }
    return defaultButtons;
  }, [isShowCancel, buttons]);

  if (!isVisible) return null;

  const presetRender = () => (
    <>
      {title ? <View className={styles.title}>{title}</View> : null}
      {content ? <View className={styles.body}>{content}</View> : null}
      {footer
        ? footer
        : finButtons.length > 0 && (
            <View className={styles.footer}>
              {finButtons.map((btn, index) => (
                <View
                  key={index}
                  className={`${styles.button} ${btn.type === 'primary' ? styles.primary : ''}`}
                  onClick={() => handleButtonClick(btn.onClick)}>
                  {btn.text}
                </View>
              ))}
            </View>
          )}
    </>
  );

  return (
    <View className={`${styles.modalWrapper} ${isActive ? styles.visible : ''}`}>
      <View className={styles.mask} onClick={handleMaskClick} />
      <View className={styles.content}>{modalRender ? modalRender : presetRender()}</View>
    </View>
  );
};

export default Modal;
