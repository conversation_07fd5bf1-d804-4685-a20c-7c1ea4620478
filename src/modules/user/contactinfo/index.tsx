import { useEffect, useRef, useState } from 'react';
import { CyButton, CySafeArea, CyCheckbox, CyCheckboxRef, CyModal } from 'hncy58-taro-components';
import Taro, { useLoad, useReady } from '@tarojs/taro';
import { Checkbox, CheckboxGroup, Image, Picker, Text, View, Input } from '@tarojs/components';
import Card from '~components/Card';
import CellItem from '~components/CellItem';
import CellPickerItem from '~components/CellPickerItem';
import PageWrap from '~components/PageWrap';
import Steps, { Step } from '~components/Steps';
import contactIcon from '~images/icon/<EMAIL>';
import { getLabelByValue, router, showModal, showToast, showLoading, hideLoading } from '~utils/common';
import styles from './index.module.scss';
import { updateContactInfo, getContactInfo } from '~services/user'
import useDict from '~hooks/useDict';
import { IPickerItem } from 'src/types/common-types';
import useUserStore from '~store/user';
import { validateForm, ValidationRule } from '~utils/validate';
import { regExpMap } from '~utils/regex';
import usePersonInfoStore from '~store/personInfo';
import goLendingPreProgress from '~utils/goLendingPreProgress';
import useFace from '~hooks/useFace';
import RequiredLabel from '../baseinfo/components/RequireLabel';

const infoSteps: Step[] = [
  {
    title: '基本信息',
    status: 'process',
  },
  {
    title: '单位信息',
    status: 'process',
  },
  {
    title: '联系人信息',
    status: 'process',
  },
];

export default () => {
  const { startFace } = useFace()
  const { user } = useUserStore();
  const { setContactInfo, resetContactInfo, contactInfo } = usePersonInfoStore();
  const checkboxRef = useRef<CyCheckboxRef>(null);
  const [signed, setSigned] = useState(false);
  const [notFromMine, setNotFromMine] = useState<boolean>(true);
  const [current, setCurrent] = useState<number>(3);
  const [relationshipMap, setRelationshipMap] = useState<IPickerItem[]>([])
  const [friendRelationshipMap, setFriendFriendRelationshipMap] = useState<IPickerItem[]>([])
  const [formData, setFormData] = useState<API.contactInfoFormData>({
    contact1Relation: '',
    contact1Mobile: '',
    contact1Name: '',
    contact2Mobile: '',
    contact2Name: '',
    contact2Relation: '',
  });
  const isFromLending = useRef(false)

  const formRules: Record<string, ValidationRule[]> = {
    contact1Relation: [
      {
        required: true,
        message: '请选择第一联系人关系',
      },
    ],
    contact1Name: [
      {
        required: true,
        message: '请输入第一联系人姓名',
      },
      {
        pattern: regExpMap.name,
        message: '第一联系人姓名格式不正确',
      }
    ],
    contact1Mobile: [
      {
        required: true,
        message: '请输入第一联系人手机号码',
      },
      {
        pattern: regExpMap.mobile,
        message: '第一联系人手机号码格式不正确',
      }
    ],
    contact2Relation: [
      {
        required: true,
        message: '请选择第二联系人关系',
      },
    ],
    contact2Name: [
      {
        required: true,
        message: '请输入第二联系人姓名',
      },
      {
        pattern: regExpMap.name,
        message: '第二联系人姓名格式不正确',
      }
    ],
    contact2Mobile: [
      {
        required: true,
        message: '请输入第二联系人手机号码',
      },
      {
        pattern: regExpMap.mobile,
        message: '第二联系人手机号码格式不正确',
      }
    ],
  }

  useEffect(() => {
    const params = Taro.getCurrentInstance().router?.params;
    setNotFromMine(params?.from !== 'mine');
    getDictMap()
  }, [])

  useReady(() => {
    console.log('页面ready', contactInfo);
    if (contactInfo && contactInfo.custUid === user.custUid) {
      CyModal.create({
        title: '温馨提示',
        content: `检测到有尚未保存的信息，是否继续填写？`,
        confirmText: `确定`,
        isShowCancel: true,
        onConfirm() {
          setFormData(contactInfo)
        },
        onCancel() {
          queryContactInfo()
        },
      })
    } else {
      queryContactInfo()
      resetContactInfo()
    }
  })

  useLoad((query) => {
    if (query.from === 'lending') {
      isFromLending.current = true;
    }
  })

  const getDictMap = async () => {
    const res = await Promise.all([
      useDict('CONTACT_RELATION'),
      useDict('FRIEND_CONTACT_RELATION'),
    ]);
    if (res.length) {
      setRelationshipMap(res[0].dictMap);
      setFriendFriendRelationshipMap(res[1].dictMap);
    }
  }

  const queryContactInfo = async () => {
    try {
      const { contactList } = await getContactInfo()
      if (contactList && contactList.length > 0) {
        setFormData({
          contact1Relation: contactList[0].relation || '',
          contact1Name: contactList[0].name || '',
          contact1Mobile: contactList[0].mobileNo || '',
          contact2Relation: contactList[1].relation || '',
          contact2Name: contactList[1].name || '',
          contact2Mobile: contactList[1].mobileNo || '',
        })
      }
    } catch (error) {
      console.log('请求错误', error)
    }

  }

  const updateFormData = (kv: Partial<API.contactInfoFormData>) => {
    const newFormData = { ...formData, ...kv };
    setContactInfo({ ...newFormData, custUid: user.custUid });
    setFormData(newFormData);
  };

  const onCheckboxChange = (e: any) => {
    setSigned(e.detail.value);
  }
  const goNext = async () => {
    // TODO: 校验表单 + 提交数据
    console.log('goNExt====');
    const result = validateForm(formData, formRules);

    if (!result.valid && result.errors) {
      const firstError = Object.values(result.errors)[0];
      showToast(firstError);
      return
    }
    if (formData.contact1Name === formData.contact2Name) {
      showToast('两个联系人不能是同一个人')
      return
    }
    if (formData.contact1Mobile === formData.contact2Mobile) {
      showToast('两个联系人手机号不能是同个手机号')
      return
    }
    if (!signed && notFromMine) {
      showToast('请勾选并同意以上信息');
      return
    }
    startFace({
      requestFrom: 'updateCustomerInfo',
      faceSuccess: () => {
        updateInfo();
      },
      faceFail: () => {
        showToast('活体识别失败');
      },
    })
  };
  const updateInfo = async () => {
    try {
      showLoading()
      const res = await updateContactInfo({
        contactList: [
          { relation: formData.contact1Relation, mobileNo: formData.contact1Mobile, name: formData.contact1Name },
          { relation: formData.contact2Relation, mobileNo: formData.contact2Mobile, name: formData.contact2Name }
        ],
      });
      resetContactInfo();
      showToast({
        message: '提交成功',
        type: 'success',
      });
      setTimeout(() => {
        if (isFromLending.current) {
          goLendingPreProgress();
        } else {
          if (notFromMine) {
            const url = Taro.getStorageSync('backUrl')
            if (url) {
              Taro.removeStorageSync('backUrl')
              router.reLaunch({ url })
            } else {
              router.reLaunch({
                url: '/pages/check/index'
              })
            }
          } else {
            router.back();
          }
        }
      }, 2000)
    } catch (error) {
      console.log('请求错误', error);
    } finally {
      hideLoading();
    }
  }
  const chooseContact = (nameKey: keyof API.contactInfoFormData, mobileKey: keyof API.contactInfoFormData) => {
    Taro.chooseContact({
      success(res) {
        console.info(res.displayName);
        updateFormData({
          [nameKey]: res.displayName,
          [mobileKey]: res.phoneNumber,
        });
      },
    });
  };
  return (
    <PageWrap className={`${styles.baseinfoPage} ${notFromMine ? '' : styles.pdt20}`}>
      {notFromMine && (
        <View className={styles.pageTop}>
          <Steps steps={infoSteps} current={current} />
        </View>
      )}
      <View className={styles.pageContent}>
        <Card title='联系人信息' note='补充亲属、朋友等联系人信息'>
          <CellPickerItem
            className={styles.CellItem}
            title={<RequiredLabel label='联系人' required />}
            placeholder='请选择联系人'
            range={relationshipMap}
            value={getLabelByValue(relationshipMap, formData.contact1Relation || '')}
            pickValue={formData.contact1Relation}
            onChange={contact1Relation => updateFormData({ contact1Relation })}
            clickable
          />
          <CellItem
            className={styles.CellItem}
            title={<RequiredLabel label='姓名' required />}
            right={
              <Input
                placeholderClass={styles.inputPlaceholder}
                className={styles.rightInput}
                placeholder='请输入联系人姓名'
                value={formData.contact1Name}
                onInput={(e: any) => updateFormData({ contact1Name: e.detail.value })}
              />
            }
            clickable
          />
          <CellItem
            className={styles.CellItem}
            title={<RequiredLabel label='手机号码' required />}
            right={
              <>
                <Input
                  placeholderClass={styles.inputPlaceholder}
                  type='number'
                  className={styles.rightInput}
                  value={formData.contact1Mobile}
                  maxlength={11}
                  placeholder='请输入联系人手机号码'
                  onInput={(e: any) => updateFormData({ contact1Mobile: e.detail.value })}
                />
                <Image onClick={() => chooseContact('contact1Name', 'contact1Mobile')} src={contactIcon} className={styles.contactImg} />
              </>
            }
            clickable
          />
        </Card>
        <Card style={{ padding: 0, marginTop: 10 }}>
          <CellPickerItem
            className={styles.CellItem}
            title={<RequiredLabel label='联系人' required />}
            placeholder='请选择联系人'
            range={friendRelationshipMap}
            value={getLabelByValue(friendRelationshipMap, formData.contact2Relation || '')}
            pickValue={formData.contact2Relation}
            onChange={contact2Relation => updateFormData({ contact2Relation })}
            clickable
          />
          <CellItem
            className={styles.CellItem}
            title={<RequiredLabel label='姓名' required />}
            right={
              <Input
                placeholderClass={styles.inputPlaceholder}
                className={styles.rightInput}
                placeholder='请输入联系人姓名'
                value={formData.contact2Name}
                onInput={(e: any) => updateFormData({ contact2Name: e.detail.value })}
              />
            }
            clickable
          />
          <CellItem
            className={styles.CellItem}
            title={<RequiredLabel label='手机号码' required />}
            right={
              <>
                <Input
                  placeholderClass={styles.inputPlaceholder}
                  type='number'
                  className={styles.rightInput}
                  maxlength={11}
                  placeholder='请输入联系人手机号码'
                  value={formData.contact2Mobile}
                  onInput={(e: any) => updateFormData({ contact2Mobile: e.detail.value })}
                />
                <Image onClick={() => chooseContact('contact2Name', 'contact2Mobile')} src={contactIcon} className={styles.contactImg} />
              </>
            }
            clickable
          />
        </Card>
      </View>
      {
        notFromMine && (
          <View className={styles.contractBar}>
            <CyCheckbox ref={checkboxRef} size={18} onChange={onCheckboxChange} />
            <View>以上信息为本人自愿提供并对真实性负责，在您借款后， 我公司将以此信息报送中国人民银行征信中心，若虚假填写，将影响您额度使用</View>
          </View>
        )
      }
      <View className={`${styles['bottom-fixed']} bottom-fixed`}>
        <CyButton round block type='primary' onClick={goNext} className={styles.btn}>
          {notFromMine ? '下一步' : '提交'}
        </CyButton>
        <CySafeArea position='bottom' />
      </View>
    </PageWrap>
  );
};
