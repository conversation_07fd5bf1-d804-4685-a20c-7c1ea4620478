import {Image, Swiper, SwiperItem, Text, View} from '@tarojs/components';
import styles from './index.module.scss';
import { useUserRegisterInterceptor } from '~hooks/useUserRegisterInterceptor';


export default () => {
  const {goLinkWithLoginInterceptor} = useUserRegisterInterceptor();
  return (
    <Swiper
      className={styles.activitySwiper}
      circular
      interval={3000}
      autoplay
      indicatorDots
      indicatorColor='rgba(200, 200, 200, .3)'
      indicatorActiveColor='#666'>
      <SwiperItem className={styles.activitySwiperItem} onClick={() => goLinkWithLoginInterceptor('https://sit.hncy58test.com/activity/activity/wheel.html?activityId=204595361208078337', true)}>
        <Image className={styles.activitySwiperImg} src='https://hncy58test-cyd.oss-cn-beijing.aliyuncs.com/multi-platform/activity/big-banner.png' />
      </SwiperItem>
      {/* <SwiperItem>
        <Image className={styles.activitySwiperImg} src='https://hncy58test-cyd.oss-cn-beijing.aliyuncs.com/multi-platform/activity/big-banner.png' />
      </SwiperItem> */}
    </Swiper>
  );
};
