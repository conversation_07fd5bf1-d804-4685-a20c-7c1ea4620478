.card_container {
  padding: 40px 0;
  background-color: #fff;
  border-radius: 16px;

  &.card_error {
    .card_title {
      border-left: 6px $color-error solid;
    }
  }

  .card_title {
    position: relative;
    display: flex;
    align-items: center;
    margin-bottom: 16px;
    color: #333;
    font-size: 32px;
    font-weight: bold;
    line-height: 1;
    border-left: 6px $color-primary solid;
    padding-left: 40px;
  }
  .card_tips {
    color: #999;
    font-size: 26px;
    line-height: 1.5;
    padding-left: 40px;
  }

  .card_arrow {
    margin-left: auto;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: transform 0.3s ease;
    padding: 0 20px;

    .card_arrow_icon {
      width: 18px;
      height: 18px;
      position: relative;

      &:after {
        content: '';
        position: absolute;
        width: 18px;
        height: 18px;
        border-right: 2px solid #999;
        border-bottom: 2px solid #999;
        transform: rotate(45deg);
        top: 0;
        left: 0;
      }
    }

    &.card_arrow_up {
      transform: rotate(180deg);
    }
  }

  .card_content {
    transition: max-height 0.3s ease-in-out;
    max-height: 9999px;
  }

  .card_content_collapsed {
    max-height: 0;
    transition: max-height 0.3s ease-in-out;
  }

  .card_over_hidden {
    overflow: hidden;
  }
}
