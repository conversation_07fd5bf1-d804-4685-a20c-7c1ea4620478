import {FC, useCallback} from 'react';
import {ITouchEvent, View, ViewProps} from '@tarojs/components';
import throttle from '../../utils/throttle';
import './index.scss';

/**
 * 组件名称：节流组件
 * 组件描述：对click事件进行节流处理
 */
const CyThrottleClickView: FC<ViewProps> = props => {
  const onClick = useCallback(
    throttle((e: ITouchEvent) => {
      props.onClick && props.onClick(e);
    }),
    [],
  );

  return (
    <View onClick={onClick} className={props.className} style={props.style}>
      {props.children}
    </View>
  );
};

export default CyThrottleClickView;
