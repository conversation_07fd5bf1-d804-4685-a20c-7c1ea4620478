import {getStorageSync, hideLoading, showLoading} from '@tarojs/taro';
import {AxiosError, AxiosResponse, InternalAxiosRequestConfig} from 'axios';
import {router, showModal, showToast} from '../utils/common';
import {CyHttpError} from './index';
import Taro from '@tarojs/taro';
import useUserStore from '~store/user';
import request, {RequestConfig} from './request';
import refreshToken from './refreshToken';
import useMainStore from '~store/main';

export const enum HTTP_STATUS {
  SUCCESS = 200,
  CLIENT_ERROR = 400,
  AUTHENTICATE = 401,
  FORBIDDEN = 403,
  NOT_FOUND = 404,
  SERVER_ERROR = 500,
  BAD_GATEWAY = 502,
  SERVICE_UNAVAILABLE = 503,
  GATEWAY_TIMEOUT = 504,
}

export type CommonResponse = {
  data?: {status?: string};
  error_message: string;
  message: string;
  code: string;
  error: string;
};

let requestCount = 0;
const startLoading = () => {
  requestCount++;
  if (requestCount === 1)
    showLoading({
      title: '加载中',
      mask: true,
    });
};
const closeLoading = () => {
  requestCount--;
  if (requestCount === 0) hideLoading();
};

// 返回成功拦截(数据统一处理)
function resSucInterceptor(response: AxiosResponse) {
  const {config} = response;
  const {loading = false, autoDecrypt = false} = config;
  //对加密数据进行统一处理
  // if (autoDecrypt && response.data['message'] && '报文已加密' === response.data['message']) {
  //   try {
  //     const enStr = decryptEcb(response.data.data);
  //     const deData = JSON.parse(enStr);
  //     response.data = deData;
  //   } catch (error) {
  //     showToast('数据解析异常', error?.message || '');
  //   }
  // }
  if (response.data && response.data.code === '0') {
    response.data = response.data.data;
  }
  if (loading) closeLoading();
  return response;
}

// 返回失败后拦截(错误处理)
const resErrInterceptor = (error: AxiosError) => {
  if (error.config?.loading) closeLoading();
  const status = error.response?.status;
  if (status === HTTP_STATUS.NOT_FOUND || status === HTTP_STATUS.BAD_GATEWAY) {
    showToast('服务器异常!');
    return Promise.reject(error);
  }
  if (error.response && error.response.data) {
    const data = error.response.data as CommonResponse;
    const cyError = new CyHttpError(data.code, data.message, data.data?.status ?? '');
    /** 如果传入了hideErrorToast，隐藏错误toast */
    if (error.config?.args?.hideErrorToast) {
      return Promise.reject(cyError);
    }
    console.log('error response', error.response, error)
    if(error.response.status === HTTP_STATUS.AUTHENTICATE) {
      // 401状态码表示未授权，需要刷新token并重试请求
      return refreshToken().then(() => {
        // 确保 error.config 存在且符合 RequestConfig 类型
        if (error.config) {
          // 确保 error.config 不为 undefined 并且符合 RequestConfig 类型
          const requestConfig = error.config as RequestConfig;
          return request(requestConfig);
        } else {
          // 如果没有请求配置，则无法重试
          return Promise.reject(new Error('无法重试请求：缺少请求配置'));
        }
      }).catch(() => {
        // 刷新token失败，继续抛出错误
        return Promise.reject(cyError);
      });
    }
    if (data.error_message || data.error) {
      showToast(data.error_message || data.error);
      const cyError = new CyHttpError(data.error_message || data.code, data.error, data.data?.status ?? '');
      return Promise.reject(cyError);
    } else {
      //判断token是否过期
      if (data.code === '3' || data.code === '1000001') {
        // token过期，需要刷新token并重试请求
        return refreshToken().then((token) => {
          // 确保 error.config 存在且符合 RequestConfig 类型
          if (error.config) {
            // 重试原请求
            // 确保 error.config 不为 undefined 并且符合 RequestConfig 类型
            const requestConfig = error.config as RequestConfig;
            return request(requestConfig);
          } else {
            // 如果没有请求配置，则无法重试
            return Promise.reject(new Error('无法重试请求：缺少请求配置'));
          }
        }).catch(() => {
          // 刷新token失败，继续抛出错误
          return Promise.reject(cyError);
        });
      } else if (data.code === '7') {
        if (data.message === 'Incorrect username or password' || data.message === '用户名或密码错误') {
          showToast('用户名或密码错误!');
        }
        if (data.message === 'Concurrency is not supported') {
          showToast('您点得太频繁了!');
        }
        if (data.message === 'Your account is disabled') {
          showToast('您的账号未激活！');
        } else if (data.message !== '') {
          if (!error.config?.notAutoToastWhenError) {
            showToast(data.message);
          }
        } else {
          showToast('会话超时,请重新登录!');
        }
      } else if (data.code === '20002') {
        if (data.message === '查询风控已认领客户失败：查询超过最大页数.') {
          showToast('数据已全部加载完毕');
        }
      } else if (data.code === '-1' && !error.config?.notAutoToastWhenError) {
        if (error.config?.args?.needIntercept) {
          return Promise.reject(cyError);
        }
        showToast(data.message || '服务器出错了，请稍候再试！');
      } else if (data.code === '*********') {
        showModal({
          title: '温馨提示',
          content: '该银行卡需要进行挂失解绑流程后才可重新绑定，请至“城一代”APP或微信公众号进行绑卡处理',
          confirmText: '确定',
          confirmColor: '#2F54EB',
          showCancel: false
        })
      } else if (data.code === 'UCS_10101') {
        router.reLaunch({
          url: `/modules/user/intercept-login/index?code=${data.code}`,
        });
        return Promise.reject(new CyHttpError(data.code, data.message, data.data?.status ?? ''))
      } else if (data.code === 'UCS_10102') {
        router.reLaunch({
          url: `/modules/user/intercept-login/index?code=${data.code}`,
        });
        return Promise.reject(new CyHttpError(data.code, data.message, data.data?.status ?? ''))
      } else if (['UCS_11003', 'UCS_11004'].includes(data.code)) {
        router.replace({
          url: `/modules/user/login/index?type=intercept&redirectUrl=${encodeURIComponent('/' + Taro.getCurrentPages()[0].route || '')}`,
        });
        return Promise.reject(new CyHttpError(data.code, data.message, data.data?.status ?? ''))
      } else {
        if (data.message !== undefined && data.message !== '') {
          if (data.message !== '同步客户照片远程服务异常' && !error.config?.notAutoToastWhenError) {
            showToast(data.message);
          }
        }
      }
    }
    return Promise.reject(cyError);
  }

  let message = error.message as string;
  if (message.includes('timeout of')) {
    error.message = '网络请求超时';
  } else if (message.includes('Network Error')) {
    error.message = '网络请求出错/超时';
  } else if (message === 'Request aborted') {
    error.message = '网络请求出错';
  }
  return Promise.reject(error);
};


// refreshToken 函数已被移除，直接使用 refresh 函数处理 token 刷新

// 请求成功拦截
async function reqSucInterceptor(config: InternalAxiosRequestConfig) {
  config = config || {};
  const {loading = false, isToken = true} = config;
  if (loading) startLoading();
  const {token} = useMainStore.getState();
  if (token && isToken) {
    config.headers['token'] = `${token}`;
  }
  return config;
}

export {reqSucInterceptor, resErrInterceptor, resSucInterceptor};
