import {useEffect, useRef, useState} from 'react';
import {CyButton, CyModal, CySafeArea} from 'hncy58-taro-components';
import {Canvas, Image, Input, Text, View} from '@tarojs/components';
import Taro, {createSelectorQuery, getFileSystemManager} from '@tarojs/taro';
import Card from '~components/Card';
import CyContractBar from '~components/CyContractBar';
import PageWrap from '~components/PageWrap';
import styles from './index.module.scss';
import { UploadInfoListCard } from '../components/UploadInfoListCard';


const UploadInfo = () => {
  /** 1. 变量定义 */
  
  /** 2. usestates + storestates */
  
  /** 3. effects */

  /** 4. 方法定义 */

  /** 5. ui */
  return (
    <PageWrap>
      <View className={styles['page-upload-info']}>
        <UploadInfoListCard />
      </View>
    </PageWrap>
  );
};

export default UploadInfo;
