.skeleton-group {
  display: flex;
  width: 100%;
  
  // 列布局
  &-column {
    flex-direction: column;
    
    .skeleton-group-avatar {
      margin-bottom: 16px;
    }
  }
  
  // 行布局
  &-row {
    flex-direction: row;
    align-items: flex-start;
    
    .skeleton-group-avatar {
      margin-right: 16px;
      flex-shrink: 0;
    }
    
    .skeleton-group-content {
      flex: 1;
    }
  }
  
  // 反向行布局
  &-row-reverse {
    flex-direction: row-reverse;
    align-items: flex-start;
    
    .skeleton-group-avatar {
      margin-left: 16px;
      flex-shrink: 0;
    }
    
    .skeleton-group-content {
      flex: 1;
    }
  }
  
  &-content {
    width: 100%;
  }
  
  &-row {
    width: 100%;
  }
} 