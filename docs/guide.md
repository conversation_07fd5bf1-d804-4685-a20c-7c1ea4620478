# 开发指引

## 环境要求

- node 版本要求，建议安装[nvm](https://github.com/nvm-sh/nvm)管理

```
"engines": {
    "node": "^18.0.0 || >=20.0.0"  # node版本18 或 > 20
}

//建议使用以下nvm命令将nvm默认设置为所需版本
nvm alias default vxx.xx
```

- yarn
- 编辑器
  - 推荐使用vscode
  - vscode需要安装 eslint + stylelint 插件

## 包管理

    使用 yarn 进行包管理

## 启动运行项目

```
yarn
yarn start # 需要做出选择 1. 运行的端 2. 环境 3. 调试/编译，按照提示选择需要运行的场景，如：h5 + sit + 调试
```

## 相关库文档

1. 基础能力可参考[taro 教程](https://taro-docs.jd.com/docs/guide)
2. ui 组件可先到[taro-ui](https://taro-ui.jd.com/#/docs/introduction)库查找是否有可使用组件
3. 使用 hooks 能力，可先到[taro-hooks](https://next-taro-hooks.pages.dev/site/hooks/intro)了解是否有可用实现
4. 全局状态管理[zustand](https://awesomedevin.github.io/zustand-vue/docs/introduce/what-is-zustand)

## 新增页面、组件

项目提供了快速创建模板页面/组件的脚本，按照脚本提示可以方便的新增

```
# 1.运行以下命令

yarn auto

# 选择创建 页面/ 组件

# 按要求格式输入创建的 页面/组件 文件名称

# 按要求格式输入 页面/组件 中文名称

# 按要求格式输入 页面/组件 描述

```

## 项目编码规范

<div class="tip custom-block" style="padding-top: 8px">
    规范还在进一步完善中，待引入检测及lint机制
</div>

- page 文件命名 形如: aa-bb
- component 文件命名 形如: AaBb
- 组件标签书写：无子元素使用单闭合标签书写
- 文件内导出建议不要使用匿名导出

## 样式

- 单位：样式默认使用 px，设计稿以宽度 750px 为标准
- UI 库：[taro-ui](https://taro-ui.jd.com/#/docs/introduction) (注意: 部分组件不支持 RN 端)
- 主题：taro-ui 暂不支持动态 css 变量，故暂时无法实现主题切换功能
- 全局样式

```
    .
    ├─ src
        └─ styles
            └─ custom-variables.scss # ui主题变量覆盖、自定义scss变量
            └─ global.scss # 全局样式定义文件

```

- 页面/组件样式： 默认写到页面/组件文件夹 index.scss 中即可

## typescript 类型定义

#### 全局类型定义

如需要定义顶层变量如 window 等，须在以下文件定义

```
## 配置文件
.
├─ typings
└─ global.d.ts # 全局类型定义

```

```
## 示例

declare global {
    const env: string;
}

```

#### 三方库类型定义拓展

部分场景引入的三方库类型定义需要被拓展，这个时候就需要在三方库基础上添加自定义的类型定义

```

#### 配置文件

.
├─ typings
└─ shime-[lib].d.ts # lib 库的类型定义

```

```typescript
// 示例

export {};

declare module 'axios' {
  interface AxiosRequestConfig {
    loading?: boolean;
    isToken?: boolean;
  }
}
```

#### 自写代码类型定义

##### 接口请求参数/返回参数类型定义

```

#### 在指定的定义文件目录中定义

.
└─ src
└─ services
└─ typings.d.ts # 接口参数类型定义文件

```

##### 其他

<div class="tip custom-block" style="padding-top: 8px">
    建议根据行数斟酌：
        1. 若文件行数本身不多，定义及实现都写到文件中即可
        1. 若文件行数过多需要经常上下滑动来书写定义及实现，此时建议将类型拆分到单独文件中 文件命名为typings.d.ts即可
</div>

## 网络请求

- 项目采用 axios + useRequest(taro-hooks) 结合的方式进行实现
- 支持的能力：
  - 自定义请求头
  - 自定义拦截器
  - 自定义错误处理
  - useRequest 的能力(如：防抖节流、轮询、错误重试等)，参考：[文档](https://next-taro-hooks.pages.dev/site/hooks/useRequest/)

新增网络请求步骤

1. src/services 目录下新增文件或在现有文件添加 request 方法
2. 通过 taro hooks 的 useRequest 组合 request 方法的方式使用, 示例如下

```javascript
/**
 * request 方法定义
 * 用户获取access_token
 */
const getAuthToken = ({code, username}: API.AuthTokenReq) =>
  request <
  API.AuthTokenRes >
  {
    url: '/ces/oauth/oauth2/token',
    method: 'GET',
    data: {
      grant_type: 'password',
      client_id: process.env.TARO_APP_CLIENT_ID,
      client_secret: process.env.TARO_APP_CLIENT_SECRET,
      password: code,
      username,
    },
  };

/**
 * 页面代码使用
 */
const {data, error, loading} = useRequest(getAccessToken());
// data 为正常返回的数据、error可接收作为错误处理、loading可用来做加载态标识
```

## 环境变量

#### 配置文件

项目采用 taro[自定义环境变量](https://docs.taro.zone/docs/env-mode-config/#%E8%87%AA%E5%AE%9A%E4%B9%89%E6%A8%A1%E5%BC%8F)实现，指定了 5 种 mode 的环境变量配置文件

```
.
├─ .env # 默认环境变量
├─ .env.bbit  # 测试环境 bbit 环境变量
├─ .env.sit  # 测试环境 sit 环境变量
├─ .env.uat  # 测试环境 uat 环境变量
└─ .env.production # 生产环境 环境变量

```

#### 环境变量新增

确认环境变量的使用场景，在相应的配置文件中配置，注意：环境变量需要使用 TARO*APP* 开头，如： TARO_APP_API

## 生命周期


## 状态管理


#### 注意点

1. 有多个状态值依赖时且依赖状态值的重渲染不需要重叠，应该使用以下3种方式获取这些状态值

``` javascript
import shallow from 'zustand/shallow'

// Object pick, re-renders the component when either state.nuts or state.honey change
const { nuts, honey } = useStore(state => ({ nuts: state.nuts, honey: state.honey }), shallow)

// Array pick, re-renders the component when either state.nuts or state.honey change
const [nuts, honey] = useStore(state => [state.nuts, state.honey], shallow)

// Mapped picks, re-renders the component when state.treats changes in order, count or keys
const treats = useStore(state => Object.keys(state.treats), shallow)
```

2. 若是组件需要的都是actions，不需要考虑重渲染问题, 可以使用以下方式不需要使用selector的方式

```javascript
// login / logout 都是action
const {login, logout} = useUserStore();

```