import {FC} from 'react';
import {EdgeInsets, SafeAreaFrameContext, useSafeAreaInsets} from 'react-native-safe-area-context';
import {View, ViewProps} from '@tarojs/components';
import './index.scss';

interface Props {
  showHeader?: boolean;
  showFooter?: boolean;
  headerBgColor?: string;
  footerBgColor?: string;
}

/**
 * 组件名称：安全区域
 * 组件描述：安全区域
 */
const CySafeArea: FC<Props & ViewProps> = ({
  children,
  showHeader = true,
  showFooter = true,
  headerBgColor = 'transparent',
  footerBgColor = 'transparent',
}) => {
  let safe: EdgeInsets = {
    top: 0,
    right: 0,
    bottom: 0,
    left: 0,
  };
  try {
    // eslint-disable-next-line react-hooks/rules-of-hooks
    safe = useSafeAreaInsets();
  } catch (error) {}
  console.log('safe', safe);
  return (
    <View>
      {showHeader && (
        <View className='comp-cy-safe-area' style={{height: safe.top, backgroundColor: headerBgColor}}></View>
      )}
      {children}
      {showFooter && (
        <View className='comp-cy-safe-area' style={{height: safe.bottom, backgroundColor: footerBgColor}}></View>
      )}
    </View>
  );
};

export default CySafeArea;
