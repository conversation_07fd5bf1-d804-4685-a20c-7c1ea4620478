import request from '../http/request';

/** 注册协议 */
export const getUserRegisterProtocal = () => {
  return request<any>({
    url: '/api/contract/userRegisterProtocal',
    method: 'GET',
  });
};

/** 征信授权书 */
export const getCreditAuthContract = () => {
  return request<any>({
    url: '/api/contract/personCreditAuthContract',
    method: 'GET',
  });
};

/** 个人信息授权书 */
export const getPersonInfoAuthContract = () => {
  return request<any>({
    url: '/api/contract/personInfoAuthContract',
    method: 'GET',
  });
};

/** 非学生承诺函 */
export const getNoStudentContract = () => {
  return request<any>({
    url: '/api/contract/notStudentCommitment',
    method: 'GET',
  });
};


/** 个人敏感信息授权书 */
export const getSensitiveInfoAuthContract = () => {
  return request<any>({
    url: '/api/contract/personSensitiveInfoAuthContract',
    method: 'GET',
  });
};

/** 清廉信贷从业告知书 */
export const getIncorruptibleCreditNoticeContract = () => {
  return request<any>({
    url: '/api/contract/incorruptibleCreditNotice',
    method: 'GET',
  });
};

/** 个人借款额度合同 */
export const getLoanLimitContract = () => {
  return request<any>({
    url: '/api/contract/loanLimitContract',
    method: 'GET',
  });
};


/** 借款借据合同 */
export const getLendingLoanContract = (data: Record<string, any>) => {
  return request<any>({
    url: '/api/contract/lending/loan',
    method: 'POST',
    data
  });
}

/** 银行卡-委托扣款授权书 */
export const getDeductionContract = () => {
  return request<any>({
    url: '/api/contract/entrusted/deduction',
    method: 'GET',
  });
};

/** 贷款合同 */
export const getLoanContracDetail = (params: {loanNo: string,contId?: string}) => {
  return request<API.LoanContractReq[]>({
    url: '/api/contract/loan/contractDetail',
    method: 'GET',
    params
  });
}

/** 贷款合同 */
export const getLoanContractList = (params: {loanNo: string,contId?: string}) => {
  return request<API.LoanContractListRes[]>({
    url: '/api/contract/loan/contractList',
    method: 'GET',
    params
  });
}

/** 资金用途提示及用途监测授权书 */
export const getLendingPurposeContract = () => {
  return request<any>({
    url: '/api/contract/lending/purposeHint',
    method: 'GET',
  });
};


export const getConnectionInfoContract = () => {
  return request<any>({
    url: '/api/contract/connectionInfoConfirmation',
    method: 'GET',
    params: {
      scene: 'infoUpdate'
    }
  });
};