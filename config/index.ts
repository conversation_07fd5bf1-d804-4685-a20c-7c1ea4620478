import {type UserConfigExport, defineConfig} from '@tarojs/cli';
import fs from 'fs-extra';
import loaderUtils from 'loader-utils';
import * as path from 'path';
import TsconfigPathsPlugin from 'tsconfig-paths-webpack-plugin';
import SpritesmithPlugin from 'webpack-spritesmith';
import devConfig from './dev';
import prodConfig from './prod';

// https://taro-docs.jd.com/docs/next/config#defineconfig-辅助函数
export default defineConfig<'webpack5'>(async (merge, {command, mode}) => {
  const copyConfig =
    process.env.TARO_ENV === 'h5'
      ? {
          patterns: [
            {
              from: 'src/static/',
              to: `dist/${process.env.TARO_ENV}/static/`,
            },
          ],
          options: {},
        }
      : process.env.TARO_ENV === 'weapp'
          ? {
              patterns: [
                {
                  from: 'src/static/sipkeyboard',
                  to: `dist/${process.env.TARO_ENV}/static/sipkeyboard`,
                },
              ],
              options: {},
            }: 
        undefined;
  const baseConfig: UserConfigExport<'webpack5'> = {
    projectName: 'rn',
    date: '2024-2-22',
    designWidth: 750,
    deviceRatio: {
      640: 2.34 / 2,
      750: 1,
      375: 2,
      828: 1.81 / 2,
    },
    sourceRoot: 'src',
    outputRoot: `dist/${process.env.TARO_ENV}`,
    compiler: {
      type: 'webpack5',
      // 仅 webpack5 支持依赖预编译配置
      prebundle: {
        enable: true,
      },
    },
    plugins: [
      '@tarojs/plugin-platform-harmony-ets',
      '@taro-hooks/plugin-react',
      // 按需导入taro-hooks
      [
        '@taro-hooks/plugin-auto-import',
        {
          // your options, see configuration: https://github.com/antfu/unplugin-auto-import#configuration
        },
      ],
      '@tarojs/plugin-http',
    ],
    defineConstants: {
      __TARO_HOOKS_REACT__: JSON.stringify(true),
      __TARO_HOOKS_VUE__: JSON.stringify(false),
      TARO_ENV: JSON.stringify(process.env.TARO_ENV?.toLocaleUpperCase()),
      ENABLE_INNER_HTML: JSON.stringify(false),
      ENABLE_ADJACENT_HTML: JSON.stringify(false),
      ENABLE_SIZE_APIS: JSON.stringify(false),
      ENABLE_TEMPLATE_CONTENT: JSON.stringify(false),
      ENABLE_CLONE_NODE: JSON.stringify(false),
      ENABLE_CONTAINS: JSON.stringify(false),
      ENABLE_MUTATION_OBSERVER: JSON.stringify(false),
    },
    copy: copyConfig,
    framework: 'react',
    cache: {
      // 开发环境开启 生成环境关闭
      enable: process.env.NODE_ENV === 'development', // Webpack 持久化缓存配置，建议开启。默认配置请参考：https://docs.taro.zone/docs/config-detail#cache
    },
    alias: {
      '@taro-hooks/core': '@taro-hooks/plugin-react/dist/runtime',
      '@tarojs/runtime': require.resolve('@tarojs/runtime'),
      '~modules': path.resolve(__dirname, '../src/modules'),
      '~icons': path.resolve(__dirname, '../src/assets/icons'),
      '~images': path.resolve(__dirname, '../src/assets/images'),
      '~components': path.resolve(__dirname, '../src/components'),
      '~store': path.resolve(__dirname, '../src/store'),
      '~utils': path.resolve(__dirname, '../src/utils'),
      '~hooks': path.resolve(__dirname, '../src/hooks'),
      '~services': path.resolve(__dirname, '../src/services'),
      '~src': path.resolve(__dirname, '../src'),
    },
    sass: {
      resource: [
        path.resolve(__dirname, '../src/styles/custom-variables.scss'),
        path.resolve(__dirname, '../src/styles/mixins.scss'),
        // path.resolve(__dirname, '../src/styles/themes/index.scss'),
        // path.resolve(__dirname, '../src/styles/global.global.scss'),
        path.resolve(__dirname, '../src/spritesmith-generated/sprite.scss'),
        require.resolve('hncy58-taro-components/dist/style/_variables.scss'),
      ],
    },
    postcss: {
      pxtransform: {
        enable: true,
        config: {},
      },
      cssModules: {
        enable: true, // 默认为 false，如需使用 css modules 功能，则设为 true
        config: {
          namingPattern: 'module', // 转换模式，取值为 global/module
          generateScopedName: '[name]__[local]___[hash:base64:5]',
        },
      },
    },
    // mini: {
    //   postcss: {
    //     pxtransform: {
    //       enable: true,
    //       config: {},
    //     },
    //     cssModules: {
    //       enable: true, // 默认为 false，如需使用 css modules 功能，则设为 true
    //       config: {
    //         namingPattern: 'module', // 转换模式，取值为 global/module
    //         generateScopedName: '[name]__[local]___[hash:base64:5]',
    //       },
    //     },
    //     url: {
    //       enable: true,
    //       config: {
    //         url: 'inline',
    //         maxSize: 10,
    //       },
    //     },
    //   },
    //   webpackChain(chain) {
    //     chain.resolve.plugin('tsconfig-paths').use(TsconfigPathsPlugin);
    //     chain.resolve.plugin('MultiPlatformPlugin').tap(args => {
    //       args[2]['include'] = ['@hncy58/rn-safe-keyboard'];
    //       return args;
    //     });
    //   },
    // },
    h5: {
      // 本地图片替换为OSS地址
      // imageUrlLoaderOption: {
      //   limit: false,
      //   name: 'images/[name].[contenthash:8][ext]',
      //   publicPath: 'https://hncy58test-cyd.oss-cn-beijing.aliyuncs.com/multi-platform/',
      // },
      publicPath: '/',
      staticDirectory: 'static',
      output: {
        filename: 'js/[name].[hash:8].js',
        chunkFilename: 'js/[name].[chunkhash:8].js',
      },
      miniCssExtractPluginOption: {
        ignoreOrder: true,
        filename: 'css/[name].[hash].css',
        chunkFilename: 'css/[name].[chunkhash].css',
      },
      postcss: {
        autoprefixer: {
          enable: true,
          config: {},
        },
        cssModules: {
          enable: true,
          config: {
            namingPattern: 'module',
            generateScopedName: '[name]__[local]___[hash:base64:5]',
          },
        },
      },
      webpackChain(chain) {
        chain.module.rules.delete('image');
        chain.resolve.plugin('tsconfig-paths').use(TsconfigPathsPlugin);
        chain.resolve.plugin('MultiPlatformPlugin').tap(args => {
          args[2]['include'] = ['@hncy58/rn-safe-keyboard'];
          return args;
        });
        chain.merge({
          // h5端替换图片路径为3x图
          plugin: {
            install: {
              plugin: require('webpack/lib/NormalModuleReplacementPlugin'),
              args: [
                /\.(gif|jpe?g|png|svg)$/i,
                function (resource: any) {
                  const request = loaderUtils.urlToRequest(resource.request).replace(/^(.*images\/)(.*)$/, '$2');
                  const iconsRequest = loaderUtils.urlToRequest(resource.request).replace(/^(.*icons\/)(.*)$/, '$2');
                  const imgDirPath = path.join(__dirname, '../src/assets/images');
                  const imgPath = path.join(imgDirPath, request);
                  const iconPath = path.join(imgDirPath, iconsRequest);
                  const x2imgPath = imgPath.replace(/(.*)\.(gif|jpe?g|png|svg)$/, '$1@2x.$2');
                  const x3imgPath = imgPath.replace(/(.*)\.(gif|jpe?g|png|svg)$/, '$1@3x.$2');
                  const x2iconPath = iconPath.replace(/(.*)\.(gif|jpe?g|png|svg)$/, '$1@2x.$2');
                  const x3iconPath = iconPath.replace(/(.*)\.(gif|jpe?g|png|svg)$/, '$1@3x.$2');
                  // 查找3倍图是否有，有即返回3倍图
                  if (fs.existsSync(x3imgPath) || fs.existsSync(x3iconPath)) {
                    resource.request = resource.request.replace(/(.*)\.(gif|jpe?g|png|svg)$/, '$1@3x.$2');
                    return;
                  }
                  // 查找2倍图是否有，有即返回2倍图
                  if (fs.existsSync(x2imgPath) || fs.existsSync(x2iconPath)) {
                    resource.request = resource.request.replace(/(.*)\.(gif|jpe?g|png|svg)$/, '$1@2x.$2');
                  }
                },
              ],
            },
          },
          module: {
            rule: {
              // 覆盖 Taro 默认的图片加载配置
              image: {
                test: /\.(png|jpe?g|gif|bpm|webp)(\?.*)?$/,
                use: [
                  {
                    loader: 'url-loader',
                    options: {
                      limit: false,
                      name: 'images/[name].[contenthash:8].[ext]',
                      // publicPath: 'https://hncy58test-cyd.oss-cn-beijing.aliyuncs.com/multi-platform/',
                    },
                  },
                ],
              },
              // 使用 svg-sprite-loader 的配置
              'svg-loader': {
                test: /.svg$/,
                use: [
                  {
                    loader: 'svg-sprite-loader',
                    options: {},
                  },
                  {
                    loader: 'svgo-loader',
                    options: {},
                  },
                ],
              },
            },
          },
        });
      },
      esnextModules: ['taro-ui'],
    },
    mini: {
      postcss: {
        pxtransform: {
          enable: true,
          config: {},
        },
        cssModules: {
          enable: true, // 默认为 false，如需使用 css modules 功能，则设为 true
          config: {
            namingPattern: 'module', // 转换模式，取值为 global/module
            generateScopedName: '[name]__[local]___[hash:base64:5]',
          },
        },
      },
      webpackChain(chain) {
        chain.resolve.plugin('MultiPlatformPlugin').tap(args => {
          args[2]['include'] = ['@hncy58/rn-safe-keyboard'];
          return args;
        });

        // chain.plugins.

        // 小程序端替换图片路径为3x图
        chain.merge({
          plugin: {
            install: {
              plugin: require('webpack/lib/NormalModuleReplacementPlugin'),
              args: [
                /\.(gif|jpe?g|png|svg)$/i,
                function (resource: any) {
                  const request = loaderUtils.urlToRequest(resource.request).replace(/^(.*images\/)(.*)$/, '$2');
                  const imgDirPath = path.join(__dirname, '../src/assets/images');
                  const imgPath = path.join(imgDirPath, request);
                  const x2imgPath = imgPath.replace(/(.*)\.(gif|jpe?g|png|svg)$/, '$1@2x.$2');
                  const x3imgPath = imgPath.replace(/(.*)\.(gif|jpe?g|png|svg)$/, '$1@3x.$2');
                  // 图片路径不存在
                  if (fs.existsSync(x3imgPath)) {
                    resource.request = resource.request.replace(/(.*)\.(gif|jpe?g|png|svg)$/, '$1@3x.$2');
                    return;
                  }
                  // 查找2倍图是否有，有即返回2倍图
                  if (fs.existsSync(x2imgPath)) {
                    resource.request = resource.request.replace(/(.*)\.(gif|jpe?g|png|svg)$/, '$1@2x.$2');
                  }
                },
              ],
            },
          },
        });
        // chain.plugin('SpritesmithPlugin').use(
        //   new class GlobalScssPlugin {
        //     apply(compiler) {
        //       compiler.hooks.compilation.tap('GlobalScssPlugin', (compilation) => {
        //         const scssContent = fs.readFileSync('../src/sprite.png', 'utf8');
        //         compilation.hooks.additionalAssets.tap('GlobalScssPlugin', () => {
        //           compilation.emitAsset(
        //             'global.scss',
        //             new compiler.webpack.sources.RawSource(scssContent)
        //           );
        //         });
        //       });
        //     }
        //   }
        // )
        // chain.plugin('SpritesmithPlugin').use(new SpritesmithPlugin({
        //   src: {
        //     cwd: path.resolve(__dirname, '../src/assets/icons'),
        //     glob: '*.png',
        //   },
        //   target: {
        //     image: path.resolve(__dirname, '../src/spritesmith-generated/sprite.[hash].png'),
        //     css: path.resolve(__dirname, '../src/spritesmith-generated/sprite.scss')
        //   },
        //   apiOptions: {
        //     cssImageRef: "https://hncy58test-cyd.oss-cn-beijing.aliyuncs.com/multi-platform/sprite.[hash].png"
        //   }
        // }))
      },
    },
    rn: {
      appName: 'rn',
      output: {
        ios: './ios/main.jsbundle',
        iosAssetsDest: './ios',
        android: './android/app/src/main/assets/index.android.bundle',
        androidAssetsDest: './android/app/src/main/res',
        // iosSourceMapUrl: '',
        iosSourcemapOutput: './ios/main.map',
        // iosSourcemapSourcesRoot: '',
        // androidSourceMapUrl: '',
        androidSourcemapOutput: './android/app/src/main/assets/index.android.map',
        // androidSourcemapSourcesRoot: '',
      },
      postcss: {
        cssModules: {
          enable: true, // 默认为 false，如需使用 css modules 功能，则设为 true
        },
      },
    },
    // harmony 相关配置
    harmony: {
      // // 将编译方式设置为使用 Vite 编译
      // compiler: 'vite',
      // 【必填】鸿蒙主应用的绝对路径，例如：
      projectPath: path.resolve(process.cwd(), './harmony'),
      // 【可选】HAP 的名称，默认为 'entry'
      hapName: 'entry',
      // 【可选】modules 的入口名称，默认为 'default'
      name: 'default',
    },
  };
  if (process.env.NODE_ENV === 'development') {
    // 本地开发构建配置（不混淆压缩）
    return merge({}, baseConfig, devConfig);
  }
  // 生产构建配置（默认开启压缩混淆等）
  return merge({}, baseConfig, prodConfig);
});
