import React, {JSXElementConstructor, ReactElement, Ref, forwardRef, useRef, useState} from 'react';
import {View} from '@tarojs/components';
import CyFlatList, {CyFlatListRef} from '../../components/CyFlatList';
import CyEmpty from '../CyEmpty';
import CyLoadMore from '../CyLoadMore';
import './index.scss';

interface Props<T> {
  list: T[];
  canLoadMore?: boolean;
  canRefresh?: boolean;
  onRefresh?: () => void;
  onLoadMore?: () => void;
  renderItem: (item: T, index: number) => ReactElement<any, string | JSXElementConstructor<any>>;
}

export interface InfiniteFlowTableView {
  endRefresh: (noMoreData?: boolean) => void; //结束加载状态
  scrollToTop: () => void; //滚动到顶部
}
/**
 * 刷新状态 空闲|刷新|加载更多|无更多数据
 */
type RefreshStatus = 'idle' | 'refreshing' | 'loading' | 'noMoreData';
/**
 * 组件名称：长列表
 * 组件描述：长列表 包含下列刷新/上拉加载更多
 */
const CyInfiniteFlowTableView = forwardRef(
  <T,>({list, canLoadMore = true, canRefresh = true, ...props}: Props<T>, ref: Ref<InfiniteFlowTableView>) => {
    const [refreshStatus, setRefreshStatus] = useState<RefreshStatus>('idle');
    const flatListRef = useRef<CyFlatListRef>(null);
    React.useImperativeHandle(ref, () => ({
      endRefresh: (noMoreData = false) => {
        console.log('endRefresh=', noMoreData);
        if (noMoreData) {
          setRefreshStatus('noMoreData');
        } else {
          setRefreshStatus('idle');
        }
      },
      scrollToTop: () => {
        flatListRef.current?.scrollToTop();
      },
    }));
    const onRefresh = () => {
      if (canRefresh) {
        if (refreshStatus === 'refreshing' || refreshStatus === 'loading') return;
        console.log('onRefresh');
        props.onRefresh && props.onRefresh();
      }
    };

    const onLoadMore = () => {
      if (canLoadMore && list.length > 0) {
        if (refreshStatus === 'refreshing' || refreshStatus === 'loading' || refreshStatus === 'noMoreData') return;
        console.log('onLoadMore');
        setRefreshStatus('loading');
        props.onLoadMore && props.onLoadMore();
      }
    };
    return (
      <View className='comp-cy-infinite-flow-table-view'>
        <CyFlatList
          ref={flatListRef}
          data={list}
          renderItem={({item, index}) => {
            return props.renderItem(item, index);
          }}
          keyExtractor={(_, index) => {
            return `${index}`;
          }}
          onRefresh={canRefresh ? onRefresh : undefined}
          refreshing={refreshStatus === 'refreshing'}
          onEndReached={onLoadMore}
          ListEmptyComponent={
            refreshStatus !== 'refreshing' && refreshStatus !== 'loading' ? <CyEmpty></CyEmpty> : <></>
          }
          ListFooterComponent={
            refreshStatus !== 'refreshing' && refreshStatus !== 'idle' && list.length > 0 ? (
              <CyLoadMore
                finished={refreshStatus === 'noMoreData'}
                loading={refreshStatus === 'loading'}
                dataLength={1}
              />
            ) : (
              <></>
            )
          }
        />
      </View>
    );
  },
);

export default CyInfiniteFlowTableView;
