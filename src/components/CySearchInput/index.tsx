import {useState, useEffect, useMemo} from 'react';
import { Text, View, Input } from '@tarojs/components';
import classNames from 'classnames';
import debounce from '~utils/debounce';
import styles from './index.module.scss';
interface CySearchInputProps<T> {
  placeholder?: string;
  value?: string;
  onInput?: (value: string) => void;
  onSelect?: (item: T) => void;
  searchFn: (data: {keyword: string}) => Promise<{items?: T[]}>;
  renderItem?: (item: T, keyword: string) => React.ReactNode;
  keyExtractor?: (item: T, index: number) => string;
  titleExtractor?: (item: T) => string;
  debounceTime?: number;
  className?: string;
  inputClassName?: string;
}

function CySearchInput<T extends {title?: string}>({
  placeholder = '请输入',
  value = '',
  onInput,
  onSelect,
  searchFn,
  renderItem,
  keyExtractor,
  titleExtractor,
  debounceTime = 600,
  className,
  inputClassName,
}: CySearchInputProps<T>) {
  const [inputValue, setInputValue] = useState(value);
  const [dataList, setDataList] = useState<T[]>([]);
  const [showDataList, setShowDataList] = useState(false);
  const [searchKeyword, setSearchKeyword] = useState('');
  const [loading, setLoading] = useState(false);
  const [lastSearchedKeyword, setLastSearchedKeyword] = useState('');
  const [cursor, setCursor] = useState(-1);
  useEffect(() => {
    setInputValue(value);
  }, [value]);

  const requestDataList = async (keyword: string) => {
    if (!keyword || !keyword.trim()) {
      setDataList([]);
      setShowDataList(false);
      return;
    }

    if (keyword === lastSearchedKeyword) {
      setShowDataList(true);
      return;
    }

    setLoading(true);
    try {
      const res = await searchFn({keyword});
      if (res?.items?.length) {
        setDataList(res.items);
        setShowDataList(true);
      } else {
        setDataList([]);
        setShowDataList(false);
      }
      setLastSearchedKeyword(keyword);
    } catch (error) {
      console.error('获取数据列表失败', error);
      setDataList([]);
      setShowDataList(false);
    } finally {
      setLoading(false);
    }
  };

  const debouncedSearch = useMemo(() => debounce(requestDataList, debounceTime), [debounceTime]);

  // 高亮显示匹配的文字
  const highlightText = (text: string, keyword: string) => {
    if (!keyword) return text;

    const result: JSX.Element[] = [];
    const lowerText = text.toLowerCase();
    const lowerKeyword = keyword.toLowerCase();

    for (let i = 0; i < text.length; i++) {
      const char = text[i];
      if (lowerKeyword.includes(lowerText[i])) {
        result.push(
          <Text key={i} style={{color: '#FF4D4F'}}>
            {char}
          </Text>,
        );
      } else {
        result.push(<Text key={i}>{char}</Text>);
      }
    }

    return result;
  };

  const handleItemSelect = (item: T) => {
    setSearchKeyword(getItemTitle(item));
    setInputValue(getItemTitle(item));
    if (onSelect) {
      onSelect(item);
    }
    setShowDataList(false);
  };

  const getItemTitle = (item: T) => {
    if (titleExtractor) {
      return titleExtractor(item);
    }
    return item.title || '';
  };

  const getItemKey = (item: T, index: number) => {
    if (keyExtractor) {
      return keyExtractor(item, index);
    }
    return index.toString();
  };

  const renderListItem = (item: T, index: number) => {
    if (renderItem) {
      return renderItem(item, searchKeyword);
    }
    return highlightText(getItemTitle(item), searchKeyword);
  };

  return (
    <View className={classNames(styles['search-container'], className)}>
      <Input
        placeholder={placeholder}
        className={classNames(styles['input'], inputClassName)}
        value={inputValue}
        cursor={cursor}
        onInput={e => {
          const value = e.detail.value;
          const cursor = e.detail.cursor;
          setInputValue(value);
          setSearchKeyword(value);
          if (onInput) {
            onInput(value);
          }
          setCursor(cursor);
          debouncedSearch(value);
        }}
        onFocus={() => {
          if (inputValue) {
            setSearchKeyword(inputValue);
            if (inputValue !== lastSearchedKeyword) {
              debouncedSearch(inputValue);
            } else if (dataList.length > 0) {
              setShowDataList(true);
            }
          }
        }}
        onClick={e => {
          e.stopPropagation();
        }}
      />
      {(loading || (showDataList && dataList.length > 0)) && (
        <View
          className={styles['dropdown-container']}
          onClick={e => {
            e.stopPropagation();
          }}>
          <View 
            className={styles['close-btn']}
            onClick={() => {
              setLoading(false);
              setShowDataList(false)
            }}
          >
            ×
          </View>
          <View className={styles['dropdown-content']}>
            {loading ? (
              <View className={styles['loading-indicator']}>
                <Text>加载中...</Text>
              </View>
            ) : (
              dataList.map((item, index) => (
                <View
                  key={getItemKey(item, index)}
                  className={styles['item']}
                  onClick={() => handleItemSelect(item)}>
                  {renderListItem(item, index)}
                </View>
              ))
            )}
          </View>
        </View>
      )}
    </View>
  );
}

export default CySearchInput; 