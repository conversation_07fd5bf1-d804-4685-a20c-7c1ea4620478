.cyButton {
  background-color: $cy-button-background-color;
  color: $cy-button-text-color;
  font-size: $font-size-lg;
  // line-height: $line-height-base;
  // padding-top: $cy-padding-nomal;
  // padding-bottom: $cy-padding-nomal;
  &--disabled {
    background-color: $cy-button-background-disable-color;
    color: $cy-button-text-disable-color;
  }
  &--inline {
    display: inline-block;
    width: auto;
  }
}

.warn {
  background-color: rgb(249, 42, 35);
}

.default {
  background-color: #fff;
}

taro-button-core[disabled] {
  color: $cy-button-text-disable-color !important;
}

taro-button-core:not([disabled]):active {
  background-color: $cy-button-background-disable-color !important;
  color: $cy-button-text-disable-color !important;
}
