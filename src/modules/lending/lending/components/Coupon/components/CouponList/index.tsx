import {useState} from 'react';
import {ScrollView, View} from '@tarojs/components';
import CouponItem from '../CouponItem';
import styles from './index.module.scss';

const CouponList = () => {
  const [expandedItems, setExpandedItems] = useState<number[]>([]);
  const [currentCoupon, setCurrentCoupon] = useState<1 | 2 | 3>(1);
  const [selectedIndex, setSelectedIndex] = useState<number | null>(null);
  const getCouponList = (type: 1 | 2 | 3) => {
    switch (type) {
      case 1:
        return [1, 23, 4, 5, 5, 5];
      case 2:
        return [1, 4];
      case 3:
        return [1];
      default:
        return [];
    }
  };

  const couponList = getCouponList(currentCoupon);

  // 切换优惠券项的展开状态
  const toggleExpand = (index: number) => {
    setExpandedItems(prev => {
      if (prev.includes(index)) {
        return prev.filter(i => i !== index);
      } else {
        return [...prev, index];
      }
    });
  };

  const handleChange = (index: 1 | 2 | 3) => {
    setCurrentCoupon(index);
  };

  const handleSelect = (index: number) => {
    if (selectedIndex === index) {
      // 如果点击的是已选中的项，则取消选中
      setSelectedIndex(null);
    } else {
      // 否则选中当前项
      setSelectedIndex(index);
    }
  };
  return (
    <ScrollView scrollY className={styles.coupon_container}>
      <ScrollView scrollX className={styles.coupon_types}>
        <View className={styles.coupon_types}>
          <View
            className={`${styles.coupon_type_item} ${currentCoupon === 1 ? styles.active : ''}`}
            onClick={() => handleChange(1)}>
            抵息券
          </View>
          <View
            className={`${styles.coupon_type_item} ${currentCoupon === 2 ? styles.active : ''}`}
            onClick={() => handleChange(2)}>
            免息券
          </View>
          <View
            className={`${styles.coupon_type_item} ${currentCoupon === 3 ? styles.active : ''}`}
            onClick={() => handleChange(3)}>
            折扣券
          </View>
        </View>
      </ScrollView>
      {couponList.map((item, index) => (
        <CouponItem
          key={index}
          index={index}
          expandedItems={expandedItems}
          isSelected={selectedIndex === index}
          onSelect={() => handleSelect(index)}
          toggleExpand={() => toggleExpand(index)}
        />
      ))}
    </ScrollView>
  );
};

export default CouponList;
