import { Image, Text, View } from "@tarojs/components"
import Taro from "@tarojs/taro"
import { CyButton, CyModal } from "hncy58-taro-components"
import CloseIcon from '~images/icon/<EMAIL>'
import LogoIcon from '~images/login/<EMAIL>'
import styles from './index.module.scss'
import { forwardRef, useImperativeHandle } from "react"

interface DownloadAppProps {
  showLink: boolean;
}
export interface DownloadAppRef {
  showDownloadDialog: () => void;
}
export default forwardRef(({showLink}: DownloadAppProps, ref) => {
  const url = process.env.NODE_ENV === 'development' ? 
  'https://sit.hncy58test.com/mobile/#/pages/download/index' : 
  'https://h5.hncy58.com/mobile/#/pages/download/index'
  
  let closeModal: (() => void) | null = null

  useImperativeHandle(ref, () => ({
    showDownloadDialog: handleDownload,
  }));

  const handleDownload = () => {
    closeModal = CyModal.create({
      maskClosable: true,
      modalRender: (
        <View className={styles.downloadModal}>
          {/* <Image className={styles.closeIcon} src={CloseIcon} /> */}
          <Image className={styles.logoIcon} src={LogoIcon} />
          <View className={styles.linkContainer}>
            <View className={styles.title}>下载 “城一代” APP</View>
            <View className={styles.subtitle}>复制链接, 在手机浏览器中打开</View>
            <View className={styles.line}></View>
            <View className={styles.linkUrl}>{url}</View>
          </View>
          <CyButton block type="primary" onClick={handleClipboard}>复制链接</CyButton>
        </View>
      ),
    })
  }
  
  const handleClipboard = () => {
    Taro.setClipboardData({
      data: url,
    })
    closeModal?.()
  }
  return showLink ? <Text style={{ color: '#2F54EB' }} onClick={handleDownload}>下载城一代APP</Text> : null
})