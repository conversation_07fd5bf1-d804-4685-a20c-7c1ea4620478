import { Image, View, type ViewProps } from '@tarojs/components';
import styles from './index.module.scss';
import classNames from 'classnames';

type EntryItem = {
  icon: string;
  name: string;
  onClick: (link: string) => void;
  isShow?: boolean;
  disabled?: boolean;
};
interface Props extends ViewProps {
  entries: EntryItem[];
  maxCols?: number;
  size?: 'small' | 'normal'
  entryItemStyle?: React.CSSProperties;
}

export default ({ entries, maxCols = 4, size, entryItemStyle }: Props) => {
  return (
    <View className={styles.entryBar}>
      {entries.map((entry, index) => {
        if (entry.hasOwnProperty('isShow') && !entry.isShow) {
          return null;
        }
        return (
          <View
            onClick={() => entry.onClick(entry.name)}
            className={classNames(styles.entryItem, size ? styles?.[size] : '')}
            style={{ width: `${100 / maxCols}%`, ...entryItemStyle }}
            key={entry.name}>
            <View className={styles.entryFigure}>
              <Image className={styles.entryIcon} src={entry.icon} />
              {entry.disabled ? <View className={styles.disableMask}>建设中</View> : null}
            </View>
            <View className={styles.entryText}>{entry.name}</View>
          </View>
        )
      }

      )}
    </View>
  );
};
