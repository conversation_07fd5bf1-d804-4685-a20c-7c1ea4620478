import {useEffect} from 'react';
import {Canvas, View} from '@tarojs/components';
import {createSelectorQuery, getSystemInfoSync} from '@tarojs/taro';
import classNames from 'classnames';
import {MaxCreditAmount} from '~utils/constant';
import styles from './index.module.scss';

interface Props {
  currentAmount: number;
  rate: number;
}

export default ({currentAmount, rate}: Props) => {
  // 检测设备像素比（DPI 缩放）
  const dpr = getSystemInfoSync().pixelRatio;
  /** 1. 变量定义 */
  const config = {
    maxAmount: MaxCreditAmount, // 最大额度
    currentAmount, // 当前额度
    gradientOuterLineWidth: 8 * dpr,
    outerLineColors: {
      low: '#20B8FF', // 绿色 - 低使用率
      medium: '#428bf8', // 黄色 - 中等使用率
      high: '#2F54EB', // 红色 - 高使用率
      tick: '#858FAD', // 刻度线颜色
      longTick: '#858FAD',
      innerGrey: '#CBD5E4',
    },
    gradientInnerLineWidth: 30 * dpr,
    innerColors: {
      low: '#fffffc',
      medium: '#CCE0FF',
      high: '#92BBFF',
    },
    ticks: {
      count: 40, // 刻度数量
      majorLength: 5 * dpr, // 长刻度长度
      minorLength: 3 * dpr, // 短刻度长度
      width: 1 * dpr, // 刻度线宽度
    },
    // 圆弧偏移量
    circleOffsetRadius: 8 * dpr,
    // 指针半径
    pointerRadius: 2 * dpr,
    pointer: {
      outerRadius: 8 * dpr,
      innerRadius: 6 * dpr,
    },
  };

  /** 2. usestates + storestates */

  /** 3. effects */
  useEffect(() => {
    let curPercent = 0;
    const timer = setInterval(() => {
      curPercent = curPercent + 0.05;
      if (curPercent >= 1) {
        draw(1);
        clearInterval(timer);
      } else {
        draw(curPercent);
      }
    }, 40);
    return () => {
      clearInterval(timer);
    };
  }, []);

  /** 4. 方法定义 */
  const draw = (percentage: number) => {
    const query = createSelectorQuery();
    query
      .select('#canvas')
      .fields({node: true, size: true})
      .exec(res => {
        console.log('canvas res', res);
        const canvas = res[0].node;
        const ctx = canvas.getContext('2d');

        // 调整 Canvas 实际分辨率
        canvas.width = res[0].width * dpr;
        canvas.height = res[0].height * dpr;

        const centerX = canvas.width / 2;
        const centerY = canvas.height - config.circleOffsetRadius - 1 * dpr;
        const radius = canvas.width / 2 - config.gradientOuterLineWidth / 2 - config.circleOffsetRadius - 1.5 * dpr;
        const gradientInnerRadius = radius - config.gradientOuterLineWidth / 2 - config.gradientInnerLineWidth / 2;
        const tickRadius = gradientInnerRadius - config.gradientInnerLineWidth / 2 - config.ticks.majorLength; // 内圈刻度半径
        const startAngle = Math.PI;
        const endAngle = 2 * Math.PI;
        const activeEndAngle = startAngle + (endAngle - startAngle) * percentage;

        // 清除画布
        ctx.clearRect(0, 0, canvas.width, canvas.height);

        // 创建渐变
        const gradient = ctx.createLinearGradient(0, 0, canvas.width, 0);
        gradient.addColorStop(0, config.outerLineColors.low);
        gradient.addColorStop(1, config.outerLineColors.high);

        const gradientInner = ctx.createLinearGradient(0, 0, canvas.width, 0);
        gradientInner.addColorStop(0, config.innerColors.low);
        gradientInner.addColorStop(1, config.innerColors.high);

        // 绘制前景外层圆弧（带渐变）
        console.log('activeEndAngle====', activeEndAngle, endAngle);
        ctx.beginPath();
        ctx.arc(centerX, centerY, radius, startAngle, activeEndAngle);
        ctx.lineWidth = config.gradientOuterLineWidth;
        ctx.strokeStyle = gradient;
        ctx.stroke();

        // 绘制前景内层圆弧（带渐变）
        ctx.beginPath();
        ctx.arc(centerX, centerY, gradientInnerRadius, startAngle, activeEndAngle);
        ctx.lineWidth = config.gradientInnerLineWidth;
        ctx.strokeStyle = gradientInner;
        ctx.stroke();

        // 绘制最内侧灰色圆弧细线
        ctx.beginPath();
        ctx.arc(centerX, centerY, tickRadius - config.ticks.majorLength, startAngle, activeEndAngle);
        ctx.lineWidth = 1;
        ctx.strokeStyle = config.outerLineColors.innerGrey;
        ctx.stroke();

        // 添加指针
        const pointerLength = radius;
        const pointerAngle = startAngle + (endAngle - startAngle) * percentage;
        const pointerX = centerX + Math.cos(pointerAngle) * pointerLength;
        const pointerY = centerY + Math.sin(pointerAngle) * pointerLength;

        ctx.strokeStyle = config.outerLineColors.tick;
        ctx.lineWidth = config.ticks.width;

        for (let i = 0; i <= config.ticks.count; i++) {
          const angle = startAngle + (endAngle - startAngle) * (i / config.ticks.count);
          const isMajorTick = i % (config.ticks.count / 5) === 0; // 每20%一个长刻度
          ctx.strokStyle = isMajorTick ? config.outerLineColors.longTick : config.outerLineColors.tick;
          // 刻度线长度
          const tickLength = isMajorTick ? config.ticks.majorLength : config.ticks.minorLength;

          // 绘制刻度线
          ctx.beginPath();
          const x1 = centerX + Math.cos(angle) * tickRadius;
          const y1 = centerY + Math.sin(angle) * tickRadius;
          const x2 = centerX + Math.cos(angle) * (tickRadius - tickLength);
          const y2 = centerY + Math.sin(angle) * (tickRadius - tickLength);
          ctx.moveTo(x1, y1);
          ctx.lineTo(x2, y2);
          ctx.stroke();
        }
        // 绘制指针外层的白色圆
        ctx.shadowColor = 'rgba(59, 59, 59, 0.4)'; // 阴影颜色
        ctx.shadowBlur = 8; // 模糊程度，对应CSS的第三个值
        ctx.shadowOffsetX = 0; // X轴偏移，对应CSS的第一个值
        ctx.shadowOffsetY = 0; // Y轴偏移，对应CSS的第二个值

        ctx.beginPath();
        ctx.arc(pointerX, pointerY, config.pointer.outerRadius, 0, 2 * Math.PI);
        ctx.fillStyle = '#fff';
        ctx.fill();

        // 如果需要清除阴影设置以便后续绘制
        ctx.shadowColor = 'transparent';

        ctx.beginPath();
        ctx.arc(pointerX, pointerY, config.pointer.innerRadius, 0, 2 * Math.PI);
        ctx.fillStyle = gradient;
        ctx.fill();
      });
  };
  return (
    <View className={styles['credit-amount-figrure']}>
      <Canvas type='2d' canvasId='canvas' className={styles['canvas']} id='canvas'></Canvas>
      <View className={styles['center-credit-amount']}>
        <View className={styles['center-credit-label']}>预授信额度(元)</View>
        <View className={styles['center-credit-value']}>{currentAmount.toFixed(2)}</View>
      </View>
      <View className={styles['figure-bottom']}>
        <View className={classNames(styles['credit-amount-tips'], styles['credit-amount-tips-main'])}>
          日利率{(rate * 100).toFixed(2)}%(年化利率单利{(rate * 360 * 100).toFixed(2)}%)
        </View>
        <View className={styles['credit-amount-tips']}>支持提前还款，额度可循环使用</View>
      </View>
    </View>
  );
};
