import {CSSProperties, FC} from 'react';
import {View} from '@tarojs/components';
import classNames from 'classnames';
import './index.scss';

interface Props {
  style?: CSSProperties;
  lineColor?: string;
  vertical?: boolean;
  className?: string;
}

type TCySplitLine = FC<Props>;

/**
 * 组件名称：分割线组件
 * 组件描述：用来分隔各部分或装饰使用
 */
const CySplitLine: TCySplitLine = ({lineColor, vertical, style, className}) => {
  return (
    <View
      className={classNames(['comp-cy-split-line', vertical ? 'split-line-vertical' : '', className])}
      style={{...style, backgroundColor: lineColor}}></View>
  );
};

export default CySplitLine;
