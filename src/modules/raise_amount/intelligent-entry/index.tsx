import {View, Text, Image} from '@tarojs/components';
import styles from './index.module.scss';
import PageWrap from '~components/PageWrap';
import { CyButton } from 'hncy58-taro-components';
import aiIcon from '~images/raiseamount/<EMAIL>'
import { raiseCreditApply } from '~services/raiseAmount';
import { hideLoading, router, showLoading } from '~utils/common';
import { getLocation } from '~utils/location';
import debounce from '~utils/debounce';

const RaiseAmount = () => {
  /** 1. 变量定义 */

  /** 2. usestates + storestates */

  /** 3. effects */

  /** 4. 方法定义 */
  const handleStartEvaluation = debounce(async () => {
    // 开始评估的逻辑
    getLocation({
      onSuccess: async () => {
        try {
          showLoading();
          const res = await raiseCreditApply();
          if (res && res?.code === '-1') {
            router.push({
              url: `/modules/raise_amount/state/index?state=fail`
            })
            return
          }
          router.push({
            url: `/modules/raise_amount/state/index?state=success`
          }) 
        } catch (error) {
          console.log('请求错误', error);
        } finally {
          hideLoading();
        }
      },
      onFail: () => {},
      usage: '用于提额'
    })
  }, 300);

  /** 5. ui */
  return (
    <PageWrap className={styles['page-raise-amount']}>
      <View className={styles.container}>
        {/* AI图标区域 */}
        <Image 
          className={styles.aiIcon}
          src={aiIcon}
          mode="aspectFit"
        />
        
        {/* 标题 */}
        <Text className={styles.title}>智能认证</Text>
        
        {/* 描述文字 */}
        <Text className={styles.description}>
          根据个人信用、个人资质等因素综合评估，快速提额
        </Text>
        
        {/* 开始评估按钮 */}
        <View className={styles.buttonContainer} onClick={handleStartEvaluation}>
          <CyButton round block type='primary'>开始评估</CyButton>
        </View>
      </View>
    </PageWrap>
  );
};

export default RaiseAmount;
