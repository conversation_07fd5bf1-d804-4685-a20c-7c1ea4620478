# 项目介绍

## 背景：


### 样式：

- 单位：样式默认使用 px，设计稿以宽度 750px 为标准
- UI 库：[taro-ui](https://taro-ui.jd.com/#/docs/introduction) (注意: 部分组件不支持RN端)
- 主题：taro-ui暂不支持动态css变量，故暂时无法实现主题切换功能

### 代码格式化



### 环境变量

### 网络请求

> 采用axios + useRequest(taro-hooks) 结合的方式进行实现
支持的能力：
    1. useRequest的功能，参考：[文档](https://next-taro-hooks.pages.dev/site/hooks/useRequest/)
    2. 

新增网络请求步骤
1. src/apis目录下新增文件或在现有文件添加request方法
2. 通过taro hooks 的 useRequest组合request方法的方式使用, 示例如下
```javascript
    const { data, error, loading } = useRequest(getAccessToken());
```
3. 可以使用useRequest的能力



### 图片
1. 图片采用OSS和本地双兼容的方案
2. 小程序和web端均优先使用OSS图片，如果OSS图片不存在，则使用本地图片
3. 原生端优先使用本地图片，本地图片不存在，则使用OSS图片
4. 图片倍数
    - 一般采用3倍图，本地图片优先使用三倍图，如果三倍图不存在，则使用二倍图、一倍图的递减方式（此处在编译阶段处理， 详情见[图片处理](https://github.com/alibaba/ice/blob/master/docs/guide/advanced/image.md)）
    - OSS中由于无法在编译阶段就确定图片的倍数，传递最大的
5. 雪碧图- 图标方案
    - 仅支持svg格式，建议图标均转为svg实现。
    - 仅支持小程序和web端，暂不支持RN端



### 分包
1. 主包：包含公共内容及登录逻辑
2. 子包
    - 进件包
    - 三方包

### 规范
1. 引入组件、utils、api等，请使用别名路径引入，如 ～components/CompA

### 组件开发规范
1. 样式需要参考taro-ui
2. 导出组件使用具名导出，不使用export default
3. 

### 原生接入

### 动态加载

### bundle 管理

### 开发步骤

### 原生 addone

### 分包

### pushy 相关
1. 选择app
pushy selectApp --platform ios
pushy selectApp --platform android

2. 发布app
pushy bundle --platform ios
pushy bundle --platform android



### 待配合处理

1. 还款支付方式查询好像没有多张银行卡的选择，目前仅展示了一张银行卡的还款方式。
2. repayment/list?repayableSummaryCategory=ACTIVE 接口 返回的列表中没有loanInitPrin参数


### 待处理
<!-- 1. 是否黑灰名单，看是否新接口或profile接口 -->
2. 额度拒绝重新授信+电核状态
3. 更新信息上线次数
4. 信息更新-个人人行征信信息返显
<!-- 5. 限制只能手机小程序登录，不能使用pc -->
6. 是否已经确认额度
<!-- 7. 借款前没有身份证信息提示下 -->
8. 借款的电核状态

2.2 基本信息前端保存一段时间


3.3 还款金额不能输入小数

3.4 还款全选最多选10笔

4.2 还款上限


#### 待优化：
1. 合同接口报错的情况添加重试
2. 首页加下拉刷新？
3. 还款银行卡选择优化


### 待2期补充
1. 还款计划接口新增返回借款日期字段
