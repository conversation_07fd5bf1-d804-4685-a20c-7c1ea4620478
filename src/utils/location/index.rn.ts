import {NativeEventEmitter, NativeModules, Platform} from 'react-native';
import {PERMISSIONS, RESULTS, openSettings, requestMultiple} from 'react-native-permissions';
import {getLocationRegeo} from '../../services/location';
import {showModal} from '../common';
import {isAndriod, isIos} from '../platform';

const LocationManager = NativeModules.NativeLocationManager;
if (LocationManager) {
  if (isIos()) {
    const locationManagerEmitter = new NativeEventEmitter(LocationManager);
    const subscription = locationManagerEmitter.addListener('LocationDidChangeAuthorization', reminder =>
      console.log('LocationDidChangeAuthorization1111', reminder),
    );
  }
}
type GetLocationOption = {
  onSuccess: (res: API.AddressComponent) => void;
  onFail?: (error: {message: string}) => void;
  regeo?: boolean; //是否进行逆地址
};

//获取单次定位位置
export const getLocation = async ({onSuccess, regeo = true, onFail}: GetLocationOption) => {
  try {
    // TODO 后续优化
    if (Platform.OS === 'android') {
      LocationManager.getLocation(async (latitude: string, longitude: string) => {
        if (!isValid(latitude, longitude)) {
          onFail && onFail({message: '获取位置失败'});
          return;
        }
        latitude = `${latitude}`;
        longitude = `${longitude}`;
        if (regeo) {
          await getLocationDetail({lon: longitude, lat: latitude, onSuccess, onFail});
        } else {
          const address: API.AddressComponent = {
            latitude,
            longitude,
            country: '',
            countryCode: '',
            province: '',
            city: '',
            citycode: '',
            district: '',
            streetNumber: {
              street: '',
              number: '',
            },
            adcode: '',
          };
          onSuccess(address);
        }
      });
      return;
    }

    let {latitude, longitude}: {latitude: string; longitude: string} = await LocationManager.getLocation();
    console.log('获取定位', {latitude, longitude});
    if (!isValid(latitude, longitude)) {
      onFail && onFail({message: '获取位置失败'});
      return;
    }
    latitude = `${latitude}`;
    longitude = `${longitude}`;
    if (regeo) {
      await getLocationDetail({lon: longitude, lat: latitude, onSuccess, onFail});
    } else {
      const address: API.AddressComponent = {
        latitude,
        longitude,
        country: '',
        countryCode: '',
        province: '',
        city: '',
        citycode: '',
        district: '',
        streetNumber: {
          street: '',
          number: '',
        },
        adcode: '',
      };
      onSuccess(address);
    }
  } catch (error) {
    console.log('error=', error);
    onFail && onFail({message: '获取位置失败'});
  }
};

const getLocationDetail = async ({
  lon,
  lat,
  onSuccess,
  onFail,
}: {
  lon: string;
  lat: string;
  onSuccess: (res: API.AddressComponent) => void;
  onFail?: (error: {message: string}) => void;
}) => {
  try {
    const data = await getLocationRegeo({
      lon,
      lat,
    });
    if (!data.regeocode.addressComponent.countryCode) {
      data.regeocode.addressComponent.countryCode = '156';
    }
    const address = controlAddressForNotArray(data.regeocode.addressComponent);
    address.latitude = lat;
    address.longitude = lon;
    onSuccess(address);
  } catch (error) {
    console.log('error=', error);
    onFail && onFail({message: '网络错误'});
  }
};

//开始持续地位
export const startUpdatingLocation = () => {
  if (LocationManager.startUpdatingLocation) {
    LocationManager.startUpdatingLocation();
  }
};

//关闭持续地位
export const stopUpdatingLocation = () => {
  if (LocationManager.startUpdatingLocation) {
    LocationManager.stopUpdatingLocation();
  }
};

//获取持续定位地址信息
export const getUpdatingLocation = async ({onSuccess, regeo = true, onFail}: GetLocationOption) => {
  try {
    let {latitude, longitude}: {latitude: string; longitude: string} = await LocationManager.getUpdatingLocation();

    if (!isValid(latitude, longitude)) {
      onFail && onFail({message: '获取位置失败'});
      return;
    }
    latitude = `${latitude}`;
    longitude = `${longitude}`;
    if (regeo) {
      await getLocationDetail({lon: longitude, lat: latitude, onSuccess, onFail});
    } else {
      const address: API.AddressComponent = {
        latitude,
        longitude,
        country: '',
        countryCode: '',
        province: '',
        city: '',
        citycode: '',
        district: '',
        streetNumber: {
          street: '',
          number: '',
        },
        adcode: '',
      };
      onSuccess(address);
    }
  } catch (error) {
    console.log('error=', error);
    onFail && onFail({message: '获取位置失败'});
  }
};

//获取定位是否可用
export const getLocationAuthStatusIsValid: () => boolean = () => {
  return LocationManager.getLocationAuthStatusIsValid() !== '0';
};

//当前应用是否可以使用/申请定位服务
export const getLocationServiceIsValid: () => boolean = () => {
  return LocationManager.getLocationServiceIsValid() !== '0';
};

//获取定位权限状态
export const getAuthorizationStatus: () => string = () => {
  return LocationManager.getAuthorizationStatus();
};

/**
 * 获取定位权限
 * @param openSeting 权限不足时是否提示打开设置
 * @returns
 */
export const requestLocationPermission = async (openSeting = true) => {
  try {
    if (isAndriod()) {
      requestMultiple([PERMISSIONS.ANDROID.ACCESS_COARSE_LOCATION, PERMISSIONS.ANDROID.ACCESS_FINE_LOCATION]).then(
        statuses => {
          if (
            statuses[PERMISSIONS.ANDROID.ACCESS_COARSE_LOCATION] === RESULTS.GRANTED &&
            statuses[PERMISSIONS.ANDROID.ACCESS_FINE_LOCATION] === RESULTS.GRANTED
          ) {
            return true;
          } else {
            if (openSeting) {
              showModal({
                title: '获取定位权限失败',
                content: '可能会影响您的正常使用，请先开启定位',
                confirmText: '去设置',
                success: res => {
                  if (res.confirm) {
                    openSettings();
                  }
                },
              });
            }
            return false;
          }
        },
      );
    } else if (isIos()) {
      requestMultiple([PERMISSIONS.IOS.LOCATION_WHEN_IN_USE]).then(statuses => {
        console.log('statuses[PERMISSIONS.IOS.LOCATION_WHEN_IN_USE]=', statuses[PERMISSIONS.IOS.LOCATION_WHEN_IN_USE]);
        if (statuses[PERMISSIONS.IOS.LOCATION_WHEN_IN_USE] === RESULTS.GRANTED) {
          return true;
        } else {
          console.log(openSeting);
          if (openSeting) {
            showModal({
              title: '获取定位权限失败',
              content: '可能会影响您的正常使用，请先开启定位',
              confirmText: '去设置',
              success: res => {
                if (res.confirm) {
                  openSettings();
                }
              },
            });
          }
          return false;
        }
      });
    } else {
      return true;
    }
  } catch (error) {
    console.log('Location Accuracy Permission Error: ', error);
    return false;
  }
};
//判断经纬度有效
const isValid = (latitude: string | number, longitude: string | number) => {
  if (latitude === undefined || latitude === null || longitude === undefined || longitude === null) {
    return false;
  }

  if (
    (typeof latitude !== 'number' && typeof latitude !== 'string') ||
    (typeof longitude !== 'number' && typeof longitude !== 'string')
  ) {
    return false;
  }
  if (typeof latitude === 'number') {
    if (Number.isNaN(latitude) || latitude < 0) {
      return false;
    }
  }

  if (typeof longitude === 'number') {
    if (Number.isNaN(longitude) || longitude < 0) {
      return false;
    }
  }

  if (typeof latitude === 'string') {
    if (latitude.length === 0 || Number.isNaN(parseFloat(latitude)) || parseFloat(latitude) < 0) {
      return false;
    }
  }

  if (typeof longitude === 'string') {
    if (longitude.length === 0 || Number.isNaN(parseFloat(longitude)) || parseFloat(longitude) < 0) {
      return false;
    }
  }
  return true;
};

/**
 * 处理高德逆地理位置问题（在没值的时候返回的是空数组，而不是空字符串。处理返回空字符串）
 * @param address
 * @returns
 */
const controlAddressForNotArray: (address: API.AddressComponent) => API.AddressComponent = address => {
  console.log('处理前=', address);
  const newAddress = {...address};
  newAddress.country = controlWord(newAddress.country);
  newAddress.countryCode = controlWord(newAddress.countryCode);
  newAddress.province = controlWord(newAddress.province);
  newAddress.city = controlWord(newAddress.city);
  newAddress.citycode = controlWord(newAddress.citycode);
  newAddress.district = controlWord(newAddress.district);
  newAddress.adcode = controlWord(newAddress.adcode);
  if (newAddress.streetNumber) {
    newAddress.streetNumber.number = controlWord(newAddress.streetNumber.number);
    newAddress.streetNumber.street = controlWord(newAddress.streetNumber.street);
    newAddress.streetNumber.direction = controlWord(newAddress.streetNumber.direction);
    newAddress.streetNumber.distance = controlWord(newAddress.streetNumber.distance);
  }
  newAddress.towncode = controlWord(newAddress.towncode);
  newAddress.township = controlWord(newAddress.township);
  console.log('处理后=', newAddress);
  return newAddress;
};

//当为空时，和1.0保持一致，传[]字符串
const controlWord: (name: any) => string = name => {
  if (name === undefined) return '[]';
  if (typeof name === 'string') {
    return name;
  } else if (Array.isArray(name)) {
    if (name.length > 0) {
      return controlWord(name[0]);
    } else {
      return '[]';
    }
  } else {
    return '[]';
  }
};
