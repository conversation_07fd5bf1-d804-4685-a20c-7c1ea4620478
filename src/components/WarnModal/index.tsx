import { Text, View } from '@tarojs/components';
import { CyModal } from 'hncy58-taro-components';
import React, { useState, useEffect, useCallback, useMemo } from 'react';
import './index.scss'
import Taro from '@tarojs/taro';

type WarnModalProps = {
	visible: boolean // 弹窗1是否显示
	onResult?: (result: 'YES' | 'NO' | 'KNOW') => void // 弹窗1结果回调
}

const WarnModal = ({ visible, onResult }: WarnModalProps) => {
	const [firstVisible, setFirstVisible] = useState(false)
	const [countdown, setCountdown] = useState(3)
	const [canClick, setCanClick] = useState(false)
	const [secondVisible, setSecondVisible] = useState(false)

	useEffect(() => {
		setTimeout(() => {
			setFirstVisible(visible)
		}, 800)
	}, [visible])

	// 倒计时效果
	useEffect(() => {
		let timer: NodeJS.Timeout | null = null;

		if (secondVisible && countdown > 0) {
			timer = setTimeout(() => setCountdown(prev => prev - 1), 1000)
		} else if (countdown === 0) {
			setCanClick(true)
		}

		return () => {
			if (timer) clearTimeout(timer)
		}
	}, [secondVisible, countdown])

	// 重置倒计时（当弹窗打开时）
	useEffect(() => {
		if (secondVisible) {
			setCountdown(3)
			setCanClick(false)
		}
	}, [secondVisible])

	// 统一处理按钮点击
	const handleAction = useCallback((action: 'yes' | 'no' | 'know' | 'close1' | 'close2') => {
		switch (action) {
			case 'yes':
				setFirstVisible(false)
				setSecondVisible(true)
				onResult?.('YES')
				break
			case 'no':
				setFirstVisible(false)
				onResult?.('NO')
				break
			case 'know':
				if (canClick) {
					setSecondVisible(false)
					onResult?.('KNOW')
				}
				break
			case 'close1':
				setFirstVisible(false)
				break
			case 'close2':
				setSecondVisible(false)
				break
		}
	}, [setFirstVisible, onResult, canClick])

	const firstModalContent = useMemo(() => (
		<View className='inquiryContent'>
			<View className='inquiryMain'>
				<View className='inquiryTip'>长银五八金融邀请您完成防电诈验证</View>
				<View className='inquiryTitle'>
					是否有<Text className='highlight fontweight'>陌生人引导</Text>您<Text className='highlight fontweight'>借款、转账</Text>？
				</View>
				<View className='inquiryText'>
					凡以"降低利率""提升额度"等理由要求您转账至个人账户或非长银五八对公账户的行为，都是诈骗，请一律不要听信，不要转账。
				</View>
				<View className='inquiryContainer'>
					<View className='inquiryContact'>
						如有疑问，请询我司官方热线：
					</View>
					<View className='inquiryPhone' onClick={() => {Taro.makePhoneCall({phoneNumber: '400-88-96511'})}}>400-88-96511</View>
				</View>
			</View>
			<View className='buttonGroup'>
				<View className='yes' onClick={() => handleAction('yes')}>有</View>
				<View className='no' onClick={() => handleAction('no')}>没有</View>
			</View>
		</View>
	), [handleAction])

	const secondModalContent = useMemo(() => (
		<View className='inquiryContent knowContainer'>
			<View className='inquiryTitle' style={{padding: 0}}>操作<Text className='highlight fontweight'>有风险</Text>,暂时无法借款</View>
			<View className='inquiryText'>请注意!任何以<Text className='highlight fontweight'>"降低利息""提升额度"</Text>等理由要求您转账到个人账户或者非指定对公账户的行为都是<Text className='highlight fontweight'>诈骗</Text>!</View>
			<View
				className='iknow'
				style={{ opacity: canClick ? 1 : 0.2 }}
				onClick={() => handleAction('know')}
			>
				我知道了{countdown > 0 ? `(${countdown})` : ''}
			</View>
		</View>
	), [canClick, countdown, handleAction])

	return (
		<>
			<CyModal
				visible={firstVisible}
				onClose={() => handleAction('close1')}
				modalRender={firstModalContent}
			/>
			<CyModal
				visible={secondVisible}
				onClose={() => handleAction('close2')}
				modalRender={secondModalContent}
			/>
		</>
	)
};

export default WarnModal;