import {
  ComponentType,
  ForwardRefRenderFunction,
  forwardRef,
  useContext,
  useImperativeHandle,
  useRef,
  useState,
} from 'react';
import {BaseEventOrig, CommonEventFunction, Input, InputProps, Target, View, ViewProps} from '@tarojs/components';
import {ENV_TYPE, getEnv} from '@tarojs/taro';
import {FocusContext} from '../CyKeyboardAvoidingView';
import './index.scss';

interface HooksTarget extends Target {
  name?: string;
}

interface BaseHooksEventOrig<T> extends BaseEventOrig<T> {
  target: HooksTarget;
}

type eventType = BaseHooksEventOrig<InputProps.inputEventDetail>;

interface Props extends InputProps {
  onChange?: (event: unknown) => void;
  onChangeOther?: (event: unknown) => void;
  onPaste?: (event: React.ClipboardEvent) => void;
  disabled?: boolean;
  controled?: boolean; //默认不可控，设置为可控是为了实时限制输入框的输入符合规定(如只可以输入数字等)
}

/**
 * 组件名称：输入框组件
 * 组件描述：包装taro输入框组件、为接入react-hook-form等库做处理
 */
const CyInput: ForwardRefRenderFunction<unknown, Props> = ({value, controled = false, ...props}, ref) => {
  const myRef = useRef<ComponentType<ViewProps>>(null);
  let getAbsolutePosition: () => void;
  const emitEvent = useContext(FocusContext);
  const [currentVal, setCurrentVal] = useState<string | undefined>(value);

  useImperativeHandle(ref, () => ({
    get value() {
      return currentVal;
    },
    set value(newValue) {
      console.log('setValue', newValue);
      setCurrentVal(newValue);
    },
  }));
  //  RN键盘做特殊处理
  if (getEnv() === ENV_TYPE.RN) {
    // 计算组件相对于屏幕的位置
    getAbsolutePosition = () => {
      if (myRef.current) {
        const current = myRef.current as any;
        if (current.measureInWindow) {
          current.measureInWindow((x: number, y: number, width: number, height: number) => {
            emitEvent({x, y});
          });
        }
      }
    };
  }

  let newOnInput = (e: eventType) => {};
  // 对RN端做特殊处理，因为RN端input组件无onChange事件且env target 与 h5端有差异
  if (getEnv() === ENV_TYPE.RN) {
    newOnInput = (e: eventType) => {
      setCurrentVal(e.detail.value);
      const newVal = {
        target: {
          name: props.name,
          value: e.detail.value,
        },
        detail: {
          value: e.detail.value,
        },
      };
      props.onInput?.(e);
      props.onChange?.(newVal);
      props.onChangeOther?.(newVal);
      console.log('输入=', e.detail.value);
    };
  } else {
    newOnInput = (e: eventType) => {
      setCurrentVal(e.detail.value);
      props.onInput?.(e);
      props.onChange?.(e);
      props.onChangeOther?.(e);
    };
  }

  const onInput = (e: eventType) => {
    const {value} = e.detail
    setCurrentVal(value);
    props.onInput?.(e);
  };

  const onFocus: CommonEventFunction<InputProps.inputForceEventDetail> = e => {
    props.onFocus && props.onFocus(e);
    getAbsolutePosition && getAbsolutePosition();
  };

  let customerValue: any;
  if (controled) {
    customerValue = {value};
  }
  return (
    <>
      {/* RN中只有View才能取到Ref，为了不影响style等传递定义一个空view */}
      {process.env.TARO_ENV === 'rn' && <View ref={myRef}></View>}
      <Input
        {...props}
        onInput={props.onInput ? onInput : newOnInput}
        style={props.style}
        defaultValue={value ?? currentVal}
        {...customerValue}
        className={`${props.className} comp-cy-input`}
        onFocus={e => {
          onFocus(e);
        }}></Input>
    </>
  );
};

export default forwardRef(CyInput);
