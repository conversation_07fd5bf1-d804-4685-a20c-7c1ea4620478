import Taro from '@tarojs/taro';
import {produce} from 'immer';
import {create} from 'zustand';
import {devtools, persist, subscribeWithSelector} from 'zustand/middleware';
import { getAccessToken } from '~services/user';
import useUserStore from './user';
import { getSystemConfig } from '~services/system';


type MainState = {
  tabbarIndex: number;
  config: API.ISystemConfigRes,
  setTabbarIndex: (index: number) => void;
  setConfig: (config: API.ISystemConfigRes) => void;
  updateConfig: () => Promise<void>;
  token: null | string;
  setToken: (token: string) => void;
};
const initTabbarIndex = 0;

const initConfig = {
  checkContactNames: "",
  customerRepaymentHistoryMaxMonth: 6,
  echoInfoEffectiveInterval: 3,
  enableModifyRepaymentDate: true,
  forceReadSeconds: 5,
  ocrAlbumSwitch: true,
  checkFaceResults: true,
}
const useMainStore = create(
  subscribeWithSelector(
    devtools<MainState>((set, get) => ({
      tabbarIndex: initTabbarIndex,
      isInitReady: 'init',
      config: initConfig,
      token: null,
      setTabbarIndex: index => {
        set(
          produce(state => {
            state.tabbarIndex = index;
          }),
          false,
          'setTabbarIndex',
        );
      },
      setToken: (token: string) => {
        set({token});
      },
      setConfig: (config: API.ISystemConfigRes) => {
        set(
          produce(state => {
            state.config = config;
          }),
          false,
         'setConfig',
        );
      },
      updateConfig: async () => {
        try {
          const res = await getSystemConfig();
          set({config: res});
        } catch(error) {
          console.error('updateConfig err:::', error);
        }
      }
    })),
  )
);


export default useMainStore;