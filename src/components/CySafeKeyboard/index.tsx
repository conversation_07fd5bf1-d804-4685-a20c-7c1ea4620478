import {ForwardRefRenderFunction, forwardRef, useImperativeHandle} from 'react';
import {View} from '@tarojs/components';
import Taro from '@tarojs/taro';
import './index.scss';

export interface CySafeKeyboardRef {
  show: () => void; //显示
  hidden: () => void; //隐藏
  clearInputValue: () => void; //清空键盘输入
  getEncryptedInputValue: () => {errorCode: string; data: string}; //获取密码加密结果
  getEncryptedClientRandom: () => {errorCode: string; data: string}; //获取客户端随机数加密结果
  checkInputValueMatch: (otherId: string) => boolean; //判断两个输入是否一致
  doneClick: () => void; //点击完成按钮
}

export type CySafeKeyboardProps = Omit<
  SafeKeyboardProps,
  | 'cipherType'
  | 'outputType'
  | 'inputRegex'
  | 'keyword'
  | 'isEncryptState'
  | 'isShowKeyboard'
  | 'isKeyAnimation'
  | 'showLastCharacter'
  | 'precision'
  | 'isHiddenBottom'
  | 'allowScreenRecord'
>;
/**
 * 组件名称：密码键盘
 * 组件描述：密码键盘
 */
const CySafeKeyboard: ForwardRefRenderFunction<CySafeKeyboardRef, CySafeKeyboardProps> = (
  {
    onInputClick,
    isClickShowboard = true,
    sipId,
    serverRandom,
    maxLength = 8,
    minLength = 6,
    isGrid = false,
    keyboardType,
    displayMode = 0,
    orderType = 0,
    placeholder,
    onInputChangeCallBack,
    onDoneClick,
  },
  ref,
) => {
  const showLastCharacter = true;
  const isEncryptState = true;
  const precision = 2;
  useImperativeHandle(ref, () => ({
    show: () => {
      //
    },
    hidden: () => {
      //
    },
    getVersion: () => {
      return '';
    },
    clearInputValue: () => {
      return {errorCode: 'string', data: 'string'};
    },
    getEncryptedInputValue: () => {
      return {errorCode: 'string', data: 'string'};
    },
    getEncryptedClientRandom: () => {
      return {errorCode: 'string', data: 'string'};
    },
    checkInputValueMatch: (otherId: string) => {
      return true;
    },
    getInputValue: () => {
      return {errorCode: 'string', data: 'string'};
    },
    doneClick: () => {
      //
    },
  }));
  return <></>;
};

export default forwardRef(CySafeKeyboard);
