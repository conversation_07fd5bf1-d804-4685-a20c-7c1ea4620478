import {useState} from 'react';
import {View} from '@tarojs/components';
import styles from './index.module.scss';
import classNames from 'classnames';

type SchedulerPlanCollapseItemProps = {
  collapseList: {
    title: React.JSX.Element;
    content: React.JSX.Element;
  }[];
};
const SchedulerPlanCollapse = ({collapseList}: SchedulerPlanCollapseItemProps) => {
  const [openIndex, setOpenIndex] = useState<number | null>(null);

  const handleToggle = (idx: number) => {
    setOpenIndex(openIndex === idx ? null : idx);
  };
  return (
    <View className={styles.schedulerBar}>
      {collapseList.map((item, idx) => (
        <View key={idx} className={styles.item}>
          <View className={styles.dot}></View>
          <View className={styles.line}></View>
          <View className={styles.content}>
            <View className={classNames(styles.header, idx === openIndex ? 'scheduler-collapse-active' : '')} onClick={() => handleToggle(idx)}>
              {item.title}
            </View>

            <View className={`${styles.detail} ${openIndex === idx ? styles.detailOpen : ''}`}>{item.content}</View>
          </View>
        </View>
      ))}
    </View>
  );
};

export default SchedulerPlanCollapse;
