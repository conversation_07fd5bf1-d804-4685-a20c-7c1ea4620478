import {View, Text, Image} from '@tarojs/components';
import styles from './index.module.scss';
import PageWrap from '~components/PageWrap';
import { CyButton } from 'hncy58-taro-components';
import RejectIcon from '~images/raiseamount/<EMAIL>'
import SuccessIcon from '~images/raiseamount/<EMAIL>'
import FailIcon from '~images/raiseamount/<EMAIL>'
import NoCreditIcon from '~images/raiseamount/<EMAIL>'
import Clock from '~components/ClockAniCanvas';
import { useState } from 'react';
import { useLoad } from '@tarojs/taro';
import { router } from '~utils/common';
import dayjs from 'dayjs';

type statusStr = 'success' | 'reject' | 'fail' | 'nodate'

const RaiseAmount = () => {
  /** 1. 变量定义 */
  const StatusMap: Record<statusStr, {icon: React.ReactNode; title: string; subtitle: string}> = {
    'success': {
      icon: <Image src={SuccessIcon} className={styles.stateIcon} />,
      title: '提交成功',
      subtitle: '如果提额成功,您将会收到短信通知哦~'
    },
    'nodate': {
      icon: <Image src={RejectIcon} className={styles.stateIcon} />,
      title: '评估未通过',
      subtitle: '很抱歉,我们暂时无法为您提供更高的额度,请稍后再试,感谢您对产品的支持!'
    },
    'fail': {
      icon: <Image src={FailIcon} className={styles.stateIcon} />,
      title: '提交失败',
      subtitle: '提额失败,请继续保持良好征信~'
    },
    'reject': {
      icon: <Image src={NoCreditIcon} className={styles.stateIcon} />,
      title: '评估未通过',
      subtitle: '您未获得我公司授信额度,感谢您的持续关注~'
    },
  }

  /** 2. usestates + storestates */
  const [status, setStatus] = useState<statusStr>('success');
  const [nextApplyDate, setNextApplyDate] = useState('');

  /** 3. effects */
  useLoad(({ state, nextApplyDate }) => {
    if (state) {
      setStatus(state);
      if (nextApplyDate) {
        setNextApplyDate(nextApplyDate);
      }
    }
  })

  /** 4. 方法定义 */
  const handleBack = () => {
    router.back();
  };

  /** 5. ui */
  return (
    <PageWrap className={styles['page-raise-amount']}>
      <View className={styles.container}>
        {/* 状态图标区域 */}
        {StatusMap[status].icon}
        
        {/* 标题 */}
        <Text className={styles.title}>{StatusMap[status].title}</Text>
        
        {/* 描述文字 */}
        <Text className={styles.description}>
        {status === 'nodate' ? `很抱歉,我们暂时无法为您提供更高的额度,请${dayjs(nextApplyDate).format("YYYY年MM月DD日")}后再试,感谢您对产品的支持!` : StatusMap[status].subtitle}
        </Text>
        
        {/* 开始评估按钮 */}
        <View className={styles.buttonContainer} onClick={handleBack}>
          <CyButton round block type='primary'>返回</CyButton>
        </View>
      </View>
    </PageWrap>
  );
};

export default RaiseAmount;
