import React, {FC, PropsWithChildren, useEffect, useRef, useState} from 'react';
import {ScrollView, Text, View} from '@tarojs/components';
import Taro from '@tarojs/taro';
import CyScrollView from '../CyScrollView';
import CyTab from '../CyTab';
import './index.scss';

interface IScrollTabProps {
  children: React.ReactNode;
  disableScroll: boolean;
  onTabChange?: (index: number) => void;
}

interface IPanelProps extends PropsWithChildren {
  title: string;
}

type TCyScrollTab = FC<IScrollTabProps> & {TabPanel: FC<IPanelProps>};

/**
 * 组件名称：支持内容及tab本身的x方向的滚动
 * 组件描述：包含tab点击，active，多tab展示等等
 */
const CyScrollTab: TCyScrollTab = ({children, disableScroll, onTabChange: _tabChange}) => {
  const scrollViewRef = useRef<any>();
  const [tabIndex, setTabIndex] = useState(0);
  let windowWidth = 0;
  Taro.getSystemInfo({
    success(res) {
      windowWidth = res.windowWidth;
    },
    fail(err) {
      console.error('获取屏幕信息失败', err);
    },
  });
  const onTabChange = (index: number) => {
    if (scrollViewRef.current && scrollViewRef.current.scrollToOffset) {
      scrollViewRef.current.scrollToOffset(index * windowWidth);
    } else {
      setTabIndex(index);
    }
  };
  const tabs = Array.isArray(children)
    ? children
        .map(tab => {
          return tab.props?.title as string;
        })
        .filter(it => it)
    : [(children as React.ReactElement)?.props?.title as string];

  useEffect(() => {
    _tabChange && _tabChange(tabIndex);
  }, [tabIndex]);
  return (
    <View className='comp-cy-scroll-tab'>
      <View style={{height: 50}}>
        <CyTab tabs={tabs} onChange={onTabChange} currentIndex={tabIndex}></CyTab>
      </View>
      {process.env.TARO_ENV === 'h5' || disableScroll ? (
        <>
          {Array.isArray(children) ? (
            <View className='content-item'>{children.find((child, idx) => tabIndex === idx)}</View>
          ) : (
            <View className='content-item'>{children}</View>
          )}
        </>
      ) : (
        <CyScrollView
          className='scroll-tab-scrollview'
          scrollWithAnimation
          scrollX
          ref={scrollViewRef}
          enhanced
          pagingEnabled
          showScrollbar={false}
          onScroll={e => {
            const num = Math.floor(e.detail.scrollLeft / windowWidth);
            const modRest = e.detail.scrollLeft % windowWidth;
            // 剩余的量超过半屏就滚动下一页，否则回弹
            if (modRest > windowWidth / 2) {
              setTabIndex(num + 1);
            } else {
              setTabIndex(Math.max(0, num));
            }
          }}>
          {React.Children.map(children, child => {
            return <View className='content-item'>{child}</View>;
          })}
        </CyScrollView>
      )}
    </View>
  );
};

CyScrollTab.TabPanel = ({title, children}) => children;

export default CyScrollTab;
