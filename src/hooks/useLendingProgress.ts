import { useDidShow } from "@tarojs/taro";
import { useEffect, useState } from "react";
import { lendingPreProgress } from "~services/lending";
import useUserStore from "~store/user";

interface IStateMap {
    needTradePassword: boolean | undefined;
    needContactInfo: boolean | undefined;
    needRefillAddress: boolean | undefined;
    needSupplementInfo: boolean | undefined;
    needBankCard: boolean | undefined;
}

export const useLendingProgress =  (): [IStateMap, string, string] => {
    const [StateMap, setStateMap] = useState<IStateMap>({
        needTradePassword: undefined,
        needContactInfo: undefined,
        needRefillAddress: undefined,
        needSupplementInfo: undefined,
        needBankCard: undefined
    });

    const [progressCode, setProgressCode] = useState<string>('');
    const [progressDesc, setProgressDesc] = useState<string>('');
    const {user} = useUserStore();

    const getLendingPreProgress = async () => {
        if(!user.registerFlag) return;
      const {progressCode, needTradePassword, progressDesc, needContactInfo, needRefillAddress, needSupplementInfo, needBankCard} =
      await lendingPreProgress();
      setStateMap({
        needTradePassword,
        needContactInfo,
        needRefillAddress,
        needSupplementInfo,
        needBankCard
      })
      setProgressCode(progressCode);
      setProgressDesc(progressDesc);
    }

    useDidShow(() => {
        if(progressCode !== 'LENDING') {
            getLendingPreProgress()
        }
    })


    return [
        StateMap,
        progressCode,
        progressDesc,
    ]
}