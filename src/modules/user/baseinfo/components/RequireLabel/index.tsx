import { Text, View } from '@tarojs/components';
import React from 'react';

interface RequiredLabelProps {
  label: string;
  required?: boolean;
}

const RequiredLabel: React.FC<RequiredLabelProps> = ({ label, required = false }) => {
  return (
    <View>
      {required && <Text style={{ color: '#F5222D', verticalAlign: 'middle' }}>*</Text>}
      {label}
    </View>
  );
};

export default RequiredLabel;