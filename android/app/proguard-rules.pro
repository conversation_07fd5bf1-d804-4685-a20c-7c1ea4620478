# Add project specific ProGuard rules here.
# By default, the flags in this file are appended to flags specified
# in /usr/local/Cellar/android-sdk/24.3.3/tools/proguard/proguard-android.txt
# You can edit the include path and order by changing the proguardFiles
# directive in build.gradle.
#
# For more details, see
#   http://developer.android.com/guide/developing/tools/proguard.html

# Add any project specific keep options here:
#3D 地图 V5.0.0之后：
-keep   class com.amap.api.maps.**{*;}
-keep   class com.autonavi.**{*;}
-keep   class com.amap.api.trace.**{*;}

#定位
-keep class com.amap.api.location.**{*;}
-keep class com.amap.api.fence.**{*;}
-keep class com.autonavi.aps.amapapi.model.**{*;}

#搜索
-keep   class com.amap.api.services.**{*;}

#2D地图
-keep class com.amap.api.maps2d.**{*;}
-keep class com.amap.api.mapcore2d.**{*;}

#导航
-keep class com.amap.api.navi.**{*;}
-keep class com.autonavi.**{*;}

# 保留了Bugly所需的类和成员变量
-dontwarn com.tencent.bugly.**
-keep public class com.tencent.bugly.**{*;}
-keep class android.support.**{*;}


# 如果你有自定义的Runnable，需要保留
-keepclassmembers class * extends java.lang.Runnable {
    public void run();
}

# 保留Parcelable的所有字段和对应方法
-keep class * implements android.os.Parcelable {
    public static final android.os.Parcelable$Creator *;
}

# 保留自定义的Application类
-keep public class com.hncy58.financeapp.MainApplication {
    *;
}

# 保留本地方法不被混淆
-keepclasseswithmembernames class * {
    native <methods>;
}

# 保留所有的enum类和方法
-keepclassmembers enum * {
    public static **[] values();
    public static ** valueOf(java.lang.String);
}

# 保留所有View的onClick方法
-keepclassmembers class * extends android.app.Activity {
   public void *(android.view.View);
}

# 保留所有的Serializable类不被混淆
-keepnames class * implements java.io.Serializable
