import React, {useCallback} from 'react';
import {Picker, Popup} from '@taroify/core';
import {IPickerItem} from 'src/types/common-types';

const PickerPopup: React.FC<{
  range: IPickerItem[];
  openPicker: boolean;
  setOpenPicker: (open: boolean) => void;
  onChange?: (value: any, options: any[]) => void;
  value?: any;
}> = ({range, openPicker, setOpenPicker, onChange, value}) => {
  const handleClose = useCallback(() => setOpenPicker(false), [setOpenPicker]);

  const handleConfirm = useCallback(
    (value: any, options: any[]) => {
      onChange?.(value, options);
      setOpenPicker(false);
    },
    [onChange, setOpenPicker],
  );

  return (
    <Popup open={openPicker} rounded placement='bottom' onClose={handleClose}>
      <Popup.Backdrop />
      <Picker columns={range} onCancel={handleClose} onConfirm={handleConfirm} value={value} />
    </Popup>
  );
};

export default PickerPopup;
