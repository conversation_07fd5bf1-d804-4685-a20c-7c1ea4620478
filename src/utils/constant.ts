const MaxCreditAmount = 200000;
const InterestRateRange = [10.8, 21.6]; // 示例数值区间
const InterestPerday = [3, 6];

const ContractCode2Info: Record<string, {name: string, api: string;}> = {
    'E23': {
        name: '《用户注册协议》',
        api: 'getUserRegisterProtocal'
    },
    'E08': {
        name: '《个人征信授权书》',
        api: 'getCreditAuthContract'
    },
    'E09': {
        name: '《个人信息授权书》',
        api: 'getPersonInfoAuthContract'
    },
    'E69': {
        name: '《非学生身份承诺函》',
        api: 'getNoStudentContract'
    },
    'E53': {
        name: '《个人借据额度合同》',
        api: 'getLoanLimitContract'
    },
    'E43': {
        name: '《敏感个人信息授权书》',
        api: 'getSensitiveInfoAuthContract'
    },
    'E51': {
        name: '《长银五八信贷从业人员清廉信贷从业告知书》',
        api: 'getIncorruptibleCreditNoticeContract'
    },
    'E54': {
        name: '《借款借据》',
        api: 'getLendingLoanContract'
    },
    'E24': {
        name: '《委托扣款授权书》',
        api: 'getDeductionContract'
    },
    'E151': {
        name: '《贷款资金用途提示及用途监测授权书》',
        api: 'getLendingPurposeContract'
    },
    'E105': {
        name: '《联系（送达）信息确认书》',
        api: 'getConnectionInfoContract'
    },
    'privacy': {
        name: '《隐私政策》',
        api: ''
    }
}

export {MaxCreditAmount, InterestRateRange, InterestPerday, ContractCode2Info};
